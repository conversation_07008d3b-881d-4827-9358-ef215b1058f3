<?xml version="1.0"?>
<Definition xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <Table Name="Achievement" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Faction" Type="int" />
    <Field Name="Instance_Id" Type="int" />
    <Field Name="Supercedes" Type="int" />
    <Field Name="Title_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="Category" Type="int" />
    <Field Name="Points" Type="int" />
    <Field Name="Ui_Order" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="IconID" Type="int" />
    <Field Name="Reward_Lang" Type="string" />
    <Field Name="Minimum_Criteria" Type="int" />
    <Field Name="Shares_Criteria" Type="int" />
    <Field Name="Criteria_Tree" Type="int" />
  </Table>
  <Table Name="Achievement_Category" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Parent" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Ui_Order" Type="int" />
  </Table>
  <Table Name="AdventureJournal" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="string" />
    <Field Name="Field07" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field09" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field11" Type="ushort" ArraySize="2" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field13" Type="ushort" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field20" Type="byte" ArraySize="2" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
  </Table>
  <Table Name="AdventureMapPOI" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" ArraySize="2" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field13" Type="int" />
  </Table>
  <Table Name="AnimationData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Flags" Type="int" />
    <Field Name="Fallback" Type="int" />
    <Field Name="BehaviorID" Type="int" />
    <Field Name="BehaviorTier" Type="int" />
  </Table>
  <Table Name="AnimKit" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="OneShotDuration" Type="int" />
    <Field Name="OneShotStopAnimKitID" Type="int" />
    <Field Name="LowDefAnimKitID" Type="int" />
  </Table>
  <Table Name="AnimKitBoneSet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="BoneDataID" Type="int" />
    <Field Name="ParentAnimKitBoneSetID" Type="int" />
    <Field Name="ExtraBoneCount" Type="int" />
    <Field Name="AltAnimKitBoneSetID" Type="int" />
  </Table>
  <Table Name="AnimKitBoneSetAlias" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="BoneDataID" Type="int" />
    <Field Name="AnimKitBoneSetID" Type="int" />
  </Table>
  <Table Name="AnimKitConfig" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ConfigFlags" Type="int" />
  </Table>
  <Table Name="AnimKitConfigBoneSet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ParentAnimKitConfigID" Type="int" />
    <Field Name="AnimKitBoneSetID" Type="int" />
    <Field Name="AnimKitPriorityID" Type="int" />
  </Table>
  <Table Name="AnimKitPriority" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Priority" Type="int" />
  </Table>
  <Table Name="AnimKitSegment" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ParentAnimKitID" Type="int" />
    <Field Name="OrderIndex" Type="int" />
    <Field Name="AnimID" Type="int" />
    <Field Name="AnimStartTime" Type="int" />
    <Field Name="AnimKitConfigID" Type="int" />
    <Field Name="StartCondition" Type="int" />
    <Field Name="StartConditionParam" Type="int" />
    <Field Name="StartConditionDelay" Type="int" />
    <Field Name="EndCondition" Type="int" />
    <Field Name="EndConditionParam" Type="int" />
    <Field Name="EndConditionDelay" Type="int" />
    <Field Name="Speed" Type="float" />
    <Field Name="SegmentFlags" Type="int" />
    <Field Name="ForcedVariation" Type="int" />
    <Field Name="OverrideConfigFlags" Type="int" />
    <Field Name="LoopToSegmentIndex" Type="int" />
  </Table>
  <Table Name="AnimReplacement" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SrcAnimID" Type="int" />
    <Field Name="DstAnimID" Type="int" />
    <Field Name="ParentAnimReplacementSetID" Type="int" />
  </Table>
  <Table Name="AnimReplacementSet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ExecOrder" Type="int" />
  </Table>
  <Table Name="AreaAssignment" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="AreaID" Type="int" />
    <Field Name="ChunkX" Type="int" />
    <Field Name="ChunkY" Type="int" />
  </Table>
  <Table Name="AreaGroup" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" AutoGenerate="true" />
    <Field Name="AreaID" Type="int" />
  </Table>
  <Table Name="AreaGroupMember" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AreaGroupId" Type="ushort" />
    <Field Name="AreaId" Type="ushort" />
  </Table>
  <Table Name="AreaPOI" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Importance" Type="int" />
    <Field Name="Icon" Type="int" />
    <Field Name="FactionID" Type="int" />
    <Field Name="Pos" Type="float" ArraySize="2" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="AreaID" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="WorldStateID" Type="int" />
    <Field Name="PlayerConditionID" Type="int" />
    <Field Name="WorldMapLink" Type="int" />
    <Field Name="PortlocID" Type="int" />
  </Table>
  <Table Name="AreaPOIState" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AreaPoiID" Type="int" />
    <Field Name="State" Type="int" />
    <Field Name="Icon" Type="int" />
    <Field Name="Description" Type="string" />
  </Table>
  <Table Name="AreaTable" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="ParentAreaID" Type="int" />
    <Field Name="AreaBit" Type="int" />
    <Field Name="Flags" Type="int" ArraySize="2" />
    <Field Name="SoundProviderPref" Type="int" />
    <Field Name="SoundProviderPrefUnderwater" Type="int" />
    <Field Name="AmbienceID" Type="int" />
    <Field Name="ZoneMusic" Type="int" />
    <Field Name="ZoneName" Type="string" />
    <Field Name="IntroSound" Type="int" />
    <Field Name="ExplorationLevel" Type="int" />
    <Field Name="AreaName_Lang" Type="string" />
    <Field Name="FactionGroupMask" Type="int" />
    <Field Name="LiquidTypeID" Type="int" ArraySize="4" />
    <Field Name="Ambient_Multiplier" Type="float" />
    <Field Name="MountFlags" Type="int" />
    <Field Name="UwintroSound" Type="int" />
    <Field Name="UwZoneMusic" Type="int" />
    <Field Name="UwAmbience" Type="int" />
    <Field Name="World_Pvp_Id" Type="int" />
    <Field Name="PvpCombatWorldStateID" Type="int" />
    <Field Name="WildBattlePetLevelMin" Type="int" />
    <Field Name="WildBattlePetLevelMax" Type="int" />
    <Field Name="WindSettingsID" Type="int" />
  </Table>
  <Table Name="AreaTrigger" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="PhaseUseFlags" Type="int" />
    <Field Name="PhaseID" Type="int" />
    <Field Name="PhaseGroupID" Type="int" />
    <Field Name="Radius" Type="float" />
    <Field Name="Box_Length" Type="float" />
    <Field Name="Box_Width" Type="float" />
    <Field Name="Box_Height" Type="float" />
    <Field Name="Box_Yaw" Type="float" />
    <Field Name="ShapeType" Type="int" />
    <Field Name="ShapeID" Type="int" />
    <Field Name="AreaTriggerActionSetID" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="AreaTriggerActionSet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="AreaTriggerBox" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Extents" Type="float" ArraySize="3" />
  </Table>
  <Table Name="AreaTriggerCylinder" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Radius" Type="float" />
    <Field Name="Height" Type="float" />
  </Table>
  <Table Name="AreaTriggerSphere" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MaxRadius" Type="float" />
  </Table>
  <Table Name="Armorlocation" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Clothmodifier" Type="float" />
    <Field Name="Leathermodifier" Type="float" />
    <Field Name="Chainmodifier" Type="float" />
    <Field Name="Platemodifier" Type="float" />
    <Field Name="Modifier" Type="float" />
  </Table>
  <Table Name="Artifact" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="ArtifactAppearance" Build="21742">
    <Field Name="Field00" Type="string" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field07" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field13" Type="int" />
    <Field Name="Field14" Type="int" />
  </Table>
  <Table Name="ArtifactAppearanceSet" Build="21742">
    <Field Name="Field00" Type="string" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ArtifactCategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="ArtifactPower" Build="21742">
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="ArtifactPowerLink" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="ArtifactPowerRank" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="ArtifactQuestXP" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" ArraySize="10" />
  </Table>
  <Table Name="ArtifactUnlock" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="AttackAnimKits" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Animation" Type="int" />
    <Field Name="Type" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="AttackAnimTypes" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="AuctionHouse" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FactionID" Type="int" />
    <Field Name="DepositRate" Type="int" />
    <Field Name="ConsignmentRate" Type="int" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="BankBagSlotPrices" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Cost" Type="int" />
  </Table>
  <Table Name="BannedAddOns" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="NameMD5_" Type="uint" ArraySize="4" />
    <Field Name="VersionMD5_" Type="uint" ArraySize="4" />
    <Field Name="LastModified" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="BarberShopStyle" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="int" />
    <Field Name="DisplayName_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="Cost_Modifier" Type="float" />
    <Field Name="Race" Type="int" />
    <Field Name="Sex" Type="int" />
    <Field Name="Data" Type="int" />
  </Table>
  <Table Name="BattlemasterList" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" ArraySize="16" />
    <Field Name="InstanceType" Type="int" />
    <Field Name="GroupsAllowed" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="MaxGroupSize" Type="int" />
    <Field Name="HolidayWorldState" Type="int" />
    <Field Name="Minlevel" Type="int" />
    <Field Name="Maxlevel" Type="int" />
    <Field Name="RatedPlayers" Type="int" />
    <Field Name="MinPlayers" Type="int" />
    <Field Name="MaxPlayers" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="IconFileDataID" Type="int" />
    <Field Name="GameType_Lang" Type="string" />
    <Field Name="PlayerConditionID" Type="int" />
  </Table>
  <Table Name="BattlePetAbility" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="string" />
    <Field Name="Field07" Type="string" />
  </Table>
  <Table Name="BattlePetAbilityEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" ArraySize="6" />
  </Table>
  <Table Name="BattlePetAbilityState" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="BattlePetAbilityTurn" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="BattlePetBreedQuality" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="float" />
  </Table>
  <Table Name="BattlePetBreedState" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="BattlePetEffectProperties" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="string" ArraySize="6" />
    <Field Name="Field03" Type="int" ArraySize="6" />
  </Table>
  <Table Name="BattlePetNPCTeamMember" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
  </Table>
  <Table Name="BattlePetSpecies" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="string" />
    <Field Name="Field08" Type="string" />
  </Table>
  <Table Name="BattlePetSpeciesState" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="BattlePetSpeciesXAbility" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="BattlePetState" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="BattlePetVisual" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="string" />
  </Table>
  <Table Name="BoneWindModifierModel" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="BoneWindModifiers" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" ArraySize="3" />
    <Field Name="Field02" Type="float" />
  </Table>
  <Table Name="Bounty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="ushort" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="BountySet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="BroadcastText" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="string" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field05" Type="int" ArraySize="3" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="CameraEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" />
  </Table>
  <Table Name="CameraEffectEntry" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="float" />
    <Field Name="Field07" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field09" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="CameraMode" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="PositionOffset" Type="float" ArraySize="3" />
    <Field Name="TargetOffset" Type="float" ArraySize="3" />
    <Field Name="PositionSmoothing" Type="float" />
    <Field Name="RotationSmoothing" Type="float" />
    <Field Name="Field07" Type="float" />
    <Field Name="LockedPositionOffsetBase" Type="int" />
    <Field Name="LockedPositionOffsetDirection" Type="int" />
    <Field Name="LockedTargetOffsetBase" Type="int" />
    <Field Name="LockedTargetOffsetDirection" Type="int" />
  </Table>
  <Table Name="CameraShakes" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ShakeType" Type="int" />
    <Field Name="Direction" Type="int" />
    <Field Name="Amplitude" Type="float" />
    <Field Name="Frequency" Type="float" />
    <Field Name="Duration" Type="float" />
    <Field Name="Phase" Type="float" />
    <Field Name="Coefficient" Type="float" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="CastableRaidBuffs" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="CastingSpellID" Type="int" />
  </Table>
  <Table Name="Cfg_Categories" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LocaleMask" Type="int" />
    <Field Name="Create_CharsetMask" Type="int" />
    <Field Name="Existing_CharsetMask" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="Cfg_Configs" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RealmType" Type="int" />
    <Field Name="PlayerKillingAllowed" Type="int" />
    <Field Name="Roleplaying" Type="int" />
    <Field Name="PlayerAttackSpeedBase" Type="int" />
    <Field Name="MaxDamageReductionPctPhysical" Type="int" />
  </Table>
  <Table Name="Cfg_Regions" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Tag" Type="string" />
    <Field Name="Region_Group_Mask" Type="int" />
    <Field Name="RulesetID" Type="int" />
  </Table>
  <Table Name="CharacterFaceBoneSet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="CharacterFacialHairStyles" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceID" Type="int" />
    <Field Name="SexID" Type="int" />
    <Field Name="VariationID" Type="int" />
    <Field Name="Geoset" Type="int" ArraySize="5" />
  </Table>
  <Table Name="CharacterLoadout" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ChrClassID" Type="int" />
    <Field Name="Purpose" Type="int" />
    <Field Name="Racemask" Type="int" />
  </Table>
  <Table Name="CharacterLoadoutItem" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CharacterLoadoutID" Type="int" />
    <Field Name="ItemID" Type="int" />
  </Table>
  <Table Name="CharBaseInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceID" Type="byte" />
    <Field Name="ClassID" Type="byte" />
    <Field Name="Padding" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="CharBaseSection" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FallbackID" Type="int" />
    <Field Name="LayoutResType" Type="int" />
  </Table>
  <Table Name="CharComponentTextureLayouts" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Width" Type="int" />
    <Field Name="Height" Type="int" />
  </Table>
  <Table Name="CharComponentTextureSections" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CharComponentTextureLayoutID" Type="int" />
    <Field Name="SectionType" Type="int" />
    <Field Name="X" Type="int" />
    <Field Name="Y" Type="int" />
    <Field Name="Width" Type="int" />
    <Field Name="Height" Type="int" />
  </Table>
  <Table Name="CharHairGeosets" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceID" Type="int" />
    <Field Name="SexID" Type="int" />
    <Field Name="VariationID" Type="int" />
    <Field Name="VariationType" Type="int" />
    <Field Name="GeosetID" Type="int" />
    <Field Name="GeosetType" Type="int" />
    <Field Name="Showscalp" Type="int" />
    <Field Name="ColorIndex" Type="int" />
  </Table>
  <Table Name="CharSections" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceID" Type="int" />
    <Field Name="SexID" Type="int" />
    <Field Name="BaseSection" Type="int" />
    <Field Name="TextureName" Type="string" ArraySize="3" />
    <Field Name="Flags" Type="int" />
    <Field Name="VariationIndex" Type="int" />
    <Field Name="ColorIndex" Type="int" />
  </Table>
  <Table Name="CharShipment" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="CharShipmentContainer" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
  </Table>
  <Table Name="CharStartOutfit" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceID" Type="byte" />
    <Field Name="ClassID" Type="byte" />
    <Field Name="SexID" Type="byte" />
    <Field Name="OutfitID" Type="byte" />
    <Field Name="ItemID" Type="int" ArraySize="24" />
    <Field Name="PetDisplayID" Type="int" />
    <Field Name="PetFamilyID" Type="int" />
  </Table>
  <Table Name="CharTitles" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Condition_ID" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Name1_Lang" Type="string" />
    <Field Name="Mask_ID" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="ChatChannels" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="FactionGroup" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Shortcut_Lang" Type="string" />
  </Table>
  <Table Name="ChatProfanity" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Text" Type="string" />
    <Field Name="Language" Type="int" />
  </Table>
  <Table Name="ChrClasses" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DisplayPower" Type="int" />
    <Field Name="PetNameToken" Type="string" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Name_Female_Lang" Type="string" />
    <Field Name="Name_Male_Lang" Type="string" />
    <Field Name="FileName" Type="string" />
    <Field Name="SpellClassSet" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="CinematicSequenceID" Type="int" />
    <Field Name="AttackPowerPerStrength" Type="int" />
    <Field Name="AttackPowerPerAgility" Type="int" />
    <Field Name="RangedAttackPowerPerAgility" Type="int" />
    <Field Name="DefaultSpec" Type="int" />
    <Field Name="CreateScreenFileDataID" Type="int" />
    <Field Name="SelectScreenFileDataID" Type="int" />
    <Field Name="LowResScreenFileDataID" Type="int" />
    <Field Name="IconFileDataID" Type="int" />
    <Field Name="Field18" Type="int" />
  </Table>
  <Table Name="ChrClassesXPowerTypes" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ClassID" Type="int" />
    <Field Name="PowerType" Type="int" />
  </Table>
  <Table Name="ChrClassRaceSex" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="ChrClassTitle" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="ChrClassUIDisplay" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="ChrClassVillain" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="ChrRaces" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="FactionID" Type="int" />
    <Field Name="ExplorationSoundID" Type="int" />
    <Field Name="MaleDisplayId" Type="int" />
    <Field Name="FemaleDisplayId" Type="int" />
    <Field Name="ClientPrefix" Type="string" />
    <Field Name="BaseLanguage" Type="int" />
    <Field Name="CreatureType" Type="int" />
    <Field Name="ResSicknessSpellID" Type="int" />
    <Field Name="SplashSoundID" Type="int" />
    <Field Name="ClientFilestring" Type="string" />
    <Field Name="CinematicSequenceID" Type="int" />
    <Field Name="Alliance" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Name_Female_Lang" Type="string" />
    <Field Name="Name_Male_Lang" Type="string" />
    <Field Name="FacialHairCustomization" Type="string" ArraySize="2" />
    <Field Name="HairCustomization" Type="string" />
    <Field Name="Race_Related" Type="int" />
    <Field Name="UnalteredVisualRaceID" Type="int" />
    <Field Name="UaMaleCreatureSoundDataID" Type="int" />
    <Field Name="UaFemaleCreatureSoundDataID" Type="int" />
    <Field Name="CharComponentTextureLayoutID" Type="int" />
    <Field Name="DefaultClassID" Type="int" />
    <Field Name="CreateScreenFileDataID" Type="int" />
    <Field Name="SelectScreenFileDataID" Type="int" />
    <Field Name="MaleCustomizeOffset" Type="float" ArraySize="3" />
    <Field Name="FemaleCustomizeOffset" Type="float" ArraySize="3" />
    <Field Name="NeutralRaceID" Type="int" />
    <Field Name="LowResScreenFileDataID" Type="int" />
    <Field Name="HighResMaleDisplayId" Type="int" />
    <Field Name="HighResFemaleDisplayId" Type="int" />
    <Field Name="CharComponentTexLayoutHiResID" Type="int" />
    <Field Name="Field34" Type="int" />
  </Table>
  <Table Name="ChrSpecialization" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="BackgroundFile" Type="string" />
    <Field Name="ClassID" Type="int" />
    <Field Name="MasterySpellID" Type="int" ArraySize="2" />
    <Field Name="OrderIndex" Type="int" />
    <Field Name="PetTalentType" Type="int" />
    <Field Name="Role" Type="int" />
    <Field Name="SpellIconID" Type="int" />
    <Field Name="RaidBuffs" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Name2_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="PrimaryStatOrder" Type="int" ArraySize="2" />
  </Table>
  <Table Name="ChrUpgradeBucket" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="ChrUpgradeBucketSpell" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="ChrUpgradeTier" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="CinematicCamera" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Model" Type="string" />
    <Field Name="SoundID" Type="int" />
    <Field Name="Origin" Type="float" ArraySize="3" />
    <Field Name="OriginFacing" Type="float" />
  </Table>
  <Table Name="CinematicSequences" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundID" Type="int" />
    <Field Name="Camera" Type="int" ArraySize="8" />
  </Table>
  <Table Name="CloakDampening" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" ArraySize="5" />
    <Field Name="Field02" Type="float" ArraySize="5" />
    <Field Name="Field03" Type="float" ArraySize="2" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="Field05" Type="float" />
  </Table>
  <Table Name="CombatCondition" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WorldStateExpressionID" Type="int" />
    <Field Name="SelfConditionID" Type="int" />
    <Field Name="TargetConditionID" Type="int" />
    <Field Name="FriendConditionID" Type="int" ArraySize="2" />
    <Field Name="FriendConditionOp" Type="int" ArraySize="2" />
    <Field Name="FriendConditionCount" Type="int" ArraySize="2" />
    <Field Name="FriendConditionLogic" Type="int" />
    <Field Name="EnemyConditionID" Type="int" ArraySize="2" />
    <Field Name="EnemyConditionOp" Type="int" ArraySize="2" />
    <Field Name="EnemyConditionCount" Type="int" ArraySize="2" />
    <Field Name="EnemyConditionLogic" Type="int" />
  </Table>
  <Table Name="ComponentModelFileData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="ComponentTextureFileData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TextureItemID" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Texture_Type" Type="byte" />
    <Field Name="FileDataRef" Type="int" />
  </Table>
  <Table Name="ConsoleScripts" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Script" Type="string" />
  </Table>
  <Table Name="ConversationLine" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="Creature" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" ArraySize="3" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" ArraySize="4" />
    <Field Name="Field05" Type="int" ArraySize="4" />
    <Field Name="Field06" Type="string" />
    <Field Name="Field07" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="CreatureDifficulty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" ArraySize="5" />
  </Table>
  <Table Name="CreatureDisplayInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ModelID" Type="int" />
    <Field Name="SoundID" Type="int" />
    <Field Name="ExtendedDisplayInfoID" Type="int" />
    <Field Name="CreatureModelScale" Type="float" />
    <Field Name="PlayerModelScale" Type="float" />
    <Field Name="CreatureModelAlpha" Type="int" />
    <Field Name="TextureVariation" Type="string" ArraySize="3" />
    <Field Name="PortraitTextureName" Type="string" />
    <Field Name="SizeClass" Type="int" />
    <Field Name="BloodID" Type="int" />
    <Field Name="NPCSoundID" Type="int" />
    <Field Name="ParticleColorID" Type="int" />
    <Field Name="CreatureGeosetData" Type="int" />
    <Field Name="ObjectEffectPackageID" Type="int" />
    <Field Name="AnimReplacementSetID" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Gender" Type="int" />
    <Field Name="StateSpellVisualKitID" Type="int" />
    <Field Name="Field19" Type="int" />
  </Table>
  <Table Name="CreatureDisplayInfoCond" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" ArraySize="2" />
    <Field Name="Field03" Type="int" ArraySize="2" />
    <Field Name="Field04" Type="int" ArraySize="2" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field13" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field15" Type="int" ArraySize="3" />
  </Table>
  <Table Name="CreatureDisplayInfoExtra" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DisplayRaceID" Type="int" />
    <Field Name="DisplaySexID" Type="int" />
    <Field Name="SkinID" Type="int" />
    <Field Name="FaceID" Type="int" />
    <Field Name="HairStyleID" Type="int" />
    <Field Name="HairColorID" Type="int" />
    <Field Name="FacialHairID" Type="int" />
    <Field Name="NPCItemDisplay" Type="int" ArraySize="11" />
    <Field Name="Flags" Type="int" />
    <Field Name="FileDataID" Type="int" />
    <Field Name="HdFileDataID" Type="int" />
  </Table>
  <Table Name="CreatureDisplayInfoTrn" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="CreatureDispXUiCamera" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="CreatureFamily" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MinScale" Type="float" />
    <Field Name="MinScaleLevel" Type="int" />
    <Field Name="MaxScale" Type="float" />
    <Field Name="MaxScaleLevel" Type="int" />
    <Field Name="SkillLine" Type="int" ArraySize="2" />
    <Field Name="PetFoodMask" Type="int" />
    <Field Name="PetTalentType" Type="int" />
    <Field Name="CategoryEnumID" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="IconFile" Type="string" />
  </Table>
  <Table Name="CreatureImmunities" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="School" Type="int" />
    <Field Name="DispelType" Type="int" />
    <Field Name="MechanicsAllowed" Type="int" />
    <Field Name="Mechanic" Type="int" />
    <Field Name="EffectsAllowed" Type="int" />
    <Field Name="Effect" Type="int" ArraySize="7" />
    <Field Name="StatesAllowed" Type="int" />
    <Field Name="State" Type="int" ArraySize="15" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="CreatureModelData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="FileDataID" Type="int" />
    <Field Name="SizeClass" Type="int" />
    <Field Name="ModelScale" Type="float" />
    <Field Name="BloodID" Type="int" />
    <Field Name="FootprintTextureID" Type="int" />
    <Field Name="FootprintTextureLength" Type="float" />
    <Field Name="FootprintTextureWidth" Type="float" />
    <Field Name="FootprintParticleScale" Type="float" />
    <Field Name="FoleyMaterialID" Type="int" />
    <Field Name="FootstepShakeSize" Type="int" />
    <Field Name="DeathThudShakeSize" Type="int" />
    <Field Name="SoundID" Type="int" />
    <Field Name="CollisionWidth" Type="float" />
    <Field Name="CollisionHeight" Type="float" />
    <Field Name="MountHeight" Type="float" />
    <Field Name="GeoBoxMin" Type="float" ArraySize="3" />
    <Field Name="GeoBoxMax" Type="float" ArraySize="3" />
    <Field Name="WorldEffectScale" Type="float" />
    <Field Name="AttachedEffectScale" Type="float" />
    <Field Name="MissileCollisionRadius" Type="float" />
    <Field Name="MissileCollisionPush" Type="float" />
    <Field Name="MissileCollisionRaise" Type="float" />
    <Field Name="OverrideLootEffectScale" Type="float" />
    <Field Name="OverrideNameScale" Type="float" />
    <Field Name="OverrideSelectionRadius" Type="float" />
    <Field Name="TamedPetBaseScale" Type="float" />
    <Field Name="CreatureGeosetDataID" Type="int" />
    <Field Name="HoverHeight" Type="float" />
  </Table>
  <Table Name="CreatureMovementInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SmoothFacingChaseRate" Type="float" />
  </Table>
  <Table Name="CreatureSoundData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundExertionID" Type="int" />
    <Field Name="SoundExertionCriticalID" Type="int" />
    <Field Name="SoundInjuryID" Type="int" />
    <Field Name="SoundInjuryCriticalID" Type="int" />
    <Field Name="SoundInjuryCrushingBlowID" Type="int" />
    <Field Name="SoundDeathID" Type="int" />
    <Field Name="SoundStunID" Type="int" />
    <Field Name="SoundStandID" Type="int" />
    <Field Name="SoundFootstepID" Type="int" />
    <Field Name="SoundAggroID" Type="int" />
    <Field Name="SoundWingFlapID" Type="int" />
    <Field Name="SoundWingGlideID" Type="int" />
    <Field Name="SoundAlertID" Type="int" />
    <Field Name="SoundFidget" Type="int" ArraySize="5" />
    <Field Name="CustomAttack" Type="int" ArraySize="4" />
    <Field Name="NPCSoundID" Type="int" />
    <Field Name="LoopSoundID" Type="int" />
    <Field Name="CreatureImpactType" Type="int" />
    <Field Name="SoundJumpStartID" Type="int" />
    <Field Name="SoundJumpEndID" Type="int" />
    <Field Name="SoundPetAttackID" Type="int" />
    <Field Name="SoundPetOrderID" Type="int" />
    <Field Name="SoundPetDismissID" Type="int" />
    <Field Name="FidgetDelaySecondsMin" Type="float" />
    <Field Name="FidgetDelaySecondsMax" Type="float" />
    <Field Name="BirthSoundID" Type="int" />
    <Field Name="SpellCastDirectedSoundID" Type="int" />
    <Field Name="SubmergeSoundID" Type="int" />
    <Field Name="SubmergedSoundID" Type="int" />
    <Field Name="CreatureSoundDataIDPet" Type="int" />
    <Field Name="TransformSoundID" Type="int" />
    <Field Name="TransformAnimatedSoundID" Type="int" />
  </Table>
  <Table Name="CreatureSpellData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Spells" Type="int" ArraySize="4" />
    <Field Name="Availability" Type="int" ArraySize="4" />
  </Table>
  <Table Name="CreatureType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="Criteria" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="int" />
    <Field Name="Asset" Type="int" />
    <Field Name="Start_Event" Type="int" />
    <Field Name="Start_Asset" Type="int" />
    <Field Name="Start_Timer" Type="int" />
    <Field Name="Fail_Event" Type="int" />
    <Field Name="Fail_Asset" Type="int" />
    <Field Name="Modifier_Tree_Id" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Eligibility_World_State_ID" Type="int" />
    <Field Name="Eligibility_World_State_Value" Type="int" />
  </Table>
  <Table Name="CriteriaTree" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CriteriaID" Type="int" />
    <Field Name="Amount" Type="ulong" />
    <Field Name="Operator" Type="int" />
    <Field Name="Parent" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="OrderIndex" Type="int" />
    <Field Name="Padding" Type="byte" ArraySize="4" />
  </Table>
  <Table Name="CriteriaTreeXEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CriteriaTreeID" Type="int" />
    <Field Name="WorldEffectID" Type="int" />
  </Table>
  <Table Name="CurrencyCategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="CurrencyTypes" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CategoryID" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="InventoryIcon" Type="string" ArraySize="2" />
    <Field Name="SpellWeight" Type="int" />
    <Field Name="SpellCategory" Type="int" />
    <Field Name="MaxQty" Type="int" />
    <Field Name="MaxEarnablePerWeek" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Quality" Type="int" />
    <Field Name="Description_Lang" Type="string" />
  </Table>
  <Table Name="Curve" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="CurvePoint" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="float" ArraySize="2" />
  </Table>
  <Table Name="DeathThudLookups" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SizeClass" Type="int" />
    <Field Name="TerrainTypeSoundID" Type="int" />
    <Field Name="SoundEntryID" Type="int" />
    <Field Name="SoundEntryIDWater" Type="int" />
  </Table>
  <Table Name="DecalProperties" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="float" />
    <Field Name="Field07" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field09" Type="float" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field13" Type="int" />
    <Field Name="Field14" Type="int" />
  </Table>
  <Table Name="DeclinedWord" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Word" Type="string" />
  </Table>
  <Table Name="DeclinedWordCases" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DeclinedWordID" Type="int" />
    <Field Name="CaseIndex" Type="int" />
    <Field Name="DeclinedWord" Type="string" />
  </Table>
  <Table Name="DestructibleModelData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="State0Wmo" Type="int" />
    <Field Name="State0ImpactEffectDoodadSet" Type="int" />
    <Field Name="State0AmbientDoodadSet" Type="int" />
    <Field Name="State0NameSet" Type="int" />
    <Field Name="State1Wmo" Type="int" />
    <Field Name="State1DestructionDoodadSet" Type="int" />
    <Field Name="State1ImpactEffectDoodadSet" Type="int" />
    <Field Name="State1AmbientDoodadSet" Type="int" />
    <Field Name="State1NameSet" Type="int" />
    <Field Name="State2Wmo" Type="int" />
    <Field Name="State2DestructionDoodadSet" Type="int" />
    <Field Name="State2ImpactEffectDoodadSet" Type="int" />
    <Field Name="State2AmbientDoodadSet" Type="int" />
    <Field Name="State2NameSet" Type="int" />
    <Field Name="State3Wmo" Type="int" />
    <Field Name="State3InitDoodadSet" Type="int" />
    <Field Name="State3AmbientDoodadSet" Type="int" />
    <Field Name="State3NameSet" Type="int" />
    <Field Name="EjectDirection" Type="int" />
    <Field Name="RepairGroundFx" Type="int" />
    <Field Name="DoNotHighlight" Type="int" />
    <Field Name="HealEffect" Type="int" />
    <Field Name="HealEffectSpeed" Type="int" />
  </Table>
  <Table Name="DeviceBlacklist" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="DeviceDefaultSettings" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="Difficulty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FallbackDifficultyID" Type="int" />
    <Field Name="InstanceType" Type="int" />
    <Field Name="MinPlayers" Type="int" />
    <Field Name="MaxPlayers" Type="int" />
    <Field Name="OldEnumValue" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="ToggleDifficultyID" Type="int" />
    <Field Name="GroupSizeHealthCurveID" Type="int" />
    <Field Name="GroupSizeDmgCurveID" Type="int" />
    <Field Name="GroupSizeSpellPointsCurveID" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="ItemBonusTreeModID" Type="int" />
  </Table>
  <Table Name="DissolveEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="float" />
    <Field Name="Field07" Type="float" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="int" />
  </Table>
  <Table Name="DriverBlacklist" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
  </Table>
  <Table Name="DungeonEncounter" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="OrderIndex" Type="int" />
    <Field Name="Bit" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="CreatureDisplayID" Type="int" />
    <Field Name="SpellIconID" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="DungeonMap" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="FloorIndex" Type="int" />
    <Field Name="Min" Type="float" ArraySize="2" />
    <Field Name="Max" Type="float" ArraySize="2" />
    <Field Name="ParentWorldMapID" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="DungeonMapChunk" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="WmoGroupID" Type="int" />
    <Field Name="DungeonMapID" Type="int" />
    <Field Name="MinZ" Type="float" />
    <Field Name="DoodadPlacementID" Type="int" />
  </Table>
  <Table Name="DurabilityCosts" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WeaponSubClassCost" Type="int" ArraySize="21" />
    <Field Name="ArmorSubClassCost" Type="int" ArraySize="8" />
  </Table>
  <Table Name="DurabilityQuality" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="EdgeGlowEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="float" />
    <Field Name="Field07" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field09" Type="float" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="Emotes" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EmoteSlashCommand" Type="string" />
    <Field Name="AnimID" Type="int" />
    <Field Name="EmoteFlags" Type="int" />
    <Field Name="EmoteSpecProc" Type="int" />
    <Field Name="EmoteSpecProcParam" Type="int" />
    <Field Name="EventSoundID" Type="int" />
    <Field Name="SpellVisualKitID" Type="int" />
  </Table>
  <Table Name="EmotesText" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="EmoteID" Type="int" />
    <Field Name="EmoteText" Type="int" ArraySize="16" />
  </Table>
  <Table Name="EmotesTextData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Text_Lang" Type="string" />
  </Table>
  <Table Name="EmotesTextSound" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EmotesTextID" Type="int" />
    <Field Name="RaceID" Type="int" />
    <Field Name="SexID" Type="int" />
    <Field Name="SoundID" Type="int" />
  </Table>
  <Table Name="EnvironmentalDamage" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EnumID" Type="int" />
    <Field Name="VisualkitID" Type="int" />
  </Table>
  <Table Name="Exhaustion" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Xp" Type="int" />
    <Field Name="Factor" Type="float" />
    <Field Name="OutdoorHours" Type="float" />
    <Field Name="InnHours" Type="float" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Threshold" Type="float" />
    <Field Name="CombatLogText" Type="string" />
  </Table>
  <Table Name="Faction" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ReputationIndex" Type="int" />
    <Field Name="ReputationRaceMask" Type="int" ArraySize="4" />
    <Field Name="ReputationClassMask" Type="int" ArraySize="4" />
    <Field Name="ReputationBase" Type="int" ArraySize="4" />
    <Field Name="ReputationFlags" Type="int" ArraySize="4" />
    <Field Name="ParentFactionID" Type="int" />
    <Field Name="ParentFactionMod" Type="float" ArraySize="2" />
    <Field Name="ParentFactionCap" Type="int" ArraySize="2" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="Expansion" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="FriendshipRepID" Type="int" />
  </Table>
  <Table Name="FactionGroup" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MaskID" Type="int" />
    <Field Name="InternalName" Type="string" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="FactionTemplate" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Faction" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="FactionGroup" Type="int" />
    <Field Name="FriendGroup" Type="int" />
    <Field Name="EnemyGroup" Type="int" />
    <Field Name="Enemies" Type="int" ArraySize="4" />
    <Field Name="Friend" Type="int" ArraySize="4" />
  </Table>
  <Table Name="FileData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FileName" Type="string" />
    <Field Name="Filepath" Type="string" />
  </Table>
  <Table Name="FileDataComplete" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Path" Type="string" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="FootprintTextures" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FootstepFileName" Type="string" />
  </Table>
  <Table Name="FootstepTerrainLookup" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CreatureFootstepID" Type="int" />
    <Field Name="TerrainSoundID" Type="int" />
    <Field Name="SoundID" Type="int" />
    <Field Name="SoundIDSplash" Type="int" />
  </Table>
  <Table Name="FriendshipRepReaction" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FriendshipRepID" Type="int" />
    <Field Name="ReactionThreshold" Type="int" />
    <Field Name="Reaction_Lang" Type="string" />
  </Table>
  <Table Name="FriendshipReputation" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FactionID" Type="int" />
    <Field Name="TextureFileID" Type="int" />
    <Field Name="Description_Lang" Type="string" />
  </Table>
  <Table Name="FullScreenEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="float" />
    <Field Name="Field07" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field09" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field11" Type="float" />
    <Field Name="Field12" Type="float" />
    <Field Name="Field13" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field15" Type="float" />
    <Field Name="Field16" Type="float" />
    <Field Name="Field17" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field19" Type="float" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field21" Type="int" />
    <Field Name="Field22" Type="float" />
    <Field Name="Field23" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field25" Type="float" />
    <Field Name="Field26" Type="float" />
    <Field Name="Field27" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field29" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field31" Type="float" />
    <Field Name="Field32" Type="float" />
    <Field Name="Field33" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field35" Type="float" />
    <Field Name="Field36" Type="byte" />
    <Field Name="Field37" Type="int" />
    <Field Name="Field38" Type="int" />
    <Field Name="Field39" Type="int" />
  </Table>
  <Table Name="GameObjectArtKit" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TextureVariation" Type="string" ArraySize="3" />
    <Field Name="AttachModel" Type="string" ArraySize="4" />
  </Table>
  <Table Name="GameObjectDiffAnimMap" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GameObjectDiffAnimID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="Animation" Type="int" />
    <Field Name="AttachmentDisplayID" Type="int" />
  </Table>
  <Table Name="GameObjectDisplayInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FileDataID" Type="int" />
    <Field Name="Sound" Type="int" ArraySize="10" />
    <Field Name="GeoBoxMin" Type="float" ArraySize="3" />
    <Field Name="GeoBoxMax" Type="float" ArraySize="3" />
    <Field Name="ObjectEffectPackageID" Type="int" />
    <Field Name="OverrideLootEffectScale" Type="float" />
    <Field Name="OverrideNameScale" Type="float" />
  </Table>
  <Table Name="GameObjectDisplayInfoXSoundKit" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="GameObjects" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="float" ArraySize="3" />
    <Field Name="Field04" Type="float" ArraySize="4" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" ArraySize="8" />
    <Field Name="Field11" Type="string" />
  </Table>
  <Table Name="GameTables" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="NumRows" Type="int" />
    <Field Name="NumColumns" Type="int" />
  </Table>
  <Table Name="GameTips" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Text_Lang" Type="string" />
    <Field Name="Min_Level" Type="int" />
    <Field Name="Max_Level" Type="int" />
  </Table>
  <Table Name="GarrAbility" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="GarrAbilityCategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
  </Table>
  <Table Name="GarrAbilityEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="float" />
    <Field Name="Field07" Type="float" />
    <Field Name="Field08" Type="float" />
  </Table>
  <Table Name="GarrBuilding" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="HordeGameObjectID" Type="int" />
    <Field Name="AllianceGameObjectID" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Type" Type="int" />
    <Field Name="Level" Type="int" />
    <Field Name="NameAlliance" Type="string" />
    <Field Name="NameHorde" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="Tooltip" Type="string" />
    <Field Name="BuildDuration" Type="int" />
    <Field Name="CostCurrencyID" Type="int" />
    <Field Name="CostCurrencyAmount" Type="int" />
    <Field Name="HordeTexPrefixKitID" Type="int" />
    <Field Name="AllianceTexPrefixKitID" Type="int" />
    <Field Name="IconFileDataID" Type="int" />
    <Field Name="BonusAmount" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="AllianceActivationScenePackageID" Type="int" />
    <Field Name="HordeActivationScenePackageID" Type="int" />
    <Field Name="MaxShipments" Type="int" />
    <Field Name="FollowerRequiredGarrAbilityID" Type="int" />
    <Field Name="FollowerGarrAbilityEffectID" Type="int" />
    <Field Name="CostMoney" Type="int" />
  </Table>
  <Table Name="GarrBuildingDoodadSet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="GarrBuildingPlotInst" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" ArraySize="2" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="GarrClassSpec" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" ArraySize="3" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="GarrClassSpecPlayerCond" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="GarrEncounter" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
  </Table>
  <Table Name="GarrEncounterSetXEncounter" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GarrEncounterXMechanic" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GarrFamilyName" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
  </Table>
  <Table Name="GarrFollItemSet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrFollItemSetMember" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="GarrFollower" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field13" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field15" Type="int" />
  </Table>
  <Table Name="GarrFollowerLevelXP" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GarrFollowerQuality" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
	<Field Name="Padding" Type="ushort"/>
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="GarrFollowerSetXFollower" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GarrFollowerType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="GarrFollowerUICreature" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="GarrFollowerXAbility" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GarrFollSupportSpell" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="GarrGivenName" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GarrMechanic" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="float" />
  </Table>
  <Table Name="GarrMechanicSetXMechanic" Build="21742">
    <Field Name="Field00" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GarrMechanicType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="string" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="GarrMission" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field12" Type="int" ArraySize="3" />
    <Field Name="Field13" Type="int" ArraySize="3" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field15" Type="int" />
    <Field Name="Field16" Type="int" />
    <Field Name="Field17" Type="int" />
    <Field Name="Field18" Type="int" />
  </Table>
  <Table Name="GarrMissionReward" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="GarrMissionTexture" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" ArraySize="2" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="GarrMissionType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="ushort" />
  </Table>
  <Table Name="GarrMissionXEncounter" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GarrMissionXFollower" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="GarrMssnBonusAbility" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="GarrPlot" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GarrPlotUICategoryID" Type="int" />
    <Field Name="PlotType" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Name" Type="string" />
    <Field Name="MinCount" Type="int" />
    <Field Name="MaxCount" Type="int" />
    <Field Name="AllianceConstructionGameObjectID" Type="int" />
    <Field Name="HordeConstructionGameObjectID" Type="int" />
  </Table>
  <Table Name="GarrPlotBuilding" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GarrPlotInstance" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="string" />
  </Table>
  <Table Name="GarrPlotUICategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GarrSiteLevel" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="GarrSiteLevelPlotInst" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="float" ArraySize="2" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="GarrSpecialization" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="float" ArraySize="2" />
    <Field Name="Field06" Type="string" />
    <Field Name="Field07" Type="string" />
  </Table>
  <Table Name="Garrstring" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
  </Table>
  <Table Name="GarrTalent" Build="21742">
    <Field Name="Field00" Type="int" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field13" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field15" Type="int" />
    <Field Name="Field16" Type="int" />
    <Field Name="Field17" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field19" Type="int" />
  </Table>
  <Table Name="GarrTalentTree" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="GarrType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="GarrUiAnimClassInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GarrClassSpecID" Type="int" />
    <Field Name="SpellVisualID" Type="int" />
    <Field Name="MovementType" Type="int" />
    <Field Name="ImpactDelaySecs" Type="float" />
  </Table>
  <Table Name="GarrUiAnimRaceInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ChrRaceID" Type="int" />
    <Field Name="Scale" Type="float" />
    <Field Name="Height" Type="float" />
    <Field Name="SingleModelScale" Type="float" />
    <Field Name="SingleModelHeight" Type="float" />
  </Table>
  <Table Name="GemProperties" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Enchant_Id" Type="int" />
    <Field Name="Maxcount_Inv" Type="int" />
    <Field Name="Maxcount_Item" Type="int" />
    <Field Name="Type" Type="int" />
    <Field Name="Min_Item_Level" Type="int" />
  </Table>
  <Table Name="Globalstrings" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="GlueScreenEmote" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ClassId" Type="int" />
    <Field Name="RaceId" Type="int" />
    <Field Name="SexId" Type="int" />
    <Field Name="LeftHandItemType" Type="int" />
    <Field Name="RightHandItemType" Type="int" />
    <Field Name="AnimKitId" Type="int" />
    <Field Name="SpellVisualKitId" Type="int" />
  </Table>
  <Table Name="GlyphBindableSpell" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="GlyphExclusiveCategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
  </Table>
  <Table Name="GlyphProperties" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="GlyphType" Type="int" />
    <Field Name="SpellIconID" Type="int" />
    <Field Name="GlyphExclusiveCategoryID" Type="int" />
  </Table>
  <Table Name="GlyphRequiredSpec" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GlyphSlot" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="int" />
    <Field Name="Tooltip" Type="int" />
  </Table>
  <Table Name="GMSurveyAnswers" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Sort_Index" Type="int" />
    <Field Name="GMSurveyQuestionID" Type="int" />
    <Field Name="Answer_Lang" Type="string" />
  </Table>
  <Table Name="GMSurveyCurrentSurvey" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GMSURVEY_ID" Type="int" />
  </Table>
  <Table Name="GMSurveyQuestions" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Question_Lang" Type="string" />
  </Table>
  <Table Name="GMSurveySurveys" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Q" Type="int" ArraySize="15" />
  </Table>
  <Table Name="GMTicketCategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Category_Lang" Type="string" />
  </Table>
  <Table Name="GroundEffectDoodad" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Doodadpath" Type="string" />
    <Field Name="Flags" Type="int" />
    <Field Name="Animscale" Type="float" />
    <Field Name="Pushscale" Type="float" />
  </Table>
  <Table Name="GroundEffectTexture" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DoodadId" Type="int" ArraySize="4" />
    <Field Name="DoodadWeight" Type="int" ArraySize="4" />
    <Field Name="Density" Type="int" />
    <Field Name="Sound" Type="int" />
  </Table>
  <Table Name="GroupFinderActivity" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field13" Type="string" />
    <Field Name="Field14" Type="string" />
  </Table>
  <Table Name="GroupFinderActivityGrp" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="GroupFinderCategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="GtArmorMitigationByLvl" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtBarberShopCostBase" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtBattlePetTypeDamageMod" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtBattlePetXP" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtChanceToMeleeCrit" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtChanceToMeleeCritBase" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtChanceToSpellCrit" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtChanceToSpellCritBase" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtCombatRatings" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtItemSocketCostPerLevel" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtNPCManaCostScaler" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtNpcTotalHp" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" AutoGenerate="true" />
    <Field Name="HP" Type="float" />
  </Table>
  <Table Name="GtNpcTotalHpExp1" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" AutoGenerate="true" />
    <Field Name="HP" Type="float" />
  </Table>
  <Table Name="GtNpcTotalHpExp2" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" AutoGenerate="true" />
    <Field Name="HP" Type="float" />
  </Table>
  <Table Name="GtNpcTotalHpExp3" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" AutoGenerate="true" />
    <Field Name="HP" Type="float" />
  </Table>
  <Table Name="GtNpcTotalHpExp4" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" AutoGenerate="true" />
    <Field Name="HP" Type="float" />
  </Table>
  <Table Name="GtNpcTotalHpExp5" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" AutoGenerate="true" />
    <Field Name="HP" Type="float" />
  </Table>
  <Table Name="GtOCTBaseHPByClass" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtOCTBaseMPByClass" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtOCTClassCombatRatingScalar" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtOCTHpPerStamina" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtOCTLevelExperience" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtRegenMPPerSpt" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtResilienceDR" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GtSpellScaling" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="GuildColorBackground" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="GuildColorBorder" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="GuildColorEmblem" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="GuildPerkSpells" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GuildLevel" Type="int" />
    <Field Name="SpellID" Type="int" />
  </Table>
  <Table Name="Heirloom" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemId" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="SourceText" Type="string" />
    <Field Name="Source" Type="int" />
    <Field Name="OldItem" Type="int" ArraySize="2" />
    <Field Name="NextDifficultyItemId" Type="int" />
    <Field Name="UpgradeItemId" Type="ushort" ArraySize="2" />
    <Field Name="ItemBonusListId" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="HelmetAnimScaling" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="HelmetGeosetVisDataID" Type="int" />
    <Field Name="RaceID" Type="int" />
    <Field Name="Amount" Type="float" />
  </Table>
  <Table Name="HelmetGeosetVisData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="HideGeoset" Type="int" ArraySize="7" />
  </Table>
  <Table Name="HighlightColor" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="HolidayDescriptions" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Description_Lang" Type="string" />
  </Table>
  <Table Name="HolidayNames" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="Holidays" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Duration" Type="int" ArraySize="10" />
    <Field Name="Date" Type="int" ArraySize="16" />
    <Field Name="Region" Type="int" />
    <Field Name="Looping" Type="int" />
    <Field Name="CalendarFlags" Type="int" ArraySize="10" />
    <Field Name="HolidayNameID" Type="int" />
    <Field Name="HolidayDescriptionID" Type="int" />
    <Field Name="TextureFileName" Type="string" />
    <Field Name="Priority" Type="int" />
    <Field Name="CalendarFilterType" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="ImportPriceArmor" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ClothModifier" Type="float" />
    <Field Name="LeatherModifier" Type="float" />
    <Field Name="ChainModifier" Type="float" />
    <Field Name="PlateModifier" Type="float" />
  </Table>
  <Table Name="ImportPriceQuality" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="ImportPriceShield" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="ImportPriceWeapon" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="InvasionClientData" Build="21742">
    <Field Name="Field00" Type="string" />
    <Field Name="Field01" Type="float" ArraySize="2" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="Item" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Class" Type="int" />
    <Field Name="Subclass" Type="int" />
    <Field Name="SoundOverrideID" Type="int" />
    <Field Name="MaterialID" Type="int" />
    <Field Name="InventoryType" Type="int" />
    <Field Name="Sheathe" Type="int" />
    <Field Name="FileDataID" Type="int" />
    <Field Name="GroupSoundsId" Type="int" />
  </Table>
  <Table Name="ItemAppearance" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DisplayID" Type="int" />
    <Field Name="IconFileDataID" Type="int" />
  </Table>
  <Table Name="ItemAppearanceXUiCamera" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="ItemArmorQuality" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Qualitymod" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="int" />
  </Table>
  <Table Name="ItemArmorShield" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemLevel" Type="int" />
    <Field Name="Quality" Type="float" ArraySize="7" />
  </Table>
  <Table Name="ItemArmorTotal" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemLevel" Type="int" />
    <Field Name="Cloth" Type="float" />
    <Field Name="Leather" Type="float" />
    <Field Name="Mail" Type="float" />
    <Field Name="Plate" Type="float" />
  </Table>
  <Table Name="ItemBagFamily" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="ItemBonus" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" ArraySize="2" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="ItemBonusListLevelDelta" Build="21742">
    <Field Name="Field00" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ItemBonusTreeNode" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="ushort" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="ItemChildEquipment" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="ItemClass" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="PriceModifier" Type="float" />
    <Field Name="ClassName_Lang" Type="string" />
  </Table>
  <Table Name="ItemContextPickerEntry" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="ItemCurrencyCost" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
  </Table>
  <Table Name="ItemDamageAmmo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Quality" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="int" />
  </Table>
  <Table Name="ItemDamageOneHand" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Quality" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="int" />
  </Table>
  <Table Name="ItemDamageOneHandCaster" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Quality" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="int" />
  </Table>
  <Table Name="ItemDamageRanged" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Quality" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="int" />
  </Table>
  <Table Name="ItemDamageThrown" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Quality" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="int" />
  </Table>
  <Table Name="ItemDamageTwoHand" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Quality" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="int" />
  </Table>
  <Table Name="ItemDamageTwoHandCaster" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Quality" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="int" />
  </Table>
  <Table Name="ItemDamageWand" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Quality" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="int" />
  </Table>
  <Table Name="ItemDisenchantLoot" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Class" Type="int" />
    <Field Name="Subclass" Type="int" />
    <Field Name="Quality" Type="int" />
    <Field Name="MinLevel" Type="int" />
    <Field Name="MaxLevel" Type="int" />
    <Field Name="SkillRequired" Type="int" />
  </Table>
  <Table Name="ItemDisplayInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ModelName" Type="string" ArraySize="2" />
    <Field Name="ModelTexture" Type="int" ArraySize="2" />
    <Field Name="GeosetGroup" Type="int" ArraySize="3" />
    <Field Name="Flags" Type="int" />
    <Field Name="SpellVisualID" Type="int" />
    <Field Name="HelmetGeosetVis" Type="int" ArraySize="2" />
    <Field Name="Texture" Type="int" ArraySize="9" />
    <Field Name="ItemVisual" Type="int" />
    <Field Name="ParticleColorID" Type="int" />
  </Table>
  <Table Name="ItemDisplayInfoMaterialRes" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="ItemDisplayXUiCamera" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="ItemEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="ItemExtendedCost" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RequiredArenaSlot" Type="int" />
    <Field Name="RequiredItem" Type="int" ArraySize="5" />
    <Field Name="RequiredItemCount" Type="int" ArraySize="5" />
    <Field Name="RequiredPersonalArenaRating" Type="int" />
    <Field Name="ItemPurchaseGroup" Type="int" />
    <Field Name="RequiredCurrency" Type="int" ArraySize="5" />
    <Field Name="RequiredCurrencyCount" Type="int" ArraySize="5" />
    <Field Name="RequiredFactionId" Type="int" />
    <Field Name="RequiredFactionStanding" Type="int" />
    <Field Name="RequirementFlags" Type="int" />
    <Field Name="RequiredAchievement" Type="int" />
    <Field Name="RequiredMoney" Type="int" />
  </Table>
  <Table Name="ItemGroupSounds" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Sound" Type="int" ArraySize="4" />
  </Table>
  <Table Name="ItemLimitCategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Quantity" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="ItemLimitCategoryCondition" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="ItemModifiedAppearance" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="ItemModifiedAppearanceExtra" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="ItemNameDescription" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="Color" Type="int" />
  </Table>
  <Table Name="ItemPetFood" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="ItemPriceBase" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemLevel" Type="int" />
    <Field Name="Armor" Type="float" />
    <Field Name="Weapon" Type="float" />
  </Table>
  <Table Name="ItemPurchaseGroup" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="int" ArraySize="8" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="ItemRandomProperties" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Enchantment" Type="int" ArraySize="5" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="ItemRandomSuffix" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="InternalName" Type="string" />
    <Field Name="Enchantment" Type="int" ArraySize="5" />
    <Field Name="AllocationPct" Type="int" ArraySize="5" />
  </Table>
  <Table Name="ItemRangedDisplayInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="ItemSearchName" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" ArraySize="3" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field07" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="int" />
  </Table>
  <Table Name="ItemSet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="ItemID" Type="int" ArraySize="17" />
    <Field Name="RequiredSkill" Type="int" />
    <Field Name="RequiredSkillRank" Type="int" />
  </Table>
  <Table Name="ItemSetSpell" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemSetID" Type="int" />
    <Field Name="SpellID" Type="int" />
    <Field Name="Threshold" Type="int" />
    <Field Name="ChrSpecID" Type="int" />
  </Table>
  <Table Name="Item-sparse" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Quality" Type="uint" />
    <Field Name="Flags" Type="uint" ArraySize="3" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="BuyCount" Type="uint" />
    <Field Name="BuyPrice" Type="uint" />
    <Field Name="SellPrice" Type="uint" />
    <Field Name="InventoryType" Type="uint" />
    <Field Name="AllowableClass" Type="int" />
    <Field Name="AllowableRace" Type="int" />
    <Field Name="ItemLevel" Type="uint" />
    <Field Name="RequiredLevel" Type="int" />
    <Field Name="RequiredSkill" Type="uint" />
    <Field Name="RequiredSkillRank" Type="uint" />
    <Field Name="RequiredSpell" Type="uint" />
    <Field Name="RequiredHonorRank" Type="uint" />
    <Field Name="RequiredCityRank" Type="uint" />
    <Field Name="RequiredReputationFaction" Type="uint" />
    <Field Name="RequiredReputationRank" Type="uint" />
    <Field Name="MaxCount" Type="uint" />
    <Field Name="Stackable" Type="uint" />
    <Field Name="ContainerSlots" Type="uint" />
    <Field Name="ItemStatType" Type="int" ArraySize="10" />
    <Field Name="ItemStatValue" Type="int" ArraySize="10" />
    <Field Name="ItemStatAllocation" Type="int" ArraySize="10" />
    <Field Name="ItemStatSocketCostMultiplier" Type="float" ArraySize="10" />
    <Field Name="ScalingStatDistribution" Type="uint" />
    <Field Name="DamageType" Type="uint" />
    <Field Name="Delay" Type="uint" />
    <Field Name="RangedModRange" Type="float" />
    <Field Name="Bonding" Type="uint" />
    <Field Name="Name" Type="string" />
    <Field Name="Name2" Type="string" />
    <Field Name="Name3" Type="string" />
    <Field Name="Name4" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="PageText" Type="uint" />
    <Field Name="LanguageID" Type="uint" />
    <Field Name="PageMaterial" Type="uint" />
    <Field Name="StartQuest" Type="uint" />
    <Field Name="LockID" Type="uint" />
    <Field Name="Material" Type="int" />
    <Field Name="Sheath" Type="uint" />
    <Field Name="RandomProperty" Type="uint" />
    <Field Name="RandomSuffix" Type="uint" />
    <Field Name="ItemSet" Type="uint" />
    <Field Name="Area" Type="uint" />
    <Field Name="Map" Type="uint" />
    <Field Name="BagFamily" Type="uint" />
    <Field Name="TotemCategory" Type="uint" />
    <Field Name="SocketColor" Type="uint" ArraySize="3" />
    <Field Name="SocketBonus" Type="uint" />
    <Field Name="GemProperties" Type="uint" />
    <Field Name="ArmorDamageModifier" Type="float" />
    <Field Name="Duration" Type="uint" />
    <Field Name="ItemLimitCategory" Type="uint" />
    <Field Name="HolidayID" Type="uint" />
    <Field Name="StatScalingFactor" Type="float" />
    <Field Name="CurrencySubstitutionID" Type="uint" />
    <Field Name="CurrencySubstitutionCount" Type="uint" />
    <Field Name="ItemNameDescriptionID" Type="uint" />
  </Table>
  <Table Name="ItemSpec" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MinLevel" Type="int" />
    <Field Name="MaxLevel" Type="int" />
    <Field Name="ItemType" Type="int" />
    <Field Name="PrimaryStat" Type="int" />
    <Field Name="SecondaryStat" Type="int" />
    <Field Name="SpecializationID" Type="int" />
  </Table>
  <Table Name="ItemSpecOverride" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="int" />
    <Field Name="SpecID" Type="int" />
  </Table>
  <Table Name="ItemSubClass" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ClassID" Type="int" />
    <Field Name="SubClassID" Type="int" />
    <Field Name="PrerequisiteProficiency" Type="int" />
    <Field Name="PostrequisiteProficiency" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="DisplayFlags" Type="int" />
    <Field Name="WeaponParrySeq" Type="int" />
    <Field Name="WeaponReadySeq" Type="int" />
    <Field Name="WeaponAttackSeq" Type="int" />
    <Field Name="WeaponSwingSize" Type="int" />
    <Field Name="DisplayName_Lang" Type="string" />
    <Field Name="VerboseName_Lang" Type="string" />
  </Table>
  <Table Name="ItemSubClassMask" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ClassID" Type="int" />
    <Field Name="Mask" Type="int" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="ItemToBattlePet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
  </Table>
  <Table Name="ItemToBattlePetSpecies" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="BattlePetSpeciesId" Type="int" />
  </Table>
  <Table Name="ItemToMountSpell" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
  </Table>
  <Table Name="ItemUpgrade" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="ItemUpgradePath" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ItemVisualEffects" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Model" Type="string" />
  </Table>
  <Table Name="ItemVisuals" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Slot" Type="int" ArraySize="5" />
  </Table>
  <Table Name="ItemXBonusTree" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="JournalEncounter" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DungeonMapID" Type="int" />
    <Field Name="WorldMapAreaID" Type="int" />
    <Field Name="Map" Type="float" ArraySize="2" />
    <Field Name="FirstSectionID" Type="int" />
    <Field Name="JournalInstanceID" Type="int" />
    <Field Name="OrderIndex" Type="int" />
    <Field Name="DifficultyMask" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="JournalEncounterCreature" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="JournalEncounterID" Type="int" />
    <Field Name="CreatureDisplayInfoID" Type="int" />
    <Field Name="OrderIndex" Type="int" />
    <Field Name="FileDataID" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
  </Table>
  <Table Name="JournalEncounterItem" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="JournalEncounterID" Type="int" />
    <Field Name="ItemID" Type="int" />
    <Field Name="DifficultyMask" Type="int" />
    <Field Name="FactionMask" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="JournalEncounterSection" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="JournalEncounterID" Type="int" />
    <Field Name="NextSiblingSectionID" Type="int" />
    <Field Name="FirstChildSectionID" Type="int" />
    <Field Name="ParentSectionID" Type="int" />
    <Field Name="OrderIndex" Type="int" />
    <Field Name="Type" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="IconFlags" Type="int" />
    <Field Name="Title_Lang" Type="string" />
    <Field Name="BodyText_Lang" Type="string" />
    <Field Name="DifficultyMask" Type="int" />
    <Field Name="IconCreatureDisplayInfoID" Type="int" />
    <Field Name="SpellID" Type="int" />
    <Field Name="IconFileDataID" Type="int" />
  </Table>
  <Table Name="JournalEncounterXDifficulty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="JournalEncounterID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
  </Table>
  <Table Name="JournalInstance" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="AreaID" Type="int" />
    <Field Name="ButtonFiledataID" Type="int" />
    <Field Name="ButtonSmallFileDataID" Type="int" />
    <Field Name="BackgroundFiledataID" Type="int" />
    <Field Name="LoreFileDataID" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
  </Table>
  <Table Name="JournalItemXDifficulty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="JournalEncounterItemID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
  </Table>
  <Table Name="JournalSectionXDifficulty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="JournalEncounterSectionID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
  </Table>
  <Table Name="JournalTier" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="JournalTierXInstance" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="JournalInstanceID" Type="int" />
  </Table>
  <Table Name="KeyChain" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" ArraySize="32" />
  </Table>
  <Table Name="KeystoneAffix" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="Languages" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="LanguageWords" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LanguageID" Type="int" />
    <Field Name="Word" Type="string" />
  </Table>
  <Table Name="LfgDungeonExpansion" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Lfg_Id" Type="int" />
    <Field Name="Expansion_Level" Type="int" />
    <Field Name="Random_Id" Type="int" />
    <Field Name="Hard_Level_Min" Type="int" />
    <Field Name="Hard_Level_Max" Type="int" />
    <Field Name="Target_Level_Min" Type="int" />
    <Field Name="Target_Level_Max" Type="int" />
  </Table>
  <Table Name="LfgDungeonGroup" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Order_Index" Type="int" />
    <Field Name="Parent_Group_Id" Type="int" />
    <Field Name="Typeid" Type="int" />
  </Table>
  <Table Name="LfgDungeons" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="MinLevel" Type="int" />
    <Field Name="MaxLevel" Type="int" />
    <Field Name="Target_Level" Type="int" />
    <Field Name="Target_Level_Min" Type="int" />
    <Field Name="Target_Level_Max" Type="int" />
    <Field Name="MapID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="TypeID" Type="int" />
    <Field Name="Faction" Type="int" />
    <Field Name="TextureFileName" Type="string" />
    <Field Name="ExpansionLevel" Type="int" />
    <Field Name="Order_Index" Type="int" />
    <Field Name="Group_Id" Type="int" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="Random_Id" Type="int" />
    <Field Name="Count_Tank" Type="int" />
    <Field Name="Count_Healer" Type="int" />
    <Field Name="Count_Damage" Type="int" />
    <Field Name="Min_Count_Tank" Type="int" />
    <Field Name="Min_Count_Healer" Type="int" />
    <Field Name="Min_Count_Damage" Type="int" />
    <Field Name="ScenarioID" Type="int" />
    <Field Name="SubType" Type="int" />
    <Field Name="Bonus_Reputation_Amount" Type="int" />
    <Field Name="MentorCharLevel" Type="int" />
    <Field Name="MentorItemLevel" Type="int" />
  </Table>
  <Table Name="LfgDungeonsGroupingMap" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LfgDungeonsID" Type="int" />
    <Field Name="Random_LfgDungeonsID" Type="int" />
    <Field Name="Group_Id" Type="int" />
  </Table>
  <Table Name="LfgRoleRequirement" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="Light" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="X" Type="float" />
    <Field Name="Y" Type="float" />
    <Field Name="Z" Type="float" />
    <Field Name="FalloffStart" Type="float" />
    <Field Name="FalloffEnd" Type="float" />
    <Field Name="LightParamsID" Type="int" ArraySize="8" />
  </Table>
  <Table Name="LightData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LightParamID" Type="int" />
    <Field Name="Time" Type="int" />
    <Field Name="DirectColor" Type="int" />
    <Field Name="AmbientColor" Type="int" />
    <Field Name="SkyTopColor" Type="int" />
    <Field Name="SkyMiddleColor" Type="int" />
    <Field Name="SkyBand1Color" Type="int" />
    <Field Name="SkyBand2Color" Type="int" />
    <Field Name="SkySmogColor" Type="int" />
    <Field Name="SkyFogColor" Type="int" />
    <Field Name="SunColor" Type="int" />
    <Field Name="CloudSunColor" Type="int" />
    <Field Name="CloudEmissiveColor" Type="int" />
    <Field Name="CloudLayer1AmbientColor" Type="int" />
    <Field Name="CloudLayer2AmbientColor" Type="int" />
    <Field Name="OceanCloseColor" Type="int" />
    <Field Name="OceanFarColor" Type="int" />
    <Field Name="RiverCloseColor" Type="int" />
    <Field Name="RiverFarColor" Type="int" />
    <Field Name="ShadowOpacity" Type="int" />
    <Field Name="FogEnd" Type="float" />
    <Field Name="FogScaler" Type="float" />
    <Field Name="CloudDensity" Type="float" />
    <Field Name="FogDensity" Type="float" />
  </Table>
  <Table Name="LightParams" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="HighlightSky" Type="int" />
    <Field Name="LightSkyboxID" Type="int" />
    <Field Name="CloudTypeID" Type="int" />
    <Field Name="Glow" Type="float" />
    <Field Name="WaterShallowAlpha" Type="float" />
    <Field Name="WaterDeepAlpha" Type="float" />
    <Field Name="OceanShallowAlpha" Type="float" />
    <Field Name="OceanDeepAlpha" Type="float" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="LightSkybox" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="LiquidMaterial" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LVF" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="LiquidObject" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FlowDirection" Type="float" />
    <Field Name="FlowSpeed" Type="float" />
    <Field Name="LiquidTypeID" Type="int" />
    <Field Name="Fishable" Type="int" />
    <Field Name="Reflection" Type="int" />
  </Table>
  <Table Name="LiquidType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Flags" Type="int" />
    <Field Name="Type" Type="int" />
    <Field Name="SoundID" Type="int" />
    <Field Name="SpellID" Type="int" />
    <Field Name="MaxDarkenDepth" Type="float" />
    <Field Name="FogDarkenintensity" Type="float" />
    <Field Name="AmbDarkenintensity" Type="float" />
    <Field Name="DirDarkenintensity" Type="float" />
    <Field Name="LightID" Type="int" />
    <Field Name="ParticleScale" Type="float" />
    <Field Name="ParticleMovement" Type="int" />
    <Field Name="ParticleTexSlots" Type="int" />
    <Field Name="MaterialID" Type="int" />
    <Field Name="Texture" Type="string" ArraySize="6" />
    <Field Name="Color" Type="int" ArraySize="2" />
    <Field Name="Float" Type="float" ArraySize="18" />
    <Field Name="Int" Type="int" ArraySize="4" />
  </Table>
  <Table Name="LoadingScreens" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="FileName" Type="string" />
    <Field Name="HasWideScreen" Type="int" />
  </Table>
  <Table Name="LoadingScreenTaxiSplines" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PathID" Type="int" />
    <Field Name="Locx" Type="float" ArraySize="10" />
    <Field Name="Locy" Type="float" ArraySize="10" />
    <Field Name="LegIndex" Type="int" />
    <Field Name="LoadingScreenID" Type="int" />
  </Table>
  <Table Name="Locale" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="Location" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" ArraySize="3" />
    <Field Name="Field02" Type="float" ArraySize="3" />
  </Table>
  <Table Name="Lock" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="int" ArraySize="8" />
    <Field Name="Index" Type="int" ArraySize="8" />
    <Field Name="Skill" Type="int" ArraySize="8" />
    <Field Name="Action" Type="int" ArraySize="8" />
  </Table>
  <Table Name="LockType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="ResourceName_Lang" Type="string" />
    <Field Name="Verb_Lang" Type="string" />
    <Field Name="CursorName" Type="string" />
  </Table>
  <Table Name="LookAtController" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field07" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field15" Type="int" />
    <Field Name="Field16" Type="int" />
    <Field Name="Field17" Type="int" />
    <Field Name="Field18" Type="int" />
  </Table>
  <Table Name="MailTemplate" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Body_Lang" Type="string" />
  </Table>
  <Table Name="ManifestinterfaceActionIcon" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ManifestinterfaceData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FilePath" Type="string" />
    <Field Name="FileName" Type="string" />
  </Table>
  <Table Name="ManifestinterfaceItemIcon" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ManifestinterfaceTOCData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FilePath" Type="string" />
  </Table>
  <Table Name="ManifestMP3" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Map" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Directory" Type="string" />
    <Field Name="InstanceType" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="MapType" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="MapName_Lang" Type="string" />
    <Field Name="AreaTableID" Type="int" />
    <Field Name="MapDescription0_Lang" Type="string" />
    <Field Name="MapDescription1_Lang" Type="string" />
    <Field Name="LoadingScreenID" Type="int" />
    <Field Name="MinimapIconScale" Type="float" />
    <Field Name="CorpseMapID" Type="int" />
    <Field Name="Corpse" Type="float" ArraySize="2" />
    <Field Name="TimeOfDayOverride" Type="int" />
    <Field Name="ExpansionID" Type="int" />
    <Field Name="RaidOffset" Type="int" />
    <Field Name="MaxPlayers" Type="int" />
    <Field Name="ParentMapID" Type="int" />
    <Field Name="CosmeticParentMapID" Type="int" />
    <Field Name="TimeOffset" Type="int" />
  </Table>
  <Table Name="MapChallengeMode" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" ArraySize="5" />
  </Table>
  <Table Name="MapDifficulty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="Message_Lang" Type="string" />
    <Field Name="RaidDuration" Type="int" />
    <Field Name="MaxPlayers" Type="int" />
    <Field Name="LockID" Type="int" />
    <Field Name="ItemBonusTreeModID" Type="int" />
  </Table>
  <Table Name="MapDifficultyXCondition" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="MarketingPromotionsXlocale" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="string" />
  </Table>
  <Table Name="Material" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="FoleySoundID" Type="int" />
    <Field Name="SheatheSoundID" Type="int" />
    <Field Name="UnsheatheSoundID" Type="int" />
  </Table>
  <Table Name="MinorTalent" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ChrSpecializationID" Type="int" />
    <Field Name="SpellID" Type="int" />
    <Field Name="OrderIndex" Type="int" />
  </Table>
  <Table Name="ModelAnimCloakDampening" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="ModelFileData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
  </Table>
  <Table Name="ModelManifest" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="ulong" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="string" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="ModelNameToManifest" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="ModelRibbonQuality" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="ModifierTree" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="int" />
    <Field Name="Asset" Type="int" />
    <Field Name="SecondaryAsset" Type="int" />
    <Field Name="Operator" Type="int" />
    <Field Name="Amount" Type="int" />
    <Field Name="Parent" Type="int" />
  </Table>
  <Table Name="Mount" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellId" Type="int" />
    <Field Name="MountTypeID" Type="int" />
    <Field Name="DisplayId" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Name" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="SourceDescription" Type="string" />
    <Field Name="Source" Type="int" />
    <Field Name="PlayerConditionId" Type="int" />
  </Table>
  <Table Name="MountCapability" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="ReqRidingSkill" Type="int" />
    <Field Name="ReqAreaID" Type="int" />
    <Field Name="ReqSpellAuraID" Type="int" />
    <Field Name="ReqSpellKnownID" Type="int" />
    <Field Name="ModSpellAuraID" Type="int" />
    <Field Name="ReqMapID" Type="int" />
  </Table>
  <Table Name="MountType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="MountTypeXCapability" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="Movie" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Volume" Type="int" />
    <Field Name="KeyID" Type="int" />
    <Field Name="AudioFileDataID" Type="int" />
    <Field Name="SubtitleFileDataID" Type="int" />
  </Table>
  <Table Name="MovieFileData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Resolution" Type="int" />
  </Table>
  <Table Name="MovieOverlays" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MovieID" Type="int" />
    <Field Name="LocaleMask" Type="int" />
    <Field Name="OverlayRangeBegin" Type="int" />
    <Field Name="OverlayRangeEnd" Type="int" />
  </Table>
  <Table Name="MovieVariation" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MovieID" Type="int" />
    <Field Name="FileDataID" Type="int" />
    <Field Name="OverlayFileDataID" Type="int" />
  </Table>
  <Table Name="NameGen" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="RaceID" Type="int" />
    <Field Name="Sex" Type="int" />
  </Table>
  <Table Name="NamesProfanity" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Language" Type="int" />
  </Table>
  <Table Name="NamesReserved" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="NamesReservedlocale" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="LocaleMask" Type="int" />
  </Table>
  <Table Name="NpcModelItemSlotDisplayInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="NPCSounds" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundID" Type="int" ArraySize="4" />
  </Table>
  <Table Name="ObjectEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="ObjectEffectGroupID" Type="int" />
    <Field Name="TriggerType" Type="int" />
    <Field Name="EventType" Type="int" />
    <Field Name="EffectRecType" Type="int" />
    <Field Name="EffectRecID" Type="int" />
    <Field Name="Attachment" Type="int" />
    <Field Name="Offset" Type="float" ArraySize="3" />
    <Field Name="ObjectEffectModifierID" Type="int" />
  </Table>
  <Table Name="ObjectEffectGroup" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="ObjectEffectModifier" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="InputType" Type="int" />
    <Field Name="MapType" Type="int" />
    <Field Name="OutputType" Type="int" />
    <Field Name="Param" Type="float" ArraySize="4" />
  </Table>
  <Table Name="ObjectEffectPackage" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="ObjectEffectPackageElem" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ObjectEffectPackageID" Type="int" />
    <Field Name="ObjectEffectGroupID" Type="int" />
    <Field Name="StateType" Type="int" />
  </Table>
  <Table Name="OutlineEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="OverrideSpellData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Spells" Type="int" ArraySize="10" />
    <Field Name="Flags" Type="int" />
    <Field Name="PlayerActionbarFileDataID" Type="int" />
  </Table>
  <Table Name="Package" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Icon" Type="string" />
    <Field Name="Cost" Type="int" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="PageTextCache" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EntryLength" Type="int" />
    <Field Name="PageText" Type="string" />
    <Field Name="NextPageID" Type="int" />
  </Table>
  <Table Name="PageTextMaterial" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="PaperDollItemFrame" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemButtonName" Type="string" />
    <Field Name="SlotIcon" Type="string" />
    <Field Name="SlotNumber" Type="int" />
  </Table>
  <Table Name="ParticleColor" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Start" Type="int" ArraySize="3" />
    <Field Name="Mid" Type="int" ArraySize="3" />
    <Field Name="End" Type="int" ArraySize="3" />
  </Table>
  <Table Name="Path" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
  </Table>
  <Table Name="PathNode" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="PathNodeProperty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="PathProperty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="Phase" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="PhaseShiftZoneSounds" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AreaID" Type="int" />
    <Field Name="WMOAreaID" Type="int" />
    <Field Name="PhaseID" Type="int" />
    <Field Name="PhaseGroupID" Type="int" />
    <Field Name="PhaseUseFlags" Type="int" />
    <Field Name="ZoneintroMusicID" Type="int" />
    <Field Name="ZoneMusicID" Type="int" />
    <Field Name="SoundAmbienceID" Type="int" />
    <Field Name="SoundProviderPreferencesID" Type="int" />
    <Field Name="UWZoneintroMusicID" Type="int" />
    <Field Name="UWZoneMusicID" Type="int" />
    <Field Name="UWSoundAmbienceID" Type="int" />
    <Field Name="UWSoundProviderPreferencesID" Type="int" />
  </Table>
  <Table Name="PhaseXPhaseGroup" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PhaseID" Type="int" />
    <Field Name="PhaseGroupID" Type="int" />
  </Table>
  <Table Name="PlayerCondition" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="MinLevel" Type="int" />
    <Field Name="MaxLevel" Type="int" />
    <Field Name="RaceMask" Type="int" />
    <Field Name="ClassMask" Type="int" />
    <Field Name="Gender" Type="int" />
    <Field Name="NativeGender" Type="int" />
    <Field Name="SkillID" Type="int" ArraySize="4" />
    <Field Name="MinSkill" Type="int" ArraySize="4" />
    <Field Name="MaxSkill" Type="int" ArraySize="4" />
    <Field Name="SkillLogic" Type="int" />
    <Field Name="LanguageID" Type="int" />
    <Field Name="MinLanguage" Type="int" />
    <Field Name="MaxLanguage" Type="int" />
    <Field Name="MinFactionID" Type="int" ArraySize="3" />
    <Field Name="MaxFactionID" Type="int" />
    <Field Name="MinReputation" Type="int" ArraySize="3" />
    <Field Name="MaxReputation" Type="int" />
    <Field Name="ReputationLogic" Type="int" />
    <Field Name="Unknown" Type="int" />
    <Field Name="MinPVPRank" Type="int" />
    <Field Name="MaxPVPRank" Type="int" />
    <Field Name="PvpMedal" Type="int" />
    <Field Name="PrevQuestLogic" Type="int" />
    <Field Name="PrevQuestID" Type="int" ArraySize="4" />
    <Field Name="CurrQuestLogic" Type="int" />
    <Field Name="CurrQuestID" Type="int" ArraySize="4" />
    <Field Name="CurrentCompletedQuestLogic" Type="int" />
    <Field Name="CurrentCompletedQuestID" Type="int" ArraySize="4" />
    <Field Name="SpellLogic" Type="int" />
    <Field Name="SpellID" Type="int" ArraySize="4" />
    <Field Name="ItemLogic" Type="int" />
    <Field Name="ItemID" Type="int" ArraySize="4" />
    <Field Name="ItemCount" Type="int" ArraySize="4" />
    <Field Name="ItemFlags" Type="int" />
    <Field Name="Explored" Type="int" ArraySize="2" />
    <Field Name="Time" Type="int" ArraySize="2" />
    <Field Name="AuraSpellLogic" Type="int" />
    <Field Name="AuraSpellID" Type="int" ArraySize="4" />
    <Field Name="WorldStateExpressionID" Type="int" />
    <Field Name="WeatherID" Type="int" />
    <Field Name="PartyStatus" Type="int" />
    <Field Name="LifetimeMaxPVPRank" Type="int" />
    <Field Name="AchievementLogic" Type="int" />
    <Field Name="Achievement" Type="int" ArraySize="4" />
    <Field Name="LfgLogic" Type="int" />
    <Field Name="LfgStatus" Type="int" ArraySize="4" />
    <Field Name="LfgCompare" Type="int" ArraySize="4" />
    <Field Name="LfgValue" Type="int" ArraySize="4" />
    <Field Name="AreaLogic" Type="int" />
    <Field Name="AreaID" Type="int" ArraySize="4" />
    <Field Name="CurrencyLogic" Type="int" />
    <Field Name="CurrencyID" Type="int" ArraySize="4" />
    <Field Name="CurrencyCount" Type="int" ArraySize="4" />
    <Field Name="QuestKillID" Type="int" />
    <Field Name="QuestKillLogic" Type="int" />
    <Field Name="QuestKillMonster" Type="int" ArraySize="4" />
    <Field Name="MinExpansionLevel" Type="int" />
    <Field Name="MaxExpansionLevel" Type="int" />
    <Field Name="MinExpansionTier" Type="int" />
    <Field Name="MaxExpansionTier" Type="int" />
    <Field Name="MinGuildLevel" Type="int" />
    <Field Name="MaxGuildLevel" Type="int" />
    <Field Name="PhaseUseFlags" Type="int" />
    <Field Name="PhaseID" Type="int" />
    <Field Name="PhaseGroupID" Type="int" />
    <Field Name="MinAvgItemLevel" Type="int" />
    <Field Name="MaxAvgItemLevel" Type="int" />
    <Field Name="MinAvgEquippedItemLevel" Type="int" />
    <Field Name="MaxAvgEquippedItemLevel" Type="int" />
    <Field Name="ChrSpecializationIndex" Type="int" />
    <Field Name="ChrSpecializationRole" Type="int" />
    <Field Name="Failure_Description_Lang" Type="string" />
    <Field Name="PowerType" Type="int" />
    <Field Name="PowerTypeComp" Type="int" />
    <Field Name="PowerTypeValue" Type="int" />
  </Table>
  <Table Name="Positioner" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="PositionerState" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="PositionerStateEntry" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="int" />
  </Table>
  <Table Name="PowerDisplay" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ActualType" Type="int" />
    <Field Name="GlobalstringBaseTag" Type="string" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="PowerType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field07" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
  </Table>
  <Table Name="PrestigeLevelInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="PvpBracketTypes" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="int" ArraySize="4" />
  </Table>
  <Table Name="PvpDifficulty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="RangeIndex" Type="int" />
    <Field Name="MinLevel" Type="int" />
    <Field Name="MaxLevel" Type="int" />
  </Table>
  <Table Name="PvpItem" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="PvpReward" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="PvpTalent" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
  </Table>
  <Table Name="PvpTalentUnlock" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="QuestFactionReward" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Difficulty" Type="int" ArraySize="10" />
  </Table>
  <Table Name="QuestFeedbackEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FileDataID" Type="int" />
    <Field Name="AttachPoint" Type="int" />
    <Field Name="Minimapobject" Type="int" />
    <Field Name="Priority" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="QuestInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="InfoName_Lang" Type="string" />
  </Table>
  <Table Name="QuestLine" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
  </Table>
  <Table Name="QuestLineXQuest" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="QuestMoneyReward" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Difficulty" Type="int" ArraySize="10" />
  </Table>
  <Table Name="QuestObjective" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="QuestPackageItem" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="QuestPOIBlob" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="NumPoints" Type="int" />
    <Field Name="MapID" Type="int" />
    <Field Name="WorldMapAreaID" Type="int" />
  </Table>
  <Table Name="QuestPOIPoint" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="X" Type="int" />
    <Field Name="Y" Type="int" />
    <Field Name="QuestPOIBlobID" Type="int" />
  </Table>
  <Table Name="QuestPOIPointCliTask" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="QuestSort" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SortName_Lang" Type="string" />
  </Table>
  <Table Name="QuestV2" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="UniqueBitFlag" Type="int" />
  </Table>
  <Table Name="QuestV2CliTask" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field07" Type="ushort" ArraySize="3" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field09" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field21" Type="int" />
  </Table>
  <Table Name="QuestXP" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Difficulty" Type="int" ArraySize="10" />
  </Table>
  <Table Name="RacialMounts" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Race" Type="int" />
    <Field Name="Spell_Id" Type="int" />
  </Table>
  <Table Name="RandPropPoints" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Epic" Type="int" ArraySize="5" />
    <Field Name="Superior" Type="int" ArraySize="5" />
    <Field Name="Good" Type="int" ArraySize="5" />
  </Table>
  <Table Name="ResearchBranch" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="ResearchFieldID" Type="int" />
    <Field Name="CurrencyID" Type="int" />
    <Field Name="Texture" Type="string" />
    <Field Name="ItemID" Type="int" />
  </Table>
  <Table Name="ResearchField" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Slot" Type="int" />
  </Table>
  <Table Name="ResearchProject" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="Rarity" Type="int" />
    <Field Name="ResearchBranchID" Type="int" />
    <Field Name="SpellID" Type="int" />
    <Field Name="NumSockets" Type="int" />
    <Field Name="Texture" Type="string" />
    <Field Name="RequiredWeight" Type="int" />
  </Table>
  <Table Name="ResearchSite" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="QuestPOIBlobID" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="AreaPOIIconEnum" Type="int" />
  </Table>
  <Table Name="Resistances" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="FizzleSoundID" Type="int" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="RewardPack" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="RewardPackXCurrencyType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="RewardPackXItem" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="RibbonQuality" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="RulesetItemUpgrade" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="RulesetRaidLootUpgrade" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="RulesetRaidOverride" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="RulesetID" Type="int" />
    <Field Name="Sharedlock" Type="int" />
    <Field Name="Raidduration" Type="int" />
  </Table>
  <Table Name="ScalingStatDistribution" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MinLevel" Type="int" />
    <Field Name="MaxLevel" Type="int" />
    <Field Name="ItemLevelCurveId" Type="int" />
  </Table>
  <Table Name="ScalingStatValues" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Charlevel" Type="int" />
    <Field Name="EffectiveLevel" Type="int" />
    <Field Name="WeaponDPS1H" Type="int" />
    <Field Name="WeaponDPS2H" Type="int" />
    <Field Name="SpellcasterDPS1H" Type="int" />
    <Field Name="SpellcasterDPS2H" Type="int" />
    <Field Name="RangedDPS" Type="int" />
    <Field Name="WandDPS" Type="int" />
    <Field Name="SpellPower" Type="int" />
    <Field Name="BudgetPrimary" Type="int" />
    <Field Name="BudgetSecondary" Type="int" />
    <Field Name="BudgetTertiary" Type="int" />
    <Field Name="BudgetSub" Type="int" />
    <Field Name="BudgetTrivial" Type="int" />
    <Field Name="ArmorShoulder" Type="int" ArraySize="4" />
    <Field Name="ArmorChest" Type="int" ArraySize="4" />
    <Field Name="ArmorHead" Type="int" ArraySize="4" />
    <Field Name="ArmorLegs" Type="int" ArraySize="4" />
    <Field Name="ArmorFeet" Type="int" ArraySize="4" />
    <Field Name="ArmorWaist" Type="int" ArraySize="4" />
    <Field Name="ArmorHands" Type="int" ArraySize="4" />
    <Field Name="ArmorWrists" Type="int" ArraySize="4" />
    <Field Name="ArmorBack" Type="int" />
    <Field Name="ArmorShield" Type="int" />
  </Table>
  <Table Name="Scenario" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="ScenarioEventEntry" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TriggerType" Type="int" />
    <Field Name="TriggerAsset" Type="int" />
  </Table>
  <Table Name="ScenarioStep" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Criteriatreeid" Type="int" />
    <Field Name="ScenarioID" Type="int" />
    <Field Name="OrderIndex" Type="int" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="Title_Lang" Type="string" />
    <Field Name="Flags" Type="int" />
    <Field Name="RelatedStep" Type="int" />
    <Field Name="Supersedes" Type="int" />
    <Field Name="RewardQuestID" Type="int" />
  </Table>
  <Table Name="SceneScript" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="SceneScriptPackage" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
  </Table>
  <Table Name="SceneScriptPackageMember" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="Scheduledinterval" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="ScheduledUniqueCategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ScheduledWorldState" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="ScheduledWorldStateGroup" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="ScheduledWorldStateXUniqCat" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="ScreenEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Effect" Type="int" />
    <Field Name="Param" Type="int" ArraySize="4" />
    <Field Name="LightParamsID" Type="int" />
    <Field Name="LightParamsFadeIn" Type="int" />
    <Field Name="LightParamsFadeOut" Type="int" />
    <Field Name="LightFlags" Type="int" />
    <Field Name="SoundAmbienceID" Type="int" />
    <Field Name="ZoneMusicID" Type="int" />
    <Field Name="TimeOfDayOverride" Type="int" />
    <Field Name="EffectMask" Type="int" />
  </Table>
  <Table Name="Screenlocation" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="SeamlessSite" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
  </Table>
  <Table Name="ServerMessages" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Text_Lang" Type="string" />
  </Table>
  <Table Name="ShadowyEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="float" />
    <Field Name="Field07" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="SkillLine" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CategoryID" Type="int" />
    <Field Name="DisplayName_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="SpellIconID" Type="int" />
    <Field Name="AlternateVerb_Lang" Type="string" />
    <Field Name="CanLink" Type="int" />
    <Field Name="ParentSkillLineID" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="SkillLineAbility" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SkillLine" Type="int" />
    <Field Name="Spell" Type="int" />
    <Field Name="RaceMask" Type="int" />
    <Field Name="ClassMask" Type="int" />
    <Field Name="MinSkillLineRank" Type="int" />
    <Field Name="SupercedesSpell" Type="int" />
    <Field Name="AcquireMethod" Type="int" />
    <Field Name="TrivialSkillLineRankHigh" Type="int" />
    <Field Name="TrivialSkillLineRankLow" Type="int" />
    <Field Name="NumSkillUps" Type="int" />
    <Field Name="UniqueBit" Type="int" />
    <Field Name="TradeSkillCategoryID" Type="int" />
  </Table>
  <Table Name="SkillLineAbilitySortedSpell" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Spell" Type="int" />
  </Table>
  <Table Name="SkillRaceClassInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SkillID" Type="int" />
    <Field Name="RaceMask" Type="int" />
    <Field Name="ClassMask" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Availability" Type="int" />
    <Field Name="MinLevel" Type="int" />
    <Field Name="SkillTierID" Type="int" />
  </Table>
  <Table Name="SkillTiers" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Value" Type="int" ArraySize="16" />
  </Table>
  <Table Name="SoundAmbience" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AmbienceID" Type="int" ArraySize="2" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="SoundAmbienceFlavor" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundAmbienceID" Type="int" />
    <Field Name="SoundEntriesIDDay" Type="int" />
    <Field Name="SoundEntriesIDNight" Type="int" />
  </Table>
  <Table Name="SoundBus" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Parent" Type="int" />
    <Field Name="DefaultPriority" Type="int" />
    <Field Name="DefaultPrioriTypenalty" Type="int" />
    <Field Name="RaidPriority" Type="int" />
    <Field Name="RaidPrioriTypenalty" Type="int" />
    <Field Name="DefaultVolume" Type="float" />
    <Field Name="RaidVolume" Type="float" />
    <Field Name="DefaultPlaybackLimit" Type="int" />
    <Field Name="RaidPlaybackLimit" Type="int" />
    <Field Name="BusEnumID" Type="int" />
  </Table>
  <Table Name="SoundBusName" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="SoundEmitterPillPoints" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundEmittersID" Type="int" />
    <Field Name="Position" Type="float" ArraySize="3" />
  </Table>
  <Table Name="SoundEmitters" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Position" Type="float" ArraySize="3" />
    <Field Name="Direction" Type="float" ArraySize="3" />
    <Field Name="SoundEntriesID" Type="int" />
    <Field Name="MapID" Type="int" />
    <Field Name="Field05" Type="int" ArraySize="2" />
    <Field Name="Name" Type="string" />
    <Field Name="EmitterType" Type="int" />
    <Field Name="PhaseID" Type="int" />
    <Field Name="PhaseGroupID" Type="int" />
  </Table>
  <Table Name="SoundEntries" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundType" Type="int" />
    <Field Name="Name" Type="string" />
    <Field Name="FileDataID" Type="int" ArraySize="20" />
    <Field Name="Freq" Type="int" ArraySize="20" />
    <Field Name="Volumefloat" Type="float" />
    <Field Name="Flags" Type="int" />
    <Field Name="MinDistance" Type="float" />
    <Field Name="DistanceCutoff" Type="float" />
    <Field Name="EAXDef" Type="int" />
    <Field Name="SoundEntriesAdvancedID" Type="int" />
    <Field Name="Volumevariationplus" Type="float" />
    <Field Name="Volumevariationminus" Type="float" />
    <Field Name="Pitchvariationplus" Type="float" />
    <Field Name="Pitchvariationminus" Type="float" />
    <Field Name="PitchAdjust" Type="float" />
    <Field Name="DialogType" Type="int" />
    <Field Name="BusOverwriteID" Type="int" />
  </Table>
  <Table Name="SoundEntriesAdvanced" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundEntryID" Type="int" />
    <Field Name="InnerRadius2D" Type="float" />
    <Field Name="TimeA" Type="int" />
    <Field Name="TimeB" Type="int" />
    <Field Name="TimeC" Type="int" />
    <Field Name="TimeD" Type="int" />
    <Field Name="RandomOffsetRange" Type="int" />
    <Field Name="Usage" Type="int" />
    <Field Name="TimeintervalMin" Type="int" />
    <Field Name="TimeintervalMax" Type="int" />
    <Field Name="VolumeSliderCategory" Type="int" />
    <Field Name="DuckToSFX" Type="float" />
    <Field Name="DuckToMusic" Type="float" />
    <Field Name="DuckToAmbience" Type="float" />
    <Field Name="InnerRadiusOfInfluence" Type="float" />
    <Field Name="OuterRadiusOfInfluence" Type="float" />
    <Field Name="TimeToDuck" Type="int" />
    <Field Name="TimeToUnduck" Type="int" />
    <Field Name="InsideAngle" Type="float" />
    <Field Name="OutsideAngle" Type="float" />
    <Field Name="OutsideVolume" Type="float" />
    <Field Name="OuterRadius2D" Type="float" />
    <Field Name="MinRandomPosOffset" Type="int" />
    <Field Name="MaxRandomPosOffset" Type="int" />
    <Field Name="DuckToDialog" Type="float" />
    <Field Name="DuckToSuppressors" Type="float" />
    <Field Name="MsOffset" Type="int" />
    <Field Name="Volume" Type="float" ArraySize="20" />
  </Table>
  <Table Name="SoundEntriesFallbacks" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundEntriesID" Type="int" />
    <Field Name="FallbackSoundEntriesID" Type="int" />
  </Table>
  <Table Name="SoundFilter" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="SoundFilterElem" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundFilterID" Type="int" />
    <Field Name="OrderIndex" Type="int" />
    <Field Name="FilterType" Type="int" />
    <Field Name="Params" Type="float" ArraySize="9" />
  </Table>
  <Table Name="SoundKit" Build="21742">
    <Field Name="Field00" Type="string" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="float" />
    <Field Name="Field07" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field09" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field11" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SoundKitAdvanced" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field09" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field11" Type="float" />
    <Field Name="Field12" Type="float" />
    <Field Name="Field13" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field15" Type="float" />
    <Field Name="Field16" Type="float" />
    <Field Name="Field17" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field19" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field21" Type="int" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="int" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field29" Type="int" />
    <Field Name="Field30" Type="int" />
    <Field Name="Field31" Type="int" />
    <Field Name="Field32" Type="int" />
  </Table>
  <Table Name="SoundKitChild" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="SoundKitEntry" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="SoundKitFallback" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="SoundOverride" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WowEditlock" Type="int" />
    <Field Name="WowEditlockUser" Type="string" />
    <Field Name="ZoneintroMusicID" Type="int" />
    <Field Name="ZoneMusicID" Type="int" />
    <Field Name="SoundAmbienceID" Type="int" />
    <Field Name="SoundProviderPreferencesID" Type="int" />
  </Table>
  <Table Name="SoundProviderPreferences" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Description" Type="string" />
    <Field Name="Flags" Type="int" />
    <Field Name="EAXEnvironmentSelection" Type="int" />
    <Field Name="EAXDecayTime" Type="float" />
    <Field Name="EAX2EnvironmentSize" Type="float" />
    <Field Name="EAX2EnvironmentDiffusion" Type="float" />
    <Field Name="EAX2Room" Type="int" />
    <Field Name="EAX2RoomHF" Type="int" />
    <Field Name="EAX2DecayHFRatio" Type="float" />
    <Field Name="EAX2Reflections" Type="int" />
    <Field Name="EAX2ReflectionsDelay" Type="float" />
    <Field Name="EAX2Reverb" Type="int" />
    <Field Name="EAX2ReverbDelay" Type="float" />
    <Field Name="EAX2RoomRolloff" Type="float" />
    <Field Name="EAX2AirAbsorption" Type="float" />
    <Field Name="EAX3RoomLF" Type="int" />
    <Field Name="EAX3DecayLFRatio" Type="float" />
    <Field Name="EAX3EchoTime" Type="float" />
    <Field Name="EAX3EchoDepth" Type="float" />
    <Field Name="EAX3ModulationTime" Type="float" />
    <Field Name="EAX3ModulationDepth" Type="float" />
    <Field Name="EAX3HFReference" Type="float" />
    <Field Name="EAX3LFReference" Type="float" />
  </Table>
  <Table Name="SourceInfo" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="SpamMessages" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Text" Type="string" />
  </Table>
  <Table Name="SpecializationSpells" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="OverridesSpellID" Type="int" />
    <Field Name="SpecId" Type="int" />
    <Field Name="Description" Type="string" />
  </Table>
  <Table Name="Spell" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="NameSubtext_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
    <Field Name="AuraDescription_Lang" Type="string" />
    <Field Name="RuneCostID" Type="int" />
    <Field Name="SpellMissileID" Type="int" />
    <Field Name="DescriptionVariablesID" Type="int" />
    <Field Name="ScalingID" Type="int" />
    <Field Name="AuraOptionsID" Type="int" />
    <Field Name="AuraRestrictionsID" Type="int" />
    <Field Name="CastingRequirementsID" Type="int" />
    <Field Name="CategoriesID" Type="int" />
    <Field Name="ClassOptionsID" Type="int" />
    <Field Name="CooldownsID" Type="int" />
    <Field Name="EquippedItemsID" Type="int" />
    <Field Name="InterruptsID" Type="int" />
    <Field Name="LevelsID" Type="int" />
    <Field Name="ReagentsID" Type="int" />
    <Field Name="ShapeshiftID" Type="int" />
    <Field Name="TargetRestrictionsID" Type="int" />
    <Field Name="TotemsID" Type="int" />
    <Field Name="RequiredProjectID" Type="int" />
    <Field Name="MiscID" Type="int" />
  </Table>
  <Table Name="SpellActionBarPref" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="SpellActivationOverlay" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="OverlayFileDataID" Type="int" />
    <Field Name="ScreenlocationID" Type="int" />
    <Field Name="Color" Type="int" />
    <Field Name="Scale" Type="float" />
    <Field Name="IconHighlightSpellClassMask" Type="int" ArraySize="4" />
    <Field Name="TriggerType" Type="int" />
    <Field Name="SoundEntriesID" Type="int" />
  </Table>
  <Table Name="SpellAuraOptions" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="CumulativeAura" Type="int" />
    <Field Name="ProcChance" Type="int" />
    <Field Name="ProcCharges" Type="int" />
    <Field Name="ProcTypeMask" Type="int" />
    <Field Name="ProcCategoryRecovery" Type="int" />
    <Field Name="SpellProcsPerMinuteID" Type="int" />
  </Table>
  <Table Name="SpellAuraRestrictions" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="SpellAuraRestrictionsDifficulty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="SpellAuraVisibility" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="Type" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="SpellAuraVisXChrSpec" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellAuraVisibilityID" Type="int" />
    <Field Name="ChrSpecializationID" Type="int" />
  </Table>
  <Table Name="SpellCastingRequirements" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FacingCasterFlags" Type="int" />
    <Field Name="MinFactionID" Type="int" />
    <Field Name="MinReputation" Type="int" />
    <Field Name="RequiredAreasID" Type="int" />
    <Field Name="RequiredAuraVision" Type="int" />
    <Field Name="RequiresSpellFocus" Type="int" />
  </Table>
  <Table Name="SpellCastTimes" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Base" Type="int" />
    <Field Name="PerLevel" Type="int" />
    <Field Name="Minimum" Type="int" />
  </Table>
  <Table Name="SpellCategories" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="Category" Type="int" />
    <Field Name="DefenseType" Type="int" />
    <Field Name="DispelType" Type="int" />
    <Field Name="Mechanic" Type="int" />
    <Field Name="PreventionType" Type="int" />
    <Field Name="StartRecoveryCategory" Type="int" />
    <Field Name="ChargeCategory" Type="int" />
  </Table>
  <Table Name="SpellCategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="UsesPerWeek" Type="byte" Padding="3" />
	<Field Name="Padding" Type="byte" ArraySize="3" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="MaxCharges" Type="int" />
    <Field Name="ChargeRecoveryTime" Type="int" />
  </Table>
  <Table Name="SpellChainEffects" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AvgSegLen" Type="float" />
    <Field Name="Width" Type="float" />
    <Field Name="NoiseScale" Type="float" />
    <Field Name="TexCoordScale" Type="float" />
    <Field Name="SegDuration" Type="int" />
    <Field Name="SegDelay" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="JointCount" Type="int" />
    <Field Name="JointOffsetRadius" Type="float" />
    <Field Name="JointsPerMinorJoint" Type="int" />
    <Field Name="MinorJointsPerMajorJoint" Type="int" />
    <Field Name="MinorJointScale" Type="float" />
    <Field Name="MajorJointScale" Type="float" />
    <Field Name="JointMoveSpeed" Type="float" />
    <Field Name="JointSmoothness" Type="float" />
    <Field Name="MinDurationBetweenJointJumps" Type="float" />
    <Field Name="MaxDurationBetweenJointJumps" Type="float" />
    <Field Name="WaveHeight" Type="float" />
    <Field Name="WaveFreq" Type="float" />
    <Field Name="WaveSpeed" Type="float" />
    <Field Name="MinWaveAngle" Type="float" />
    <Field Name="MaxWaveAngle" Type="float" />
    <Field Name="MinWaveSpin" Type="float" />
    <Field Name="MaxWaveSpin" Type="float" />
    <Field Name="ArcHeight" Type="float" />
    <Field Name="MinArcAngle" Type="float" />
    <Field Name="MaxArcAngle" Type="float" />
    <Field Name="MinArcSpin" Type="float" />
    <Field Name="MaxArcSpin" Type="float" />
    <Field Name="DelayBetweenEffects" Type="float" />
    <Field Name="MinFlickerOnDuration" Type="float" />
    <Field Name="MaxFlickerOnDuration" Type="float" />
    <Field Name="MinFlickerOffDuration" Type="float" />
    <Field Name="MaxFlickerOffDuration" Type="float" />
    <Field Name="PulseSpeed" Type="float" />
    <Field Name="PulseOnLength" Type="float" />
    <Field Name="PulseFadeLength" Type="float" />
    <Field Name="Alpha" Type="byte" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
    <Field Name="BlendMode" Type="byte" />
    <Field Name="RenderLayer" Type="int" />
    <Field Name="TextureLength" Type="float" />
    <Field Name="WavePhase" Type="float" />
  </Table>
  <Table Name="SpellClassOptions" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" ArraySize="4" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="SpellCooldowns" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="CategoryRecoveryTime" Type="int" />
    <Field Name="RecoveryTime" Type="int" />
    <Field Name="StartRecoveryTime" Type="int" />
  </Table>
  <Table Name="SpellDescriptionVariables" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Variables" Type="string" />
  </Table>
  <Table Name="SpellDispelType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Mask" Type="int" />
    <Field Name="ImmunityPossible" Type="int" />
    <Field Name="InternalName" Type="string" />
  </Table>
  <Table Name="SpellDuration" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Duration" Type="int" />
    <Field Name="DurationPerLevel" Type="int" />
    <Field Name="MaxDuration" Type="int" />
  </Table>
  <Table Name="SpellEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="Effect" Type="int" />
    <Field Name="EffectAmplitude" Type="float" />
    <Field Name="EffectAura" Type="int" />
    <Field Name="EffectAuraPeriod" Type="int" />
    <Field Name="EffectBasePoints" Type="int" />
    <Field Name="EffectBonusCoefficient" Type="float" />
    <Field Name="EffectChainAmplitude" Type="float" />
    <Field Name="EffectChaintargets" Type="int" />
    <Field Name="EffectDieSides" Type="int" />
    <Field Name="EffectItemType" Type="int" />
    <Field Name="EffectMechanic" Type="int" />
    <Field Name="EffectMiscValue" Type="int" ArraySize="2" />
    <Field Name="EffectPointsPerResource" Type="float" />
    <Field Name="EffectRadiusIndex" Type="int" ArraySize="2" />
    <Field Name="EffectRealPointsPerLevel" Type="float" />
    <Field Name="EffectSpellClassMask" Type="int" ArraySize="4" />
    <Field Name="EffectTriggerSpell" Type="int" />
    <Field Name="EffectPos_Facing" Type="float" />
    <Field Name="ImplicitTarget" Type="int" ArraySize="2" />
    <Field Name="SpellID" Type="int" />
    <Field Name="EffectIndex" Type="int" />
    <Field Name="EffectAttributes" Type="int" />
    <Field Name="BonusCoefficientFromAP" Type="float" />
  </Table>
  <Table Name="SpellEffectCameraShakes" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" ArraySize="3" />
  </Table>
  <Table Name="SpellEffectEmission" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="ushort" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="SpellEffectGroupSize" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="float" />
  </Table>
  <Table Name="SpellEffectScaling" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Coefficient" Type="float" />
    <Field Name="Variance" Type="float" />
    <Field Name="ResourceCoefficient" Type="float" />
    <Field Name="SpellEffectID" Type="int" />
  </Table>
  <Table Name="SpellEquippedItems" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="EquippedItemClass" Type="int" />
    <Field Name="EquippedItemInvTypes" Type="int" />
    <Field Name="EquippedItemSubclass" Type="int" />
  </Table>
  <Table Name="SpellFlyout" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="RaceMask" Type="int" />
    <Field Name="ClassMask" Type="int" />
    <Field Name="SpellIconID" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Description_Lang" Type="string" />
  </Table>
  <Table Name="SpellFlyoutItem" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellFlyoutID" Type="int" />
    <Field Name="SpellID" Type="int" />
    <Field Name="Slot" Type="int" />
  </Table>
  <Table Name="SpellFocusObject" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
  </Table>
  <Table Name="SpellIcon" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TextureFileName" Type="string" />
  </Table>
  <Table Name="Spellinterrupts" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="AurainterruptFlags" Type="int" ArraySize="2" />
    <Field Name="ChannelinterruptFlags" Type="int" ArraySize="2" />
    <Field Name="InterruptFlags" Type="int" />
  </Table>
  <Table Name="SpellItemEnchantment" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Charges" Type="int" />
    <Field Name="Effect" Type="int" ArraySize="3" />
    <Field Name="EffectPointsMin" Type="int" ArraySize="3" />
    <Field Name="EffectArg" Type="int" ArraySize="3" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="ItemVisual" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Src_ItemID" Type="int" />
    <Field Name="Condition_Id" Type="int" />
    <Field Name="RequiredSkillID" Type="int" />
    <Field Name="RequiredSkillRank" Type="int" />
    <Field Name="MinLevel" Type="int" />
    <Field Name="MaxLevel" Type="int" />
    <Field Name="ItemLevel" Type="int" />
    <Field Name="ScalingClass" Type="int" />
    <Field Name="ScalingClassRestricted" Type="int" />
    <Field Name="EffectScalingPoints" Type="float" ArraySize="3" />
  </Table>
  <Table Name="SpellItemEnchantmentCondition" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Lt_OperandType" Type="byte" ArraySize="5" />
    <Field Name="Lt_Operand" Type="int" ArraySize="5" />
    <Field Name="Operator" Type="byte" ArraySize="5" />
    <Field Name="Rt_OperandType" Type="byte" ArraySize="5" />
    <Field Name="Rt_Operand" Type="int" ArraySize="5" />
    <Field Name="Logic" Type="byte" ArraySize="5" />
  </Table>
  <Table Name="SpellKeyboundOverride" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Function" Type="string" />
  </Table>
  <Table Name="SpellLabel" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="SpellLearnSpell" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LearnSpellID" Type="int" />
    <Field Name="OverridesSpellID" Type="int" />
    <Field Name="SpellID" Type="int" />
  </Table>
  <Table Name="SpellLevels" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="BaseLevel" Type="int" />
    <Field Name="MaxLevel" Type="int" />
    <Field Name="SpellLevel" Type="int" />
  </Table>
  <Table Name="SpellMechanic" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="StateName_Lang" Type="string" />
  </Table>
  <Table Name="SpellMisc" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Attributes" Type="int" ArraySize="14" />
    <Field Name="CastingTimeIndex" Type="int" />
    <Field Name="DurationIndex" Type="int" />
    <Field Name="RangeIndex" Type="int" />
    <Field Name="Speed" Type="float" />
    <Field Name="SpellIconID" Type="int" />
    <Field Name="ActiveIconID" Type="int" />
    <Field Name="SchoolMask" Type="int" />
    <Field Name="MultistrikeSpeedMod" Type="float" />
  </Table>
  <Table Name="SpellMiscDifficulty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="SpellMissile" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="float" />
    <Field Name="Field07" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field09" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field11" Type="float" />
    <Field Name="Field12" Type="float" />
    <Field Name="Field13" Type="float" />
    <Field Name="Field14" Type="float" />
  </Table>
  <Table Name="SpellMissileMotion" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="string" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="SpellPower" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field12" Type="float" />
  </Table>
  <Table Name="SpellPowerDifficulty" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="SpellProceduralEffect" Build="21742">
    <Field Name="Field00" Type="float" ArraySize="4" />
    <Field Name="Field01" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellProcsPerMinute" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="BaseProcRate" Type="float" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="SpellProcsPerMinuteMod" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="int" />
    <Field Name="Param" Type="int" />
    <Field Name="Coeff" Type="float" />
    <Field Name="SpellProcsPerMinuteID" Type="int" />
  </Table>
  <Table Name="SpellRadius" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Radius" Type="float" />
    <Field Name="RadiusPerLevel" Type="float" />
    <Field Name="RadiusMin" Type="float" />
    <Field Name="RadiusMax" Type="float" />
  </Table>
  <Table Name="SpellRange" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RangeMin" Type="float" ArraySize="2" />
    <Field Name="RangeMax" Type="float" ArraySize="2" />
    <Field Name="Flags" Type="int" />
    <Field Name="DisplayName_Lang" Type="string" />
    <Field Name="DisplayNameShort_Lang" Type="string" />
  </Table>
  <Table Name="SpellReagents" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="Reagent" Type="int" ArraySize="8" />
    <Field Name="ReagentCount" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="SpellReagentsCurrency" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="ushort" />
  </Table>
  <Table Name="SpellRuneCost" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Blood" Type="int" />
    <Field Name="Unholy" Type="int" />
    <Field Name="Frost" Type="int" />
    <Field Name="Chromatic" Type="int" />
    <Field Name="RunicPower" Type="int" />
  </Table>
  <Table Name="SpellScaling" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CastTimeMin" Type="int" />
    <Field Name="CastTimeMax" Type="int" />
    <Field Name="CastTimeMaxLevel" Type="int" />
    <Field Name="Class" Type="int" />
    <Field Name="NerfFactor" Type="float" />
    <Field Name="NerfMaxLevel" Type="int" />
    <Field Name="MaxScalingLevel" Type="int" />
    <Field Name="ScalesFromItemLevel" Type="int" />
  </Table>
  <Table Name="SpellShapeshift" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ShapeshiftExclude" Type="int" ArraySize="2" />
    <Field Name="ShapeshiftMask" Type="int" ArraySize="2" />
    <Field Name="StanceBarOrder" Type="int" />
  </Table>
  <Table Name="SpellShapeshiftForm" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="BonusActionBar" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Flags" Type="int" />
    <Field Name="CreatureType" Type="int" />
    <Field Name="AttackIconID" Type="int" />
    <Field Name="CombatRoundTime" Type="int" />
    <Field Name="CreatureDisplayID" Type="int" ArraySize="4" />
    <Field Name="PresetSpellID" Type="int" ArraySize="8" />
    <Field Name="MountTypeID" Type="int" />
    <Field Name="ExitSoundEntriesID" Type="int" />
  </Table>
  <Table Name="SpellSpecialUnitEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellVisualEffectNameID" Type="int" />
  </Table>
  <Table Name="SpellTargetRestrictions" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="ConeAngle" Type="float" />
    <Field Name="Width" Type="float" />
    <Field Name="MaxTargets" Type="int" />
    <Field Name="MaxTargetLevel" Type="int" />
    <Field Name="TargetCreatureType" Type="int" />
    <Field Name="Targets" Type="int" />
  </Table>
  <Table Name="SpellTotems" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RequiredTotemCategoryID" Type="int" ArraySize="2" />
    <Field Name="Totem" Type="int" ArraySize="2" />
  </Table>
  <Table Name="SpellVisual" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field13" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field15" Type="int" />
    <Field Name="Field16" Type="int" />
    <Field Name="Field17" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field19" Type="float" ArraySize="3" />
    <Field Name="Field20" Type="float" ArraySize="3" />
    <Field Name="Field21" Type="int" />
    <Field Name="Field22" Type="int" />
    <Field Name="Field23" Type="int" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field25" Type="int" />
  </Table>
  <Table Name="SpellVisualAnim" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="ushort" />
  </Table>
  <Table Name="SpellVisualColorEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field07" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="int" />
  </Table>
  <Table Name="SpellVisualEffectName" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="float" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
  </Table>
  <Table Name="SpellVisualKit" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field12" Type="int" ArraySize="3" />
    <Field Name="Field13" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field15" Type="int" />
    <Field Name="Field16" Type="int" ArraySize="4" />
    <Field Name="Field17" Type="float" ArraySize="4" />
    <Field Name="Field18" Type="float" ArraySize="4" />
    <Field Name="Field19" Type="float" ArraySize="4" />
    <Field Name="Field20" Type="float" ArraySize="4" />
    <Field Name="Field21" Type="int" />
    <Field Name="Field22" Type="float" />
  </Table>
  <Table Name="SpellVisualKitAreaModel" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="float" />
  </Table>
  <Table Name="SpellVisualKitEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="SpellVisualKitModelAttach" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="float" />
    <Field Name="Field07" Type="float" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field13" Type="int" />
    <Field Name="Field14" Type="int" />
  </Table>
  <Table Name="SpellVisualMissile" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="float" ArraySize="3" />
    <Field Name="Field12" Type="float" ArraySize="3" />
  </Table>
  <Table Name="SpellXSpellVisual" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="int" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="SpellVisualID" Type="int" ArraySize="2" />
    <Field Name="Field04" Type="float" />
    <Field Name="PlayerConditionID" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="Startup_strings" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Message_Lang" Type="string" />
  </Table>
  <Table Name="Stationery" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="int" />
    <Field Name="Texture" Type="string" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="StringLookups" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="String" Type="string" />
  </Table>
  <Table Name="SummonProperties" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Control" Type="int" />
    <Field Name="Faction" Type="int" />
    <Field Name="Title" Type="int" />
    <Field Name="Slot" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="TactKey" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" ArraySize="16" />
  </Table>
  <Table Name="TactKeyLookup" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="byte" ArraySize="8" />
  </Table>
  <Table Name="Talent" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpecID" Type="int" />
    <Field Name="TierID" Type="int" />
    <Field Name="ColumnIndex" Type="int" />
    <Field Name="SpellID" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="CategoryMask" Type="int" ArraySize="2" />
    <Field Name="ClassID" Type="int" />
    <Field Name="OverridesSpellID" Type="int" />
    <Field Name="Description_Lang" Type="string" />
  </Table>
  <Table Name="TaxiNodes" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="MountCreatureID" Type="int" ArraySize="2" />
    <Field Name="ConditionID" Type="int" />
    <Field Name="LearnableIndex" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="MapOffset" Type="float" ArraySize="2" />
  </Table>
  <Table Name="TaxiPath" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FromTaxiNode" Type="int" />
    <Field Name="ToTaxiNode" Type="int" />
    <Field Name="Cost" Type="int" />
  </Table>
  <Table Name="TaxiPathNode" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PathID" Type="int" />
    <Field Name="NodeIndex" Type="int" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="Loc" Type="float" ArraySize="3" />
    <Field Name="Flags" Type="int" />
    <Field Name="Delay" Type="int" />
    <Field Name="ArrivalEventID" Type="int" />
    <Field Name="DepartureEventID" Type="int" />
  </Table>
  <Table Name="TerrainMaterial" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Shader" Type="int" />
    <Field Name="EnvMapPath" Type="string" />
  </Table>
  <Table Name="TerrainType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TerrainID" Type="int" />
    <Field Name="TerrainDesc" Type="string" />
    <Field Name="FootstepSprayRun" Type="int" />
    <Field Name="FootstepSprayWalk" Type="int" />
    <Field Name="SoundID" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="TerrainTypeSounds" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TextureBlendSet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" ArraySize="3" />
    <Field Name="Field02" Type="float" ArraySize="3" />
    <Field Name="Field03" Type="float" ArraySize="3" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field05" Type="float" ArraySize="3" />
    <Field Name="Field06" Type="float" ArraySize="4" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="TextureFileData" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="TotemCategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="TotemCategoryType" Type="int" />
    <Field Name="TotemCategoryMask" Type="int" />
  </Table>
  <Table Name="Toy" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemId" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="SourceText" Type="string" />
    <Field Name="SourceType" Type="int" />
  </Table>
  <Table Name="TradeSkillCategory" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Skilllineid" Type="int" />
    <Field Name="Parenttradeskillcategoryid" Type="int" />
    <Field Name="Orderindex" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="TradeSkillItem" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="TransformMatrix" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" ArraySize="3" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
  </Table>
  <Table Name="TransmogSet" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="TransmogSetItem" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="TransportAnimation" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TransportID" Type="int" />
    <Field Name="TimeIndex" Type="int" />
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="SequenceID" Type="int" />
  </Table>
  <Table Name="TransportPhysics" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WaveAmp" Type="float" />
    <Field Name="WaveTimeScale" Type="float" />
    <Field Name="RollAmp" Type="float" />
    <Field Name="RollTimeScale" Type="float" />
    <Field Name="PitchAmp" Type="float" />
    <Field Name="PitchTimeScale" Type="float" />
    <Field Name="MaxBank" Type="float" />
    <Field Name="MaxBankTurnSpeed" Type="float" />
    <Field Name="SpeedDampThresh" Type="float" />
    <Field Name="SpeedDamp" Type="float" />
  </Table>
  <Table Name="TransportRotation" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GameObjectsID" Type="int" />
    <Field Name="TimeIndex" Type="int" />
    <Field Name="Rot" Type="float" ArraySize="4" />
  </Table>
  <Table Name="Trophy" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="TrophyInstance" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TrophyType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="UiCamera" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="float" ArraySize="3" />
    <Field Name="Field03" Type="float" ArraySize="3" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="int" />
  </Table>
  <Table Name="UiCameraType" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="UiCamFbackTransmogChrRace" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="UiCamFbackTransmogWeapon" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="UiMapPOI" Build="21742">
    <Field Name="Field00" Type="int" />
    <Field Name="Field01" Type="float" ArraySize="3" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="UiTextureAtlas" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
  </Table>
  <Table Name="UiTextureAtlasMember" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
  </Table>
  <Table Name="UiTextureKit" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
  </Table>
  <Table Name="UnitBlood" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CombatBloodSpurtFront" Type="int" ArraySize="2" />
    <Field Name="CombatBloodSpurtBack" Type="int" ArraySize="2" />
    <Field Name="GroundBlood" Type="string" ArraySize="5" />
  </Table>
  <Table Name="UnitBloodLevels" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Violencelevel" Type="int" ArraySize="3" />
  </Table>
  <Table Name="UnitCondition" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="Variable" Type="int" ArraySize="8" />
    <Field Name="Op" Type="int" ArraySize="8" />
    <Field Name="Value" Type="int" ArraySize="8" />
  </Table>
  <Table Name="UnitPowerBar" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MinPower" Type="int" />
    <Field Name="MaxPower" Type="int" />
    <Field Name="StartPower" Type="int" />
    <Field Name="CenterPower" Type="int" />
    <Field Name="RegenerationPeace" Type="float" />
    <Field Name="RegenerationCombat" Type="float" />
    <Field Name="BarType" Type="int" />
    <Field Name="FileDataID" Type="int" ArraySize="6" />
    <Field Name="Color" Type="int" ArraySize="6" />
    <Field Name="Flags" Type="int" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Cost_Lang" Type="string" />
    <Field Name="OutOfError_Lang" Type="string" />
    <Field Name="ToolTip_Lang" Type="string" />
    <Field Name="StartInset" Type="float" />
    <Field Name="EndInset" Type="float" />
  </Table>
  <Table Name="Vehicle" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="FlagsB" Type="int" />
    <Field Name="TurnSpeed" Type="float" />
    <Field Name="PitchSpeed" Type="float" />
    <Field Name="PitchMin" Type="float" />
    <Field Name="PitchMax" Type="float" />
    <Field Name="SeatID" Type="int" ArraySize="8" />
    <Field Name="MouseLookOffsetPitch" Type="float" />
    <Field Name="CameraFadeDistScalarMin" Type="float" />
    <Field Name="CameraFadeDistScalarMax" Type="float" />
    <Field Name="CameraPitchOffset" Type="float" />
    <Field Name="FacingLimitRight" Type="float" />
    <Field Name="FacingLimitLeft" Type="float" />
    <Field Name="MsslTrgtTurnLingering" Type="float" />
    <Field Name="MsslTrgtPitchLingering" Type="float" />
    <Field Name="MsslTrgtMouseLingering" Type="float" />
    <Field Name="MsslTrgtEndOpacity" Type="float" />
    <Field Name="MsslTrgtArcSpeed" Type="float" />
    <Field Name="MsslTrgtArcRepeat" Type="float" />
    <Field Name="MsslTrgtArcWidth" Type="float" />
    <Field Name="MsslTrgtImpactRadius" Type="float" ArraySize="2" />
    <Field Name="MsslTrgtArcTexture" Type="string" />
    <Field Name="MsslTrgtImpactTexture" Type="string" />
    <Field Name="MsslTrgtImpactModel" Type="string" ArraySize="2" />
    <Field Name="CameraYawOffset" Type="float" />
    <Field Name="UilocomotionType" Type="int" />
    <Field Name="MsslTrgtImpactTexRadius" Type="float" />
    <Field Name="VehicleUIIndicatorID" Type="int" />
    <Field Name="PowerDisplayID" Type="int" ArraySize="3" />
  </Table>
  <Table Name="VehicleSeat" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="AttachmentID" Type="int" />
    <Field Name="AttachmentOffset" Type="float" ArraySize="3" />
    <Field Name="EnterPreDelay" Type="float" />
    <Field Name="EnterSpeed" Type="float" />
    <Field Name="EnterGravity" Type="float" />
    <Field Name="EnterMinDuration" Type="float" />
    <Field Name="EnterMaxDuration" Type="float" />
    <Field Name="EnterMinArcHeight" Type="float" />
    <Field Name="EnterMaxArcHeight" Type="float" />
    <Field Name="EnterAnimStart" Type="int" />
    <Field Name="EnterAnimLoop" Type="int" />
    <Field Name="RideAnimStart" Type="int" />
    <Field Name="RideAnimLoop" Type="int" />
    <Field Name="RideUpperAnimStart" Type="int" />
    <Field Name="RideUpperAnimLoop" Type="int" />
    <Field Name="ExitPreDelay" Type="float" />
    <Field Name="ExitSpeed" Type="float" />
    <Field Name="ExitGravity" Type="float" />
    <Field Name="ExitMinDuration" Type="float" />
    <Field Name="ExitMaxDuration" Type="float" />
    <Field Name="ExitMinArcHeight" Type="float" />
    <Field Name="ExitMaxArcHeight" Type="float" />
    <Field Name="ExitAnimStart" Type="int" />
    <Field Name="ExitAnimLoop" Type="int" />
    <Field Name="ExitAnimEnd" Type="int" />
    <Field Name="PassengerYaw" Type="float" />
    <Field Name="PassengerPitch" Type="float" />
    <Field Name="PassengerRoll" Type="float" />
    <Field Name="PassengerAttachmentID" Type="int" />
    <Field Name="VehicleEnterAnim" Type="int" />
    <Field Name="VehicleExitAnim" Type="int" />
    <Field Name="VehicleRideAnimLoop" Type="int" />
    <Field Name="VehicleEnterAnimBone" Type="int" />
    <Field Name="VehicleExitAnimBone" Type="int" />
    <Field Name="VehicleRideAnimLoopBone" Type="int" />
    <Field Name="VehicleEnterAnimDelay" Type="float" />
    <Field Name="VehicleExitAnimDelay" Type="float" />
    <Field Name="VehicleAbilityDisplay" Type="int" />
    <Field Name="EnterUISoundID" Type="int" />
    <Field Name="ExitUISoundID" Type="int" />
    <Field Name="FlagsB" Type="int" />
    <Field Name="CameraEnteringDelay" Type="float" />
    <Field Name="CameraEnteringDuration" Type="float" />
    <Field Name="CameraExitingDelay" Type="float" />
    <Field Name="CameraExitingDuration" Type="float" />
    <Field Name="CameraOffset" Type="float" ArraySize="3" />
    <Field Name="CameraPosChaseRate" Type="float" />
    <Field Name="CameraFacingChaseRate" Type="float" />
    <Field Name="CameraEnteringZoom" Type="float" />
    <Field Name="CameraSeatZoomMin" Type="float" />
    <Field Name="CameraSeatZoomMax" Type="float" />
    <Field Name="EnterAnimKitID" Type="int" />
    <Field Name="RideAnimKitID" Type="int" />
    <Field Name="ExitAnimKitID" Type="int" />
    <Field Name="VehicleEnterAnimKitID" Type="int" />
    <Field Name="VehicleRideAnimKitID" Type="int" />
    <Field Name="VehicleExitAnimKitID" Type="int" />
    <Field Name="CameraModeID" Type="int" />
    <Field Name="FlagsC" Type="int" />
    <Field Name="UiSkinFileDataID" Type="int" />
  </Table>
  <Table Name="VehicleUIIndicator" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="BackgroundTexture" Type="string" />
  </Table>
  <Table Name="VehicleUIIndSeat" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="VehicleUIIndicatorID" Type="int" />
    <Field Name="VirtualSeatIndex" Type="int" />
    <Field Name="XPos" Type="float" />
    <Field Name="YPos" Type="float" />
  </Table>
  <Table Name="VideoHardware" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="VendorID" Type="int" />
    <Field Name="DeviceID" Type="int" />
    <Field Name="FarclipIdx" Type="int" />
    <Field Name="TerrainLODDistIdx" Type="int" />
    <Field Name="TerrainShadowLOD" Type="int" />
    <Field Name="DetailDoodadDensityIdx" Type="int" />
    <Field Name="DetailDoodadAlpha" Type="int" />
    <Field Name="AnimatingDoodadIdx" Type="int" />
    <Field Name="Trilinear" Type="int" />
    <Field Name="NumLights" Type="int" />
    <Field Name="Specularity" Type="int" />
    <Field Name="WaterLODIdx" Type="int" />
    <Field Name="ParticleDensityIdx" Type="int" />
    <Field Name="UnitDrawDistIdx" Type="int" />
    <Field Name="SmallCullDistIdx" Type="int" />
    <Field Name="ResolutionIdx" Type="int" />
    <Field Name="BaseMipLevel" Type="int" />
    <Field Name="OglOverrides" Type="string" />
    <Field Name="D3dOverrides" Type="string" />
    <Field Name="FixLag" Type="int" />
    <Field Name="Multisample" Type="int" />
    <Field Name="Atlasdisable" Type="int" />
  </Table>
  <Table Name="Vignette" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
  </Table>
  <Table Name="VocalUISounds" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="VocalUIEnum" Type="int" />
    <Field Name="RaceID" Type="int" />
    <Field Name="NormalSoundID" Type="int" ArraySize="2" />
    <Field Name="PissedSoundID" Type="int" ArraySize="2" />
  </Table>
  <Table Name="WbAccessControlList" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="WbCertBlacklist" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="byte" ArraySize="20" />
  </Table>
  <Table Name="WbCertWhitelist" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
	<Field Name="Padding" Type="byte" ArraySize="2" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="WbPermissions" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="WeaponImpactSounds" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WeaponSubClassID" Type="int" />
    <Field Name="ParrySoundType" Type="int" />
    <Field Name="ImpactSoundID" Type="int" ArraySize="10" />
    <Field Name="CritImpactSoundID" Type="int" ArraySize="10" />
  </Table>
  <Table Name="WeaponSwingSounds2" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SwingType" Type="int" />
    <Field Name="Crit" Type="int" />
    <Field Name="SoundID" Type="int" />
  </Table>
  <Table Name="WeaponTrail" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
  </Table>
  <Table Name="WeaponTrailModelDef" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="ushort" />
  </Table>
  <Table Name="WeaponTrailParam" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="Weather" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AmbienceID" Type="int" />
    <Field Name="Type" Type="int" />
    <Field Name="EffectType" Type="int" />
    <Field Name="Intensity" Type="float" ArraySize="2" />
    <Field Name="TransitionSkyBox" Type="float" />
    <Field Name="EffectColor" Type="float" ArraySize="3" />
    <Field Name="EffectTexture" Type="string" />
    <Field Name="SoundAmbienceID" Type="int" />
    <Field Name="WindSettingsID" Type="int" />
  </Table>
  <Table Name="WindSettings" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="float" />
    <Field Name="Field02" Type="float" ArraySize="3" />
    <Field Name="Field03" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field05" Type="float" ArraySize="3" />
    <Field Name="Field06" Type="float" />
    <Field Name="Field07" Type="float" ArraySize="3" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="WMOAreaTable" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WMOID" Type="int" />
    <Field Name="NameSetID" Type="int" />
    <Field Name="WMOGroupID" Type="int" />
    <Field Name="SoundProviderPref" Type="int" />
    <Field Name="SoundProviderPrefUnderwater" Type="int" />
    <Field Name="AmbienceID" Type="int" />
    <Field Name="ZoneMusic" Type="int" />
    <Field Name="IntroSound" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="AreaTableID" Type="int" />
    <Field Name="AreaName_Lang" Type="string" />
    <Field Name="UwintroSound" Type="int" />
    <Field Name="UwZoneMusic" Type="int" />
    <Field Name="UwAmbience" Type="int" />
  </Table>
  <Table Name="WmoMinimapTexture" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field03" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="World_PVP_Area" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Area_ID" Type="int" />
    <Field Name="Next_Time_Worldstate" Type="int" />
    <Field Name="Game_Time_Worldstate" Type="int" />
    <Field Name="Battle_Populate_Time" Type="int" />
    <Field Name="Min_Level" Type="int" />
    <Field Name="Max_Level" Type="int" />
  </Table>
  <Table Name="WorldBosslockout" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="string" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="WorldChunkSounds" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="ChunkX" Type="int" />
    <Field Name="ChunkY" Type="int" />
    <Field Name="SubchunkX" Type="int" />
    <Field Name="SubchunkY" Type="int" />
    <Field Name="SoundOverrideID" Type="int" />
  </Table>
  <Table Name="WorldEffect" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TargetType" Type="int" />
    <Field Name="TargetAsset" Type="int" />
    <Field Name="QuestFeedbackEffectID" Type="int" />
    <Field Name="PlayerConditionID" Type="int" />
    <Field Name="CombatConditionID" Type="int" />
    <Field Name="WhenToDisplay" Type="int" />
  </Table>
  <Table Name="WorldElapsedTimer" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name_Lang" Type="string" />
    <Field Name="Flags" Type="int" />
    <Field Name="Type" Type="int" />
  </Table>
  <Table Name="WorldMapArea" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="AreaID" Type="int" />
    <Field Name="AreaName" Type="string" />
    <Field Name="LocLeft" Type="float" />
    <Field Name="LocRight" Type="float" />
    <Field Name="LocTop" Type="float" />
    <Field Name="LocBottom" Type="float" />
    <Field Name="DisplayMapID" Type="int" />
    <Field Name="DefaultDungeonFloor" Type="int" />
    <Field Name="ParentWorldMapID" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="LevelRangeMin" Type="int" />
    <Field Name="LevelRangeMax" Type="int" />
  </Table>
  <Table Name="WorldMapContinent" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="LeftBoundary" Type="int" />
    <Field Name="RightBoundary" Type="int" />
    <Field Name="TopBoundary" Type="int" />
    <Field Name="BottomBoundary" Type="int" />
    <Field Name="ContinentOffset" Type="float" ArraySize="2" />
    <Field Name="Scale" Type="float" />
    <Field Name="TaxiMin" Type="float" ArraySize="2" />
    <Field Name="TaxiMax" Type="float" ArraySize="2" />
    <Field Name="WorldMapID" Type="int" />
  </Table>
  <Table Name="WorldMapOverlay" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapAreaID" Type="int" />
    <Field Name="AreaID" Type="int" ArraySize="4" />
    <Field Name="TextureName" Type="string" />
    <Field Name="TextureWidth" Type="int" />
    <Field Name="TextureHeight" Type="int" />
    <Field Name="OffsetX" Type="int" />
    <Field Name="OffsetY" Type="int" />
    <Field Name="HitRectTop" Type="int" />
    <Field Name="HitRectLeft" Type="int" />
    <Field Name="HitRectBottom" Type="int" />
    <Field Name="HitRectRight" Type="int" />
    <Field Name="PlayerConditionID" Type="int" />
  </Table>
  <Table Name="WorldMapTransforms" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="RegionMin" Type="float" ArraySize="3" />
    <Field Name="RegionMax" Type="float" ArraySize="3" />
    <Field Name="NewMapID" Type="int" />
    <Field Name="RegionOffset" Type="float" ArraySize="2" />
    <Field Name="NewDungeonMapID" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="NewAreaID" Type="int" />
    <Field Name="RegionScale" Type="float" />
  </Table>
  <Table Name="WorldSafelocs" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Continent" Type="int" />
    <Field Name="Loc" Type="float" ArraySize="3" />
    <Field Name="Facing" Type="float" />
    <Field Name="AreaName_Lang" Type="string" />
  </Table>
  <Table Name="WorldState" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="WorldStateExpression" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Expression" Type="string" />
  </Table>
  <Table Name="WorldStateUI" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="AreaID" Type="int" />
    <Field Name="PhaseUseFlags" Type="int" />
    <Field Name="PhaseID" Type="int" />
    <Field Name="PhaseGroupID" Type="int" />
    <Field Name="Icon" Type="string" />
    <Field Name="String_Lang" Type="string" />
    <Field Name="Tooltip_Lang" Type="string" />
    <Field Name="StateVariable" Type="int" />
    <Field Name="Type" Type="int" />
    <Field Name="DynamicIcon" Type="string" />
    <Field Name="DynamicTooltip_Lang" Type="string" />
    <Field Name="ExtendedUI" Type="string" />
    <Field Name="ExtendedUIStateVariable" Type="int" ArraySize="3" />
  </Table>
  <Table Name="WorldStateZoneSounds" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WorldStateID" Type="int" />
    <Field Name="WorldStateValue" Type="int" />
    <Field Name="AreaID" Type="int" />
    <Field Name="WMOAreaID" Type="int" />
    <Field Name="ZoneintroMusicID" Type="int" />
    <Field Name="ZoneMusicID" Type="int" />
    <Field Name="SoundAmbienceID" Type="int" />
    <Field Name="SoundProviderPreferencesID" Type="int" />
  </Table>
  <Table Name="WOWCache" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" AutoGenerate="true" />
    <Field Name="HashID" Type="string" />
    <Field Name="EntryLength" Type="int" />
    <Field Name="DataLength" Type="int" />
    <Field Name="ModuleData" Type="string" />
  </Table>
  <Table Name="WowError_Strings" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ErrorName" Type="string" />
    <Field Name="ErrorString" Type="loc" />
  </Table>
  <Table Name="ZoneintroMusicTable" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="SoundID" Type="int" />
    <Field Name="Priority" Type="int" />
    <Field Name="MinDelayMinutes" Type="int" />
  </Table>
  <Table Name="ZoneLight" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="MapID" Type="int" />
    <Field Name="LightID" Type="int" />
  </Table>
  <Table Name="ZoneLightPoint" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ZoneLightID" Type="int" />
    <Field Name="Pos" Type="float" ArraySize="2" />
    <Field Name="PointOrder" Type="int" />
  </Table>
  <Table Name="ZoneMusic" Build="21742">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SetName" Type="string" />
    <Field Name="SilenceintervalMin" Type="int" ArraySize="2" />
    <Field Name="SilenceintervalMax" Type="int" ArraySize="2" />
    <Field Name="Sounds" Type="int" ArraySize="2" />
  </Table>
</Definition>