﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Specialized;
using System.IO;
using System.Windows.Forms;
using System.Net;

namespace WDBXEditor.Common
{
	public static class Constants
	{
		public const string VERSION = "1.1.9.a";
		public const string AUTO_GENERATED = "AutoGenerated";
		public const StringComparison IGNORECASE = StringComparison.CurrentCultureIgnoreCase;
		public static readonly string DEFINITION_DIR = Path.Combine(Path.GetDirectoryName(Application.ExecutablePath), "Definitions/");
		public static readonly string TEMP_FOLDER = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "WDBXEditor");
		public static readonly string OFFSET_MAP_PATH = Path.Combine(DEFINITION_DIR, "Offsets.json");

		[Flags]
		public enum HeaderFlags : short
		{
			None = 0x0,
			OffsetMap = 0x1,
			RelationshipData = 0x2,
			IndexMap = 0x4,
			Unknown = 0x8,
			Compressed = 0x10,
		}

		public enum TextWowEnum
		{
			enUS,
			enGB,
			koKR,
			frFR,
			deDE,
			enCN,
			zhCN,
			enTW,
			zhTW,
			esES,
			esMX,
			ruRU,
			ptPT,
			ptBR,
			itIT,
			Unk,
		}

		public enum UpdateMode
		{
			Insert,
			Update,
			Replace
		}

		public enum ImportFlags
		{
			None,
			FixIds,
			TakeNewest
		}

		public enum CompressionType
		{
			None = 0,
			Immediate = 1,
			Sparse = 2,
			Pallet = 3,
			PalletArray = 4,
			SignedImmediate = 5
		}

		public enum Expansion
		{
			Alpha,
			Beta,
			Classic,
			TBC,
			WotLK,
			Cata,
			MoP,
			WoD,
			Legion,
			BfA
		}

		public enum ExpansionFinalBuild
		{
			Alpha = 3494,
			Beta = 3988,
			Classic = 6005,
			TBC = 8606,
			WotLK = 12340,
			Cata = 15595,
			MoP = 18414,
			WoD = 21742,
			Legion = 25901,
			BfA = 26926,
		}

		static Constants()
		{
			Task.Run(LoadListFile);
		}

		#region CommonDataColumn

		public static readonly short[] CommonDataBits = new short[] { 0, 16, 24, 0, 0 }; //String Int16 Byte Float Int32

		public static readonly Dictionary<TypeCode, byte> CommonDataTypes = new Dictionary<TypeCode, byte>()
		{
			{ TypeCode.String, 0 },
			{ TypeCode.Int16, 1 },
			{ TypeCode.UInt16, 1 },
			{ TypeCode.Byte, 2 },
			{ TypeCode.SByte, 2 },
			{ TypeCode.Single, 3 },
			{ TypeCode.Int32, 4 },
			{ TypeCode.UInt32, 4 },
		};
		#endregion

		#region Client Builds
		public static bool IsBuild(int build, Expansion expansion)
		{
			switch (expansion)
			{
				case Expansion.Alpha:
					return build <= (int)ExpansionFinalBuild.Alpha;
				case Expansion.Beta:
					return build > (int)ExpansionFinalBuild.Alpha && build <= (int)ExpansionFinalBuild.Beta;
				case Expansion.Classic:
					return build > (int)ExpansionFinalBuild.Beta && build <= (int)ExpansionFinalBuild.Classic;
				case Expansion.TBC:
					return build > (int)ExpansionFinalBuild.Classic && build <= (int)ExpansionFinalBuild.TBC;
				case Expansion.WotLK:
					return build > (int)ExpansionFinalBuild.TBC && build <= (int)ExpansionFinalBuild.WotLK;
				case Expansion.Cata:
					return build > (int)ExpansionFinalBuild.WotLK && build <= (int)ExpansionFinalBuild.Cata;
				case Expansion.MoP:
					return build > (int)ExpansionFinalBuild.Cata && build <= (int)ExpansionFinalBuild.MoP;
				case Expansion.WoD:
					return build > (int)ExpansionFinalBuild.MoP && build <= (int)ExpansionFinalBuild.WoD;
				case Expansion.Legion:
					return build > (int)ExpansionFinalBuild.WoD && build <= (int)ExpansionFinalBuild.Legion;
				case Expansion.BfA:
					return build > (int)ExpansionFinalBuild.Legion;
			}

			return false;
		}

		public static string BuildText(int build)
		{
			var first = Builds.First();
			var last = Builds.Last();
			string lastText = $"{first.Value} ({build})";

			if (build <= first.Key)
				return lastText; //First build
			else if (build >= last.Key)
				return $"{last.Value} ({build})"; //Last build

			foreach (var b in Builds)
			{
				if (build < b.Key)
					return lastText;

				lastText = $"{b.Value} ({build})";
			}

			return lastText;
		}

		private static readonly SortedDictionary<int, string> Builds = new SortedDictionary<int, string>()
		{
			{3368, "Alpha 0.5.3"},
			{3494, "Alpha 0.5.5"},
			{3694, "Beta 0.7.0"},
			{3702, "Beta 0.7.1"},
			{3712, "Beta 0.7.6"},
			{3734, "Beta 0.8.0"},
			{3807, "Beta 0.9.0"},
			{3810, "Beta 0.9.1"},
			{3892, "Beta 0.10.0"},
			{3925, "Beta 0.11.0"},
			{3988, "Beta 0.12.0"},
			{3980, "Classic 1.0.0"},
			{3989, "Classic 1.0.1"},
			{4044, "Classic 1.1.0"},
			{4062, "Classic 1.1.1"},
			{4125, "Classic 1.1.2"},
			{4147, "Classic 1.2.0"},
			{4150, "Classic 1.2.1"},
			{4196, "Classic 1.2.2"},
			{4211, "Classic 1.2.3"},
			{4222, "Classic 1.2.4"},
			{4284, "Classic 1.3.0"},
			{4297, "Classic 1.3.1"},
			{4341, "Classic 1.4.0"},
			{4364, "Classic 1.4.1"},
			{4375, "Classic 1.4.2"},
			{4442, "Classic 1.5.0"},
			{4499, "Classic 1.5.1"},
			{4500, "Classic 1.6.0"},
			{4544, "Classic 1.6.1"},
			{4671, "Classic 1.7.0"},
			{4695, "Classic 1.7.1"},
			{4735, "Classic 1.8.0"},
			{4769, "Classic 1.8.1"},
			{4784, "Classic 1.8.2"},
			{4807, "Classic 1.8.3"},
			{4878, "Classic 1.8.4"},
			{4937, "Classic 1.9.0"},
			{4983, "Classic 1.9.1"},
			{4996, "Classic 1.9.2"},
			{5059, "Classic 1.9.3"},
			{5086, "Classic 1.9.4"},
			{5195, "Classic 1.10.0"},
			{5230, "Classic 1.10.1"},
			{5302, "Classic 1.10.2"},
			{5428, "Classic 1.11.0"},
			{5464, "Classic 1.11.2"},
			{5595, "Classic 1.12.0"},
			{5875, "Classic 1.12.1"},
			{6005, "Classic 1.12.2"},
			{6080, "TBC 2.0.0"},
			{6180, "TBC 2.0.1"},
			{6299, "TBC 2.0.3"},
			{6314, "TBC 2.0.4"},
			{6320, "TBC 2.0.5"},
			{6337, "TBC 2.0.6"},
			{6383, "TBC 2.0.7"},
			{6403, "TBC 2.0.8"},
			{6448, "TBC 2.0.10"},
			{6546, "TBC 2.0.12"},
			{6729, "TBC 2.1.0"},
			{6739, "TBC 2.1.1"},
			{6803, "TBC 2.1.2"},
			{6898, "TBC 2.1.3"},
			{7272, "TBC 2.2.0"},
			{7318, "TBC 2.2.2"},
			{7359, "TBC 2.2.3"},
			{7561, "TBC 2.3.0"},
			{7741, "TBC 2.3.2"},
			{7799, "TBC 2.3.3"},
			{8089, "TBC 2.4.0"},
			{8125, "TBC 2.4.1"},
			{8278, "TBC 2.4.2"},
			{8606, "TBC 2.4.3"},
			{8820, "WotLK 3.0.1"},
			{9061, "WotLK 3.0.2"},
			{9183, "WotLK 3.0.3"},
			{9506, "WotLK 3.0.8"},
			{9551, "WotLK 3.0.9"},
			{9757, "WotLK 3.1.0"},
			{9835, "WotLK 3.1.1"},
			{9889, "WotLK 3.1.2"},
			{9947, "WotLK 3.1.3"},
			{10314, "WotLK 3.2.0"},
			{10505, "WotLK 3.2.2"},
			{11159, "WotLK 3.3.0"},
			{11599, "WotLK 3.3.2"},
			{11723, "WotLK 3.3.3"},
			{12340, "WotLK 3.3.5"},
			{12759, "Cata 4.0.0"},
			{13205, "Cata 4.0.1"},
			{13329, "Cata 4.0.3"},
			{13623, "Cata 4.0.6"},
			{14007, "Cata 4.1.0"},
			{14480, "Cata 4.2.0"},
			{14545, "Cata 4.2.2"},
			{15050, "Cata 4.3.0"},
			{15211, "Cata 4.3.2"},
			{15354, "Cata 4.3.3"},
			{15595, "Cata 4.3.4"},
			{15851, "MoP 5.0.1"},
			{15882, "MoP 5.0.3"},
			{16016, "MoP 5.0.4"},
			{16135, "MoP 5.0.5"},
			{16357, "MoP 5.1.0"},
			{16826, "MoP 5.2.0"},
			{17128, "MoP 5.3.0"},
			{17399, "MoP 5.4.0"},
			{17538, "MoP 5.4.1"},
			{17688, "MoP 5.4.2"},
			{18019, "MoP 5.4.7"},
			{18414, "MoP 5.4.8"},
			{19027, "WoD 6.0.2"},
			{19033, "WoD 6.0.2"},
			{19034, "WoD 6.0.2"},
			{19085, "WoD 6.0.3"},
			{19102, "WoD 6.0.3"},
			{19103, "WoD 6.0.3"},
			{19116, "WoD 6.0.3"},
			{19200, "WoD 6.0.3"},
			{19206, "WoD 6.0.3"},
			{19227, "WoD 6.0.3"},
			{19243, "WoD 6.0.3"},
			{19342, "WoD 6.0.3"},
			{19678, "WoD 6.1.0"},
			{19701, "WoD 6.1.0"},
			{19702, "WoD 6.1.0"},
			{19802, "WoD 6.1.2"},
			{19831, "WoD 6.1.2"},
			{19854, "WoD 6.1.2"},
			{19865, "WoD 6.1.2"},
			{20173, "WoD 6.2.0"},
			{20182, "WoD 6.2.0a"},
			{20201, "WoD 6.2.0"},
			{20216, "WoD 6.2.0"},
			{20253, "WoD 6.2.0"},
			{20318, "WoD 6.2.0"},
			{20338, "WoD 6.2.0"},
			{20444, "WoD 6.2.2"},
			{20490, "WoD 6.2.2"},
			{20574, "WoD 6.2.2"},
			{20726, "WoD 6.2.3"},
			{20779, "WoD 6.2.3"},
			{20886, "WoD 6.2.3"},
			{21315, "WoD 6.2.4"},
			{21336, "WoD 6.2.4"},
			{21343, "WoD 6.2.4"},
			{21345, "WoD 6.2.4"},
			{21348, "WoD 6.2.4"},
			{21355, "WoD 6.2.4"},
			{21463, "WoD 6.2.4"},
			{21676, "WoD 6.2.4"},
			{21742, "WoD 6.2.4"},
			{21992, "Legion 7.0.3"},
			{22133, "Legion 7.0.3"},
			{22248, "Legion 7.0.3"},
			{22267, "Legion 7.0.3"},
			{22277, "Legion 7.0.3"},
			{22280, "Legion 7.0.3"},
			{22289, "Legion 7.0.3"},
			{22293, "Legion 7.0.3"},
			{22345, "Legion 7.0.3"},
			{22393, "Legion 7.0.3"},
			{22410, "Legion 7.0.3"},
			{22423, "Legion 7.0.3"},
			{22498, "Legion 7.0.3"},
			{22522, "Legion 7.0.3"},
			{22566, "Legion 7.0.3"},
			{22594, "Legion 7.0.3"},
			{22624, "Legion 7.0.3"},
			{22747, "Legion 7.0.3"},
			{22810, "Legion 7.0.3"},
			{22900, "Legion 7.1.0"},
			{22908, "Legion 7.1.0"},
			{22950, "Legion 7.1.0"},
			{22989, "Legion 7.1.0"},
			{22995, "Legion 7.1.0"},
			{22996, "Legion 7.1.0"},
			{23171, "Legion 7.1.0"},
			{23222, "Legion 7.1.0"},
			{23360, "Legion 7.1.5"},
			{23420, "Legion 7.1.5"},
			{23826, "Legion 7.2.0"},
			{23835, "Legion 7.2.0"},
			{23836, "Legion 7.2.0"},
			{23846, "Legion 7.2.0"},
			{23852, "Legion 7.2.0"},
			{23857, "Legion 7.2.0"},
			{23877, "Legion 7.2.0"},
			{23905, "Legion 7.2.0"},
			{23911, "Legion 7.2.0"},
			{23937, "Legion 7.2.0"},
			{24015, "Legion 7.2.0"},
			{24330, "Legion 7.2.5"},
			{24367, "Legion 7.2.5"},
			{24414, "Legion 7.2.5"},
			{24415, "Legion 7.2.5"},
			{24430, "Legion 7.2.5"},
			{24461, "Legion 7.2.5"},
			{24742, "Legion 7.2.5"},
			{24887, "Legion 7.3.0"},
			{24920, "Legion 7.3.0"},
			{24931, "Legion 7.3.0"},
			{24956, "Legion 7.3.0"},
			{24970, "Legion 7.3.0"},
			{24974, "Legion 7.3.0"},
			{25021, "Legion 7.3.0"},
			{25195, "Legion 7.3.0"},
			{25326, "Legion 7.3.2"},
			{25383, "Legion 7.3.2"},
			{25442, "Legion 7.3.2"},
			{25455, "Legion 7.3.2"},
			{25477, "Legion 7.3.2"},
			{25480, "Legion 7.3.2"},
			{25497, "Legion 7.3.2"},
			{25516, "Legion 7.3.2"},
			{25549, "Legion 7.3.2"},
			{25848, "Legion 7.3.5"},
			{25860, "Legion 7.3.5"},
			{25864, "Legion 7.3.5"},
			{25875, "Legion 7.3.5"},
			{25881, "Legion 7.3.5"},
			{25901, "Legion 7.3.5"},
			{25928, "Legion 7.3.5"},
			{25937, "Legion 7.3.5"},
			{25944, "Legion 7.3.5"},
			{25946, "Legion 7.3.5"},
			{25950, "Legion 7.3.5"},
			{25961, "Legion 7.3.5"},
			{25996, "Legion 7.3.5"},
			{26124, "Legion 7.3.5"},
			{26365, "Legion 7.3.5"},
			{26654, "Legion 7.3.5"},
			{26755, "Legion 7.3.5"},
			{26822, "Legion 7.3.5"},
			{26899, "Legion 7.3.5"},
			{26926, "BfA 8.0.1"},
			{26972, "Legion 7.3.5"},
			{27026, "BfA 8.0.1"},
			{27101, "BfA 8.0.1"},
			{27144, "BfA 8.0.1"},
			{27165, "BfA 8.0.1"},
			{27178, "BfA 8.0.1"},
			{27219, "BfA 8.0.1"},
			{27291, "BfA 8.0.1"},
			{27326, "BfA 8.0.1"},
			{27353, "BfA 8.0.1"},
			{27355, "BfA 8.0.1"},
			{27356, "BfA 8.0.1"},
			{27366, "BfA 8.0.1"},
			{27377, "BfA 8.0.1"},
			{27404, "BfA 8.0.1"},
			{27481, "BfA 8.0.1"},
			{27547, "BfA 8.0.1"},
			{27602, "BfA 8.0.1"},
			{27791, "BfA 8.0.1"},
			{27843, "BfA 8.0.1"},
			{27980, "BfA 8.0.1"},
			{28153, "BfA 8.0.1"},
			{28657, "BfA 8.1.0"},
			{28724, "BfA 8.1.0"},
			{28768, "BfA 8.1.0"},
			{28807, "BfA 8.1.0"},
			{28822, "BfA 8.1.0"},
			{28833, "BfA 8.1.0"},
			{29088, "BfA 8.1.0"},
			{29139, "BfA 8.1.0"},
			{29235, "BfA 8.1.0"},
			{29285, "BfA 8.1.0"},
			{29297, "BfA 8.1.0"},
			{29482, "BfA 8.1.0"},
			{29600, "BfA 8.1.0"},
			{29620, "BfA 8.1.5"},
			{29621, "BfA 8.1.0"},
			{29683, "BfA 8.1.5"},
			{29701, "BfA 8.1.5"},
			{29704, "BfA 8.1.5"},
			{29705, "BfA 8.1.5"},
			{29718, "BfA 8.1.5"},
			{29732, "BfA 8.1.5"},
			{29737, "BfA 8.1.5"},
			{29814, "BfA 8.1.5"},
			{29869, "BfA 8.1.5"},
			{29896, "BfA 8.1.5"},
			{29981, "BfA 8.1.5"},
			{30477, "BfA 8.1.5"},
			{30706, "BfA 8.1.5"},
			{30827, "BfA 8.2.0"},
			{30898, "BfA 8.2.0"},
			{30918, "BfA 8.2.0"},
			{30920, "BfA 8.2.0"},
			{30948, "BfA 8.2.0"},
			{30993, "BfA 8.2.0"},
			{31229, "BfA 8.2.0"},
			{31429, "BfA 8.2.0"},
			{31478, "BfA 8.2.0"},
			{31884, "BfA 8.2.5"},
			{31921, "BfA 8.2.5"},
			{31958, "BfA 8.2.5"},
			{31960, "BfA 8.2.5"},
			{31961, "BfA 8.2.5"},
			{31984, "BfA 8.2.5"},
			{32028, "BfA 8.2.5"},
			{32144, "BfA 8.2.5"},
			{32185, "BfA 8.2.5"},
			{32265, "BfA 8.2.5"},
			{32294, "BfA 8.2.5"},
			{32305, "BfA 8.2.5"},
			{32494, "BfA 8.2.5"},
			{32580, "BfA 8.2.5"},
			{32638, "BfA 8.2.5"},
			{32722, "BfA 8.2.5"},
			{32750, "BfA 8.2.5"},
			{32978, "BfA 8.2.5"},
			{33051, "BfA 8.3.0"},
			{33062, "BfA 8.3.0"},
			{33073, "BfA 8.3.0"},
			{33084, "BfA 8.3.0"},
			{33095, "BfA 8.3.0"},
			{33115, "BfA 8.3.0"},
			{33169, "BfA 8.3.0"},
			{33237, "BfA 8.3.0"},
			{33369, "BfA 8.3.0"},
			{33528, "BfA 8.3.0"},
			{33724, "BfA 8.3.0"},
			{33775, "BfA 8.3.0"},
			{33941, "BfA 8.3.0"},
			{34220, "BfA 8.3.0"},
			{34601, "BfA 8.3.0"},
			{34769, "BfA 8.3.0"},
			{34963, "BfA 8.3.0"},
			{35249, "BfA 8.3.7"},
			{35284, "BfA 8.3.7"},
			{35435, "BfA 8.3.7"},
		};
		#endregion

		#region File Types
		public static string FileRegexPattern => $"^(.{ SupportedFileTypes["All Files"].Replace(";*.", "|.*.") })$";

		public static readonly Dictionary<string, string> SupportedFileTypes = new Dictionary<string, string>()
		{
			{"All Files", "*.dbc;*.db2;*.adb;*.wdb;DBCache.bin" },
			{"DBC Files", "*.dbc" },
			{"DB2 Files", "*.db2" },
			{"ADB Files", "*.adb" },
			{"WDB Files", "*.wdb" },
			{"Hotfix Files", "DBCache.bin" },
		};
		#endregion

		#region DB FileNames
		public static List<string> ClientDBFileNames { get; private set; } = new List<string>
		{
			"DBFILESCLIENT\\ACHIEVEMENT.DB2",
			"DBFILESCLIENT\\ACHIEVEMENT.DBC",
			"DBFILESCLIENT\\ACHIEVEMENT_CATEGORY.DB2",
			"DBFILESCLIENT\\ACHIEVEMENT_CATEGORY.DBC",
			"DBFILESCLIENT\\ACHIEVEMENT_CRITERIA.DBC",
			"DBFILESCLIENT\\ADVENTUREJOURNAL.DB2",
			"DBFILESCLIENT\\ADVENTUREMAPPOI.DB2",
			"DBFILESCLIENT\\ALLIEDRACE.DB2",
			"DBFILESCLIENT\\ALLIEDRACERACIALABILITY.DB2",
			"DBFILESCLIENT\\ANIMATIONDATA.DB2",
			"DBFILESCLIENT\\ANIMATIONDATA.DBC",
			"DBFILESCLIENT\\ANIMKIT.DB2",
			"DBFILESCLIENT\\ANIMKIT.DBC",
			"DBFILESCLIENT\\ANIMKITBONESET.DB2",
			"DBFILESCLIENT\\ANIMKITBONESET.DBC",
			"DBFILESCLIENT\\ANIMKITBONESETALIAS.DB2",
			"DBFILESCLIENT\\ANIMKITBONESETALIAS.DBC",
			"DBFILESCLIENT\\ANIMKITCONFIG.DB2",
			"DBFILESCLIENT\\ANIMKITCONFIG.DBC",
			"DBFILESCLIENT\\ANIMKITCONFIGBONESET.DB2",
			"DBFILESCLIENT\\ANIMKITCONFIGBONESET.DBC",
			"DBFILESCLIENT\\ANIMKITPRIORITY.DB2",
			"DBFILESCLIENT\\ANIMKITPRIORITY.DBC",
			"DBFILESCLIENT\\ANIMKITREPLACEMENT.DB2",
			"DBFILESCLIENT\\ANIMKITSEGMENT.DB2",
			"DBFILESCLIENT\\ANIMKITSEGMENT.DBC",
			"DBFILESCLIENT\\ANIMREPLACEMENT.DB2",
			"DBFILESCLIENT\\ANIMREPLACEMENT.DBC",
			"DBFILESCLIENT\\ANIMREPLACEMENTSET.DB2",
			"DBFILESCLIENT\\ANIMREPLACEMENTSET.DBC",
			"DBFILESCLIENT\\AREAASSIGNMENT.DB2",
			"DBFILESCLIENT\\AREAASSIGNMENT.DBC",
			"DBFILESCLIENT\\AREAFARCLIPOVERRIDE.DB2",
			"DBFILESCLIENT\\AREAGROUP.DB2",
			"DBFILESCLIENT\\AREAGROUP.DBC",
			"DBFILESCLIENT\\AREAGROUPMEMBER.DB2",
			"DBFILESCLIENT\\AREAMIDIAMBIENCES.DBC",
			"DBFILESCLIENT\\AREAPOI.DB2",
			"DBFILESCLIENT\\AREAPOI.DBC",
			"DBFILESCLIENT\\AREAPOISTATE.DB2",
			"DBFILESCLIENT\\AREATABLE.DB2",
			"DBFILESCLIENT\\AREATABLE.DBC",
			"DBFILESCLIENT\\AREATRIGGER.DB2",
			"DBFILESCLIENT\\AREATRIGGER.DBC",
			"DBFILESCLIENT\\AREATRIGGERACTIONSET.DB2",
			"DBFILESCLIENT\\AREATRIGGERACTIONSET.DBC",
			"DBFILESCLIENT\\AREATRIGGERBOX.DB2",
			"DBFILESCLIENT\\AREATRIGGERBOX.DBC",
			"DBFILESCLIENT\\AREATRIGGERCYLINDER.DB2",
			"DBFILESCLIENT\\AREATRIGGERCYLINDER.DBC",
			"DBFILESCLIENT\\AREATRIGGERSPHERE.DB2",
			"DBFILESCLIENT\\AREATRIGGERSPHERE.DBC",
			"DBFILESCLIENT\\ARMORLOCATION.DB2",
			"DBFILESCLIENT\\ARMORLOCATION.DBC",
			"DBFILESCLIENT\\ARTIFACT.DB2",
			"DBFILESCLIENT\\ARTIFACTAPPEARANCE.DB2",
			"DBFILESCLIENT\\ARTIFACTAPPEARANCESET.DB2",
			"DBFILESCLIENT\\ARTIFACTCATEGORY.DB2",
			"DBFILESCLIENT\\ARTIFACTPOWER.DB2",
			"DBFILESCLIENT\\ARTIFACTPOWERLINK.DB2",
			"DBFILESCLIENT\\ARTIFACTPOWERPICKER.DB2",
			"DBFILESCLIENT\\ARTIFACTPOWERRANK.DB2",
			"DBFILESCLIENT\\ARTIFACTQUESTXP.DB2",
			"DBFILESCLIENT\\ARTIFACTTIER.DB2",
			"DBFILESCLIENT\\ARTIFACTUNLOCK.DB2",
			"DBFILESCLIENT\\ATTACKANIMKITS.DBC",
			"DBFILESCLIENT\\ATTACKANIMTYPES.DBC",
			"DBFILESCLIENT\\AUCTIONHOUSE.DB2",
			"DBFILESCLIENT\\AUCTIONHOUSE.DBC",
			"DBFILESCLIENT\\AZERITEEMPOWEREDITEM.DB2",
			"DBFILESCLIENT\\AZERITEITEM.DB2",
			"DBFILESCLIENT\\AZERITEPOWER.DB2",
			"DBFILESCLIENT\\AZERITEPOWERSETMEMBER.DB2",
			"DBFILESCLIENT\\AZERITETIERUNLOCK.DB2",
			"DBFILESCLIENT\\BANKBAGSLOTPRICES.DB2",
			"DBFILESCLIENT\\BANKBAGSLOTPRICES.DBC",
			"DBFILESCLIENT\\BANNEDADDONS.DB2",
			"DBFILESCLIENT\\BANNEDADDONS.DBC",
			"DBFILESCLIENT\\BARBERSHOPSTYLE.DB2",
			"DBFILESCLIENT\\BARBERSHOPSTYLE.DBC",
			"DBFILESCLIENT\\BATTLEMASTERLIST.DB2",
			"DBFILESCLIENT\\BATTLEMASTERLIST.DBC",
			"DBFILESCLIENT\\BATTLEPETABILITY.DB2",
			"DBFILESCLIENT\\BATTLEPETABILITYEFFECT.DB2",
			"DBFILESCLIENT\\BATTLEPETABILITYSTATE.DB2",
			"DBFILESCLIENT\\BATTLEPETABILITYTURN.DB2",
			"DBFILESCLIENT\\BATTLEPETABILITYTURN.DBC",
			"DBFILESCLIENT\\BATTLEPETBREEDQUALITY.DB2",
			"DBFILESCLIENT\\BATTLEPETBREEDSTATE.DB2",
			"DBFILESCLIENT\\BATTLEPETDISPLAYOVERRIDE.DB2",
			"DBFILESCLIENT\\BATTLEPETEFFECTPROPERTIES.DB2",
			"DBFILESCLIENT\\BATTLEPETNPCTEAMMEMBER.DB2",
			"DBFILESCLIENT\\BATTLEPETSPECIES.DB2",
			"DBFILESCLIENT\\BATTLEPETSPECIESSTATE.DB2",
			"DBFILESCLIENT\\BATTLEPETSPECIESXABILITY.DB2",
			"DBFILESCLIENT\\BATTLEPETSPECIESXABILITY.DBC",
			"DBFILESCLIENT\\BATTLEPETSTATE.DB2",
			"DBFILESCLIENT\\BATTLEPETVISUAL.DB2",
			"DBFILESCLIENT\\BEAMEFFECT.DB2",
			"DBFILESCLIENT\\BONEWINDMODIFIERMODEL.DB2",
			"DBFILESCLIENT\\BONEWINDMODIFIERS.DB2",
			"DBFILESCLIENT\\BOUNTY.DB2",
			"DBFILESCLIENT\\BOUNTYSET.DB2",
			"DBFILESCLIENT\\BROADCASTTEXT.DB2",
			"DBFILESCLIENT\\BROADCASTTEXT_INTERNAL.DB2",
			"DBFILESCLIENT\\CAMERAEFFECT.DB2",
			"DBFILESCLIENT\\CAMERAEFFECTENTRY.DB2",
			"DBFILESCLIENT\\CAMERAMODE.DB2",
			"DBFILESCLIENT\\CAMERAMODE.DBC",
			"DBFILESCLIENT\\CAMERASHAKES.DB2",
			"DBFILESCLIENT\\CAMERASHAKES.DBC",
			"DBFILESCLIENT\\CASTABLERAIDBUFFS.DB2",
			"DBFILESCLIENT\\CASTABLERAIDBUFFS.DBC",
			"DBFILESCLIENT\\CELESTIALBODY.DB2",
			"DBFILESCLIENT\\CFG_CATEGORIES.DB2",
			"DBFILESCLIENT\\CFG_CATEGORIES.DBC",
			"DBFILESCLIENT\\CFG_CONFIGS.DB2",
			"DBFILESCLIENT\\CFG_CONFIGS.DBC",
			"DBFILESCLIENT\\CFG_REGIONS.DB2",
			"DBFILESCLIENT\\CFG_REGIONS.DBC",
			"DBFILESCLIENT\\CHARACTERCREATECAMERAS.DBC",
			"DBFILESCLIENT\\CHARACTERFACEBONESET.DB2",
			"DBFILESCLIENT\\CHARACTERFACIALHAIRSTYLES.DB2",
			"DBFILESCLIENT\\CHARACTERFACIALHAIRSTYLES.DBC",
			"DBFILESCLIENT\\CHARACTERLOADOUT.DB2",
			"DBFILESCLIENT\\CHARACTERLOADOUT.DBC",
			"DBFILESCLIENT\\CHARACTERLOADOUTITEM.DB2",
			"DBFILESCLIENT\\CHARACTERLOADOUTITEM.DBC",
			"DBFILESCLIENT\\CHARACTERSERVICEINFO.DB2",
			"DBFILESCLIENT\\CHARBASEINFO.DB2",
			"DBFILESCLIENT\\CHARBASEINFO.DBC",
			"DBFILESCLIENT\\CHARBASESECTION.DB2",
			"DBFILESCLIENT\\CHARBASESECTION.DBC",
			"DBFILESCLIENT\\CHARCOMPONENTTEXTURELAYOUTS.DB2",
			"DBFILESCLIENT\\CHARCOMPONENTTEXTURELAYOUTS.DBC",
			"DBFILESCLIENT\\CHARCOMPONENTTEXTURESECTIONS.DB2",
			"DBFILESCLIENT\\CHARCOMPONENTTEXTURESECTIONS.DBC",
			"DBFILESCLIENT\\CHARHAIRGEOSETS.DB2",
			"DBFILESCLIENT\\CHARHAIRGEOSETS.DBC",
			"DBFILESCLIENT\\CHARHAIRTEXTURES.DBC",
			"DBFILESCLIENT\\CHARSECTIONS.DB2",
			"DBFILESCLIENT\\CHARSECTIONS.DBC",
			"DBFILESCLIENT\\CHARSHIPMENT.DB2",
			"DBFILESCLIENT\\CHARSHIPMENTCONTAINER.DB2",
			"DBFILESCLIENT\\CHARSTARTOUTFIT.DB2",
			"DBFILESCLIENT\\CHARSTARTOUTFIT.DBC",
			"DBFILESCLIENT\\CHARTITLES.DB2",
			"DBFILESCLIENT\\CHARTITLES.DBC",
			"DBFILESCLIENT\\CHARVARIATIONS.DBC",
			"DBFILESCLIENT\\CHATCHANNELS.DB2",
			"DBFILESCLIENT\\CHATCHANNELS.DBC",
			"DBFILESCLIENT\\CHATPROFANITY.DB2",
			"DBFILESCLIENT\\CHATPROFANITY.DBC",
			"DBFILESCLIENT\\CHRCLASSES.DB2",
			"DBFILESCLIENT\\CHRCLASSES.DBC",
			"DBFILESCLIENT\\CHRCLASSESXPOWERTYPES.DB2",
			"DBFILESCLIENT\\CHRCLASSESXPOWERTYPES.DBC",
			"DBFILESCLIENT\\CHRCLASSRACESEX.DB2",
			"DBFILESCLIENT\\CHRCLASSTITLE.DB2",
			"DBFILESCLIENT\\CHRCLASSUIDISPLAY.DB2",
			"DBFILESCLIENT\\CHRCLASSVILLAIN.DB2",
			"DBFILESCLIENT\\CHRCUSTOMIZATION.DB2",
			"DBFILESCLIENT\\CHRPROFICIENCY.DBC",
			"DBFILESCLIENT\\CHRRACES.DB2",
			"DBFILESCLIENT\\CHRRACES.DBC",
			"DBFILESCLIENT\\CHRSPECIALIZATION.DB2",
			"DBFILESCLIENT\\CHRSPECIALIZATION.DBC",
			"DBFILESCLIENT\\CHRUPGRADEBUCKET.DB2",
			"DBFILESCLIENT\\CHRUPGRADEBUCKETSPELL.DB2",
			"DBFILESCLIENT\\CHRUPGRADETIER.DB2",
			"DBFILESCLIENT\\CINEMATIC.DBC",
			"DBFILESCLIENT\\CINEMATICCAMERA.DB2",
			"DBFILESCLIENT\\CINEMATICCAMERA.DBC",
			"DBFILESCLIENT\\CINEMATICSEQUENCES.DB2",
			"DBFILESCLIENT\\CINEMATICSEQUENCES.DBC",
			"DBFILESCLIENT\\CINEMATICSUBTITLE.DBC",
			"DBFILESCLIENT\\CLOAKDAMPENING.DB2",
			"DBFILESCLIENT\\COMBATCONDITION.DB2",
			"DBFILESCLIENT\\COMBATCONDITION.DBC",
			"DBFILESCLIENT\\COMMENTATORSTARTLOCATION.DB2",
			"DBFILESCLIENT\\COMMENTATORTRACKEDCOOLDOWN.DB2",
			"DBFILESCLIENT\\COMPONENTMODELFILEDATA.DB2",
			"DBFILESCLIENT\\COMPONENTTEXTUREFILEDATA.DB2",
			"DBFILESCLIENT\\CONFIGURATIONWARNING.DB2",
			"DBFILESCLIENT\\CONSOLESCRIPTS.DB2",
			"DBFILESCLIENT\\CONTRIBUTION.DB2",
			"DBFILESCLIENT\\CONVERSATIONLINE.DB2",
			"DBFILESCLIENT\\CREATURE.DB2",
			"DBFILESCLIENT\\CREATURE.DBC",
			"DBFILESCLIENT\\CREATURE_INTERNAL.DB2",
			"DBFILESCLIENT\\CREATUREDIFFICULTY.DB2",
			"DBFILESCLIENT\\CREATUREDIFFICULTY.DBC",
			"DBFILESCLIENT\\CREATUREDIFFICULTY_INTERNAL.DB2",
			"DBFILESCLIENT\\CREATUREDISPLAYINFO.DB2",
			"DBFILESCLIENT\\CREATUREDISPLAYINFO.DBC",
			"DBFILESCLIENT\\CREATUREDISPLAYINFOCOND.DB2",
			"DBFILESCLIENT\\CREATUREDISPLAYINFOEVT.DB2",
			"DBFILESCLIENT\\CREATUREDISPLAYINFOEXTRA.DB2",
			"DBFILESCLIENT\\CREATUREDISPLAYINFOEXTRA.DBC",
			"DBFILESCLIENT\\CREATUREDISPLAYINFOGEOSETDATA.DB2",
			"DBFILESCLIENT\\CREATUREDISPLAYINFOTRN.DB2",
			"DBFILESCLIENT\\CREATUREDISPXUICAMERA.DB2",
			"DBFILESCLIENT\\CREATUREFAMILY.DB2",
			"DBFILESCLIENT\\CREATUREFAMILY.DBC",
			"DBFILESCLIENT\\CREATUREIMMUNITIES.DB2",
			"DBFILESCLIENT\\CREATUREIMMUNITIES.DBC",
			"DBFILESCLIENT\\CREATUREMODELDATA.DB2",
			"DBFILESCLIENT\\CREATUREMODELDATA.DBC",
			"DBFILESCLIENT\\CREATUREMOVEMENTINFO.DB2",
			"DBFILESCLIENT\\CREATUREMOVEMENTINFO.DBC",
			"DBFILESCLIENT\\CREATURESOUNDDATA.DB2",
			"DBFILESCLIENT\\CREATURESOUNDDATA.DBC",
			"DBFILESCLIENT\\CREATURESPELLDATA.DBC",
			"DBFILESCLIENT\\CREATURETYPE.DB2",
			"DBFILESCLIENT\\CREATURETYPE.DBC",
			"DBFILESCLIENT\\CREATUREXCONTRIBUTION.DB2",
			"DBFILESCLIENT\\CREATUREXDISPLAYINFO.DB2",
			"DBFILESCLIENT\\CRITERIA.DB2",
			"DBFILESCLIENT\\CRITERIA.DBC",
			"DBFILESCLIENT\\CRITERIATREE.DB2",
			"DBFILESCLIENT\\CRITERIATREE.DBC",
			"DBFILESCLIENT\\CRITERIATREEXEFFECT.DB2",
			"DBFILESCLIENT\\CRITERIATREEXEFFECT.DBC",
			"DBFILESCLIENT\\CURRENCYCATEGORY.DB2",
			"DBFILESCLIENT\\CURRENCYCATEGORY.DBC",
			"DBFILESCLIENT\\CURRENCYTYPES.DB2",
			"DBFILESCLIENT\\CURRENCYTYPES.DBC",
			"DBFILESCLIENT\\CURVE.DB2",
			"DBFILESCLIENT\\CURVEPOINT.DB2",
			"DBFILESCLIENT\\DANCEMOVES.DBC",
			"DBFILESCLIENT\\DEATHTHUDLOOKUPS.DB2",
			"DBFILESCLIENT\\DEATHTHUDLOOKUPS.DBC",
			"DBFILESCLIENT\\DECALPROPERTIES.DB2",
			"DBFILESCLIENT\\DECLINEDWORD.DB2",
			"DBFILESCLIENT\\DECLINEDWORD.DBC",
			"DBFILESCLIENT\\DECLINEDWORDCASES.DB2",
			"DBFILESCLIENT\\DECLINEDWORDCASES.DBC",
			"DBFILESCLIENT\\DESTRUCTIBLEMODELDATA.DB2",
			"DBFILESCLIENT\\DESTRUCTIBLEMODELDATA.DBC",
			"DBFILESCLIENT\\DEVICEBLACKLIST.DB2",
			"DBFILESCLIENT\\DEVICEDEFAULTSETTINGS.DB2",
			"DBFILESCLIENT\\DIFFICULTY.DB2",
			"DBFILESCLIENT\\DIFFICULTY.DBC",
			"DBFILESCLIENT\\DISSOLVEEFFECT.DB2",
			"DBFILESCLIENT\\DRIVERBLACKLIST.DB2",
			"DBFILESCLIENT\\DUNGEONENCOUNTER.DB2",
			"DBFILESCLIENT\\DUNGEONENCOUNTER.DBC",
			"DBFILESCLIENT\\DUNGEONMAP.DB2",
			"DBFILESCLIENT\\DUNGEONMAP.DBC",
			"DBFILESCLIENT\\DUNGEONMAPCHUNK.DB2",
			"DBFILESCLIENT\\DUNGEONMAPCHUNK.DBC",
			"DBFILESCLIENT\\DURABILITYCOSTS.DB2",
			"DBFILESCLIENT\\DURABILITYCOSTS.DBC",
			"DBFILESCLIENT\\DURABILITYQUALITY.DB2",
			"DBFILESCLIENT\\DURABILITYQUALITY.DBC",
			"DBFILESCLIENT\\EDGEGLOWEFFECT.DB2",
			"DBFILESCLIENT\\EMOTEANIMS.DBC",
			"DBFILESCLIENT\\EMOTES.DB2",
			"DBFILESCLIENT\\EMOTES.DBC",
			"DBFILESCLIENT\\EMOTESTEXT.DB2",
			"DBFILESCLIENT\\EMOTESTEXT.DBC",
			"DBFILESCLIENT\\EMOTESTEXTDATA.DB2",
			"DBFILESCLIENT\\EMOTESTEXTDATA.DBC",
			"DBFILESCLIENT\\EMOTESTEXTSOUND.DB2",
			"DBFILESCLIENT\\EMOTESTEXTSOUND.DBC",
			"DBFILESCLIENT\\ENVIRONMENTALDAMAGE.DB2",
			"DBFILESCLIENT\\ENVIRONMENTALDAMAGE.DBC",
			"DBFILESCLIENT\\EXHAUSTION.DB2",
			"DBFILESCLIENT\\EXHAUSTION.DBC",
			"DBFILESCLIENT\\FACTION.DB2",
			"DBFILESCLIENT\\FACTION.DBC",
			"DBFILESCLIENT\\FACTIONGROUP.DB2",
			"DBFILESCLIENT\\FACTIONGROUP.DBC",
			"DBFILESCLIENT\\FACTIONTEMPLATE.DB2",
			"DBFILESCLIENT\\FACTIONTEMPLATE.DBC",
			"DBFILESCLIENT\\FEEDBACKPATH.DBC",
			"DBFILESCLIENT\\FILEDATA.DBC",
			"DBFILESCLIENT\\FILEDATACOMPLETE.DB2",
			"DBFILESCLIENT\\FILEDATACOMPLETE.DBC",
			"DBFILESCLIENT\\FILEPATHS.DB2",
			"DBFILESCLIENT\\FOOTPRINTTEXTURES.DB2",
			"DBFILESCLIENT\\FOOTPRINTTEXTURES.DBC",
			"DBFILESCLIENT\\FOOTSTEPTERRAINLOOKUP.DB2",
			"DBFILESCLIENT\\FOOTSTEPTERRAINLOOKUP.DBC",
			"DBFILESCLIENT\\FRIENDSHIPREPREACTION.DB2",
			"DBFILESCLIENT\\FRIENDSHIPREPREACTION.DBC",
			"DBFILESCLIENT\\FRIENDSHIPREPUTATION.DB2",
			"DBFILESCLIENT\\FRIENDSHIPREPUTATION.DBC",
			"DBFILESCLIENT\\FULLSCREENEFFECT.DB2",
			"DBFILESCLIENT\\GAMEOBJECTARTKIT.DB2",
			"DBFILESCLIENT\\GAMEOBJECTARTKIT.DBC",
			"DBFILESCLIENT\\GAMEOBJECTDIFFANIMMAP.DB2",
			"DBFILESCLIENT\\GAMEOBJECTDIFFANIMMAP.DBC",
			"DBFILESCLIENT\\GAMEOBJECTDISPLAYINFO.DB2",
			"DBFILESCLIENT\\GAMEOBJECTDISPLAYINFO.DBC",
			"DBFILESCLIENT\\GAMEOBJECTDISPLAYINFOXSOUNDKIT.DB2",
			"DBFILESCLIENT\\GAMEOBJECTS.DB2",
			"DBFILESCLIENT\\GAMEOBJECTS_INTERNAL.DB2",
			"DBFILESCLIENT\\GAMEOBJECTSCLIENT.DBC",
			"DBFILESCLIENT\\GAMETABLES.DB2",
			"DBFILESCLIENT\\GAMETABLES.DBC",
			"DBFILESCLIENT\\GAMETIPS.DB2",
			"DBFILESCLIENT\\GAMETIPS.DBC",
			"DBFILESCLIENT\\GARRABILITY.DB2",
			"DBFILESCLIENT\\GARRABILITYCATEGORY.DB2",
			"DBFILESCLIENT\\GARRABILITYEFFECT.DB2",
			"DBFILESCLIENT\\GARRBUILDING.DB2",
			"DBFILESCLIENT\\GARRBUILDINGDOODADSET.DB2",
			"DBFILESCLIENT\\GARRBUILDINGPLOTINST.DB2",
			"DBFILESCLIENT\\GARRCLASSSPEC.DB2",
			"DBFILESCLIENT\\GARRCLASSSPECPLAYERCOND.DB2",
			"DBFILESCLIENT\\GARRENCOUNTER.DB2",
			"DBFILESCLIENT\\GARRENCOUNTERSETXENCOUNTER.DB2",
			"DBFILESCLIENT\\GARRENCOUNTERXMECHANIC.DB2",
			"DBFILESCLIENT\\GARRFAMILYNAME.DB2",
			"DBFILESCLIENT\\GARRFOLLITEMSET.DB2",
			"DBFILESCLIENT\\GARRFOLLITEMSETMEMBER.DB2",
			"DBFILESCLIENT\\GARRFOLLOWER.DB2",
			"DBFILESCLIENT\\GARRFOLLOWERLEVELXP.DB2",
			"DBFILESCLIENT\\GARRFOLLOWERQUALITY.DB2",
			"DBFILESCLIENT\\GARRFOLLOWERSETXFOLLOWER.DB2",
			"DBFILESCLIENT\\GARRFOLLOWERTYPE.DB2",
			"DBFILESCLIENT\\GARRFOLLOWERUICREATURE.DB2",
			"DBFILESCLIENT\\GARRFOLLOWERXABILITY.DB2",
			"DBFILESCLIENT\\GARRFOLLSUPPORTSPELL.DB2",
			"DBFILESCLIENT\\GARRGIVENNAME.DB2",
			"DBFILESCLIENT\\GARRITEMLEVELIPGRADEDATA.DB2",
			"DBFILESCLIENT\\GARRITEMLEVELUPGRADEDATA.DB2",
			"DBFILESCLIENT\\GARRMECHANIC.DB2",
			"DBFILESCLIENT\\GARRMECHANICSETXMECHANIC.DB2",
			"DBFILESCLIENT\\GARRMECHANICTYPE.DB2",
			"DBFILESCLIENT\\GARRMISSION.DB2",
			"DBFILESCLIENT\\GARRMISSIONREWARD.DB2",
			"DBFILESCLIENT\\GARRMISSIONTEXTURE.DB2",
			"DBFILESCLIENT\\GARRMISSIONTYPE.DB2",
			"DBFILESCLIENT\\GARRMISSIONXENCOUNTER.DB2",
			"DBFILESCLIENT\\GARRMISSIONXFOLLOWER.DB2",
			"DBFILESCLIENT\\GARRMSSNBONUSABILITY.DB2",
			"DBFILESCLIENT\\GARRPLOT.DB2",
			"DBFILESCLIENT\\GARRPLOTBUILDING.DB2",
			"DBFILESCLIENT\\GARRPLOTINSTANCE.DB2",
			"DBFILESCLIENT\\GARRPLOTUICATEGORY.DB2",
			"DBFILESCLIENT\\GARRSITELEVEL.DB2",
			"DBFILESCLIENT\\GARRSITELEVELPLOTINST.DB2",
			"DBFILESCLIENT\\GARRSPECIALIZATION.DB2",
			"DBFILESCLIENT\\GARRSTRING.DB2",
			"DBFILESCLIENT\\GARRTALENT.DB2",
			"DBFILESCLIENT\\GARRTALENTTREE.DB2",
			"DBFILESCLIENT\\GARRTYPE.DB2",
			"DBFILESCLIENT\\GARRUIANIMCLASSINFO.DB2",
			"DBFILESCLIENT\\GARRUIANIMCLASSINFO.DBC",
			"DBFILESCLIENT\\GARRUIANIMRACEINFO.DB2",
			"DBFILESCLIENT\\GARRUIANIMRACEINFO.DBC",
			"DBFILESCLIENT\\GEMPROPERTIES.DB2",
			"DBFILESCLIENT\\GEMPROPERTIES.DBC",
			"DBFILESCLIENT\\GEMPROPERTIES_INTERNAL.DB2",
			"DBFILESCLIENT\\GEMPROPERTIES_INTERNAL.DBC",
			"DBFILESCLIENT\\GLOBALSTRINGS.DB2",
			"DBFILESCLIENT\\GLUESCREENEMOTE.DB2",
			"DBFILESCLIENT\\GLUESCREENEMOTE.DBC",
			"DBFILESCLIENT\\GLYPHBINDABLESPELL.DB2",
			"DBFILESCLIENT\\GLYPHEXCLUSIVECATEGORY.DB2",
			"DBFILESCLIENT\\GLYPHPROPERTIES.DB2",
			"DBFILESCLIENT\\GLYPHPROPERTIES.DBC",
			"DBFILESCLIENT\\GLYPHREQUIREDSPEC.DB2",
			"DBFILESCLIENT\\GLYPHSLOT.DB2",
			"DBFILESCLIENT\\GLYPHSLOT.DBC",
			"DBFILESCLIENT\\GMSURVEYANSWERS.DB2",
			"DBFILESCLIENT\\GMSURVEYANSWERS.DBC",
			"DBFILESCLIENT\\GMSURVEYCURRENTSURVEY.DB2",
			"DBFILESCLIENT\\GMSURVEYCURRENTSURVEY.DBC",
			"DBFILESCLIENT\\GMSURVEYQUESTIONS.DB2",
			"DBFILESCLIENT\\GMSURVEYQUESTIONS.DBC",
			"DBFILESCLIENT\\GMSURVEYSURVEYS.DB2",
			"DBFILESCLIENT\\GMSURVEYSURVEYS.DBC",
			"DBFILESCLIENT\\GMTICKETCATEGORY.DBC",
			"DBFILESCLIENT\\GROUNDEFFECTDOODAD.DB2",
			"DBFILESCLIENT\\GROUNDEFFECTDOODAD.DBC",
			"DBFILESCLIENT\\GROUNDEFFECTTEXTURE.DB2",
			"DBFILESCLIENT\\GROUNDEFFECTTEXTURE.DBC",
			"DBFILESCLIENT\\GROUPFINDERACTIVITY.DB2",
			"DBFILESCLIENT\\GROUPFINDERACTIVITYGRP.DB2",
			"DBFILESCLIENT\\GROUPFINDERCATEGORY.DB2",
			"DBFILESCLIENT\\GTARMORMITIGATIONBYLVL.DBC",
			"DBFILESCLIENT\\GTARTIFACTLEVELXP.DBC",
			"DBFILESCLIENT\\GTBARBERSHOPCOSTBASE.DBC",
			"DBFILESCLIENT\\GTBATTLEPETTYPEDAMAGEMOD.DBC",
			"DBFILESCLIENT\\GTBATTLEPETXP.DBC",
			"DBFILESCLIENT\\GTCHALLENGEMODEDAMAGE.DBC",
			"DBFILESCLIENT\\GTCHALLENGEMODEHEALTH.DBC",
			"DBFILESCLIENT\\GTCHANCETOMELEECRIT.DBC",
			"DBFILESCLIENT\\GTCHANCETOMELEECRITBASE.DBC",
			"DBFILESCLIENT\\GTCHANCETOSPELLCRIT.DBC",
			"DBFILESCLIENT\\GTCHANCETOSPELLCRITBASE.DBC",
			"DBFILESCLIENT\\GTCOMBATRATINGS.DBC",
			"DBFILESCLIENT\\GTCOMBATRATINGSMULTBYILVL.DBC",
			"DBFILESCLIENT\\GTHONORLEVEL.DBC",
			"DBFILESCLIENT\\GTITEMSOCKETCOSTPERLEVEL.DBC",
			"DBFILESCLIENT\\GTNPCDAMAGEBYCLASS.DBC",
			"DBFILESCLIENT\\GTNPCDAMAGEBYCLASSEXP1.DBC",
			"DBFILESCLIENT\\GTNPCDAMAGEBYCLASSEXP2.DBC",
			"DBFILESCLIENT\\GTNPCDAMAGEBYCLASSEXP3.DBC",
			"DBFILESCLIENT\\GTNPCDAMAGEBYCLASSEXP4.DBC",
			"DBFILESCLIENT\\GTNPCDAMAGEBYCLASSEXP5.DBC",
			"DBFILESCLIENT\\GTNPCDAMAGEBYCLASSEXP6.DBC",
			"DBFILESCLIENT\\GTNPCMANACOSTSCALER.DBC",
			"DBFILESCLIENT\\GTNPCTOTALHP.DBC",
			"DBFILESCLIENT\\GTNPCTOTALHPEXP1.DBC",
			"DBFILESCLIENT\\GTNPCTOTALHPEXP2.DBC",
			"DBFILESCLIENT\\GTNPCTOTALHPEXP3.DBC",
			"DBFILESCLIENT\\GTNPCTOTALHPEXP4.DBC",
			"DBFILESCLIENT\\GTNPCTOTALHPEXP5.DBC",
			"DBFILESCLIENT\\GTNPCTOTALHPEXP6.DBC",
			"DBFILESCLIENT\\GTOCTBASEHPBYCLASS.DBC",
			"DBFILESCLIENT\\GTOCTBASEMPBYCLASS.DBC",
			"DBFILESCLIENT\\GTOCTCLASSCOMBATRATINGSCALAR.DBC",
			"DBFILESCLIENT\\GTOCTHPPERSTAMINA.DBC",
			"DBFILESCLIENT\\GTOCTLEVELEXPERIENCE.DBC",
			"DBFILESCLIENT\\GTOCTREGENHP.DBC",
			"DBFILESCLIENT\\GTOCTREGENMP.DBC",
			"DBFILESCLIENT\\GTREGENHPPERSPT.DBC",
			"DBFILESCLIENT\\GTREGENMPPERSPT.DBC",
			"DBFILESCLIENT\\GTRESILIENCEDR.DBC",
			"DBFILESCLIENT\\GTSANDBOXSCALING.DBC",
			"DBFILESCLIENT\\GTSPELLSCALING.DBC",
			"DBFILESCLIENT\\GUILDCOLORBACKGROUND.DB2",
			"DBFILESCLIENT\\GUILDCOLORBACKGROUND.DBC",
			"DBFILESCLIENT\\GUILDCOLORBORDER.DB2",
			"DBFILESCLIENT\\GUILDCOLORBORDER.DBC",
			"DBFILESCLIENT\\GUILDCOLOREMBLEM.DB2",
			"DBFILESCLIENT\\GUILDCOLOREMBLEM.DBC",
			"DBFILESCLIENT\\GUILDPERKSPELLS.DB2",
			"DBFILESCLIENT\\GUILDPERKSPELLS.DBC",
			"DBFILESCLIENT\\HEIRLOOM.DB2",
			"DBFILESCLIENT\\HELMETANIMSCALING.DB2",
			"DBFILESCLIENT\\HELMETANIMSCALING.DBC",
			"DBFILESCLIENT\\HELMETGEOSETVISDATA.DB2",
			"DBFILESCLIENT\\HELMETGEOSETVISDATA.DBC",
			"DBFILESCLIENT\\HIGHLIGHTCOLOR.DB2",
			"DBFILESCLIENT\\HOLIDAYDESCRIPTIONS.DB2",
			"DBFILESCLIENT\\HOLIDAYDESCRIPTIONS.DBC",
			"DBFILESCLIENT\\HOLIDAYNAMES.DB2",
			"DBFILESCLIENT\\HOLIDAYNAMES.DBC",
			"DBFILESCLIENT\\HOLIDAYS.DB2",
			"DBFILESCLIENT\\HOLIDAYS.DBC",
			"DBFILESCLIENT\\HOTFIX.DB2",
			"DBFILESCLIENT\\IMPORTPRICEARMOR.DB2",
			"DBFILESCLIENT\\IMPORTPRICEARMOR.DBC",
			"DBFILESCLIENT\\IMPORTPRICEQUALITY.DB2",
			"DBFILESCLIENT\\IMPORTPRICEQUALITY.DBC",
			"DBFILESCLIENT\\IMPORTPRICESHIELD.DB2",
			"DBFILESCLIENT\\IMPORTPRICESHIELD.DBC",
			"DBFILESCLIENT\\IMPORTPRICEWEAPON.DB2",
			"DBFILESCLIENT\\IMPORTPRICEWEAPON.DBC",
			"DBFILESCLIENT\\INVASIONCLIENTDATA.DB2",
			"DBFILESCLIENT\\ITEM.DB2",
			"DBFILESCLIENT\\ITEM.DBC",
			"DBFILESCLIENT\\ITEM_INTERNAL.DB2",
			"DBFILESCLIENT\\ITEMAPPEARANCE.DB2",
			"DBFILESCLIENT\\ITEMAPPEARANCEXUICAMERA.DB2",
			"DBFILESCLIENT\\ITEMARMORQUALITY.DB2",
			"DBFILESCLIENT\\ITEMARMORQUALITY.DBC",
			"DBFILESCLIENT\\ITEMARMORSHIELD.DB2",
			"DBFILESCLIENT\\ITEMARMORSHIELD.DBC",
			"DBFILESCLIENT\\ITEMARMORTOTAL.DB2",
			"DBFILESCLIENT\\ITEMARMORTOTAL.DBC",
			"DBFILESCLIENT\\ITEMBAGFAMILY.DB2",
			"DBFILESCLIENT\\ITEMBAGFAMILY.DBC",
			"DBFILESCLIENT\\ITEMBONUS.DB2",
			"DBFILESCLIENT\\ITEMBONUSLISTLEVELDELTA.DB2",
			"DBFILESCLIENT\\ITEMBONUSTREENODE.DB2",
			"DBFILESCLIENT\\ITEMCHILDEQUIPMENT.DB2",
			"DBFILESCLIENT\\ITEMCHILDEQUIPMENT_INTERNAL.DB2",
			"DBFILESCLIENT\\ITEMCLASS.DB2",
			"DBFILESCLIENT\\ITEMCLASS.DBC",
			"DBFILESCLIENT\\ITEMCONDEXTCOSTS.DBC",
			"DBFILESCLIENT\\ITEMCONTEXTDISPLAY.DB2",
			"DBFILESCLIENT\\ITEMCONTEXTPICKERENTRY.DB2",
			"DBFILESCLIENT\\ITEMCURRENCYCOST.DB2",
			"DBFILESCLIENT\\ITEMCURRENCYCOST_INTERNAL.DB2",
			"DBFILESCLIENT\\ITEMDAMAGEAMMO.DB2",
			"DBFILESCLIENT\\ITEMDAMAGEAMMO.DBC",
			"DBFILESCLIENT\\ITEMDAMAGEONEHAND.DB2",
			"DBFILESCLIENT\\ITEMDAMAGEONEHAND.DBC",
			"DBFILESCLIENT\\ITEMDAMAGEONEHANDCASTER.DB2",
			"DBFILESCLIENT\\ITEMDAMAGEONEHANDCASTER.DBC",
			"DBFILESCLIENT\\ITEMDAMAGERANGED.DBC",
			"DBFILESCLIENT\\ITEMDAMAGETHROWN.DBC",
			"DBFILESCLIENT\\ITEMDAMAGETWOHAND.DB2",
			"DBFILESCLIENT\\ITEMDAMAGETWOHAND.DBC",
			"DBFILESCLIENT\\ITEMDAMAGETWOHANDCASTER.DB2",
			"DBFILESCLIENT\\ITEMDAMAGETWOHANDCASTER.DBC",
			"DBFILESCLIENT\\ITEMDAMAGEWAND.DBC",
			"DBFILESCLIENT\\ITEMDISENCHANTLOOT.DB2",
			"DBFILESCLIENT\\ITEMDISENCHANTLOOT.DBC",
			"DBFILESCLIENT\\ITEMDISPLAYINFO.DB2",
			"DBFILESCLIENT\\ITEMDISPLAYINFO.DBC",
			"DBFILESCLIENT\\ITEMDISPLAYINFOMATERIALRES.DB2",
			"DBFILESCLIENT\\ITEMDISPLAYXUICAMERA.DB2",
			"DBFILESCLIENT\\ITEMEFFECT.DB2",
			"DBFILESCLIENT\\ITEMEFFECT_INTERNAL.DB2",
			"DBFILESCLIENT\\ITEMEXTENDEDCOST.DB2",
			"DBFILESCLIENT\\ITEMEXTENDEDCOST.DBC",
			"DBFILESCLIENT\\ITEMGROUPSOUNDS.DB2",
			"DBFILESCLIENT\\ITEMGROUPSOUNDS.DBC",
			"DBFILESCLIENT\\ITEMLEVELSELECTOR.DB2",
			"DBFILESCLIENT\\ITEMLEVELSELECTORQUALITY.DB2",
			"DBFILESCLIENT\\ITEMLEVELSELECTORQUALITYSET.DB2",
			"DBFILESCLIENT\\ITEMLIMITCATEGORY.DB2",
			"DBFILESCLIENT\\ITEMLIMITCATEGORY.DBC",
			"DBFILESCLIENT\\ITEMLIMITCATEGORYCONDITION.DB2",
			"DBFILESCLIENT\\ITEMMODIFIEDAPPEARANCE.DB2",
			"DBFILESCLIENT\\ITEMMODIFIEDAPPEARANCE_INTERNAL.DB2",
			"DBFILESCLIENT\\ITEMMODIFIEDAPPEARANCEEXTRA.DB2",
			"DBFILESCLIENT\\ITEMNAMEDESCRIPTION.DB2",
			"DBFILESCLIENT\\ITEMNAMEDESCRIPTION.DBC",
			"DBFILESCLIENT\\ITEMPETFOOD.DB2",
			"DBFILESCLIENT\\ITEMPETFOOD.DBC",
			"DBFILESCLIENT\\ITEMPRICEBASE.DB2",
			"DBFILESCLIENT\\ITEMPRICEBASE.DBC",
			"DBFILESCLIENT\\ITEMPURCHASEGROUP.DBC",
			"DBFILESCLIENT\\ITEMRANDOMPROPERTIES.DB2",
			"DBFILESCLIENT\\ITEMRANDOMPROPERTIES.DBC",
			"DBFILESCLIENT\\ITEMRANDOMSUFFIX.DB2",
			"DBFILESCLIENT\\ITEMRANDOMSUFFIX.DBC",
			"DBFILESCLIENT\\ITEMRANGEDDISPLAYINFO.DB2",
			"DBFILESCLIENT\\ITEMREFORGE.DBC",
			"DBFILESCLIENT\\ITEMSEARCHNAME.DB2",
			"DBFILESCLIENT\\ITEMSET.DB2",
			"DBFILESCLIENT\\ITEMSET.DBC",
			"DBFILESCLIENT\\ITEMSETSPELL.DB2",
			"DBFILESCLIENT\\ITEMSETSPELL.DBC",
			"DBFILESCLIENT\\ITEMSPARSE.DB2",
			"DBFILESCLIENT\\ITEM-SPARSE.DB2",
			"DBFILESCLIENT\\ITEMSPEC.DB2",
			"DBFILESCLIENT\\ITEMSPEC.DBC",
			"DBFILESCLIENT\\ITEMSPECOVERRIDE.DB2",
			"DBFILESCLIENT\\ITEMSPECOVERRIDE.DBC",
			"DBFILESCLIENT\\ITEMSPECOVERRIDE_INTERNAL.DB2",
			"DBFILESCLIENT\\ITEMSUBCLASS.DB2",
			"DBFILESCLIENT\\ITEMSUBCLASS.DBC",
			"DBFILESCLIENT\\ITEMSUBCLASSMASK.DB2",
			"DBFILESCLIENT\\ITEMSUBCLASSMASK.DBC",
			"DBFILESCLIENT\\ITEMTOBATTLEPET.DB2",
			"DBFILESCLIENT\\ITEMTOBATTLEPETSPECIES.DB2",
			"DBFILESCLIENT\\ITEMTOMOUNTSPELL.DB2",
			"DBFILESCLIENT\\ITEMUPGRADE.DB2",
			"DBFILESCLIENT\\ITEMUPGRADE.DBC",
			"DBFILESCLIENT\\ITEMUPGRADEPATH.DBC",
			"DBFILESCLIENT\\ITEMVISUALEFFECTS.DB2",
			"DBFILESCLIENT\\ITEMVISUALEFFECTS.DBC",
			"DBFILESCLIENT\\ITEMVISUALS.DB2",
			"DBFILESCLIENT\\ITEMVISUALS.DBC",
			"DBFILESCLIENT\\ITEMXBONUSTREE.DB2",
			"DBFILESCLIENT\\ITEMXBONUSTREE_INTERNAL.DB2",
			"DBFILESCLIENT\\JOURNALENCOUNTER.DB2",
			"DBFILESCLIENT\\JOURNALENCOUNTER.DBC",
			"DBFILESCLIENT\\JOURNALENCOUNTERCREATURE.DB2",
			"DBFILESCLIENT\\JOURNALENCOUNTERCREATURE.DBC",
			"DBFILESCLIENT\\JOURNALENCOUNTERITEM.DB2",
			"DBFILESCLIENT\\JOURNALENCOUNTERITEM.DBC",
			"DBFILESCLIENT\\JOURNALENCOUNTERSECTION.DB2",
			"DBFILESCLIENT\\JOURNALENCOUNTERSECTION.DBC",
			"DBFILESCLIENT\\JOURNALENCOUNTERXDIFFICULTY.DB2",
			"DBFILESCLIENT\\JOURNALENCOUNTERXDIFFICULTY.DBC",
			"DBFILESCLIENT\\JOURNALENCOUNTERXMAPLOC.DB2",
			"DBFILESCLIENT\\JOURNALINSTANCE.DB2",
			"DBFILESCLIENT\\JOURNALINSTANCE.DBC",
			"DBFILESCLIENT\\JOURNALITEMXDIFFICULTY.DB2",
			"DBFILESCLIENT\\JOURNALITEMXDIFFICULTY.DBC",
			"DBFILESCLIENT\\JOURNALSECTIONXDIFFICULTY.DB2",
			"DBFILESCLIENT\\JOURNALSECTIONXDIFFICULTY.DBC",
			"DBFILESCLIENT\\JOURNALTIER.DB2",
			"DBFILESCLIENT\\JOURNALTIER.DBC",
			"DBFILESCLIENT\\JOURNALTIERXINSTANCE.DB2",
			"DBFILESCLIENT\\JOURNALTIERXINSTANCE.DBC",
			"DBFILESCLIENT\\KEYCHAIN.DB2",
			"DBFILESCLIENT\\KEYSTONEAFFIX.DB2",
			"DBFILESCLIENT\\LANGUAGES.DB2",
			"DBFILESCLIENT\\LANGUAGES.DBC",
			"DBFILESCLIENT\\LANGUAGEWORDS.DB2",
			"DBFILESCLIENT\\LANGUAGEWORDS.DBC",
			"DBFILESCLIENT\\LFGDUNGEONEXPANSION.DB2",
			"DBFILESCLIENT\\LFGDUNGEONEXPANSION.DBC",
			"DBFILESCLIENT\\LFGDUNGEONGROUP.DB2",
			"DBFILESCLIENT\\LFGDUNGEONGROUP.DBC",
			"DBFILESCLIENT\\LFGDUNGEONS.DB2",
			"DBFILESCLIENT\\LFGDUNGEONS.DBC",
			"DBFILESCLIENT\\LFGDUNGEONSGROUPINGMAP.DB2",
			"DBFILESCLIENT\\LFGDUNGEONSGROUPINGMAP.DBC",
			"DBFILESCLIENT\\LFGROLEREQUIREMENT.DB2",
			"DBFILESCLIENT\\LIGHT.DB2",
			"DBFILESCLIENT\\LIGHT.DBC",
			"DBFILESCLIENT\\LIGHTDATA.DB2",
			"DBFILESCLIENT\\LIGHTDATA.DBC",
			"DBFILESCLIENT\\LIGHTFLOATBAND.DBC",
			"DBFILESCLIENT\\LIGHTINTBAND.DBC",
			"DBFILESCLIENT\\LIGHTPARAMS.DB2",
			"DBFILESCLIENT\\LIGHTPARAMS.DBC",
			"DBFILESCLIENT\\LIGHTSKYBOX.DB2",
			"DBFILESCLIENT\\LIGHTSKYBOX.DBC",
			"DBFILESCLIENT\\LIQUIDMATERIAL.DB2",
			"DBFILESCLIENT\\LIQUIDMATERIAL.DBC",
			"DBFILESCLIENT\\LIQUIDOBJECT.DB2",
			"DBFILESCLIENT\\LIQUIDOBJECT.DBC",
			"DBFILESCLIENT\\LIQUIDTYPE.DB2",
			"DBFILESCLIENT\\LIQUIDTYPE.DBC",
			"DBFILESCLIENT\\LOADINGSCREENS.DB2",
			"DBFILESCLIENT\\LOADINGSCREENS.DBC",
			"DBFILESCLIENT\\LOADINGSCREENTAXISPLINES.DB2",
			"DBFILESCLIENT\\LOADINGSCREENTAXISPLINES.DBC",
			"DBFILESCLIENT\\LOCALE.DB2",
			"DBFILESCLIENT\\LOCATION.DB2",
			"DBFILESCLIENT\\LOCK.DB2",
			"DBFILESCLIENT\\LOCK.DBC",
			"DBFILESCLIENT\\LOCKTYPE.DB2",
			"DBFILESCLIENT\\LOCKTYPE.DBC",
			"DBFILESCLIENT\\LOOKATCONTROLLER.DB2",
			"DBFILESCLIENT\\MAILTEMPLATE.DB2",
			"DBFILESCLIENT\\MAILTEMPLATE.DBC",
			"DBFILESCLIENT\\MANAGEDWORLDSTATE.DB2",
			"DBFILESCLIENT\\MANAGEDWORLDSTATEBUFF.DB2",
			"DBFILESCLIENT\\MANAGEDWORLDSTATEINPUT.DB2",
			"DBFILESCLIENT\\MANIFESTINTERFACEACTIONICON.DB2",
			"DBFILESCLIENT\\MANIFESTINTERFACEACTIONICON.DBC",
			"DBFILESCLIENT\\MANIFESTINTERFACEDATA.DB2",
			"DBFILESCLIENT\\MANIFESTINTERFACEDATA.DBC",
			"DBFILESCLIENT\\MANIFESTINTERFACEITEMICON.DB2",
			"DBFILESCLIENT\\MANIFESTINTERFACEITEMICON.DBC",
			"DBFILESCLIENT\\MANIFESTINTERFACETOCDATA.DB2",
			"DBFILESCLIENT\\MANIFESTINTERFACETOCDATA.DBC",
			"DBFILESCLIENT\\MANIFESTMP3.DB2",
			"DBFILESCLIENT\\MANIFESTMP3.DBC",
			"DBFILESCLIENT\\MAP.DB2",
			"DBFILESCLIENT\\MAP.DBC",
			"DBFILESCLIENT\\MAPCELESTIALBODY.DB2",
			"DBFILESCLIENT\\MAPCHALLENGEMODE.DB2",
			"DBFILESCLIENT\\MAPDIFFICULTY.DB2",
			"DBFILESCLIENT\\MAPDIFFICULTY.DBC",
			"DBFILESCLIENT\\MAPDIFFICULTYXCONDITION.DB2",
			"DBFILESCLIENT\\MAPLOADINGSCREEN.DB2",
			"DBFILESCLIENT\\MARKETINGPROMOTIONSXLOCALE.DB2",
			"DBFILESCLIENT\\MATERIAL.DB2",
			"DBFILESCLIENT\\MATERIAL.DBC",
			"DBFILESCLIENT\\MINORTALENT.DB2",
			"DBFILESCLIENT\\MINORTALENT.DBC",
			"DBFILESCLIENT\\MISSILETARGETING.DB2",
			"DBFILESCLIENT\\MODELANIMCLOAKDAMPENING.DB2",
			"DBFILESCLIENT\\MODELFILEDATA.DB2",
			"DBFILESCLIENT\\MODELFILEDATA.DBC",
			"DBFILESCLIENT\\MODELMANIFEST.DB2",
			"DBFILESCLIENT\\MODELNAMETOMANIFEST.DB2",
			"DBFILESCLIENT\\MODELRIBBONQUALITY.DB2",
			"DBFILESCLIENT\\MODIFIERTREE.DB2",
			"DBFILESCLIENT\\MODIFIERTREE.DBC",
			"DBFILESCLIENT\\MOUNT.DB2",
			"DBFILESCLIENT\\MOUNTCAPABILITY.DB2",
			"DBFILESCLIENT\\MOUNTCAPABILITY.DBC",
			"DBFILESCLIENT\\MOUNTTYPE.DB2",
			"DBFILESCLIENT\\MOUNTTYPE.DBC",
			"DBFILESCLIENT\\MOUNTTYPEXCAPABILITY.DB2",
			"DBFILESCLIENT\\MOUNTXDISPLAY.DB2",
			"DBFILESCLIENT\\MOVIE.DB2",
			"DBFILESCLIENT\\MOVIE.DBC",
			"DBFILESCLIENT\\MOVIEFILEDATA.DB2",
			"DBFILESCLIENT\\MOVIEFILEDATA.DBC",
			"DBFILESCLIENT\\MOVIEOVERLAYS.DBC",
			"DBFILESCLIENT\\MOVIEVARIATION.DB2",
			"DBFILESCLIENT\\MOVIEVARIATION.DBC",
			"DBFILESCLIENT\\NAMEGEN.DB2",
			"DBFILESCLIENT\\NAMEGEN.DBC",
			"DBFILESCLIENT\\NAMESPROFANITY.DB2",
			"DBFILESCLIENT\\NAMESPROFANITY.DBC",
			"DBFILESCLIENT\\NAMESRESERVED.DB2",
			"DBFILESCLIENT\\NAMESRESERVED.DBC",
			"DBFILESCLIENT\\NAMESRESERVEDLOCALE.DB2",
			"DBFILESCLIENT\\NAMESRESERVEDLOCALE.DBC",
			"DBFILESCLIENT\\NPCMODELITEMSLOTDISPLAYINFO.DB2",
			"DBFILESCLIENT\\NPCSOUNDS.DB2",
			"DBFILESCLIENT\\NPCSOUNDS.DBC",
			"DBFILESCLIENT\\OBJECTEFFECT.DB2",
			"DBFILESCLIENT\\OBJECTEFFECT.DBC",
			"DBFILESCLIENT\\OBJECTEFFECTGROUP.DB2",
			"DBFILESCLIENT\\OBJECTEFFECTGROUP.DBC",
			"DBFILESCLIENT\\OBJECTEFFECTMODIFIER.DB2",
			"DBFILESCLIENT\\OBJECTEFFECTMODIFIER.DBC",
			"DBFILESCLIENT\\OBJECTEFFECTPACKAGE.DB2",
			"DBFILESCLIENT\\OBJECTEFFECTPACKAGE.DBC",
			"DBFILESCLIENT\\OBJECTEFFECTPACKAGEELEM.DB2",
			"DBFILESCLIENT\\OBJECTEFFECTPACKAGEELEM.DBC",
			"DBFILESCLIENT\\OUTLINEEFFECT.DB2",
			"DBFILESCLIENT\\OVERRIDESPELLDATA.DB2",
			"DBFILESCLIENT\\OVERRIDESPELLDATA.DBC",
			"DBFILESCLIENT\\PACKAGE.DBC",
			"DBFILESCLIENT\\PAGETEXTMATERIAL.DB2",
			"DBFILESCLIENT\\PAGETEXTMATERIAL.DBC",
			"DBFILESCLIENT\\PAPERDOLLITEMFRAME.DB2",
			"DBFILESCLIENT\\PAPERDOLLITEMFRAME.DBC",
			"DBFILESCLIENT\\PARAGONREPUTATION.DB2",
			"DBFILESCLIENT\\PARTICLECOLOR.DB2",
			"DBFILESCLIENT\\PARTICLECOLOR.DBC",
			"DBFILESCLIENT\\PATH.DB2",
			"DBFILESCLIENT\\PATHNODE.DB2",
			"DBFILESCLIENT\\PATHNODEPROPERTY.DB2",
			"DBFILESCLIENT\\PATHPROPERTY.DB2",
			"DBFILESCLIENT\\PETITIONTYPE.DBC",
			"DBFILESCLIENT\\PETLOYALTY.DBC",
			"DBFILESCLIENT\\PETPERSONALITY.DBC",
			"DBFILESCLIENT\\PHASE.DB2",
			"DBFILESCLIENT\\PHASE.DBC",
			"DBFILESCLIENT\\PHASE_INTERNAL.DB2",
			"DBFILESCLIENT\\PHASE_INTERNAL.DBC",
			"DBFILESCLIENT\\PHASESHIFTZONESOUNDS.DB2",
			"DBFILESCLIENT\\PHASESHIFTZONESOUNDS.DBC",
			"DBFILESCLIENT\\PHASEXPHASEGROUP.DB2",
			"DBFILESCLIENT\\PHASEXPHASEGROUP.DBC",
			"DBFILESCLIENT\\PLAYERCONDITION.DB2",
			"DBFILESCLIENT\\PLAYERCONDITION.DBC",
			"DBFILESCLIENT\\POSITIONER.DB2",
			"DBFILESCLIENT\\POSITIONERSTATE.DB2",
			"DBFILESCLIENT\\POSITIONERSTATEENTRY.DB2",
			"DBFILESCLIENT\\POWERDISPLAY.DB2",
			"DBFILESCLIENT\\POWERDISPLAY.DBC",
			"DBFILESCLIENT\\POWERTYPE.DB2",
			"DBFILESCLIENT\\PRESTIGELEVELINFO.DB2",
			"DBFILESCLIENT\\PVPBRACKETTYPES.DB2",
			"DBFILESCLIENT\\PVPDIFFICULTY.DB2",
			"DBFILESCLIENT\\PVPDIFFICULTY.DBC",
			"DBFILESCLIENT\\PVPITEM.DB2",
			"DBFILESCLIENT\\PVPREWARD.DB2",
			"DBFILESCLIENT\\PVPSCALINGEFFECT.DB2",
			"DBFILESCLIENT\\PVPSCALINGEFFECTTYPE.DB2",
			"DBFILESCLIENT\\PVPSCALINGEFFECTYYPE.DB2",
			"DBFILESCLIENT\\PVPTALENT.DB2",
			"DBFILESCLIENT\\PVPTALENTUNLOCK.DB2",
			"DBFILESCLIENT\\QUESTFACTIONREWARD.DB2",
			"DBFILESCLIENT\\QUESTFACTIONREWARD.DBC",
			"DBFILESCLIENT\\QUESTFEEDBACKEFFECT.DB2",
			"DBFILESCLIENT\\QUESTFEEDBACKEFFECT.DBC",
			"DBFILESCLIENT\\QUESTINFO.DB2",
			"DBFILESCLIENT\\QUESTINFO.DBC",
			"DBFILESCLIENT\\QUESTLINE.DB2",
			"DBFILESCLIENT\\QUESTLINEXQUEST.DB2",
			"DBFILESCLIENT\\QUESTMONEYREWARD.DB2",
			"DBFILESCLIENT\\QUESTMONEYREWARD.DBC",
			"DBFILESCLIENT\\QUESTOBJECTIVE.DB2",
			"DBFILESCLIENT\\QUESTOBJECTIVECLITASK.DB2",
			"DBFILESCLIENT\\QUESTPACKAGEITEM.DB2",
			"DBFILESCLIENT\\QUESTPOIBLOB.DB2",
			"DBFILESCLIENT\\QUESTPOIBLOB.DBC",
			"DBFILESCLIENT\\QUESTPOIPOINT.DB2",
			"DBFILESCLIENT\\QUESTPOIPOINT.DBC",
			"DBFILESCLIENT\\QUESTPOIPOINTCLITASK.DB2",
			"DBFILESCLIENT\\QUESTSORT.DB2",
			"DBFILESCLIENT\\QUESTSORT.DBC",
			"DBFILESCLIENT\\QUESTV2.DB2",
			"DBFILESCLIENT\\QUESTV2.DBC",
			"DBFILESCLIENT\\QUESTV2_INTERNAL.DB2",
			"DBFILESCLIENT\\QUESTV2CLITASK.DB2",
			"DBFILESCLIENT\\QUESTXGROUPACTIVITY.DB2",
			"DBFILESCLIENT\\QUESTXP.DB2",
			"DBFILESCLIENT\\QUESTXP.DBC",
			"DBFILESCLIENT\\RACIALMOUNTS.DB2",
			"DBFILESCLIENT\\RACIALMOUNTS.DBC",
			"DBFILESCLIENT\\RANDPROPPOINTS.DB2",
			"DBFILESCLIENT\\RANDPROPPOINTS.DBC",
			"DBFILESCLIENT\\RELICSLOTTIERREQUIREMENT.DB2",
			"DBFILESCLIENT\\RELICTALENT.DB2",
			"DBFILESCLIENT\\RESEARCHBRANCH.DB2",
			"DBFILESCLIENT\\RESEARCHBRANCH.DBC",
			"DBFILESCLIENT\\RESEARCHFIELD.DB2",
			"DBFILESCLIENT\\RESEARCHFIELD.DBC",
			"DBFILESCLIENT\\RESEARCHPROJECT.DB2",
			"DBFILESCLIENT\\RESEARCHPROJECT.DBC",
			"DBFILESCLIENT\\RESEARCHSITE.DB2",
			"DBFILESCLIENT\\RESEARCHSITE.DBC",
			"DBFILESCLIENT\\RESISTANCES.DB2",
			"DBFILESCLIENT\\RESISTANCES.DBC",
			"DBFILESCLIENT\\REWARDPACK.DB2",
			"DBFILESCLIENT\\REWARDPACKXCURRENCYTYPE.DB2",
			"DBFILESCLIENT\\REWARDPACKXITEM.DB2",
			"DBFILESCLIENT\\RIBBONQUALITY.DB2",
			"DBFILESCLIENT\\RULESETITEMUPGRADE.DB2",
			"DBFILESCLIENT\\RULESETRAIDLOOTUPGRADE.DB2",
			"DBFILESCLIENT\\RULESETRAIDOVERRIDE.DB2",
			"DBFILESCLIENT\\RULESETRAIDOVERRIDE.DBC",
			"DBFILESCLIENT\\SANDBOXSCALING.DB2",
			"DBFILESCLIENT\\SCALINGSTATDISTRIBUTION.DB2",
			"DBFILESCLIENT\\SCALINGSTATDISTRIBUTION.DBC",
			"DBFILESCLIENT\\SCALINGSTATDISTRIBUTION_INTERNAL.DB2",
			"DBFILESCLIENT\\SCALINGSTATVALUES.DBC",
			"DBFILESCLIENT\\SCENARIO.DB2",
			"DBFILESCLIENT\\SCENARIO.DBC",
			"DBFILESCLIENT\\SCENARIO_INTERNAL.DB2",
			"DBFILESCLIENT\\SCENARIOEVENTENTRY.DB2",
			"DBFILESCLIENT\\SCENARIOEVENTENTRY.DBC",
			"DBFILESCLIENT\\SCENARIOSTEP.DB2",
			"DBFILESCLIENT\\SCENARIOSTEP.DBC",
			"DBFILESCLIENT\\SCENESCRIPT.DB2",
			"DBFILESCLIENT\\SCENESCRIPT.DBC",
			"DBFILESCLIENT\\SCENESCRIPTGLOBALTEXT.DB2",
			"DBFILESCLIENT\\SCENESCRIPTPACKAGE.DB2",
			"DBFILESCLIENT\\SCENESCRIPTPACKAGE.DBC",
			"DBFILESCLIENT\\SCENESCRIPTPACKAGE_INTERNAL.DB2",
			"DBFILESCLIENT\\SCENESCRIPTPACKAGEMEMBER.DB2",
			"DBFILESCLIENT\\SCENESCRIPTPACKAGEMEMBER.DBC",
			"DBFILESCLIENT\\SCENESCRIPTTEXT.DB2",
			"DBFILESCLIENT\\SCHEDULEDINTERVAL.DB2",
			"DBFILESCLIENT\\SCHEDULEDUNIQUECATEGORY.DB2",
			"DBFILESCLIENT\\SCHEDULEDWORLDSTATE.DB2",
			"DBFILESCLIENT\\SCHEDULEDWORLDSTATEGROUP.DB2",
			"DBFILESCLIENT\\SCHEDULEDWORLDSTATEXUNIQCAT.DB2",
			"DBFILESCLIENT\\SCHEDULEDWORLDSTATEXUNIQUECAT.DB2",
			"DBFILESCLIENT\\SCREENEFFECT.DB2",
			"DBFILESCLIENT\\SCREENEFFECT.DBC",
			"DBFILESCLIENT\\SCREENLOCATION.DB2",
			"DBFILESCLIENT\\SCREENLOCATION.DBC",
			"DBFILESCLIENT\\SDREPLACEMENTMODEL.DB2",
			"DBFILESCLIENT\\SEAMLESSSITE.DB2",
			"DBFILESCLIENT\\SERVERMESSAGES.DB2",
			"DBFILESCLIENT\\SERVERMESSAGES.DBC",
			"DBFILESCLIENT\\SHADOWYEFFECT.DB2",
			"DBFILESCLIENT\\SHEATHESOUNDLOOKUPS.DBC",
			"DBFILESCLIENT\\SKILLCOSTSDATA.DBC",
			"DBFILESCLIENT\\SKILLLINE.DB2",
			"DBFILESCLIENT\\SKILLLINE.DBC",
			"DBFILESCLIENT\\SKILLLINEABILITY.DB2",
			"DBFILESCLIENT\\SKILLLINEABILITY.DBC",
			"DBFILESCLIENT\\SKILLLINEABILITYSORTEDSPELL.DBC",
			"DBFILESCLIENT\\SKILLLINECATEGORY.DBC",
			"DBFILESCLIENT\\SKILLRACECLASSINFO.DB2",
			"DBFILESCLIENT\\SKILLRACECLASSINFO.DBC",
			"DBFILESCLIENT\\SKILLTIERS.DBC",
			"DBFILESCLIENT\\SOUNDAMBIENCE.DB2",
			"DBFILESCLIENT\\SOUNDAMBIENCE.DBC",
			"DBFILESCLIENT\\SOUNDAMBIENCEFLAVOR.DB2",
			"DBFILESCLIENT\\SOUNDAMBIENCEFLAVOR.DBC",
			"DBFILESCLIENT\\SOUNDBUS.DB2",
			"DBFILESCLIENT\\SOUNDBUS.DBC",
			"DBFILESCLIENT\\SOUNDBUSNAME.DB2",
			"DBFILESCLIENT\\SOUNDBUSNAME.DBC",
			"DBFILESCLIENT\\SOUNDBUSOVERRIDE.DB2",
			"DBFILESCLIENT\\SOUNDCHARACTERMACROLINES.DBC",
			"DBFILESCLIENT\\SOUNDEMITTERPILLPOINTS.DB2",
			"DBFILESCLIENT\\SOUNDEMITTERPILLPOINTS.DBC",
			"DBFILESCLIENT\\SOUNDEMITTERS.DB2",
			"DBFILESCLIENT\\SOUNDEMITTERS.DBC",
			"DBFILESCLIENT\\SOUNDENTRIES.DB2",
			"DBFILESCLIENT\\SOUNDENTRIES.DBC",
			"DBFILESCLIENT\\SOUNDENTRIESADVANCED.DB2",
			"DBFILESCLIENT\\SOUNDENTRIESADVANCED.DBC",
			"DBFILESCLIENT\\SOUNDENTRIESFALLBACKS.DBC",
			"DBFILESCLIENT\\SOUNDENVELOPE.DB2",
			"DBFILESCLIENT\\SOUNDFILTER.DB2",
			"DBFILESCLIENT\\SOUNDFILTER.DBC",
			"DBFILESCLIENT\\SOUNDFILTERELEM.DB2",
			"DBFILESCLIENT\\SOUNDFILTERELEM.DBC",
			"DBFILESCLIENT\\SOUNDKIT.DB2",
			"DBFILESCLIENT\\SOUNDKIT_INTERNAL.DB2",
			"DBFILESCLIENT\\SOUNDKITADVANCED.DB2",
			"DBFILESCLIENT\\SOUNDKITCHILD.DB2",
			"DBFILESCLIENT\\SOUNDKITENTRY.DB2",
			"DBFILESCLIENT\\SOUNDKITFALLBACK.DB2",
			"DBFILESCLIENT\\SOUNDKITNAME.DB2",
			"DBFILESCLIENT\\SOUNDOVERRIDE.DB2",
			"DBFILESCLIENT\\SOUNDOVERRIDE.DBC",
			"DBFILESCLIENT\\SOUNDPROVIDERPREFERENCES.DB2",
			"DBFILESCLIENT\\SOUNDPROVIDERPREFERENCES.DBC",
			"DBFILESCLIENT\\SOUNDSAMPLEPREFERENCES.DBC",
			"DBFILESCLIENT\\SOUNDWATERTYPE.DBC",
			"DBFILESCLIENT\\SOURCEINFO.DB2",
			"DBFILESCLIENT\\SPAMMESSAGES.DB2",
			"DBFILESCLIENT\\SPAMMESSAGES.DBC",
			"DBFILESCLIENT\\SPECIALIZATIONSPELLS.DB2",
			"DBFILESCLIENT\\SPECIALIZATIONSPELLS.DBC",
			"DBFILESCLIENT\\SPELL.DB2",
			"DBFILESCLIENT\\SPELL.DBC",
			"DBFILESCLIENT\\SPELL_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLACTIONBARPREF.DB2",
			"DBFILESCLIENT\\SPELLACTIVATIONOVERLAY.DB2",
			"DBFILESCLIENT\\SPELLACTIVATIONOVERLAY.DBC",
			"DBFILESCLIENT\\SPELLACTIVATIONOVERLAY_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLACTIVATIONOVERLAY_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLAURANAMES.DBC",
			"DBFILESCLIENT\\SPELLAURAOPTIONS.DB2",
			"DBFILESCLIENT\\SPELLAURAOPTIONS.DBC",
			"DBFILESCLIENT\\SPELLAURAOPTIONS_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLAURAOPTIONS_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLAURARESTRICTIONS.DB2",
			"DBFILESCLIENT\\SPELLAURARESTRICTIONS.DBC",
			"DBFILESCLIENT\\SPELLAURARESTRICTIONS_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLAURARESTRICTIONS_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLAURARESTRICTIONSDIFFICULTY.DB2",
			"DBFILESCLIENT\\SPELLAURAVISIBILITY.DB2",
			"DBFILESCLIENT\\SPELLAURAVISIBILITY.DBC",
			"DBFILESCLIENT\\SPELLAURAVISIBILITY_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLAURAVISIBILITY_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLAURAVISXCHRSPEC.DB2",
			"DBFILESCLIENT\\SPELLAURAVISXCHRSPEC.DBC",
			"DBFILESCLIENT\\SPELLAURAVISXTALENTTAB.DBC",
			"DBFILESCLIENT\\SPELLCASTINGREQUIREMENTS.DB2",
			"DBFILESCLIENT\\SPELLCASTINGREQUIREMENTS.DBC",
			"DBFILESCLIENT\\SPELLCASTINGREQUIREMENTS_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLCASTINGREQUIREMENTS_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLCASTTIMES.DB2",
			"DBFILESCLIENT\\SPELLCASTTIMES.DBC",
			"DBFILESCLIENT\\SPELLCATEGORIES.DB2",
			"DBFILESCLIENT\\SPELLCATEGORIES.DBC",
			"DBFILESCLIENT\\SPELLCATEGORIES_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLCATEGORIES_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLCATEGORY.DB2",
			"DBFILESCLIENT\\SPELLCATEGORY.DBC",
			"DBFILESCLIENT\\SPELLCHAINEFFECTS.DB2",
			"DBFILESCLIENT\\SPELLCHAINEFFECTS.DBC",
			"DBFILESCLIENT\\SPELLCLASSOPTIONS.DB2",
			"DBFILESCLIENT\\SPELLCLASSOPTIONS.DBC",
			"DBFILESCLIENT\\SPELLCLASSOPTIONS_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLCLASSOPTIONS_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLCOOLDOWNS.DB2",
			"DBFILESCLIENT\\SPELLCOOLDOWNS.DBC",
			"DBFILESCLIENT\\SPELLCOOLDOWNS_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLCOOLDOWNS_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLDESCRIPTIONVARIABLES.DB2",
			"DBFILESCLIENT\\SPELLDESCRIPTIONVARIABLES.DBC",
			"DBFILESCLIENT\\SPELLDIFFICULTY.DBC",
			"DBFILESCLIENT\\SPELLDISPELTYPE.DB2",
			"DBFILESCLIENT\\SPELLDISPELTYPE.DBC",
			"DBFILESCLIENT\\SPELLDURATION.DB2",
			"DBFILESCLIENT\\SPELLDURATION.DBC",
			"DBFILESCLIENT\\SPELLEFFECT.DB2",
			"DBFILESCLIENT\\SPELLEFFECT.DBC",
			"DBFILESCLIENT\\SPELLEFFECT_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLEFFECT_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLEFFECTAUTODESCRIPTION.DB2",
			"DBFILESCLIENT\\SPELLEFFECTCAMERASHAKES.DB2",
			"DBFILESCLIENT\\SPELLEFFECTCAMERASHAKES.DBC",
			"DBFILESCLIENT\\SPELLEFFECTEMISSION.DB2",
			"DBFILESCLIENT\\SPELLEFFECTEXTRA.DB2",
			"DBFILESCLIENT\\SPELLEFFECTGROUPSIZE.DB2",
			"DBFILESCLIENT\\SPELLEFFECTGROUPSIZE_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLEFFECTNAMES.DBC",
			"DBFILESCLIENT\\SPELLEFFECTSCALING.DB2",
			"DBFILESCLIENT\\SPELLEFFECTSCALING.DBC",
			"DBFILESCLIENT\\SPELLEQUIPPEDITEMS.DB2",
			"DBFILESCLIENT\\SPELLEQUIPPEDITEMS.DBC",
			"DBFILESCLIENT\\SPELLEQUIPPEDITEMS_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLEQUIPPEDITEMS_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLFLYOUT.DB2",
			"DBFILESCLIENT\\SPELLFLYOUT.DBC",
			"DBFILESCLIENT\\SPELLFLYOUTITEM.DB2",
			"DBFILESCLIENT\\SPELLFLYOUTITEM.DBC",
			"DBFILESCLIENT\\SPELLFOCUSOBJECT.DB2",
			"DBFILESCLIENT\\SPELLFOCUSOBJECT.DBC",
			"DBFILESCLIENT\\SPELLICON.DB2",
			"DBFILESCLIENT\\SPELLICON.DBC",
			"DBFILESCLIENT\\SPELLINTERRUPTS.DB2",
			"DBFILESCLIENT\\SPELLINTERRUPTS.DBC",
			"DBFILESCLIENT\\SPELLINTERRUPTS_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLINTERRUPTS_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLITEMENCHANTMENT.DB2",
			"DBFILESCLIENT\\SPELLITEMENCHANTMENT.DBC",
			"DBFILESCLIENT\\SPELLITEMENCHANTMENTCONDITION.DB2",
			"DBFILESCLIENT\\SPELLITEMENCHANTMENTCONDITION.DBC",
			"DBFILESCLIENT\\SPELLKEYBOUNDOVERRIDE.DB2",
			"DBFILESCLIENT\\SPELLKEYBOUNDOVERRIDE.DBC",
			"DBFILESCLIENT\\SPELLLABEL.DB2",
			"DBFILESCLIENT\\SPELLLABEL_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLLEARNSPELL.DB2",
			"DBFILESCLIENT\\SPELLLEARNSPELL.DBC",
			"DBFILESCLIENT\\SPELLLEARNSPELL_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLLEARNSPELL_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLLEVELS.DB2",
			"DBFILESCLIENT\\SPELLLEVELS.DBC",
			"DBFILESCLIENT\\SPELLLEVELS_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLLEVELS_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLMECHANIC.DB2",
			"DBFILESCLIENT\\SPELLMECHANIC.DBC",
			"DBFILESCLIENT\\SPELLMISC.DB2",
			"DBFILESCLIENT\\SPELLMISC.DBC",
			"DBFILESCLIENT\\SPELLMISC_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLMISC_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLMISCDIFFICULTY.DB2",
			"DBFILESCLIENT\\SPELLMISSILE.DB2",
			"DBFILESCLIENT\\SPELLMISSILE.DBC",
			"DBFILESCLIENT\\SPELLMISSILE_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLMISSILE_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLMISSILEMOTION.DB2",
			"DBFILESCLIENT\\SPELLMISSILEMOTION.DBC",
			"DBFILESCLIENT\\SPELLPOWER.DB2",
			"DBFILESCLIENT\\SPELLPOWER.DBC",
			"DBFILESCLIENT\\SPELLPOWER_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLPOWER_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLPOWERDIFFICULTY.DB2",
			"DBFILESCLIENT\\SPELLPROCEDURALEFFECT.DB2",
			"DBFILESCLIENT\\SPELLPROCSPERMINUTE.DB2",
			"DBFILESCLIENT\\SPELLPROCSPERMINUTE.DBC",
			"DBFILESCLIENT\\SPELLPROCSPERMINUTEMOD.DB2",
			"DBFILESCLIENT\\SPELLPROCSPERMINUTEMOD.DBC",
			"DBFILESCLIENT\\SPELLRADIUS.DB2",
			"DBFILESCLIENT\\SPELLRADIUS.DBC",
			"DBFILESCLIENT\\SPELLRANGE.DB2",
			"DBFILESCLIENT\\SPELLRANGE.DBC",
			"DBFILESCLIENT\\SPELLREAGENTS.DB2",
			"DBFILESCLIENT\\SPELLREAGENTS.DBC",
			"DBFILESCLIENT\\SPELLREAGENTS_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLREAGENTS_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLREAGENTSCURRENCY.DB2",
			"DBFILESCLIENT\\SPELLREAGENTSCURRENCY_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLRUNECOST.DB2",
			"DBFILESCLIENT\\SPELLRUNECOST.DBC",
			"DBFILESCLIENT\\SPELLRUNECOST_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLSCALING.DB2",
			"DBFILESCLIENT\\SPELLSCALING.DBC",
			"DBFILESCLIENT\\SPELLSCALING_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLSCALING_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLSHAPESHIFT.DB2",
			"DBFILESCLIENT\\SPELLSHAPESHIFT.DBC",
			"DBFILESCLIENT\\SPELLSHAPESHIFT_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLSHAPESHIFT_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLSHAPESHIFTFORM.DB2",
			"DBFILESCLIENT\\SPELLSHAPESHIFTFORM.DBC",
			"DBFILESCLIENT\\SPELLSPECIALUNITEFFECT.DB2",
			"DBFILESCLIENT\\SPELLSPECIALUNITEFFECT.DBC",
			"DBFILESCLIENT\\SPELLTARGETRESTRICTIONS.DB2",
			"DBFILESCLIENT\\SPELLTARGETRESTRICTIONS.DBC",
			"DBFILESCLIENT\\SPELLTARGETRESTRICTIONS_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLTARGETRESTRICTIONS_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLTOTEMS.DB2",
			"DBFILESCLIENT\\SPELLTOTEMS.DBC",
			"DBFILESCLIENT\\SPELLTOTEMS_INTERNAL.DB2",
			"DBFILESCLIENT\\SPELLTOTEMS_INTERNAL.DBC",
			"DBFILESCLIENT\\SPELLVISUAL.DB2",
			"DBFILESCLIENT\\SPELLVISUAL.DBC",
			"DBFILESCLIENT\\SPELLVISUALANIM.DB2",
			"DBFILESCLIENT\\SPELLVISUALANIMNAME.DBC",
			"DBFILESCLIENT\\SPELLVISUALCOLOREFFECT.DB2",
			"DBFILESCLIENT\\SPELLVISUALEFFECTNAME.DB2",
			"DBFILESCLIENT\\SPELLVISUALEFFECTNAME.DBC",
			"DBFILESCLIENT\\SPELLVISUALEVENT.DB2",
			"DBFILESCLIENT\\SPELLVISUALKIT.DB2",
			"DBFILESCLIENT\\SPELLVISUALKIT.DBC",
			"DBFILESCLIENT\\SPELLVISUALKITAREAMODEL.DB2",
			"DBFILESCLIENT\\SPELLVISUALKITAREAMODEL.DBC",
			"DBFILESCLIENT\\SPELLVISUALKITEFFECT.DB2",
			"DBFILESCLIENT\\SPELLVISUALKITMODELATTACH.DB2",
			"DBFILESCLIENT\\SPELLVISUALKITMODELATTACH.DBC",
			"DBFILESCLIENT\\SPELLVISUALMISSILE.DB2",
			"DBFILESCLIENT\\SPELLVISUALPRECASTTRANSITIONS.DBC",
			"DBFILESCLIENT\\SPELLXDESCRIPTIONVARIABLES.DB2",
			"DBFILESCLIENT\\SPELLXSPELLVISUAL.DB2",
			"DBFILESCLIENT\\SPELLXSPELLVISUAL_INTERNAL.DB2",
			"DBFILESCLIENT\\STABLESLOTPRICES.DBC",
			"DBFILESCLIENT\\STARTUP_STRINGS.DB2",
			"DBFILESCLIENT\\STARTUP_STRINGS.DBC",
			"DBFILESCLIENT\\STARTUPFILES.DB2",
			"DBFILESCLIENT\\STATIONERY.DB2",
			"DBFILESCLIENT\\STATIONERY.DBC",
			"DBFILESCLIENT\\STRINGLOOKUPS.DB2",
			"DBFILESCLIENT\\STRINGLOOKUPS.DBC",
			"DBFILESCLIENT\\SUMMONPROPERTIES.DB2",
			"DBFILESCLIENT\\SUMMONPROPERTIES.DBC",
			"DBFILESCLIENT\\TACTKEY.DB2",
			"DBFILESCLIENT\\TACTKEYLOOKUP.DB2",
			"DBFILESCLIENT\\TALENT.DB2",
			"DBFILESCLIENT\\TALENT.DBC",
			"DBFILESCLIENT\\TALENTTAB.DBC",
			"DBFILESCLIENT\\TAXINODES.DB2",
			"DBFILESCLIENT\\TAXINODES.DBC",
			"DBFILESCLIENT\\TAXIPATH.DB2",
			"DBFILESCLIENT\\TAXIPATH.DBC",
			"DBFILESCLIENT\\TAXIPATHNODE.DB2",
			"DBFILESCLIENT\\TAXIPATHNODE.DBC",
			"DBFILESCLIENT\\TERRAINMATERIAL.DB2",
			"DBFILESCLIENT\\TERRAINMATERIAL.DBC",
			"DBFILESCLIENT\\TERRAINTYPE.DB2",
			"DBFILESCLIENT\\TERRAINTYPE.DBC",
			"DBFILESCLIENT\\TERRAINTYPESOUNDS.DB2",
			"DBFILESCLIENT\\TERRAINTYPESOUNDS.DBC",
			"DBFILESCLIENT\\TEXTUREBLENDSET.DB2",
			"DBFILESCLIENT\\TEXTUREFILEDATA.DB2",
			"DBFILESCLIENT\\TOTEMCATEGORY.DB2",
			"DBFILESCLIENT\\TOTEMCATEGORY.DBC",
			"DBFILESCLIENT\\TOY.DB2",
			"DBFILESCLIENT\\TRADESKILLCATEGORY.DB2",
			"DBFILESCLIENT\\TRADESKILLCATEGORY.DBC",
			"DBFILESCLIENT\\TRADESKILLITEM.DB2",
			"DBFILESCLIENT\\TRANSFORMMATRIX.DB2",
			"DBFILESCLIENT\\TRANSMOGHOLIDAY.DB2",
			"DBFILESCLIENT\\TRANSMOGSET.DB2",
			"DBFILESCLIENT\\TRANSMOGSETGROUP.DB2",
			"DBFILESCLIENT\\TRANSMOGSETITEM.DB2",
			"DBFILESCLIENT\\TRANSPORTANIMATION.DB2",
			"DBFILESCLIENT\\TRANSPORTANIMATION.DBC",
			"DBFILESCLIENT\\TRANSPORTPHYSICS.DB2",
			"DBFILESCLIENT\\TRANSPORTPHYSICS.DBC",
			"DBFILESCLIENT\\TRANSPORTROTATION.DB2",
			"DBFILESCLIENT\\TRANSPORTROTATION.DBC",
			"DBFILESCLIENT\\TROPHY.DB2",
			"DBFILESCLIENT\\TROPHYINSTANCE.DB2",
			"DBFILESCLIENT\\TROPHYTYPE.DB2",
			"DBFILESCLIENT\\UICAMERA.DB2",
			"DBFILESCLIENT\\UICAMERATYPE.DB2",
			"DBFILESCLIENT\\UICAMFBACKTRANSMOGCHRRACE.DB2",
			"DBFILESCLIENT\\UICAMFBACKTRANSMOGWEAPON.DB2",
			"DBFILESCLIENT\\UIEXPANSIONDISPLAYINFO.DB2",
			"DBFILESCLIENT\\UIEXPANSIONDISPLAYINFOICON.DB2",
			"DBFILESCLIENT\\UIMAPPOI.DB2",
			"DBFILESCLIENT\\UIMODELSCENE.DB2",
			"DBFILESCLIENT\\UIMODELSCENEACTOR.DB2",
			"DBFILESCLIENT\\UIMODELSCENEACTORDISPLAY.DB2",
			"DBFILESCLIENT\\UIMODELSCENECAMERA.DB2",
			"DBFILESCLIENT\\UISOUNDLOOKUPS.DBC",
			"DBFILESCLIENT\\UITEXTUREATLAS.DB2",
			"DBFILESCLIENT\\UITEXTUREATLASMEMBER.DB2",
			"DBFILESCLIENT\\UITEXTUREKIT.DB2",
			"DBFILESCLIENT\\UNITBLOOD.DB2",
			"DBFILESCLIENT\\UNITBLOOD.DBC",
			"DBFILESCLIENT\\UNITBLOODLEVELS.DB2",
			"DBFILESCLIENT\\UNITBLOODLEVELS.DBC",
			"DBFILESCLIENT\\UNITCONDITION.DB2",
			"DBFILESCLIENT\\UNITCONDITION.DBC",
			"DBFILESCLIENT\\UNITPOWERBAR.DB2",
			"DBFILESCLIENT\\UNITPOWERBAR.DBC",
			"DBFILESCLIENT\\UNITTESTSPARSE.DB2",
			"DBFILESCLIENT\\VEHICLE.DB2",
			"DBFILESCLIENT\\VEHICLE.DBC",
			"DBFILESCLIENT\\VEHICLESEAT.DB2",
			"DBFILESCLIENT\\VEHICLESEAT.DBC",
			"DBFILESCLIENT\\VEHICLEUIINDICATOR.DB2",
			"DBFILESCLIENT\\VEHICLEUIINDICATOR.DBC",
			"DBFILESCLIENT\\VEHICLEUIINDSEAT.DB2",
			"DBFILESCLIENT\\VEHICLEUIINDSEAT.DBC",
			"DBFILESCLIENT\\VIDEOHARDWARE.DB2",
			"DBFILESCLIENT\\VIDEOHARDWARE.DBC",
			"DBFILESCLIENT\\VIGNETTE.DB2",
			"DBFILESCLIENT\\VIRTUALATTACHMENT.DB2",
			"DBFILESCLIENT\\VIRTUALATTACHMENTCUSTOMIZATION.DB2",
			"DBFILESCLIENT\\VOCALUISOUNDS.DB2",
			"DBFILESCLIENT\\VOCALUISOUNDS.DBC",
			"DBFILESCLIENT\\WBACCESSCONTROLLIST.DB2",
			"DBFILESCLIENT\\WBACCESSCONTROLLIST_INTERNAL.DB2",
			"DBFILESCLIENT\\WBCERTBLACKLIST.DB2",
			"DBFILESCLIENT\\WBCERTWHITELIST.DB2",
			"DBFILESCLIENT\\WBCERTWHITELIST_INTERNAL.DB2",
			"DBFILESCLIENT\\WBPERMISSIONS.DB2",
			"DBFILESCLIENT\\WEAPONIMPACTSOUNDS.DB2",
			"DBFILESCLIENT\\WEAPONIMPACTSOUNDS.DBC",
			"DBFILESCLIENT\\WEAPONSWINGSOUNDS2.DB2",
			"DBFILESCLIENT\\WEAPONSWINGSOUNDS2.DBC",
			"DBFILESCLIENT\\WEAPONTRAIL.DB2",
			"DBFILESCLIENT\\WEAPONTRAILMODELDEF.DB2",
			"DBFILESCLIENT\\WEAPONTRAILPARAM.DB2",
			"DBFILESCLIENT\\WEATHER.DB2",
			"DBFILESCLIENT\\WEATHER.DBC",
			"DBFILESCLIENT\\WINDSETTINGS.DB2",
			"DBFILESCLIENT\\WMOAREATABLE.DB2",
			"DBFILESCLIENT\\WMOAREATABLE.DBC",
			"DBFILESCLIENT\\WMOMINIMAPTEXTURE.DB2",
			"DBFILESCLIENT\\WORLD_PVP_AREA.DB2",
			"DBFILESCLIENT\\WORLD_PVP_AREA.DBC",
			"DBFILESCLIENT\\WORLDBOSSLOCKOUT.DB2",
			"DBFILESCLIENT\\WORLDCHUNKSOUNDS.DB2",
			"DBFILESCLIENT\\WORLDCHUNKSOUNDS.DBC",
			"DBFILESCLIENT\\WORLDEFFECT.DB2",
			"DBFILESCLIENT\\WORLDEFFECT.DBC",
			"DBFILESCLIENT\\WORLDELAPSEDTIMER.DB2",
			"DBFILESCLIENT\\WORLDELAPSEDTIMER.DBC",
			"DBFILESCLIENT\\WORLDMAPAREA.DB2",
			"DBFILESCLIENT\\WORLDMAPAREA.DBC",
			"DBFILESCLIENT\\WORLDMAPCONTINENT.DB2",
			"DBFILESCLIENT\\WORLDMAPCONTINENT.DBC",
			"DBFILESCLIENT\\WORLDMAPOVERLAY.DB2",
			"DBFILESCLIENT\\WORLDMAPOVERLAY.DBC",
			"DBFILESCLIENT\\WORLDMAPTRANSFORMS.DB2",
			"DBFILESCLIENT\\WORLDMAPTRANSFORMS.DBC",
			"DBFILESCLIENT\\WORLDSAFELOCS.DB2",
			"DBFILESCLIENT\\WORLDSAFELOCS.DBC",
			"DBFILESCLIENT\\WORLDSTATE.DB2",
			"DBFILESCLIENT\\WORLDSTATE.DBC",
			"DBFILESCLIENT\\WORLDSTATEEXPRESSION.DB2",
			"DBFILESCLIENT\\WORLDSTATEEXPRESSION.DBC",
			"DBFILESCLIENT\\WORLDSTATEUI.DB2",
			"DBFILESCLIENT\\WORLDSTATEUI.DBC",
			"DBFILESCLIENT\\WORLDSTATEZONESOUNDS.DB2",
			"DBFILESCLIENT\\WORLDSTATEZONESOUNDS.DBC",
			"DBFILESCLIENT\\WOWERROR_STRINGS.DBC",
			"DBFILESCLIENT\\ZONEINTROMUSICTABLE.DB2",
			"DBFILESCLIENT\\ZONEINTROMUSICTABLE.DBC",
			"DBFILESCLIENT\\ZONELIGHT.DB2",
			"DBFILESCLIENT\\ZONELIGHT.DBC",
			"DBFILESCLIENT\\ZONELIGHTPOINT.DB2",
			"DBFILESCLIENT\\ZONELIGHTPOINT.DBC",
			"DBFILESCLIENT\\ZONEMUSIC.DB2",
			"DBFILESCLIENT\\ZONEMUSIC.DBC",
			"DBFILESCLIENT\\ZONESTORY.DB2",
		};

		private static async Task LoadListFile()
		{
			string listfile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "listfile.txt");

			if (!File.Exists(listfile) || (DateTime.Now - File.GetLastWriteTime(listfile)).TotalHours > 24)
			{
				try
				{
					string url = "https://bnet.marlam.in/listfile.php?t=" + DateTime.Now.Ticks;

					HttpWebRequest req = (HttpWebRequest)WebRequest.Create(url);
					req.UserAgent = "WDBXEditor/1.0 (+https://github.com/WowDevTools/WDBXEditor)"; // for tracking purposes

					using (WebResponse resp = await req.GetResponseAsync())
					using (FileStream fs = File.Create(listfile))
						resp.GetResponseStream().CopyTo(fs);
					req.Abort();
				}
				catch { return; }
			}

			ClientDBFileNames = File.ReadAllLines(listfile).AsParallel()
									.Where(x => x.EndsWith("dbc") || x.EndsWith("db2"))
									.Select(x => x.ToUpper().Replace("/", "\\"))
									.ToList();
		}
		#endregion
	}
}
