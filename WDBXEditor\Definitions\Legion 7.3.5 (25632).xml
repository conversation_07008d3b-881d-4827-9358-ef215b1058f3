<?xml version="1.0"?>
<Definition xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<Table Name="Achievement" Build="25632">
		<Field Name="Title" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Reward" Type="string" />
		<Field Name="Flags" Type="uint" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="Supercedes" Type="ushort" />
		<Field Name="Category" Type="ushort" />
		<Field Name="UIOrder" Type="ushort" />
		<Field Name="SharesCriteria" Type="ushort" />
		<Field Name="Faction" Type="byte" />
		<Field Name="Points" Type="byte" />
		<Field Name="MinimumCriteria" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileDataID" Type="uint" />
		<Field Name="CriteriaTree" Type="uint" />
	</Table>
	<Table Name="Achievement_category" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Adventurejournal" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="string" />
		<Field Name="Field02" Type="string" />
		<Field Name="Field03" Type="string" />
		<Field Name="Field04" Type="string" />
		<Field Name="Field05" Type="string" />
		<Field Name="Field06" Type="int" />
		<Field Name="Field07" Type="int" />
		<Field Name="Field08" Type="ushort" />
		<Field Name="Field09" Type="ushort" />
		<Field Name="Field0A" Type="ushort" />
		<Field Name="Field0B" Type="ushort" ArraySize="2" />
		<Field Name="Field0C" Type="ushort" />
		<Field Name="Field0D" Type="ushort" />
		<Field Name="Field0E" Type="byte" />
		<Field Name="Field0F" Type="byte" />
		<Field Name="Field10" Type="byte" />
		<Field Name="Field11" Type="byte" />
		<Field Name="Field12" Type="byte" />
		<Field Name="Field13" Type="byte" ArraySize="2" />
		<Field Name="Field14" Type="byte" />
		<Field Name="Field15" Type="int" />
		<Field Name="Field16" Type="byte" />
	</Table>
	<Table Name="Adventuremappoi" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="float" ArraySize="2" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="int" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="ushort" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="ushort" />
		<Field Name="FieldC" Type="int" />
		<Field Name="FieldD" Type="ushort" />
	</Table>
	<Table Name="alliedrace" Build="25632">
		<Field Name="field0" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="field2" Type="byte" />
		<Field Name="field3" Type="ushort" />
		<Field Name="field4" Type="int" />
		<Field Name="field5" Type="int" />
		<Field Name="field6" Type="int" />
		<Field Name="field7" Type="ushort" />
	</Table>
	<Table Name="Alliedrace" Build="25632">
		<Field Name="Field0" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="int" />
	</Table>
	<Table Name="alliedraceracialability" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="field1" Type="string" />
		<Field Name="field2" Type="string" />
		<Field Name="field3" Type="byte" />
		<Field Name="field4" Type="int" />
	</Table>
	<Table Name="Alliedraceracialability" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Animationdata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="AnimKit" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OneShotDuration" Type="uint" />
		<Field Name="OneShotStopAnimKitID" Type="ushort" />
		<Field Name="LowDefAnimKitID" Type="ushort" />
	</Table>
	<Table Name="Animkitboneset" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Animkitbonesetalias" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Animkitconfig" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
	</Table>
	<Table Name="Animkitconfigboneset" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Animkitpriority" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
	</Table>
	<Table Name="Animkitreplacement" Build="25632">
		<Field Name="Field0" Type="ushort" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Animkitsegment" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="int" />
		<Field Name="Field02" Type="int" />
		<Field Name="Field03" Type="int" />
		<Field Name="Field04" Type="float" />
		<Field Name="Field05" Type="int" />
		<Field Name="Field06" Type="ushort" />
		<Field Name="Field07" Type="ushort" />
		<Field Name="Field08" Type="ushort" />
		<Field Name="Field09" Type="ushort" />
		<Field Name="Field0A" Type="ushort" />
		<Field Name="Field0B" Type="ushort" />
		<Field Name="Field0C" Type="byte" />
		<Field Name="Field0D" Type="byte" />
		<Field Name="Field0E" Type="byte" />
		<Field Name="Field0F" Type="byte" />
		<Field Name="Field10" Type="byte" />
		<Field Name="Field11" Type="byte" />
		<Field Name="Field12" Type="int" />
	</Table>
	<Table Name="Animreplacement" Build="25632">
		<Field Name="Field0" Type="ushort" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Animreplacementset" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
	</Table>
	<Table Name="Areafarclipoverride" Build="25632">
		<Field Name="Field0" Type="int" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="AreaGroupMember" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AreaID" Type="ushort" />
	</Table>
	<Table Name="Areapoi" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="string" />
		<Field Name="Field02" Type="string" />
		<Field Name="Field03" Type="int" />
		<Field Name="Field04" Type="float" ArraySize="3" />
		<Field Name="Field05" Type="int" />
		<Field Name="Field06" Type="int" />
		<Field Name="Field07" Type="ushort" />
		<Field Name="Field08" Type="ushort" />
		<Field Name="Field09" Type="ushort" />
		<Field Name="Field0A" Type="ushort" />
		<Field Name="Field0B" Type="byte" />
		<Field Name="Field0C" Type="byte" />
		<Field Name="Field0D" Type="ushort" />
		<Field Name="Field0E" Type="ushort" />
		<Field Name="Field0F" Type="int" />
		<Field Name="Field10" Type="int" />
	</Table>
	<Table Name="Areapoistate" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="int" />
	</Table>
	<Table Name="AreaTable" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ZoneName" Type="string" />
		<Field Name="AreaName" Type="string" />
		<Field Name="Flags" Type="uint" ArraySize="2" />
		<Field Name="AmbientMultiplier" Type="float" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="ParentAreaID" Type="ushort" />
		<Field Name="AreaBit" Type="short" />
		<Field Name="AmbienceID" Type="ushort" />
		<Field Name="ZoneMusic" Type="ushort" />
		<Field Name="IntroSound" Type="ushort" />
		<Field Name="LiquidTypeID" Type="ushort" ArraySize="4" />
		<Field Name="UWZoneMusic" Type="ushort" />
		<Field Name="UWAmbience" Type="ushort" />
		<Field Name="PvPCombatWorldStateID" Type="ushort" />
		<Field Name="SoundProviderPref" Type="byte" />
		<Field Name="SoundProviderPrefUnderwater" Type="byte" />
		<Field Name="ExplorationLevel" Type="byte" />
		<Field Name="FactionGroupMask" Type="byte" />
		<Field Name="MountFlags" Type="byte" />
		<Field Name="WildBattlePetLevelMin" Type="byte" />
		<Field Name="WildBattlePetLevelMax" Type="byte" />
		<Field Name="WindSettingsID" Type="byte" />
		<Field Name="UWIntroSound" Type="uint" />
	</Table>
	<Table Name="AreaTrigger" Build="25632">
		<Field Type="float" ArraySize="3" />
		<Field Name="Radius" Type="float" />
		<Field Name="BoxLength" Type="float" />
		<Field Name="BoxWidth" Type="float" />
		<Field Name="BoxHeight" Type="float" />
		<Field Name="BoxYaw" Type="float" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="PhaseID" Type="ushort" />
		<Field Name="PhaseGroupID" Type="ushort" />
		<Field Name="ShapeID" Type="ushort" />
		<Field Name="AreaTriggerActionSetID" Type="ushort" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="ShapeType" Type="byte" />
		<Field Name="Flag" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Areatriggeractionset" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
	</Table>
	<Table Name="Areatriggerbox" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="3" />
	</Table>
	<Table Name="Areatriggercylinder" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
	</Table>
	<Table Name="Areatriggersphere" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
	</Table>
	<Table Name="ArmorLocation" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Modifier" Type="float" ArraySize="5" />
	</Table>
	<Table Name="Artifact" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="BarConnectedColor" Type="uint" />
		<Field Name="BarDisconnectedColor" Type="uint" />
		<Field Name="TitleColor" Type="uint" />
		<Field Name="ClassUiTextureKitID" Type="ushort" />
		<Field Name="SpecID" Type="ushort" />
		<Field Name="ArtifactCategoryID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UiModelSceneID" Type="uint" />
		<Field Name="SpellVisualKitID" Type="uint" />
	</Table>
	<Table Name="ArtifactAppearance" Build="25632">
		<Field Name="Name" Type="string" />
		<Field Name="SwatchColor" Type="uint" />
		<Field Name="ModelDesaturation" Type="float" />
		<Field Name="ModelAlpha" Type="float" />
		<Field Name="ShapeshiftDisplayID" Type="uint" />
		<Field Name="ArtifactAppearanceSetID" Type="ushort" />
		<Field Name="Unknown" Type="ushort" />
		<Field Name="DisplayIndex" Type="byte" />
		<Field Name="AppearanceModID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ModifiesShapeshiftFormDisplay" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionID" Type="uint" />
		<Field Name="ItemAppearanceID" Type="uint" />
		<Field Name="AltItemAppearanceID" Type="uint" />
	</Table>
	<Table Name="ArtifactAppearanceSet" Build="25632">
		<Field Name="Name" Type="string" />
		<Field Name="Name2" Type="string" />
		<Field Name="UiCameraID" Type="ushort" />
		<Field Name="AltHandUICameraID" Type="ushort" />
		<Field Name="DisplayIndex" Type="byte" />
		<Field Name="AttachmentPoint" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ArtifactCategory" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ArtifactKnowledgeCurrencyID" Type="ushort" />
		<Field Name="ArtifactKnowledgeMultiplierCurveID" Type="ushort" />
	</Table>
	<Table Name="ArtifactPower" Build="25632">
		<Field Name="Pos" Type="float" ArraySize="2" />
		<Field Name="ArtifactID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MaxRank" Type="byte" />
		<Field Name="ArtifactTier" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RelicType" Type="int" />
	</Table>
	<Table Name="ArtifactPowerLink" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FromArtifactPowerID" Type="ushort" />
		<Field Name="ToArtifactPowerID" Type="ushort" />
	</Table>
	<Table Name="ArtifactPowerPicker" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionID" Type="uint" />
	</Table>
	<Table Name="ArtifactPowerRank" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="Value" Type="float" />
		<Field Name="Unknown" Type="ushort" />
		<Field Name="Rank" Type="byte" />
	</Table>
	<Table Name="ArtifactQuestXP" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Exp" Type="uint" ArraySize="10" />
	</Table>
	<Table Name="Artifacttier" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Artifactunlock" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="ushort" />
	</Table>
	<Table Name="AuctionHouse" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="FactionID" Type="ushort" />
		<Field Name="DepositRate" Type="byte" />
		<Field Name="ConsignmentRate" Type="byte" />
	</Table>
	<Table Name="BankBagSlotPrices" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Cost" Type="uint" />
	</Table>
	<Table Name="BannedAddons" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Version" Type="string" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="BarberShopStyle" Build="25632">
		<Field Name="DisplayName" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="CostModifier" Type="float" />
		<Field Name="Type" Type="byte" />
		<Field Name="Race" Type="byte" />
		<Field Name="Sex" Type="byte" />
		<Field Name="Data" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="BattlemasterList" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="GameType" Type="string" />
		<Field Name="ShortDescription" Type="string" />
		<Field Name="LongDescription" Type="string" />
		<Field Name="IconFileDataID" Type="uint" />
		<Field Name="MapID" Type="ushort" ArraySize="16" />
		<Field Name="HolidayWorldState" Type="ushort" />
		<Field Name="PlayerConditionID" Type="ushort" />
		<Field Name="InstanceType" Type="byte" />
		<Field Name="GroupsAllowed" Type="byte" />
		<Field Name="MaxGroupSize" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
		<Field Name="RatedPlayers" Type="byte" />
		<Field Name="MinPlayers" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Battlepetability" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="int" />
	</Table>
	<Table Name="Battlepetabilityeffect" Build="25632">
		<Field Name="Field0" Type="ushort" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" ArraySize="6" />
		<Field Name="Field5" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Battlepetabilitystate" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Battlepetabilityturn" Build="25632">
		<Field Name="Field0" Type="ushort" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="BattlePetBreedQuality" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Modifier" Type="float" />
		<Field Name="Quality" Type="byte" />
	</Table>
	<Table Name="BattlePetBreedState" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="short" />
		<Field Name="State" Type="byte" />
	</Table>
	<Table Name="Battlepetdisplayoverride" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Battlepeteffectproperties" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" ArraySize="6" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" ArraySize="6" />
	</Table>
	<Table Name="BattlePetSpecies" Build="25632">
		<Field Name="SourceText" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="CreatureID" Type="uint" />
		<Field Name="IconFileID" Type="uint" />
		<Field Name="SummonSpellID" Type="uint" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="PetType" Type="byte" />
		<Field Name="Source" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CardModelSceneID" Type="uint" />
		<Field Name="LoadoutModelSceneID" Type="uint" />
	</Table>
	<Table Name="BattlePetSpeciesState" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="int" />
		<Field Name="State" Type="byte" />
	</Table>
	<Table Name="Battlepetspeciesxability" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Battlepetstate" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Battlepetvisual" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="Beameffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="ushort" />
		<Field Name="Field8" Type="ushort" />
		<Field Name="Field9" Type="ushort" />
		<Field Name="FieldA" Type="ushort" />
	</Table>
	<Table Name="Bonewindmodifiermodel" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
	</Table>
	<Table Name="Bonewindmodifiers" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="3" />
		<Field Name="Field2" Type="float" />
	</Table>
	<Table Name="Bounty" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
	</Table>
	<Table Name="Bountyset" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="int" />
	</Table>
	<Table Name="BroadcastText" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaleText" Type="string" />
		<Field Name="FemaleText" Type="string" />
		<Field Name="EmoteID" Type="ushort" ArraySize="3" />
		<Field Name="EmoteDelay" Type="ushort" ArraySize="3" />
		<Field Name="UnkEmoteID" Type="ushort" />
		<Field Name="Language" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="PlayerConditionID" Type="uint" />
		<Field Name="SoundID" Type="uint" ArraySize="2" />
	</Table>
	<Table Name="Cameraeffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
	</Table>
	<Table Name="Cameraeffectentry" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="float" />
		<Field Name="Field02" Type="float" />
		<Field Name="Field03" Type="float" />
		<Field Name="Field04" Type="float" />
		<Field Name="Field05" Type="float" />
		<Field Name="Field06" Type="float" />
		<Field Name="Field07" Type="float" />
		<Field Name="Field08" Type="float" />
		<Field Name="Field09" Type="ushort" />
		<Field Name="Field0A" Type="byte" />
		<Field Name="Field0B" Type="byte" />
		<Field Name="Field0C" Type="byte" />
		<Field Name="Field0D" Type="byte" />
		<Field Name="Field0E" Type="byte" />
		<Field Name="Field0F" Type="byte" />
	</Table>
	<Table Name="Cameramode" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="3" />
		<Field Name="Field2" Type="float" ArraySize="3" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="byte" />
	</Table>
	<Table Name="Castableraidbuffs" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
	</Table>
	<Table Name="Celestialbody" Build="25632">
		<Field Name="Field0" Type="int" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" ArraySize="2" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" ArraySize="2" />
		<Field Name="Field6" Type="float" ArraySize="2" />
		<Field Name="Field7" Type="float" ArraySize="2" />
		<Field Name="Field8" Type="float" />
		<Field Name="Field9" Type="float" ArraySize="2" />
		<Field Name="FieldA" Type="float" />
		<Field Name="FieldB" Type="float" ArraySize="3" />
		<Field Name="FieldC" Type="float" />
		<Field Name="FieldD" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Cfg_categories" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Cfg_configs" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Cfg_regions" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Characterfaceboneset" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="CharacterFacialHairStyles" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Geoset" Type="uint" ArraySize="5" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="VariationID" Type="byte" />
	</Table>
	<Table Name="Characterloadout" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ulong" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Characterloadoutitem" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Characterserviceinfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="string" />
		<Field Name="Field4" Type="string" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="int" />
		<Field Name="Field7" Type="string" />
		<Field Name="Field8" Type="int" />
		<Field Name="Field9" Type="int" />
		<Field Name="FieldA" Type="int" />
		<Field Name="FieldB" Type="int" />
	</Table>
	<Table Name="Charbaseinfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="CharBaseSection" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Variation" Type="byte" />
		<Field Name="ResolutionVariation" Type="byte" />
		<Field Name="Resolution" Type="byte" />
	</Table>
	<Table Name="Charcomponenttexturelayouts" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Charcomponenttexturesections" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="Charhairgeosets" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RootGeosetID" Type="int" />
		<Field Name="Race" Type="byte" />
		<Field Name="Sex" Type="byte" />
		<Field Name="CharSection_ID" Type="byte" />
		<Field Name="Flag" Type="byte" />
		<Field Name="Expansion" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="int" />
	</Table>
	<Table Name="CharSections" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TextureFileDataID" Type="uint" ArraySize="3" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="BaseSection" Type="byte" />
		<Field Name="VariationIndex" Type="byte" />
		<Field Name="ColorIndex" Type="byte" />
	</Table>
	<Table Name="Charshipment" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="ushort" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
	</Table>
	<Table Name="Charshipmentcontainer" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="string" />
		<Field Name="Field02" Type="string" />
		<Field Name="Field03" Type="int" />
		<Field Name="Field04" Type="ushort" />
		<Field Name="Field05" Type="ushort" />
		<Field Name="Field06" Type="ushort" />
		<Field Name="Field07" Type="ushort" />
		<Field Name="Field08" Type="ushort" />
		<Field Name="Field09" Type="ushort" />
		<Field Name="Field0A" Type="byte" />
		<Field Name="Field0B" Type="byte" />
		<Field Name="Field0C" Type="byte" />
		<Field Name="Field0D" Type="byte" />
		<Field Name="Field0E" Type="byte" />
		<Field Name="Field0F" Type="byte" />
		<Field Name="Field10" Type="byte" />
	</Table>
	<Table Name="CharStartOutfit" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" ArraySize="24" />
		<Field Name="PetDisplayID" Type="uint" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="GenderID" Type="byte" />
		<Field Name="OutfitID" Type="byte" />
		<Field Name="PetFamilyID" Type="byte" />
	</Table>
	<Table Name="CharTitles" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="NameMale" Type="string" />
		<Field Name="NameFemale" Type="string" />
		<Field Name="MaskID" Type="ushort" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ChatChannels" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Shortcut" Type="string" />
		<Field Name="Flags" Type="uint" />
		<Field Name="FactionGroup" Type="byte" />
	</Table>
	<Table Name="Chatprofanity" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="ChrClasses" Build="25632">
		<Field Name="PetNameToken" Type="string" />
		<Field Name="Name" Type="string" />
		<Field Name="NameFemale" Type="string" />
		<Field Name="NameMale" Type="string" />
		<Field Name="Filename" Type="string" />
		<Field Name="CreateScreenFileDataID" Type="uint" />
		<Field Name="SelectScreenFileDataID" Type="uint" />
		<Field Name="IconFileDataID" Type="uint" />
		<Field Name="LowResScreenFileDataID" Type="uint" />
		<Field Name="StartingLevel" Type="uint" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="CinematicSequenceID" Type="ushort" />
		<Field Name="DefaultSpec" Type="ushort" />
		<Field Name="PowerType" Type="byte" />
		<Field Name="SpellClassSet" Type="byte" />
		<Field Name="AttackPowerPerStrength" Type="byte" />
		<Field Name="AttackPowerPerAgility" Type="byte" />
		<Field Name="RangedAttackPowerPerAgility" Type="byte" />
		<Field Name="Unk1" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ChrClassesXPowerTypes" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PowerType" Type="byte" />
	</Table>
	<Table Name="Chrclassracesex" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="byte" />
	</Table>
	<Table Name="Chrclasstitle" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Chrclassuidisplay" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Chrclassvillain" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Chrcustomization" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="int" ArraySize="3" />
	</Table>
	<Table Name="ChrRaces" Build="25632">
		<Field Name="ClientPrefix" Type="string" />
		<Field Name="ClientFileString" Type="string" />
		<Field Name="Name" Type="string" />
		<Field Name="NameFemale" Type="string" />
		<Field Name="LowercaseName" Type="string" />
		<Field Name="LowercaseNameFemale" Type="string" />
		<Field Name="Flags" Type="uint" />
		<Field Name="MaleDisplayID" Type="uint" />
		<Field Name="FemaleDisplayID" Type="uint" />
		<Field Name="CreateScreenFileDataID" Type="uint" />
		<Field Name="SelectScreenFileDataID" Type="uint" />
		<Field Name="MaleCustomizeOffset" Type="float" ArraySize="3" />
		<Field Name="FemaleCustomizeOffset" Type="float" ArraySize="3" />
		<Field Name="LowResScreenFileDataID" Type="uint" />
		<Field Name="StartingLevel" Type="uint" />
		<Field Name="UIDisplayOrder" Type="uint" />
		<Field Name="FactionID" Type="ushort" />
		<Field Name="ResSicknessSpellID" Type="ushort" />
		<Field Name="SplashSoundID" Type="ushort" />
		<Field Name="CinematicSequenceID" Type="ushort" />
		<Field Name="BaseLanguage" Type="byte" />
		<Field Name="CreatureType" Type="byte" />
		<Field Name="TeamID" Type="byte" />
		<Field Name="RaceRelated" Type="byte" />
		<Field Name="UnalteredVisualRaceID" Type="byte" />
		<Field Name="CharComponentTextureLayoutID" Type="byte" />
		<Field Name="DefaultClassID" Type="byte" />
		<Field Name="NeutralRaceID" Type="byte" />
		<Field Name="ItemAppearanceFrameRaceID" Type="byte" />
		<Field Name="CharComponentTexLayoutHiResID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HighResMaleDisplayID" Type="uint" />
		<Field Name="HighResFemaleDisplayID" Type="uint" />
		<Field Name="HeritageArmorAchievementID" Type="uint" />
		<Field Name="MaleCorpseBonesModelFileDataID" Type="uint" />
		<Field Name="FemaleCorpseBonesModelFileDataID" Type="uint" />
		<Field Name="AlteredFormTransitionSpellVisualID" Type="uint" ArraySize="3" />
		<Field Name="AlteredFormTransitionSpellVisualKitID" Type="uint" ArraySize="3" />
	</Table>
	<Table Name="ChrSpecialization" Build="25632">
		<Field Name="Name" Type="string" />
		<Field Name="Name2" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="MasterySpellID" Type="uint" ArraySize="2" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PetTalentType" Type="byte" />
		<Field Name="Role" Type="byte" />
		<Field Name="PrimaryStatOrder" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileDataID" Type="uint" />
		<Field Name="Flags" Type="uint" />
		<Field Name="AnimReplacementSetID" Type="uint" />
	</Table>
	<Table Name="Chrupgradebucket" Build="25632">
		<Field Name="Field0" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Chrupgradebucketspell" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
	</Table>
	<Table Name="Chrupgradetier" Build="25632">
		<Field Name="Field0" Type="int" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="CinematicCamera" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundID" Type="uint" />
		<Field Name="Origin" Type="float" ArraySize="3" />
		<Field Name="OriginFacing" Type="float" />
		<Field Name="ModelFileDataID" Type="uint" />
	</Table>
	<Table Name="CinematicSequences" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundID" Type="uint" />
		<Field Name="Camera" Type="ushort" ArraySize="8" />
	</Table>
	<Table Name="Cloakdampening" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="5" />
		<Field Name="Field2" Type="float" ArraySize="5" />
		<Field Name="Field3" Type="int" ArraySize="2" />
		<Field Name="Field4" Type="float" ArraySize="2" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="float" />
		<Field Name="Field7" Type="float" />
	</Table>
	<Table Name="Combatcondition" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" ArraySize="2" />
		<Field Name="Field5" Type="ushort" ArraySize="2" />
		<Field Name="Field6" Type="byte" ArraySize="2" />
		<Field Name="Field7" Type="byte" ArraySize="2" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" ArraySize="2" />
		<Field Name="FieldA" Type="byte" ArraySize="2" />
		<Field Name="FieldB" Type="byte" />
	</Table>
	<Table Name="Commentatorstartlocation" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="3" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Commentatortrackedcooldown" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Componentmodelfiledata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Componenttexturefiledata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Configurationwarning" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Contribution" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" ArraySize="4" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="ConversationLine" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BroadcastTextID" Type="uint" />
		<Field Name="SpellVisualKitID" Type="uint" />
		<Field Name="Duration" Type="uint" />
		<Field Name="NextLineID" Type="ushort" />
		<Field Name="Unk1" Type="ushort" />
		<Field Name="Yell" Type="byte" />
		<Field Name="Unk2" Type="byte" />
		<Field Name="Unk3" Type="byte" />
	</Table>
	<Table Name="Creature" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="string" />
		<Field Name="Field4" Type="string" />
		<Field Name="Field5" Type="int" ArraySize="3" />
		<Field Name="Field6" Type="int" />
		<Field Name="Field7" Type="int" ArraySize="4" />
		<Field Name="Field8" Type="float" ArraySize="4" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="byte" />
		<Field Name="FieldC" Type="byte" />
	</Table>
	<Table Name="Creaturedifficulty" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" ArraySize="7" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="CreatureDisplayInfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureModelScale" Type="float" />
		<Field Name="ModelID" Type="ushort" />
		<Field Name="NPCSoundID" Type="ushort" />
		<Field Name="SizeClass" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Gender" Type="byte" />
		<Field Name="ExtendedDisplayInfoID" Type="uint" />
		<Field Name="PortraitTextureFileDataID" Type="uint" />
		<Field Name="CreatureModelAlpha" Type="byte" />
		<Field Name="SoundID" Type="ushort" />
		<Field Name="PlayerModelScale" Type="float" />
		<Field Name="PortraitCreatureDisplayInfoID" Type="uint" />
		<Field Name="BloodID" Type="byte" />
		<Field Name="ParticleColorID" Type="ushort" />
		<Field Name="CreatureGeosetData" Type="uint" />
		<Field Name="ObjectEffectPackageID" Type="ushort" />
		<Field Name="AnimReplacementSetID" Type="ushort" />
		<Field Name="UnarmedWeaponSubclass" Type="byte" />
		<Field Name="StateSpellVisualKitID" Type="uint" />
		<Field Name="InstanceOtherPlayerPetScale" Type="float" />
		<Field Name="MountSpellVisualKitID" Type="uint" />
		<Field Name="TextureVariation" Type="uint" ArraySize="3" />
	</Table>
	<Table Name="SDReplacementModel" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SdFileDataId" Type="int" />
	</Table>
	<Table Name="Creaturedisplayinfocond" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ulong" />
		<Field Name="Field2" Type="int" ArraySize="2" />
		<Field Name="Field3" Type="int" ArraySize="2" />
		<Field Name="Field4" Type="int" ArraySize="2" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="ushort" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="byte" />
		<Field Name="FieldC" Type="byte" />
		<Field Name="FieldD" Type="ushort" />
		<Field Name="FieldE" Type="int" ArraySize="3" />
	</Table>
	<Table Name="Creaturedisplayinfoevt" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="CreatureDisplayInfoExtra" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="uint" />
		<Field Name="HDFileDataID" Type="uint" />
		<Field Name="DisplayRaceID" Type="byte" />
		<Field Name="DisplaySexID" Type="byte" />
		<Field Name="DisplayClassID" Type="byte" />
		<Field Name="SkinID" Type="byte" />
		<Field Name="FaceID" Type="byte" />
		<Field Name="HairStyleID" Type="byte" />
		<Field Name="HairColorID" Type="byte" />
		<Field Name="FacialHairID" Type="byte" />
		<Field Name="CustomDisplayOption" Type="byte" ArraySize="3" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Creaturedisplayinfotrn" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="uint" />
		<Field Name="Field5" Type="int" />
	</Table>
	<Table Name="Creaturedispxuicamera" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="CreatureFamily" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="MinScale" Type="float" />
		<Field Name="MaxScale" Type="float" />
		<Field Name="IconFileDataID" Type="uint" />
		<Field Name="SkillLine" Type="ushort" ArraySize="2" />
		<Field Name="PetFoodMask" Type="ushort" />
		<Field Name="MinScaleLevel" Type="byte" />
		<Field Name="MaxScaleLevel" Type="byte" />
		<Field Name="PetTalentType" Type="byte" />
	</Table>
	<Table Name="Creatureimmunities" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" ArraySize="2" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="int" ArraySize="8" />
		<Field Name="Field9" Type="int" ArraySize="16" />
	</Table>
	<Table Name="CreatureModelData" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelScale" Type="float" />
		<Field Name="FootprintTextureLength" Type="float" />
		<Field Name="FootprintTextureWidth" Type="float" />
		<Field Name="FootprintParticleScale" Type="float" />
		<Field Name="CollisionWidth" Type="float" />
		<Field Name="CollisionHeight" Type="float" />
		<Field Name="MountHeight" Type="float" />
		<Field Name="GeoBoxMin" Type="float" ArraySize="3" />
		<Field Name="GeoBoxMax" Type="float" ArraySize="3" />
		<Field Name="WorldEffectScale" Type="float" />
		<Field Name="AttachedEffectScale" Type="float" />
		<Field Name="MissileCollisionRadius" Type="float" />
		<Field Name="MissileCollisionPush" Type="float" />
		<Field Name="MissileCollisionRaise" Type="float" />
		<Field Name="OverrideLootEffectScale" Type="float" />
		<Field Name="OverrideNameScale" Type="float" />
		<Field Name="OverrideSelectionRadius" Type="float" />
		<Field Name="TamedPetBaseScale" Type="float" />
		<Field Name="HoverHeight" Type="float" />
		<Field Name="Flags" Type="uint" />
		<Field Name="FileDataID" Type="uint" />
		<Field Name="SizeClass" Type="uint" />
		<Field Name="BloodID" Type="uint" />
		<Field Name="FootprintTextureID" Type="uint" />
		<Field Name="FoleyMaterialID" Type="uint" />
		<Field Name="FootstepEffectID" Type="uint" />
		<Field Name="DeathThudEffectID" Type="uint" />
		<Field Name="SoundID" Type="uint" />
		<Field Name="CreatureGeosetDataID" Type="uint" />
	</Table>
	<Table Name="Creaturemovementinfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
	</Table>
	<Table Name="Creaturesounddata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="float" />
		<Field Name="Field02" Type="float" />
		<Field Name="Field03" Type="byte" />
		<Field Name="Field04" Type="uint" />
		<Field Name="Field05" Type="int" />
		<Field Name="Field06" Type="uint" />
		<Field Name="Field07" Type="uint" />
		<Field Name="Field08" Type="int" />
		<Field Name="Field09" Type="uint" />
		<Field Name="Field0A" Type="uint" />
		<Field Name="Field0B" Type="uint" />
		<Field Name="Field0C" Type="uint" />
		<Field Name="Field0D" Type="uint" />
		<Field Name="Field0E" Type="uint" />
		<Field Name="Field0F" Type="uint" />
		<Field Name="Field10" Type="uint" />
		<Field Name="Field11" Type="uint" />
		<Field Name="Field12" Type="uint" />
		<Field Name="Field13" Type="uint" />
		<Field Name="Field14" Type="uint" />
		<Field Name="Field15" Type="uint" />
		<Field Name="Field16" Type="uint" />
		<Field Name="Field17" Type="uint" />
		<Field Name="Field18" Type="uint" />
		<Field Name="Field19" Type="uint" />
		<Field Name="Field1A" Type="uint" />
		<Field Name="Field1B" Type="uint" />
		<Field Name="Field1C" Type="uint" />
		<Field Name="Field1D" Type="uint" />
		<Field Name="Field1E" Type="uint" />
		<Field Name="Field1F" Type="uint" />
		<Field Name="Field20" Type="uint" />
		<Field Name="Field21" Type="uint" />
		<Field Name="Field22" Type="uint" />
		<Field Name="Field23" Type="uint" />
		<Field Name="Field24" Type="uint" ArraySize="5" />
		<Field Name="Field25" Type="uint" ArraySize="4" />
	</Table>
	<Table Name="CreatureType" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Creaturexcontribution" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
	</Table>
	<Table Name="Criteria" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="byte" />
	</Table>
	<Table Name="CriteriaTree" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
		<Field Name="Amount" Type="uint" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="Operator" Type="byte" />
		<Field Name="CriteriaID" Type="uint" />
		<Field Name="Parent" Type="uint" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="Criteriatreexeffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
	</Table>
	<Table Name="Currencycategory" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="CurrencyTypes" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="MaxQty" Type="uint" />
		<Field Name="MaxEarnablePerWeek" Type="uint" />
		<Field Name="Flags" Type="uint" />
		<Field Name="CategoryID" Type="byte" />
		<Field Name="SpellCategory" Type="byte" />
		<Field Name="Quality" Type="byte" />
		<Field Name="InventoryIconFileDataID" Type="uint" />
		<Field Name="SpellWeight" Type="uint" />
	</Table>
	<Table Name="Curve" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="byte" />
		<Field Name="Unused" Type="byte" />
	</Table>
	<Table Name="CurvePoint" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="X" Type="float" />
		<Field Name="Y" Type="float" />
		<Field Name="CurveID" Type="ushort" />
		<Field Name="Index" Type="byte" />
	</Table>
	<Table Name="DBCache" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Deaththudlookups" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="int" />
	</Table>
	<Table Name="Decalproperties" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="int" />
		<Field Name="Field02" Type="float" />
		<Field Name="Field03" Type="float" />
		<Field Name="Field04" Type="float" />
		<Field Name="Field05" Type="float" />
		<Field Name="Field06" Type="float" />
		<Field Name="Field07" Type="float" />
		<Field Name="Field08" Type="float" />
		<Field Name="Field09" Type="float" />
		<Field Name="Field0A" Type="byte" />
		<Field Name="Field0B" Type="byte" />
		<Field Name="Field0C" Type="ushort" />
		<Field Name="Field0D" Type="ushort" />
		<Field Name="Field0E" Type="byte" />
		<Field Name="Field0F" Type="byte" />
		<Field Name="Field10" Type="byte" />
	</Table>
	<Table Name="DestructibleModelData" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StateDamagedDisplayID" Type="ushort" />
		<Field Name="StateDestroyedDisplayID" Type="ushort" />
		<Field Name="StateRebuildingDisplayID" Type="ushort" />
		<Field Name="StateSmokeDisplayID" Type="ushort" />
		<Field Name="HealEffectSpeed" Type="ushort" />
		<Field Name="StateDamagedImpactEffectDoodadSet" Type="byte" />
		<Field Name="StateDamagedAmbientDoodadSet" Type="byte" />
		<Field Name="StateDamagedNameSet" Type="byte" />
		<Field Name="StateDestroyedDestructionDoodadSet" Type="byte" />
		<Field Name="StateDestroyedImpactEffectDoodadSet" Type="byte" />
		<Field Name="StateDestroyedAmbientDoodadSet" Type="byte" />
		<Field Name="StateDestroyedNameSet" Type="byte" />
		<Field Name="StateRebuildingDestructionDoodadSet" Type="byte" />
		<Field Name="StateRebuildingImpactEffectDoodadSet" Type="byte" />
		<Field Name="StateRebuildingAmbientDoodadSet" Type="byte" />
		<Field Name="StateRebuildingNameSet" Type="byte" />
		<Field Name="StateSmokeInitDoodadSet" Type="byte" />
		<Field Name="StateSmokeAmbientDoodadSet" Type="byte" />
		<Field Name="StateSmokeNameSet" Type="byte" />
		<Field Name="EjectDirection" Type="byte" />
		<Field Name="DoNotHighlight" Type="byte" />
		<Field Name="HealEffect" Type="byte" />
	</Table>
	<Table Name="Deviceblacklist" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Devicedefaultsettings" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Difficulty" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="GroupSizeHealthCurveID" Type="ushort" />
		<Field Name="GroupSizeDmgCurveID" Type="ushort" />
		<Field Name="GroupSizeSpellPointsCurveID" Type="ushort" />
		<Field Name="FallbackDifficultyID" Type="byte" />
		<Field Name="InstanceType" Type="byte" />
		<Field Name="MinPlayers" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="OldEnumValue" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ToggleDifficultyID" Type="byte" />
		<Field Name="ItemBonusTreeModID" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="Dissolveeffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="float" />
		<Field Name="Field7" Type="float" />
		<Field Name="Field8" Type="float" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="ushort" />
		<Field Name="FieldC" Type="int" />
		<Field Name="FieldD" Type="int" />
		<Field Name="FieldE" Type="byte" />
	</Table>
	<Table Name="Driverblacklist" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="DungeonEncounter" Build="25632">
		<Field Name="Name" Type="string" />
		<Field Name="CreatureDisplayID" Type="uint" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="Bit" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OrderIndex" Type="int" />
		<Field Name="TextureFileDataID" Type="uint" />
	</Table>
	<Table Name="Dungeonmap" Build="25632">
		<Field Name="Field0" Type="float" ArraySize="2" />
		<Field Name="Field1" Type="float" ArraySize="2" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Dungeonmapchunk" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
	</Table>
	<Table Name="DurabilityCosts" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WeaponSubClassCost" Type="ushort" ArraySize="21" />
		<Field Name="ArmorSubClassCost" Type="ushort" ArraySize="8" />
	</Table>
	<Table Name="DurabilityQuality" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QualityMod" Type="float" />
	</Table>
	<Table Name="Edgegloweffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="float" />
		<Field Name="Field7" Type="float" />
		<Field Name="Field8" Type="float" />
		<Field Name="Field9" Type="float" />
		<Field Name="FieldA" Type="float" />
		<Field Name="FieldB" Type="byte" />
		<Field Name="FieldC" Type="ushort" />
		<Field Name="FieldD" Type="int" />
	</Table>
	<Table Name="Emotes" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="long" />
		<Field Name="EmoteSlashCommand" Type="string" />
		<Field Name="SpellVisualKitID" Type="uint" />
		<Field Name="EmoteFlags" Type="uint" />
		<Field Name="AnimID" Type="ushort" />
		<Field Name="EmoteSpecProc" Type="byte" />
		<Field Name="EmoteSpecProcParam" Type="uint" />
		<Field Name="EmoteSoundID" Type="uint" />
		<Field Name="ClassMask" Type="int" />
	</Table>
	<Table Name="EmotesText" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="EmoteID" Type="ushort" />
	</Table>
	<Table Name="Emotestextdata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="EmotesTextSound" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceId" Type="byte" />
		<Field Name="SexId" Type="byte" />
		<Field Name="ClassId" Type="byte" />
		<Field Name="SoundId" Type="uint" />
	</Table>
	<Table Name="Environmentaldamage" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Exhaustion" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="float" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Faction" Build="25632">
		<Field Name="ReputationRaceMask" Type="ulong" ArraySize="4" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ReputationBase" Type="int" ArraySize="4" />
		<Field Name="ParentFactionModIn" Type="float" />
		<Field Name="ParentFactionModOut" Type="float" />
		<Field Name="ReputationMax" Type="uint" ArraySize="4" />
		<Field Name="ReputationIndex" Type="short" />
		<Field Name="ReputationClassMask" Type="ushort" ArraySize="4" />
		<Field Name="ReputationFlags" Type="ushort" ArraySize="4" />
		<Field Name="ParentFactionID" Type="ushort" />
		<Field Name="ParagonFactionID" Type="ushort" />
		<Field Name="ParentFactionCapIn" Type="byte" />
		<Field Name="ParentFactionCapOut" Type="byte" />
		<Field Name="Expansion" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="FriendshipRepID" Type="byte" />
	</Table>
	<Table Name="Factiongroup" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" />
	</Table>
	<Table Name="FactionTemplate" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Faction" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="Enemies" Type="ushort" ArraySize="4" />
		<Field Name="Friends" Type="ushort" ArraySize="4" />
		<Field Name="Mask" Type="byte" />
		<Field Name="FriendMask" Type="byte" />
		<Field Name="EnemyMask" Type="byte" />
	</Table>
	<Table Name="Footprinttextures" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Footstepterrainlookup" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="uint" />
		<Field Name="Field4" Type="uint" />
	</Table>
	<Table Name="Friendshiprepreaction" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Friendshipreputation" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Fullscreeneffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="float" />
		<Field Name="Field02" Type="float" />
		<Field Name="Field03" Type="float" />
		<Field Name="Field04" Type="float" />
		<Field Name="Field05" Type="int" />
		<Field Name="Field06" Type="float" />
		<Field Name="Field07" Type="float" />
		<Field Name="Field08" Type="float" />
		<Field Name="Field09" Type="float" />
		<Field Name="Field0A" Type="float" />
		<Field Name="Field0B" Type="int" />
		<Field Name="Field0C" Type="float" />
		<Field Name="Field0D" Type="float" />
		<Field Name="Field0E" Type="float" />
		<Field Name="Field0F" Type="float" />
		<Field Name="Field10" Type="float" />
		<Field Name="Field11" Type="int" />
		<Field Name="Field12" Type="float" />
		<Field Name="Field13" Type="float" />
		<Field Name="Field14" Type="int" />
		<Field Name="Field15" Type="int" />
		<Field Name="Field16" Type="float" />
		<Field Name="Field17" Type="float" />
		<Field Name="Field18" Type="byte" />
		<Field Name="Field19" Type="ushort" />
		<Field Name="Field1A" Type="ushort" />
		<Field Name="Field1B" Type="int" />
	</Table>
	<Table Name="Gameobjectartkit" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" ArraySize="3" />
	</Table>
	<Table Name="Gameobjectdiffanimmap" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="GameObjectDisplayInfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="uint" />
		<Field Name="GeoBoxMin" Type="float" ArraySize="3" />
		<Field Name="GeoBoxMax" Type="float" ArraySize="3" />
		<Field Name="OverrideLootEffectScale" Type="float" />
		<Field Name="OverrideNameScale" Type="float" />
		<Field Name="ObjectEffectPackageID" Type="ushort" />
	</Table>
	<Table Name="Gameobjectdisplayinfoxsoundkit" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="uint" />
	</Table>
	<Table Name="GameObjects" Build="25632">
		<Field Name="Name" Type="string" />
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="RotationX" Type="float" />
		<Field Name="RotationY" Type="float" />
		<Field Name="RotationZ" Type="float" />
		<Field Name="RotationW" Type="float" />
		<Field Name="Size" Type="float" />
		<Field Name="Data" Type="int" ArraySize="8" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="DisplayID" Type="ushort" />
		<Field Name="PhaseID" Type="ushort" />
		<Field Name="PhaseGroupID" Type="ushort" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Gametips" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="GarrAbility" Build="25632">
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="IconFileDataID" Type="uint" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="OtherFactionGarrAbilityID" Type="ushort" />
		<Field Name="GarrAbilityCategoryID" Type="byte" />
		<Field Name="FollowerTypeID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Garrabilitycategory" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Garrabilityeffect" Build="25632">
		<Field Name="Field0" Type="float" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrBuilding" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="NameAlliance" Type="string" />
		<Field Name="NameHorde" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Tooltip" Type="string" />
		<Field Name="HordeGameObjectID" Type="uint" />
		<Field Name="AllianceGameObjectID" Type="uint" />
		<Field Name="IconFileDataID" Type="uint" />
		<Field Name="CostCurrencyID" Type="ushort" />
		<Field Name="HordeTexPrefixKitID" Type="ushort" />
		<Field Name="AllianceTexPrefixKitID" Type="ushort" />
		<Field Name="AllianceActivationScenePackageID" Type="ushort" />
		<Field Name="HordeActivationScenePackageID" Type="ushort" />
		<Field Name="FollowerRequiredGarrAbilityID" Type="ushort" />
		<Field Name="FollowerGarrAbilityEffectID" Type="ushort" />
		<Field Name="CostMoney" Type="short" />
		<Field Name="Unknown" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="Level" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MaxShipments" Type="byte" />
		<Field Name="GarrTypeID" Type="byte" />
		<Field Name="BuildDuration" Type="int" />
		<Field Name="CostCurrencyAmount" Type="int" />
		<Field Name="BonusAmount" Type="int" />
	</Table>
	<Table Name="Garrbuildingdoodadset" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="GarrBuildingPlotInst" Build="25632">
		<Field Name="LandmarkOffset" Type="float" ArraySize="2" />
		<Field Name="UiTextureAtlasMemberID" Type="ushort" />
		<Field Name="GarrSiteLevelPlotInstID" Type="ushort" />
		<Field Name="GarrBuildingID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrClassSpec" Build="25632">
		<Field Name="NameMale" Type="string" />
		<Field Name="NameFemale" Type="string" />
		<Field Name="NameGenderless" Type="string" />
		<Field Name="ClassAtlasID" Type="ushort" />
		<Field Name="GarrFollItemSetID" Type="ushort" />
		<Field Name="Limit" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Garrclassspecplayercond" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="byte" />
	</Table>
	<Table Name="Garrencounter" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field6" Type="byte" />
	</Table>
	<Table Name="Garrencountersetxencounter" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
	</Table>
	<Table Name="Garrencounterxmechanic" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Garrfollitemsetmember" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="GarrFollower" Build="25632">
		<Field Name="HordeSourceText" Type="string" />
		<Field Name="AllianceSourceText" Type="string" />
		<Field Name="Name" Type="string" />
		<Field Name="HordeCreatureID" Type="uint" />
		<Field Name="AllianceCreatureID" Type="uint" />
		<Field Name="HordePortraitIconID" Type="uint" />
		<Field Name="AlliancePortraitIconID" Type="uint" />
		<Field Name="HordeAddedBroadcastTextID" Type="uint" />
		<Field Name="AllianceAddedBroadcastTextID" Type="uint" />
		<Field Name="HordeGarrFollItemSetID" Type="ushort" />
		<Field Name="AllianceGarrFollItemSetID" Type="ushort" />
		<Field Name="ItemLevelWeapon" Type="ushort" />
		<Field Name="ItemLevelArmor" Type="ushort" />
		<Field Name="HordeListPortraitTextureKitID" Type="ushort" />
		<Field Name="AllianceListPortraitTextureKitID" Type="ushort" />
		<Field Name="FollowerTypeID" Type="byte" />
		<Field Name="HordeUiAnimRaceInfoID" Type="byte" />
		<Field Name="AllianceUiAnimRaceInfoID" Type="byte" />
		<Field Name="Quality" Type="byte" />
		<Field Name="HordeGarrClassSpecID" Type="byte" />
		<Field Name="AllianceGarrClassSpecID" Type="byte" />
		<Field Name="Level" Type="byte" />
		<Field Name="Unknown1" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Unknown2" Type="byte" />
		<Field Name="Unknown3" Type="byte" />
		<Field Name="GarrTypeID" Type="byte" />
		<Field Name="MaxDurability" Type="byte" />
		<Field Name="Class" Type="byte" />
		<Field Name="HordeFlavorTextGarrStringID" Type="byte" />
		<Field Name="AllianceFlavorTextGarrStringID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Garrfollowerlevelxp" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Garrfollowerquality" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="Garrfollowertype" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="Garrfolloweruicreature" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="GarrFollowerXAbility" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrAbilityID" Type="ushort" />
		<Field Name="FactionIndex" Type="byte" />
	</Table>
	<Table Name="Garrfollsupportspell" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Garritemlevelupgradedata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="int" />
	</Table>
	<Table Name="Garrmechanic" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Garrmechanicsetxmechanic" Build="25632">
		<Field Name="Field0" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Garrmechanictype" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Garrmission" Build="25632">
		<Field Name="Field00" Type="string" />
		<Field Name="Field01" Type="string" />
		<Field Name="Field02" Type="string" />
		<Field Name="Field03" Type="int" />
		<Field Name="Field04" Type="int" />
		<Field Name="Field05" Type="float" ArraySize="2" />
		<Field Name="Field06" Type="float" ArraySize="2" />
		<Field Name="Field07" Type="ushort" />
		<Field Name="Field08" Type="ushort" />
		<Field Name="Field09" Type="ushort" />
		<Field Name="Field0A" Type="byte" />
		<Field Name="Field0B" Type="byte" />
		<Field Name="Field0C" Type="byte" />
		<Field Name="Field0D" Type="byte" />
		<Field Name="Field0E" Type="byte" />
		<Field Name="Field0F" Type="byte" />
		<Field Name="Field10" Type="byte" />
		<Field Name="Field11" Type="byte" />
		<Field Name="Field12" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field14" Type="uint" />
		<Field Name="Field15" Type="uint" />
		<Field Name="Field16" Type="uint" />
		<Field Name="Field17" Type="uint" />
		<Field Name="Field18" Type="uint" />
		<Field Name="Field19" Type="uint" />
		<Field Name="Field1A" Type="uint" />
		<Field Name="Field1B" Type="uint" />
	</Table>
	<Table Name="Garrmissiontexture" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="2" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Garrmissiontype" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Garrmissionxencounter" Build="25632">
		<Field Name="Field0" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Garrmissionxfollower" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Garrmssnbonusability" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="GarrPlot" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="AllianceConstructionGameObjectID" Type="uint" />
		<Field Name="HordeConstructionGameObjectID" Type="uint" />
		<Field Name="GarrPlotUICategoryID" Type="byte" />
		<Field Name="PlotType" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MinCount" Type="uint" />
		<Field Name="MaxCount" Type="uint" />
	</Table>
	<Table Name="GarrPlotBuilding" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrPlotID" Type="byte" />
		<Field Name="GarrBuildingID" Type="byte" />
	</Table>
	<Table Name="GarrPlotInstance" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="GarrPlotID" Type="byte" />
	</Table>
	<Table Name="Garrplotuicategory" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="GarrSiteLevel" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TownHall" Type="float" ArraySize="2" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="SiteID" Type="ushort" />
		<Field Name="MovieID" Type="ushort" />
		<Field Name="UpgradeResourceCost" Type="ushort" />
		<Field Name="UpgradeMoneyCost" Type="ushort" />
		<Field Name="Level" Type="byte" />
		<Field Name="UITextureKitID" Type="byte" />
		<Field Name="Level2" Type="byte" />
	</Table>
	<Table Name="GarrSiteLevelPlotInst" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Landmark" Type="float" ArraySize="2" />
		<Field Name="GarrSiteLevelID" Type="ushort" />
		<Field Name="GarrPlotInstanceID" Type="byte" />
		<Field Name="Unknown" Type="byte" />
	</Table>
	<Table Name="Garrspecialization" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="float" ArraySize="2" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="Garrstring" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Garrtalent" Build="25632">
		<Field Name="Field00" Type="string" />
		<Field Name="Field01" Type="string" />
		<Field Name="Field02" Type="int" />
		<Field Name="Field03" Type="int" />
		<Field Name="Field04" Type="byte" />
		<Field Name="Field05" Type="byte" />
		<Field Name="Field06" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field08" Type="int" />
		<Field Name="Field09" Type="int" />
		<Field Name="Field0A" Type="int" />
		<Field Name="Field0B" Type="int" />
		<Field Name="Field0C" Type="int" />
		<Field Name="Field0D" Type="int" />
		<Field Name="Field0E" Type="int" />
		<Field Name="Field0F" Type="int" />
		<Field Name="Field10" Type="int" />
		<Field Name="Field11" Type="int" />
		<Field Name="Field12" Type="int" />
		<Field Name="Field13" Type="int" />
	</Table>
	<Table Name="Garrtalenttree" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Garrtype" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="int" ArraySize="2" />
	</Table>
	<Table Name="Garruianimclassinfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="int" />
	</Table>
	<Table Name="Garruianimraceinfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="float" />
		<Field Name="Field7" Type="float" />
		<Field Name="Field8" Type="float" />
		<Field Name="Field9" Type="float" />
		<Field Name="FieldA" Type="float" />
		<Field Name="FieldB" Type="float" />
		<Field Name="FieldC" Type="float" />
		<Field Name="FieldD" Type="byte" />
	</Table>
	<Table Name="GemProperties" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="uint" />
		<Field Name="EnchantID" Type="ushort" />
		<Field Name="MinItemLevel" Type="ushort" />
	</Table>
	<Table Name="Globalstrings" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="GlyphBindableSpell" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
	</Table>
	<Table Name="Glyphexclusivecategory" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="GlyphProperties" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="SpellIconID" Type="ushort" />
		<Field Name="Type" Type="byte" />
		<Field Name="GlyphExclusiveCategoryID" Type="byte" />
	</Table>
	<Table Name="GlyphRequiredSpec" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrSpecializationID" Type="ushort" />
	</Table>
	<Table Name="Gmsurveyanswers" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Gmsurveycurrentsurvey" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
	</Table>
	<Table Name="Gmsurveyquestions" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Gmsurveysurveys" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" ArraySize="15" />
	</Table>
	<Table Name="Groundeffectdoodad" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="int" />
	</Table>
	<Table Name="Groundeffecttexture" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" ArraySize="4" />
		<Field Name="Field2" Type="byte" ArraySize="4" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Groupfinderactivity" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="byte" />
		<Field Name="FieldC" Type="byte" />
		<Field Name="FieldD" Type="byte" />
		<Field Name="FieldE" Type="byte" />
	</Table>
	<Table Name="Groupfinderactivitygrp" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Groupfindercategory" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="GuildColorBackground" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="GuildColorBorder" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="GuildColorEmblem" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="GuildPerkSpells" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
	</Table>
	<Table Name="Heirloom" Build="25632">
		<Field Name="SourceText" Type="string" />
		<Field Name="ItemID" Type="uint" />
		<Field Name="OldItem" Type="uint" ArraySize="2" />
		<Field Name="NextDifficultyItemID" Type="uint" />
		<Field Name="UpgradeItemID" Type="uint" ArraySize="3" />
		<Field Name="ItemBonusListID" Type="ushort" ArraySize="3" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Source" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Helmetanimscaling" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="int" />
	</Table>
	<Table Name="Helmetgeosetvisdata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" ArraySize="9" />
	</Table>
	<Table Name="Highlightcolor" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Holidaydescriptions" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Holidaynames" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Holidays" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Date" Type="uint" ArraySize="16" />
		<Field Name="Duration" Type="ushort" ArraySize="10" />
		<Field Name="Region" Type="ushort" />
		<Field Name="Looping" Type="byte" />
		<Field Name="CalendarFlags" Type="byte" ArraySize="10" />
		<Field Name="Priority" Type="byte" />
		<Field Name="CalendarFilterType" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="HolidayNameID" Type="uint" />
		<Field Name="HolidayDescriptionID" Type="uint" />
		<Field Name="TextureFileDataID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="ImportPriceArmor" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClothFactor" Type="float" />
		<Field Name="LeatherFactor" Type="float" />
		<Field Name="MailFactor" Type="float" />
		<Field Name="PlateFactor" Type="float" />
	</Table>
	<Table Name="ImportPriceQuality" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Factor" Type="float" />
	</Table>
	<Table Name="ImportPriceShield" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Factor" Type="float" />
	</Table>
	<Table Name="ImportPriceWeapon" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Factor" Type="float" />
	</Table>
	<Table Name="Invasionclientdata" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="float" ArraySize="2" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="ushort" />
	</Table>
	<Table Name="Item" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="uint" />
		<Field Name="Class" Type="byte" />
		<Field Name="SubClass" Type="byte" />
		<Field Name="SoundOverrideSubclass" Type="byte" />
		<Field Name="Material" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="Sheath" Type="byte" />
		<Field Name="GroupSoundsID" Type="byte" />
	</Table>
	<Table Name="ItemAppearance" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayID" Type="uint" />
		<Field Name="IconFileDataID" Type="uint" />
		<Field Name="UIOrder" Type="uint" />
		<Field Name="ObjectComponentSlot" Type="byte" />
	</Table>
	<Table Name="Itemappearancexuicamera" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="ItemArmorQuality" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QualityMod" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemArmorShield" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemArmorTotal" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="float" ArraySize="4" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemBagFamily" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="ItemBonus" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="int" ArraySize="3" />
		<Field Name="BonusListID" Type="ushort" />
		<Field Name="Type" Type="byte" />
		<Field Name="Index" Type="byte" />
	</Table>
	<Table Name="ItemBonusListLevelDelta" Build="25632">
		<Field Name="Delta" Type="short" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ItemBonusTreeNode" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SubTreeID" Type="ushort" />
		<Field Name="BonusListID" Type="ushort" />
		<Field Name="ItemLevelSelectorID" Type="ushort" />
		<Field Name="BonusTreeModID" Type="byte" />
	</Table>
	<Table Name="ItemChildEquipment" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AltItemID" Type="uint" />
		<Field Name="AltEquipmentSlot" Type="byte" />
	</Table>
	<Table Name="ItemClass" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="PriceMod" Type="float" />
		<Field Name="OldEnumValue" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Itemcontextpickerentry" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="int" />
	</Table>
	<Table Name="ItemCurrencyCost" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="uint" />
	</Table>
	<Table Name="ItemDamageAmmo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DPS" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemDamageOneHand" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DPS" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemDamageOneHandCaster" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DPS" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemDamageTwoHand" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DPS" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemDamageTwoHandCaster" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DPS" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemDisenchantLoot" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinItemLevel" Type="ushort" />
		<Field Name="MaxItemLevel" Type="ushort" />
		<Field Name="RequiredDisenchantSkill" Type="ushort" />
		<Field Name="ItemSubClass" Type="byte" />
		<Field Name="ItemQuality" Type="byte" />
		<Field Name="Expansion" Type="byte" />
	</Table>
	<Table Name="Itemdisplayinfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="int" />
		<Field Name="Field02" Type="int" />
		<Field Name="ItemVisualID" Type="int" />
		<Field Name="Field04" Type="int" />
		<Field Name="Field05" Type="int" />
		<Field Name="Field06" Type="int" />
		<Field Name="Field07" Type="int" />
		<Field Name="Field08" Type="int" />
		<Field Name="Field09" Type="int" />
		<Field Name="Field0A" Type="int" />
		<Field Name="Modelfiledata" Type="int" ArraySize="2" />
		<Field Name="Texturefiledata" Type="int" ArraySize="2" />
		<Field Name="Field0D" Type="int" ArraySize="4" />
		<Field Name="Field0E" Type="int" ArraySize="4" />
		<Field Name="GeosetFlag" Type="int" ArraySize="2" />
	</Table>
	<Table Name="Itemdisplayinfomaterialres" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Itemdisplayxuicamera" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="ItemEffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="Cooldown" Type="int" />
		<Field Name="CategoryCooldown" Type="int" />
		<Field Name="Charges" Type="short" />
		<Field Name="Category" Type="ushort" />
		<Field Name="ChrSpecializationID" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Trigger" Type="byte" />
	</Table>
	<Table Name="ItemExtendedCost" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RequiredItem" Type="uint" ArraySize="5" />
		<Field Name="RequiredCurrencyCount" Type="uint" ArraySize="5" />
		<Field Name="RequiredItemCount" Type="ushort" ArraySize="5" />
		<Field Name="RequiredPersonalArenaRating" Type="ushort" />
		<Field Name="RequiredCurrency" Type="ushort" ArraySize="5" />
		<Field Name="RequiredArenaSlot" Type="byte" />
		<Field Name="RequiredFactionId" Type="byte" />
		<Field Name="RequiredFactionStanding" Type="byte" />
		<Field Name="RequirementFlags" Type="byte" />
		<Field Name="RequiredAchievement" Type="byte" />
	</Table>
	<Table Name="Itemgroupsounds" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" ArraySize="4" />
	</Table>
	<Table Name="ItemLevelSelector" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevel" Type="ushort" />
		<Field Name="ItemLevelSelectorQualitySetID" Type="ushort" />
	</Table>
	<Table Name="ItemLevelSelectorQuality" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemBonusListID" Type="uint" />
		<Field Name="Quality" Type="byte" />
	</Table>
	<Table Name="ItemLevelSelectorQualitySet" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevelMin" Type="ushort" />
		<Field Name="ItemLevelMax" Type="ushort" />
	</Table>
	<Table Name="ItemLimitCategory" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Quantity" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Itemlimitcategorycondition" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="ItemModifiedAppearance" Build="25632">
		<Field Name="ItemID" Type="uint" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AppearanceModID" Type="byte" />
		<Field Name="AppearanceID" Type="ushort" />
		<Field Name="Index" Type="byte" />
		<Field Name="SourceType" Type="byte" />
	</Table>
	<Table Name="Itemmodifiedappearanceextra" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Itemnamedescription" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
	</Table>
	<Table Name="Itempetfood" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="ItemPriceBase" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ArmorFactor" Type="float" />
		<Field Name="WeaponFactor" Type="float" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemRandomProperties" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Enchantment" Type="ushort" ArraySize="5" />
	</Table>
	<Table Name="ItemRandomSuffix" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Enchantment" Type="ushort" ArraySize="5" />
		<Field Name="AllocationPct" Type="ushort" ArraySize="5" />
	</Table>
	<Table Name="Itemrangeddisplayinfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="int" />
	</Table>
	<Table Name="ItemSearchName" Build="25632">
		<Field Name="AllowableRace" Type="ulong" />
		<Field Name="Name" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="uint" ArraySize="3" />
		<Field Name="ItemLevel" Type="ushort" />
		<Field Name="Quality" Type="byte" />
		<Field Name="RequiredExpansion" Type="byte" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="RequiredReputationFaction" Type="ushort" />
		<Field Name="RequiredReputationRank" Type="byte" />
		<Field Name="AllowableClass" Type="int" />
		<Field Name="RequiredSkill" Type="ushort" />
		<Field Name="RequiredSkillRank" Type="ushort" />
		<Field Name="RequiredSpell" Type="uint" />
	</Table>
	<Table Name="ItemSet" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="ItemID" Type="uint" ArraySize="17" />
		<Field Name="RequiredSkillRank" Type="ushort" />
		<Field Name="RequiredSkill" Type="uint" />
		<Field Name="Flags" Type="uint" />
	</Table>
	<Table Name="ItemSetSpell" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="ChrSpecID" Type="ushort" />
		<Field Name="Threshold" Type="byte" />
	</Table>
	<Table Name="ItemSparse" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AllowableRace" Type="long" />
		<Field Name="Name" Type="string" />
		<Field Name="Name2" Type="string" />
		<Field Name="Name3" Type="string" />
		<Field Name="Name4" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Flags" Type="uint" ArraySize="4" />
		<Field Name="Unk1" Type="float" />
		<Field Name="Unk2" Type="float" />
		<Field Name="BuyCount" Type="uint" />
		<Field Name="BuyPrice" Type="uint" />
		<Field Name="SellPrice" Type="uint" />
		<Field Name="RequiredSpell" Type="uint" />
		<Field Name="MaxCount" Type="uint" />
		<Field Name="Stackable" Type="uint" />
		<Field Name="ItemStatAllocation" Type="int" ArraySize="10" />
		<Field Name="ItemStatSocketCostMultiplier" Type="float" ArraySize="10" />
		<Field Name="RangedModRange" Type="float" />
		<Field Name="BagFamily" Type="uint" />
		<Field Name="ArmorDamageModifier" Type="float" />
		<Field Name="Duration" Type="uint" />
		<Field Name="StatScalingFactor" Type="float" />
		<Field Name="AllowableClass" Type="short" />
		<Field Name="ItemLevel" Type="ushort" />
		<Field Name="RequiredSkill" Type="ushort" />
		<Field Name="RequiredSkillRank" Type="ushort" />
		<Field Name="RequiredReputationFaction" Type="ushort" />
		<Field Name="ItemStatValue" Type="short" ArraySize="10" />
		<Field Name="ScalingStatDistribution" Type="ushort" />
		<Field Name="Delay" Type="ushort" />
		<Field Name="PageText" Type="ushort" />
		<Field Name="StartQuest" Type="ushort" />
		<Field Name="LockID" Type="ushort" />
		<Field Name="RandomProperty" Type="ushort" />
		<Field Name="RandomSuffix" Type="ushort" />
		<Field Name="ItemSet" Type="ushort" />
		<Field Name="Area" Type="ushort" />
		<Field Name="Map" Type="ushort" />
		<Field Name="TotemCategory" Type="ushort" />
		<Field Name="SocketBonus" Type="ushort" />
		<Field Name="GemProperties" Type="ushort" />
		<Field Name="ItemLimitCategory" Type="ushort" />
		<Field Name="HolidayID" Type="ushort" />
		<Field Name="RequiredTransmogHolidayID" Type="ushort" />
		<Field Name="ItemNameDescriptionID" Type="ushort" />
		<Field Name="Quality" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="RequiredHonorRank" Type="byte" />
		<Field Name="RequiredCityRank" Type="byte" />
		<Field Name="RequiredReputationRank" Type="byte" />
		<Field Name="ContainerSlots" Type="byte" />
		<Field Name="ItemStatType" Type="byte" ArraySize="10" />
		<Field Name="DamageType" Type="byte" />
		<Field Name="Bonding" Type="byte" />
		<Field Name="LanguageID" Type="byte" />
		<Field Name="PageMaterial" Type="byte" />
		<Field Name="Material" Type="byte" />
		<Field Name="Sheath" Type="byte" />
		<Field Name="SocketColor" Type="byte" ArraySize="3" />
		<Field Name="CurrencySubstitutionID" Type="byte" />
		<Field Name="CurrencySubstitutionCount" Type="byte" />
		<Field Name="ArtifactID" Type="byte" />
		<Field Name="RequiredExpansion" Type="byte" />
	</Table>
	<Table Name="ItemSpec" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpecID" Type="ushort" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
		<Field Name="ItemType" Type="byte" />
		<Field Name="PrimaryStat" Type="byte" />
		<Field Name="SecondaryStat" Type="byte" />
	</Table>
	<Table Name="ItemSpecOverride" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpecID" Type="ushort" />
	</Table>
	<Table Name="Itemsubclass" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
	</Table>
	<Table Name="Itemsubclassmask" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="ItemUpgrade" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CurrencyCost" Type="uint" />
		<Field Name="PrevItemUpgradeID" Type="ushort" />
		<Field Name="CurrencyID" Type="ushort" />
		<Field Name="ItemUpgradePathID" Type="byte" />
		<Field Name="ItemLevelBonus" Type="byte" />
	</Table>
	<Table Name="Itemvisuals" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" ArraySize="5" />
	</Table>
	<Table Name="ItemXBonusTree" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BonusTreeID" Type="ushort" />
	</Table>
	<Table Name="Journalencounter" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="float" ArraySize="2" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="ushort" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="int" />
	</Table>
	<Table Name="Journalencountercreature" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Journalencounteritem" Build="25632">
		<Field Name="Field0" Type="int" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Journalencountersection" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="int" />
		<Field Name="Field7" Type="ushort" />
		<Field Name="Field8" Type="ushort" />
		<Field Name="Field9" Type="ushort" />
		<Field Name="FieldA" Type="ushort" />
		<Field Name="FieldB" Type="ushort" />
		<Field Name="FieldC" Type="ushort" />
		<Field Name="FieldD" Type="byte" />
		<Field Name="FieldE" Type="byte" />
		<Field Name="FieldF" Type="byte" />
	</Table>
	<Table Name="Journalencounterxdifficulty" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
	</Table>
	<Table Name="Journalencounterxmaploc" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="2" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" />
	</Table>
	<Table Name="Journalinstance" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="ushort" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Journalitemxdifficulty" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
	</Table>
	<Table Name="Journalsectionxdifficulty" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
	</Table>
	<Table Name="Journaltier" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Journaltierxinstance" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Keychain" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Key" Type="byte" ArraySize="32" />
	</Table>
	<Table Name="Keystoneaffix" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Languages" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Languagewords" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Lfgdungeonexpansion" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="byte" />
	</Table>
	<Table Name="Lfgdungeongroup" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="LFGDungeons" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Flags" Type="uint" />
		<Field Name="MinItemLevel" Type="float" />
		<Field Name="MaxLevel" Type="ushort" />
		<Field Name="TargetLevelMax" Type="ushort" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="RandomID" Type="ushort" />
		<Field Name="ScenarioID" Type="ushort" />
		<Field Name="LastBossJournalEncounterID" Type="ushort" />
		<Field Name="BonusReputationAmount" Type="ushort" />
		<Field Name="MentorItemLevel" Type="ushort" />
		<Field Name="PlayerConditionID" Type="ushort" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="TargetLevel" Type="byte" />
		<Field Name="TargetLevelMin" Type="byte" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="Faction" Type="byte" />
		<Field Name="Expansion" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="GroupID" Type="byte" />
		<Field Name="CountTank" Type="byte" />
		<Field Name="CountHealer" Type="byte" />
		<Field Name="CountDamage" Type="byte" />
		<Field Name="MinCountTank" Type="byte" />
		<Field Name="MinCountHealer" Type="byte" />
		<Field Name="MinCountDamage" Type="byte" />
		<Field Name="SubType" Type="byte" />
		<Field Name="MentorCharLevel" Type="byte" />
		<Field Name="TextureFileDataID" Type="int" />
		<Field Name="RewardIconFileDataID" Type="int" />
		<Field Name="ProposalTextureFileDataID" Type="int" />
	</Table>
	<Table Name="Lfgdungeonsgroupingmap" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Lfgrolerequirement" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="int" />
	</Table>
	<Table Name="Light" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="FalloffStart" Type="float" />
		<Field Name="FalloffEnd" Type="float" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="LightParamsID" Type="ushort" ArraySize="8" />
	</Table>
	<Table Name="Lightdata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="int" />
		<Field Name="Field02" Type="int" />
		<Field Name="Field03" Type="int" />
		<Field Name="Field04" Type="int" />
		<Field Name="Field05" Type="int" />
		<Field Name="Field06" Type="int" />
		<Field Name="Field07" Type="int" />
		<Field Name="Field08" Type="int" />
		<Field Name="Field09" Type="int" />
		<Field Name="Field0A" Type="int" />
		<Field Name="Field0B" Type="int" />
		<Field Name="Field0C" Type="int" />
		<Field Name="Field0D" Type="int" />
		<Field Name="Field0E" Type="int" />
		<Field Name="Field0F" Type="int" />
		<Field Name="Field10" Type="int" />
		<Field Name="Field11" Type="int" />
		<Field Name="Field12" Type="int" />
		<Field Name="Field13" Type="float" />
		<Field Name="Field14" Type="float" />
		<Field Name="Field15" Type="float" />
		<Field Name="Field16" Type="float" />
		<Field Name="Field17" Type="float" />
		<Field Name="Field18" Type="float" />
		<Field Name="Field19" Type="float" />
		<Field Name="Field1A" Type="float" />
		<Field Name="Field1B" Type="float" />
		<Field Name="Field1C" Type="int" />
		<Field Name="Field1D" Type="int" />
		<Field Name="Field1E" Type="int" />
		<Field Name="Field1F" Type="int" />
		<Field Name="Field20" Type="int" />
		<Field Name="Field21" Type="int" />
		<Field Name="Field22" Type="ushort" />
	</Table>
	<Table Name="Lightparams" Build="25632">
		<Field Name="Field00" Type="float" />
		<Field Name="Field04" Type="float" />
		<Field Name="Field08" Type="float" />
		<Field Name="Field0C" Type="float" />
		<Field Name="Field10" Type="float" />
		<Field Name="Field14" Type="int" ArraySize="3" />
		<Field Name="LightSkyboxID" Type="ushort" />
		<Field Name="Field22" Type="byte" />
		<Field Name="Field23" Type="byte" />
		<Field Name="Field24" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Lightskybox" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SkyboxPathName" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Liquidmaterial" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Liquidobject" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="LiquidType" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Texture" Type="string" ArraySize="6" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="MaxDarkenDepth" Type="float" />
		<Field Name="FogDarkenIntensity" Type="float" />
		<Field Name="AmbDarkenIntensity" Type="float" />
		<Field Name="DirDarkenIntensity" Type="float" />
		<Field Name="ParticleScale" Type="float" />
		<Field Name="Color" Type="uint" ArraySize="2" />
		<Field Name="Float" Type="float" ArraySize="18" />
		<Field Name="Int" Type="uint" ArraySize="4" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="LightID" Type="ushort" />
		<Field Name="Type" Type="byte" />
		<Field Name="ParticleMovement" Type="byte" />
		<Field Name="ParticleTexSlots" Type="byte" />
		<Field Name="MaterialID" Type="byte" />
		<Field Name="DepthTexCount" Type="byte" ArraySize="6" />
		<Field Name="SoundID" Type="uint" />
	</Table>
	<Table Name="Loadingscreens" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Loadingscreentaxisplines" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="10" />
		<Field Name="Field2" Type="float" ArraySize="10" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Locale" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Location" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="3" />
		<Field Name="Field2" Type="float" ArraySize="3" />
	</Table>
	<Table Name="Lock" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Index" Type="uint" ArraySize="8" />
		<Field Name="Skill" Type="ushort" ArraySize="8" />
		<Field Name="Type" Type="byte" ArraySize="8" />
		<Field Name="Action" Type="byte" ArraySize="8" />
	</Table>
	<Table Name="Locktype" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Lookatcontroller" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="float" />
		<Field Name="Field02" Type="float" />
		<Field Name="Field03" Type="float" />
		<Field Name="Field04" Type="float" />
		<Field Name="Field05" Type="ushort" />
		<Field Name="Field06" Type="ushort" />
		<Field Name="Field07" Type="ushort" />
		<Field Name="Field08" Type="ushort" />
		<Field Name="Field09" Type="byte" />
		<Field Name="Field0A" Type="byte" />
		<Field Name="Field0B" Type="byte" />
		<Field Name="Field0C" Type="byte" />
		<Field Name="Field0D" Type="byte" />
		<Field Name="Field0E" Type="byte" />
		<Field Name="Field0F" Type="ushort" />
		<Field Name="Field10" Type="int" />
		<Field Name="Field11" Type="byte" />
		<Field Name="Field12" Type="int" />
	</Table>
	<Table Name="MailTemplate" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Body" Type="string" />
	</Table>
	<Table Name="Managedworldstate" Build="25632">
		<Field Name="Field0" Type="int" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="int" />
		<Field Name="Field7" Type="int" />
		<Field Name="Field8" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Managedworldstatebuff" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Managedworldstateinput" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Manifestinterfaceactionicon" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Manifestinterfacedata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
	</Table>
	<Table Name="Manifestinterfaceitemicon" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Manifestinterfacetocdata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Manifestmp3" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Map" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Directory" Type="string" />
		<Field Name="MapName" Type="string" />
		<Field Name="MapDescription0" Type="string" />
		<Field Name="MapDescription1" Type="string" />
		<Field Name="ShortDescription" Type="string" />
		<Field Name="LongDescription" Type="string" />
		<Field Name="Flags" Type="uint" ArraySize="2" />
		<Field Name="MinimapIconScale" Type="float" />
		<Field Name="CorpsePos" Type="float" ArraySize="2" />
		<Field Name="AreaTableID" Type="ushort" />
		<Field Name="LoadingScreenID" Type="ushort" />
		<Field Name="CorpseMapID" Type="ushort" />
		<Field Name="TimeOfDayOverride" Type="ushort" />
		<Field Name="ParentMapID" Type="ushort" />
		<Field Name="CosmeticParentMapID" Type="ushort" />
		<Field Name="WindSettingsID" Type="ushort" />
		<Field Name="InstanceType" Type="byte" />
		<Field Name="unk5" Type="byte" />
		<Field Name="ExpansionID" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="TimeOffset" Type="byte" />
	</Table>
	<Table Name="Mapcelestialbody" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Mapchallengemode" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" ArraySize="3" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="MapDifficulty" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Message_lang" Type="string" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="RaidDurationType" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="LockID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ItemBonusTreeModID" Type="byte" />
		<Field Name="Context" Type="uint" />
	</Table>
	<Table Name="Mapdifficultyxcondition" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Maploadingscreen" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="2" />
		<Field Name="Field2" Type="float" ArraySize="2" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Marketingpromotionsxlocale" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="Material" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
	</Table>
	<Table Name="Minortalent" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
	</Table>
	<Table Name="Missiletargeting" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="float" />
		<Field Name="Field7" Type="float" />
		<Field Name="Field8" Type="float" ArraySize="2" />
		<Field Name="Field9" Type="float" />
		<Field Name="FieldA" Type="int" />
		<Field Name="FieldB" Type="int" />
		<Field Name="FieldC" Type="int" ArraySize="2" />
	</Table>
	<Table Name="Modelanimcloakdampening" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Modelfiledata" Build="25632">
		<Field Name="Field0" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Modelribbonquality" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
	</Table>
	<Table Name="ModifierTree" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Asset" Type="uint" ArraySize="2" />
		<Field Name="Parent" Type="uint" />
		<Field Name="Type" Type="byte" />
		<Field Name="Unk700" Type="byte" />
		<Field Name="Operator" Type="byte" />
		<Field Name="Amount" Type="byte" />
	</Table>
	<Table Name="Mount" Build="25632">
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="SourceDescription" Type="string" />
		<Field Name="SpellId" Type="uint" />
		<Field Name="CameraPivotMultiplier" Type="float" />
		<Field Name="MountTypeId" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="Source" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionId" Type="uint" />
		<Field Name="UiModelSceneID" Type="int" />
	</Table>
	<Table Name="MountCapability" Build="25632">
		<Field Name="RequiredSpell" Type="uint" />
		<Field Name="SpeedModSpell" Type="uint" />
		<Field Name="RequiredRidingSkill" Type="ushort" />
		<Field Name="RequiredArea" Type="ushort" />
		<Field Name="RequiredMap" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RequiredAura" Type="uint" />
	</Table>
	<Table Name="MountTypeXCapability" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MountTypeID" Type="ushort" />
		<Field Name="MountCapabilityID" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="MountXDisplay" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayID" Type="uint" />
		<Field Name="PlayerConditionID" Type="uint" />
	</Table>
	<Table Name="Movie" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AudioFileDataID" Type="uint" />
		<Field Name="SubtitleFileDataID" Type="uint" />
		<Field Name="Volume" Type="byte" />
		<Field Name="KeyID" Type="byte" />
	</Table>
	<Table Name="Moviefiledata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
	</Table>
	<Table Name="Movievariation" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="NameGen" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Race" Type="byte" />
		<Field Name="Sex" Type="byte" />
	</Table>
	<Table Name="NamesProfanity" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Language" Type="byte" />
	</Table>
	<Table Name="NamesReserved" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="NamesReservedLocale" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="LocaleMask" Type="byte" />
	</Table>
	<Table Name="Npcmodelitemslotdisplayinfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayID" Type="int" />
		<Field Name="Slot" Type="byte" />
	</Table>
	<Table Name="Npcsounds" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" ArraySize="4" />
	</Table>
	<Table Name="Objecteffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="3" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="int" />
		<Field Name="Field8" Type="int" />
	</Table>
	<Table Name="Objecteffectmodifier" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="4" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Objecteffectpackageelem" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Outlineeffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="int" />
	</Table>
	<Table Name="OverrideSpellData" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" ArraySize="10" />
		<Field Name="PlayerActionbarFileDataID" Type="uint" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Pagetextmaterial" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Paperdollitemframe" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Paragonreputation" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Particlecolor" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" ArraySize="3" />
		<Field Name="Field2" Type="int" ArraySize="3" />
		<Field Name="Field3" Type="int" ArraySize="3" />
	</Table>
	<Table Name="Path" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="Pathnode" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Pathnodeproperty" Build="25632">
		<Field Name="Field0" Type="ushort" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field4" Type="int" />
	</Table>
	<Table Name="Pathproperty" Build="25632">
		<Field Name="Field0" Type="int" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Phase" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="ushort" />
	</Table>
	<Table Name="Phaseshiftzonesounds" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="ushort" />
		<Field Name="FieldB" Type="ushort" />
		<Field Name="FieldC" Type="byte" />
		<Field Name="FieldD" Type="byte" />
	</Table>
	<Table Name="PhaseXPhaseGroup" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PhaseID" Type="ushort" />
	</Table>
	<Table Name="PlayerCondition" Build="25632">
		<Field Name="RaceMask" Type="long" />
		<Field Name="FailureDescription" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MinLevel" Type="ushort" />
		<Field Name="MaxLevel" Type="ushort" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="Gender" Type="byte" />
		<Field Name="NativeGender" Type="byte" />
		<Field Name="SkillLogic" Type="uint" />
		<Field Name="LanguageID" Type="byte" />
		<Field Name="MinLanguage" Type="byte" />
		<Field Name="MaxLanguage" Type="int" />
		<Field Name="MaxFactionID" Type="ushort" />
		<Field Name="MaxReputation" Type="byte" />
		<Field Name="ReputationLogic" Type="uint" />
		<Field Name="Unknown1" Type="byte" />
		<Field Name="MinPVPRank" Type="byte" />
		<Field Name="MaxPVPRank" Type="byte" />
		<Field Name="PvpMedal" Type="byte" />
		<Field Name="PrevQuestLogic" Type="uint" />
		<Field Name="CurrQuestLogic" Type="uint" />
		<Field Name="CurrentCompletedQuestLogic" Type="uint" />
		<Field Name="SpellLogic" Type="uint" />
		<Field Name="ItemLogic" Type="uint" />
		<Field Name="ItemFlags" Type="byte" />
		<Field Name="AuraSpellLogic" Type="uint" />
		<Field Name="WorldStateExpressionID" Type="ushort" />
		<Field Name="WeatherID" Type="byte" />
		<Field Name="PartyStatus" Type="byte" />
		<Field Name="LifetimeMaxPVPRank" Type="byte" />
		<Field Name="AchievementLogic" Type="uint" />
		<Field Name="LfgLogic" Type="uint" />
		<Field Name="AreaLogic" Type="uint" />
		<Field Name="CurrencyLogic" Type="uint" />
		<Field Name="QuestKillID" Type="ushort" />
		<Field Name="QuestKillLogic" Type="uint" />
		<Field Name="MinExpansionLevel" Type="byte" />
		<Field Name="MaxExpansionLevel" Type="byte" />
		<Field Name="MinExpansionTier" Type="byte" />
		<Field Name="MaxExpansionTier" Type="byte" />
		<Field Name="MinGuildLevel" Type="byte" />
		<Field Name="MaxGuildLevel" Type="byte" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="PhaseID" Type="ushort" />
		<Field Name="PhaseGroupID" Type="uint" />
		<Field Name="MinAvgItemLevel" Type="int" />
		<Field Name="MaxAvgItemLevel" Type="int" />
		<Field Name="MinAvgEquippedItemLevel" Type="ushort" />
		<Field Name="MaxAvgEquippedItemLevel" Type="ushort" />
		<Field Name="ChrSpecializationIndex" Type="byte" />
		<Field Name="ChrSpecializationRole" Type="byte" />
		<Field Name="PowerType" Type="byte" />
		<Field Name="PowerTypeComp" Type="byte" />
		<Field Name="PowerTypeValue" Type="byte" />
		<Field Name="ModifierTreeID" Type="uint" />
		<Field Name="MainHandItemSubclassMask" Type="int" />
		<Field Name="SkillID" Type="ushort" ArraySize="4" />
		<Field Name="MinSkill" Type="ushort" ArraySize="4" />
		<Field Name="MaxSkill" Type="ushort" ArraySize="4" />
		<Field Name="MinFactionID" Type="uint" ArraySize="3" />
		<Field Name="MinReputation" Type="byte" ArraySize="3" />
		<Field Name="PrevQuestID" Type="ushort" ArraySize="4" />
		<Field Name="CurrQuestID" Type="ushort" ArraySize="4" />
		<Field Name="CurrentCompletedQuestID" Type="ushort" ArraySize="4" />
		<Field Name="SpellID" Type="int" ArraySize="4" />
		<Field Name="ItemID" Type="int" ArraySize="4" />
		<Field Name="ItemCount" Type="uint" ArraySize="4" />
		<Field Name="Explored" Type="ushort" ArraySize="2" />
		<Field Name="Time" Type="uint" ArraySize="2" />
		<Field Name="AuraSpellID" Type="uint" ArraySize="4" />
		<Field Name="AuraCount" Type="byte" ArraySize="4" />
		<Field Name="Achievement" Type="ushort" ArraySize="4" />
		<Field Name="LfgStatus" Type="byte" ArraySize="4" />
		<Field Name="LfgCompare" Type="byte" ArraySize="4" />
		<Field Name="LfgValue" Type="uint" ArraySize="4" />
		<Field Name="AreaID" Type="ushort" ArraySize="4" />
		<Field Name="CurrencyID" Type="uint" ArraySize="4" />
		<Field Name="CurrencyCount" Type="uint" ArraySize="4" />
		<Field Name="QuestKillMonster" Type="uint" ArraySize="6" />
		<Field Name="MovementFlags" Type="int" ArraySize="2" />
	</Table>
	<Table Name="Positioner" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Positionerstate" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="ushort" />
		<Field Name="Field8" Type="int" />
	</Table>
	<Table Name="Positionerstateentry" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="ushort" />
	</Table>
	<Table Name="PowerDisplay" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GlobalStringBaseTag" Type="string" />
		<Field Name="PowerType" Type="byte" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="PowerType" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PowerTypeToken" Type="string" />
		<Field Name="PowerCostToken" Type="string" />
		<Field Name="RegenerationPeace" Type="float" />
		<Field Name="RegenerationCombat" Type="float" />
		<Field Name="MaxPower" Type="short" />
		<Field Name="RegenerationDelay" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="PowerTypeEnum" Type="byte" />
		<Field Name="RegenerationMin" Type="byte" />
		<Field Name="RegenerationCenter" Type="byte" />
		<Field Name="RegenerationMax" Type="byte" />
		<Field Name="UIModifier" Type="byte" />
	</Table>
	<Table Name="PrestigeLevelInfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PrestigeText" Type="string" />
		<Field Name="IconID" Type="uint" />
		<Field Name="PrestigeLevel" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Pvpbrackettypes" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="int" ArraySize="4" />
	</Table>
	<Table Name="PVPDifficulty" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BracketID" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
	</Table>
	<Table Name="Pvpitem" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="PvpReward" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HonorLevel" Type="uint" />
		<Field Name="Prestige" Type="uint" />
		<Field Name="RewardPackID" Type="uint" />
	</Table>
	<Table Name="Pvpscalingeffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Pvpscalingeffecttype" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Pvptalent" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="ushort" />
		<Field Name="FieldA" Type="byte" />
	</Table>
	<Table Name="Pvptalentunlock" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="QuestFactionReward" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QuestRewFactionValue" Type="short" ArraySize="10" />
	</Table>
	<Table Name="Questfeedbackeffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
	</Table>
	<Table Name="Questinfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Questline" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Questlinexquest" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="QuestMoneyReward" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Money" Type="uint" ArraySize="10" />
	</Table>
	<Table Name="Questobjective" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="QuestPackageItem" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="uint" />
		<Field Name="QuestPackageID" Type="ushort" />
		<Field Name="FilterType" Type="byte" />
		<Field Name="ItemCount" Type="uint" />
	</Table>
	<Table Name="Questpoiblob" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="Questpoipoint" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="QuestSort" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SortName" Type="string" />
		<Field Name="SortOrder" Type="byte" />
	</Table>
	<Table Name="QuestV2" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UniqueBitFlag" Type="ushort" />
	</Table>
	<Table Name="Questv2clitask" Build="25632">
		<Field Name="Field00" Type="ulong" />
		<Field Name="Field01" Type="string" />
		<Field Name="Field02" Type="string" />
		<Field Name="Field03" Type="int" />
		<Field Name="Field04" Type="ushort" />
		<Field Name="Field05" Type="ushort" />
		<Field Name="Field06" Type="ushort" />
		<Field Name="Field07" Type="ushort" ArraySize="3" />
		<Field Name="Field08" Type="ushort" />
		<Field Name="Field09" Type="ushort" />
		<Field Name="Field0A" Type="byte" />
		<Field Name="Field0B" Type="byte" />
		<Field Name="Field0C" Type="byte" />
		<Field Name="Field0D" Type="byte" />
		<Field Name="Field0E" Type="byte" />
		<Field Name="Field0F" Type="byte" />
		<Field Name="Field10" Type="byte" />
		<Field Name="Field11" Type="byte" />
		<Field Name="Field12" Type="byte" />
		<Field Name="Field13" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field15" Type="int" />
		<Field Name="Field16" Type="int" />
		<Field Name="Field17" Type="int" />
	</Table>
	<Table Name="Questxgroupactivity" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="QuestXP" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Exp" Type="ushort" ArraySize="10" />
	</Table>
	<Table Name="RandPropPoints" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EpicPropertiesPoints" Type="uint" ArraySize="5" />
		<Field Name="RarePropertiesPoints" Type="uint" ArraySize="5" />
		<Field Name="UncommonPropertiesPoints" Type="uint" ArraySize="5" />
	</Table>
	<Table Name="Relicslottierrequirement" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Relictalent" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Researchbranch" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="int" />
	</Table>
	<Table Name="Researchfield" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Researchproject" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field7" Type="int" />
		<Field Name="Field8" Type="byte" />
	</Table>
	<Table Name="Researchsite" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Resistances" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="RewardPack" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Money" Type="uint" />
		<Field Name="ArtifactXPMultiplier" Type="float" />
		<Field Name="ArtifactXPDifficulty" Type="byte" />
		<Field Name="ArtifactCategoryID" Type="byte" />
		<Field Name="TitleID" Type="uint" />
		<Field Name="Unused" Type="uint" />
	</Table>
	<Table Name="Rewardpackxcurrencytype" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="RewardPackXItem" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="uint" />
		<Field Name="Amount" Type="uint" />
	</Table>
	<Table Name="Ribbonquality" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="RulesetItemUpgrade" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="uint" />
		<Field Name="ItemUpgradeID" Type="ushort" />
	</Table>
	<Table Name="SandboxScaling" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinLevel" Type="uint" />
		<Field Name="MaxLevel" Type="uint" />
		<Field Name="Flags" Type="uint" />
	</Table>
	<Table Name="ScalingStatDistribution" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevelCurveID" Type="ushort" />
		<Field Name="MinLevel" Type="uint" />
		<Field Name="MaxLevel" Type="uint" />
	</Table>
	<Table Name="Scenario" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Data" Type="ushort" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Type" Type="byte" />
	</Table>
	<Table Name="Scenarioevententry" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="ScenarioStep" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
		<Field Name="Name" Type="string" />
		<Field Name="ScenarioID" Type="ushort" />
		<Field Name="PreviousStepID" Type="ushort" />
		<Field Name="QuestRewardID" Type="ushort" />
		<Field Name="Step" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CriteriaTreeID" Type="uint" />
		<Field Name="BonusRequiredStepID" Type="uint" />
	</Table>
	<Table Name="SceneScript" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PrevScriptId" Type="ushort" />
		<Field Name="NextScriptId" Type="ushort" />
	</Table>
	<Table Name="SceneScriptGlobalText" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Script" Type="string" />
	</Table>
	<Table Name="SceneScriptPackage" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="Scenescriptpackagemember" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="SceneScriptText" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Script" Type="string" />
	</Table>
	<Table Name="Scheduledinterval" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Scheduledworldstate" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
	</Table>
	<Table Name="Scheduledworldstategroup" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="ushort" />
	</Table>
	<Table Name="Scheduledworldstatexuniqcat" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
	</Table>
	<Table Name="Screeneffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" ArraySize="4" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="int" />
		<Field Name="FieldC" Type="ushort" />
	</Table>
	<Table Name="Screenlocation" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Seamlesssite" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
	</Table>
	<Table Name="Servermessages" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Shadowyeffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="float" />
		<Field Name="Field7" Type="float" />
		<Field Name="Field8" Type="float" />
		<Field Name="Field9" Type="int" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="byte" />
		<Field Name="FieldC" Type="ushort" />
		<Field Name="FieldD" Type="byte" />
	</Table>
	<Table Name="SkillLine" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayName" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="AlternateVerb" Type="string" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="CategoryID" Type="byte" />
		<Field Name="CanLink" Type="byte" />
		<Field Name="IconFileDataID" Type="uint" />
		<Field Name="ParentSkillLineID" Type="uint" />
	</Table>
	<Table Name="SkillLineAbility" Build="25632">
		<Field Name="RaceMask" Type="ulong" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="SupercedesSpell" Type="uint" />
		<Field Name="SkillLine" Type="ushort" />
		<Field Name="TrivialSkillLineRankHigh" Type="ushort" />
		<Field Name="TrivialSkillLineRankLow" Type="ushort" />
		<Field Name="UniqueBit" Type="ushort" />
		<Field Name="TradeSkillCategoryID" Type="ushort" />
		<Field Name="NumSkillUps" Type="byte" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="MinSkillLineRank" Type="ushort" />
		<Field Name="AcquireMethod" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SkillRaceClassInfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="long" />
		<Field Name="SkillID" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="SkillTierID" Type="ushort" />
		<Field Name="Availability" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="ClassMask" Type="int" />
	</Table>
	<Table Name="Soundambience" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="int" ArraySize="2" />
	</Table>
	<Table Name="Soundambienceflavor" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="uint" />
	</Table>
	<Table Name="Soundbus" Build="25632">
		<Field Name="Field0" Type="float" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Soundbusoverride" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
	</Table>
	<Table Name="Soundemitterpillpoints" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="3" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Soundemitters" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="float" ArraySize="3" />
		<Field Name="Field2" Type="float" ArraySize="3" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field9" Type="int" />
		<Field Name="FieldA" Type="byte" />
	</Table>
	<Table Name="Soundenvelope" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="Soundfilter" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Soundfilterelem" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="9" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="SoundKit" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VolumeFloat" Type="float" />
		<Field Name="MinDistance" Type="float" />
		<Field Name="DistanceCutoff" Type="float" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="SoundEntriesAdvancedID" Type="ushort" />
		<Field Name="SoundType" Type="byte" />
		<Field Name="DialogType" Type="byte" />
		<Field Name="EAXDef" Type="byte" />
		<Field Name="VolumeVariationPlus" Type="float" />
		<Field Name="VolumeVariationMinus" Type="float" />
		<Field Name="PitchVariationPlus" Type="float" />
		<Field Name="PitchVariationMinus" Type="float" />
		<Field Name="PitchAdjust" Type="float" />
		<Field Name="BusOverwriteID" Type="ushort" />
		<Field Name="Unk700" Type="byte" />
	</Table>
	<Table Name="Soundkitadvanced" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="float" />
		<Field Name="Field02" Type="float" />
		<Field Name="Field03" Type="float" />
		<Field Name="Field04" Type="float" />
		<Field Name="Field05" Type="float" />
		<Field Name="Field06" Type="int" />
		<Field Name="Field07" Type="int" />
		<Field Name="Field08" Type="float" />
		<Field Name="Field09" Type="byte" />
		<Field Name="Field0A" Type="uint" />
		<Field Name="Field0B" Type="int" />
		<Field Name="Field0C" Type="int" />
		<Field Name="Field0D" Type="int" />
		<Field Name="Field0E" Type="int" />
		<Field Name="Field0F" Type="int" />
		<Field Name="Field10" Type="int" />
		<Field Name="Field11" Type="int" />
		<Field Name="Field12" Type="int" />
		<Field Name="Field13" Type="int" />
		<Field Name="Field14" Type="int" />
		<Field Name="Field15" Type="float" />
		<Field Name="Field16" Type="float" />
		<Field Name="Field17" Type="float" />
		<Field Name="Field18" Type="float" />
		<Field Name="Field19" Type="float" />
		<Field Name="Field1A" Type="float" />
		<Field Name="Field1B" Type="float" />
		<Field Name="Field1C" Type="float" />
		<Field Name="Field1D" Type="int" />
		<Field Name="Field1E" Type="int" />
		<Field Name="Field1F" Type="int" />
		<Field Name="Field20" Type="int" />
		<Field Name="Field21" Type="int" />
		<Field Name="Field22" Type="int" />
		<Field Name="Field23" Type="int" />
		<Field Name="Field24" Type="int" />
		<Field Name="Field25" Type="int" />
		<Field Name="Field26" Type="int" />
		<Field Name="Field27" Type="int" />
	</Table>
	<Table Name="Soundkitchild" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="uint" />
		<Field Name="Field2" Type="int" />
	</Table>
	<Table Name="Soundkitentry" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="uint" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="float" />
	</Table>
	<Table Name="Soundkitfallback" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Soundkitname" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="Soundoverride" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Soundproviderpreferences" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="string" />
		<Field Name="Field02" Type="float" />
		<Field Name="Field03" Type="float" />
		<Field Name="Field04" Type="float" />
		<Field Name="Field05" Type="float" />
		<Field Name="Field06" Type="float" />
		<Field Name="Field07" Type="float" />
		<Field Name="Field08" Type="float" />
		<Field Name="Field09" Type="float" />
		<Field Name="Field0A" Type="float" />
		<Field Name="Field0B" Type="float" />
		<Field Name="Field0C" Type="float" />
		<Field Name="Field0D" Type="float" />
		<Field Name="Field0E" Type="float" />
		<Field Name="Field0F" Type="float" />
		<Field Name="Field10" Type="float" />
		<Field Name="Field11" Type="ushort" />
		<Field Name="Field12" Type="ushort" />
		<Field Name="Field13" Type="ushort" />
		<Field Name="Field14" Type="ushort" />
		<Field Name="Field15" Type="ushort" />
		<Field Name="Field16" Type="byte" />
		<Field Name="Field17" Type="byte" />
	</Table>
	<Table Name="Sourceinfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Spammessages" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="SpecializationSpells" Build="25632">
		<Field Name="Description" Type="string" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="OverridesSpellID" Type="uint" />
		<Field Name="SpecID" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Spell" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="NameSubtext" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="AuraDescription" Type="string" />
	</Table>
	<Table Name="Spellactionbarpref" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Spellactivationoverlay" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="int" ArraySize="4" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="ushort" />
	</Table>
	<Table Name="SpellAuraOptions" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ProcCharges" Type="uint" />
		<Field Name="ProcTypeMask" Type="uint" />
		<Field Name="ProcCategoryRecovery" Type="uint" />
		<Field Name="CumulativeAura" Type="ushort" />
		<Field Name="SpellProcsPerMinuteID" Type="ushort" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="ProcChance" Type="byte" />
	</Table>
	<Table Name="SpellAuraRestrictions" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CasterAuraSpell" Type="uint" />
		<Field Name="TargetAuraSpell" Type="uint" />
		<Field Name="ExcludeCasterAuraSpell" Type="uint" />
		<Field Name="ExcludeTargetAuraSpell" Type="uint" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="CasterAuraState" Type="byte" />
		<Field Name="TargetAuraState" Type="byte" />
		<Field Name="ExcludeCasterAuraState" Type="byte" />
		<Field Name="ExcludeTargetAuraState" Type="byte" />
	</Table>
	<Table Name="Spellauravisibility" Build="25632">
		<Field Name="Field0" Type="byte" />
		<Field Name="Field1" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Spellauravisxchrspec" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
	</Table>
	<Table Name="SpellCastingRequirements" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="MinFactionID" Type="ushort" />
		<Field Name="RequiredAreasID" Type="ushort" />
		<Field Name="RequiresSpellFocus" Type="ushort" />
		<Field Name="FacingCasterFlags" Type="byte" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="RequiredAuraVision" Type="byte" />
	</Table>
	<Table Name="SpellCastTimes" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CastTime" Type="int" />
		<Field Name="MinCastTime" Type="int" />
		<Field Name="CastTimePerLevel" Type="short" />
	</Table>
	<Table Name="SpellCategories" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Category" Type="ushort" />
		<Field Name="StartRecoveryCategory" Type="ushort" />
		<Field Name="ChargeCategory" Type="ushort" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="DefenseType" Type="byte" />
		<Field Name="DispelType" Type="byte" />
		<Field Name="Mechanic" Type="byte" />
		<Field Name="PreventionType" Type="byte" />
	</Table>
	<Table Name="SpellCategory" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="ChargeRecoveryTime" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UsesPerWeek" Type="byte" />
		<Field Name="MaxCharges" Type="byte" />
		<Field Name="ChargeCategoryType" Type="uint" />
	</Table>
	<Table Name="Spellchaineffects" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="float" />
		<Field Name="Field02" Type="float" />
		<Field Name="Field03" Type="float" />
		<Field Name="Field04" Type="int" />
		<Field Name="Field05" Type="int" />
		<Field Name="Field06" Type="float" />
		<Field Name="Field07" Type="float" />
		<Field Name="Field08" Type="float" />
		<Field Name="Field09" Type="float" />
		<Field Name="Field0A" Type="float" />
		<Field Name="Field0B" Type="float" />
		<Field Name="Field0C" Type="float" />
		<Field Name="Field0D" Type="float" />
		<Field Name="Field0E" Type="float" />
		<Field Name="Field0F" Type="float" />
		<Field Name="Field10" Type="float" />
		<Field Name="Field11" Type="float" />
		<Field Name="Field12" Type="float" />
		<Field Name="Field13" Type="float" />
		<Field Name="Field14" Type="float" />
		<Field Name="Field15" Type="float" />
		<Field Name="Field16" Type="float" />
		<Field Name="Field17" Type="float" />
		<Field Name="Field18" Type="float" />
		<Field Name="Field19" Type="float" />
		<Field Name="Field1A" Type="float" />
		<Field Name="Field1B" Type="float" />
		<Field Name="Field1C" Type="float" />
		<Field Name="Field1D" Type="float" />
		<Field Name="Field1E" Type="float" />
		<Field Name="Field1F" Type="float" />
		<Field Name="Field20" Type="float" />
		<Field Name="Field21" Type="float" />
		<Field Name="Field22" Type="float" />
		<Field Name="Field23" Type="float" />
		<Field Name="Field24" Type="float" ArraySize="3" />
		<Field Name="Field25" Type="float" ArraySize="3" />
		<Field Name="Field26" Type="float" ArraySize="3" />
		<Field Name="Field27" Type="float" ArraySize="3" />
		<Field Name="Field28" Type="int" />
		<Field Name="Field29" Type="float" />
		<Field Name="Field2A" Type="float" />
		<Field Name="Field2B" Type="float" />
		<Field Name="Field2C" Type="float" />
		<Field Name="Field2D" Type="ushort" />
		<Field Name="Field2E" Type="ushort" />
		<Field Name="Field2F" Type="ushort" ArraySize="11" />
		<Field Name="Field30" Type="ushort" />
		<Field Name="Field31" Type="byte" />
		<Field Name="Field32" Type="byte" />
		<Field Name="Field33" Type="byte" />
		<Field Name="Field34" Type="byte" />
		<Field Name="Field35" Type="byte" />
		<Field Name="Field36" Type="byte" />
		<Field Name="Field37" Type="byte" />
		<Field Name="Field38" Type="byte" />
		<Field Name="Field39" Type="byte" />
		<Field Name="Field3A" Type="byte" />
		<Field Name="Field3B" Type="int" />
		<Field Name="Field3C" Type="int" ArraySize="3" />
	</Table>
	<Table Name="SpellClassOptions" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="SpellClassMask" Type="int" ArraySize="4" />
		<Field Name="SpellClassSet" Type="byte" />
		<Field Name="ModalNextSpell" Type="uint" />
	</Table>
	<Table Name="SpellCooldowns" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CategoryRecoveryTime" Type="uint" />
		<Field Name="RecoveryTime" Type="uint" />
		<Field Name="StartRecoveryTime" Type="uint" />
		<Field Name="DifficultyID" Type="byte" />
	</Table>
	<Table Name="Spelldescriptionvariables" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Spelldispeltype" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="SpellDuration" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Duration" Type="int" />
		<Field Name="MaxDuration" Type="int" />
		<Field Name="DurationPerLevel" Type="int" />
	</Table>
	<Table Name="SpellEffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Effect" Type="uint" />
		<Field Name="EffectBasePoints" Type="int" />
		<Field Name="EffectIndex" Type="uint" />
		<Field Name="EffectAura" Type="uint" />
		<Field Name="DifficultyID" Type="uint" />
		<Field Name="EffectAmplitude" Type="float" />
		<Field Name="EffectAuraPeriod" Type="uint" />
		<Field Name="EffectBonusCoefficient" Type="float" />
		<Field Name="EffectChainAmplitude" Type="float" />
		<Field Name="EffectChainTargets" Type="uint" />
		<Field Name="EffectDieSides" Type="int" />
		<Field Name="EffectItemType" Type="uint" />
		<Field Name="EffectMechanic" Type="uint" />
		<Field Name="EffectPointsPerResource" Type="float" />
		<Field Name="EffectRealPointsPerLevel" Type="float" />
		<Field Name="EffectTriggerSpell" Type="uint" />
		<Field Name="EffectPosFacing" Type="float" />
		<Field Name="EffectAttributes" Type="uint" />
		<Field Name="BonusCoefficientFromAP" Type="float" />
		<Field Name="PvPMultiplier" Type="float" />
		<Field Name="Coefficient" Type="float" />
		<Field Name="Variance" Type="float" />
		<Field Name="ResourceCoefficient" Type="float" />
		<Field Name="GroupSizeCoefficient" Type="float" />
		<Field Name="EffectSpellClassMask" Type="int" ArraySize="4" />
		<Field Name="EffectMiscValue" Type="int" />
		<Field Name="EffectMiscValueB" Type="int" />
		<Field Name="EffectRadiusIndex" Type="uint" />
		<Field Name="EffectRadiusMaxIndex" Type="uint" />
		<Field Name="ImplicitTarget" Type="uint" ArraySize="2" />
	</Table>
	<Table Name="Spelleffectemission" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="SpellEquippedItems" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="EquippedItemInventoryTypeMask" Type="int" />
		<Field Name="EquippedItemSubClassMask" Type="int" />
		<Field Name="EquippedItemClass" Type="byte" />
	</Table>
	<Table Name="Spellflyout" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ulong" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="string" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="int" />
	</Table>
	<Table Name="Spellflyoutitem" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="SpellFocusObject" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SpellInterrupts" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="InterruptFlags" Type="ushort" />
		<Field Name="AuraInterruptFlags" Type="uint" ArraySize="2" />
		<Field Name="ChannelInterruptFlags" Type="uint" ArraySize="2" />
	</Table>
	<Table Name="SpellItemEnchantment" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="EffectSpellID" Type="uint" ArraySize="3" />
		<Field Name="EffectScalingPoints" Type="float" ArraySize="3" />
		<Field Name="TransmogCost" Type="uint" />
		<Field Name="TextureFileDataID" Type="uint" />
		<Field Name="EffectPointsMin" Type="ushort" ArraySize="3" />
		<Field Name="ItemVisual" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="RequiredSkillID" Type="ushort" />
		<Field Name="RequiredSkillRank" Type="ushort" />
		<Field Name="ItemLevel" Type="ushort" />
		<Field Name="Charges" Type="byte" />
		<Field Name="Effect" Type="byte" ArraySize="3" />
		<Field Name="ConditionID" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
		<Field Name="ScalingClass" Type="byte" />
		<Field Name="ScalingClassRestricted" Type="byte" />
		<Field Name="PlayerConditionID" Type="uint" />
	</Table>
	<Table Name="SpellItemEnchantmentCondition" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LTOperand" Type="uint" ArraySize="5" />
		<Field Name="LTOperandType" Type="byte" ArraySize="5" />
		<Field Name="Operator" Type="byte" ArraySize="5" />
		<Field Name="RTOperandType" Type="byte" ArraySize="5" />
		<Field Name="RTOperand" Type="byte" ArraySize="5" />
		<Field Name="Logic" Type="byte" ArraySize="5" />
	</Table>
	<Table Name="Spellkeyboundoverride" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Spelllabel" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
	</Table>
	<Table Name="SpellLearnSpell" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LearnSpellID" Type="uint" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="OverridesSpellID" Type="uint" />
	</Table>
	<Table Name="SpellLevels" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseLevel" Type="ushort" />
		<Field Name="MaxLevel" Type="ushort" />
		<Field Name="SpellLevel" Type="ushort" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="MaxUsableLevel" Type="byte" />
	</Table>
	<Table Name="Spellmechanic" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="SpellMisc" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CastingTimeIndex" Type="ushort" />
		<Field Name="DurationIndex" Type="ushort" />
		<Field Name="RangeIndex" Type="ushort" />
		<Field Name="SchoolMask" Type="byte" />
		<Field Name="IconFileDataID" Type="uint" />
		<Field Name="Speed" Type="float" />
		<Field Name="ActiveIconFileDataID" Type="uint" />
		<Field Name="MultistrikeSpeedMod" Type="float" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="Attributes" Type="uint" />
		<Field Name="AttributesEx" Type="uint" />
		<Field Name="AttributesExB" Type="uint" />
		<Field Name="AttributesExC" Type="uint" />
		<Field Name="AttributesExD" Type="uint" />
		<Field Name="AttributesExE" Type="uint" />
		<Field Name="AttributesExF" Type="uint" />
		<Field Name="AttributesExG" Type="uint" />
		<Field Name="AttributesExH" Type="uint" />
		<Field Name="AttributesExI" Type="uint" />
		<Field Name="AttributesExJ" Type="uint" />
		<Field Name="AttributesExK" Type="uint" />
		<Field Name="AttributesExL" Type="uint" />
		<Field Name="AttributesExM" Type="uint" />
	</Table>
	<Table Name="Spellmissile" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field01" Type="int" />
		<Field Name="Field02" Type="float" />
		<Field Name="Field03" Type="float" />
		<Field Name="Field04" Type="float" />
		<Field Name="Field05" Type="float" />
		<Field Name="Field06" Type="float" />
		<Field Name="Field07" Type="float" />
		<Field Name="Field08" Type="int" />
		<Field Name="Field09" Type="float" />
		<Field Name="Field0A" Type="int" />
		<Field Name="Field0B" Type="float" />
		<Field Name="Field0C" Type="float" />
		<Field Name="Field0D" Type="float" />
		<Field Name="Field0E" Type="float" />
		<Field Name="Field0F" Type="byte" />
	</Table>
	<Table Name="Spellmissilemotion" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="SpellPower" Build="25632">
		<Field Name="ManaCost" Type="int" />
		<Field Name="ManaCostPercentage" Type="float" />
		<Field Name="ManaCostPercentagePerSecond" Type="float" />
		<Field Name="RequiredAura" Type="uint" />
		<Field Name="HealthCostPercentage" Type="float" />
		<Field Name="PowerIndex" Type="byte" />
		<Field Name="PowerType" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ManaCostPerLevel" Type="int" />
		<Field Name="ManaCostPerSecond" Type="int" />
		<Field Name="ManaCostAdditional" Type="int" />
		<Field Name="PowerDisplayID" Type="uint" />
		<Field Name="UnitPowerBarID" Type="uint" />
	</Table>
	<Table Name="SpellPowerDifficulty" Build="25632">
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="PowerIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Spellproceduraleffect" Build="25632">
		<Field Name="Field0" Type="int" ArraySize="4" />
		<Field Name="Field1" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="SpellProcsPerMinute" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseProcRate" Type="float" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellProcsPerMinuteMod" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Coeff" Type="float" />
		<Field Name="Param" Type="ushort" />
		<Field Name="Type" Type="byte" />
	</Table>
	<Table Name="SpellRadius" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Radius" Type="float" />
		<Field Name="RadiusPerLevel" Type="float" />
		<Field Name="RadiusMin" Type="float" />
		<Field Name="RadiusMax" Type="float" />
	</Table>
	<Table Name="SpellRange" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayName" Type="string" />
		<Field Name="DisplayNameShort" Type="string" />
		<Field Name="MinRangeHostile" Type="float" />
		<Field Name="MinRangeFriend" Type="float" />
		<Field Name="MaxRangeHostile" Type="float" />
		<Field Name="MaxRangeFriend" Type="float" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellReagents" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="Reagent" Type="int" ArraySize="8" />
		<Field Name="ReagentCount" Type="ushort" ArraySize="8" />
	</Table>
	<Table Name="Spellreagentscurrency" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="SpellScaling" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="ScalesFromItemLevel" Type="ushort" />
		<Field Name="ScalingClass" Type="int" />
		<Field Name="MinScalingLevel" Type="uint" />
		<Field Name="MaxScalingLevel" Type="uint" />
	</Table>
	<Table Name="SpellShapeshift" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="ShapeshiftExclude" Type="uint" ArraySize="2" />
		<Field Name="ShapeshiftMask" Type="uint" ArraySize="2" />
		<Field Name="StanceBarOrder" Type="byte" />
	</Table>
	<Table Name="SpellShapeshiftForm" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="WeaponDamageVariance" Type="float" />
		<Field Name="Flags" Type="uint" />
		<Field Name="CombatRoundTime" Type="ushort" />
		<Field Name="MountTypeID" Type="ushort" />
		<Field Name="CreatureType" Type="byte" />
		<Field Name="BonusActionBar" Type="byte" />
		<Field Name="AttackIconFileDataID" Type="uint" />
		<Field Name="CreatureDisplayID" Type="uint" ArraySize="4" />
		<Field Name="PresetSpellID" Type="uint" ArraySize="8" />
	</Table>
	<Table Name="Spellspecialuniteffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="SpellTargetRestrictions" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ConeAngle" Type="float" />
		<Field Name="Width" Type="float" />
		<Field Name="Targets" Type="uint" />
		<Field Name="TargetCreatureType" Type="ushort" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="MaxAffectedTargets" Type="byte" />
		<Field Name="MaxTargetLevel" Type="uint" />
	</Table>
	<Table Name="SpellTotems" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="Totem" Type="uint" ArraySize="2" />
		<Field Name="RequiredTotemCategoryID" Type="ushort" ArraySize="2" />
	</Table>
	<Table Name="Spellvisual" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MissileCastOffset" Type="float" ArraySize="3" />
		<Field Name="MissileImpactOffset" Type="float" ArraySize="3" />
		<Field Name="Flags" Type="int" />
		<Field Name="SpellVisualMissileSetID" Type="ushort" />
		<Field Name="MissileDestinationAttachment" Type="byte" />
		<Field Name="MissileAttachment" Type="byte" />
		<Field Name="MissileCastPositionerID" Type="int" />
		<Field Name="MissileImpactPositionerID" Type="int" />
		<Field Name="MissileTargetingKit" Type="int" />
		<Field Name="AnimEventSoundID" Type="int" />
		<Field Name="DamageNumberDelay" Type="ushort" />
		<Field Name="HostileSpellVisualID" Type="int" />
		<Field Name="CasterSpellVisualID" Type="int" />
		<Field Name="LowViolenceSpellVisualID" Type="int" />
	</Table>
	<Table Name="Spellvisualanim" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Spellvisualcoloreffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="ushort" />
		<Field Name="Field8" Type="ushort" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="ushort" />
	</Table>
	<Table Name="Spellvisualeffectname" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="float" />
		<Field Name="Field7" Type="int" />
		<Field Name="Field8" Type="int" />
		<Field Name="Field9" Type="int" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="int" />
		<Field Name="FieldC" Type="int" />
		<Field Name="FieldD" Type="ushort" />
	</Table>
	<Table Name="Spellvisualevent" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="int" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="int" />
	</Table>
	<Table Name="Spellvisualkit" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="int" />
	</Table>
	<Table Name="Spellvisualkitareamodel" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="byte" />
	</Table>
	<Table Name="Spellvisualkiteffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
	</Table>
	<Table Name="Spellvisualkitmodelattach" Build="25632">
		<Field Name="Field00" Type="float" ArraySize="3" />
		<Field Name="Field01" Type="float" ArraySize="3" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field03" Type="ushort" />
		<Field Name="Field04" Type="byte" />
		<Field Name="Field05" Type="byte" />
		<Field Name="Field06" Type="ushort" />
		<Field Name="Field07" Type="float" />
		<Field Name="Field08" Type="float" />
		<Field Name="Field09" Type="float" />
		<Field Name="Field0A" Type="float" />
		<Field Name="Field0B" Type="float" />
		<Field Name="Field0C" Type="float" />
		<Field Name="Field0D" Type="float" />
		<Field Name="Field0E" Type="float" />
		<Field Name="Field0F" Type="int" />
		<Field Name="Field10" Type="int" />
		<Field Name="Field11" Type="int" />
		<Field Name="Field12" Type="float" />
		<Field Name="Field13" Type="int" />
		<Field Name="Field14" Type="float" />
	</Table>
	<Table Name="Spellvisualmissile" Build="25632">
		<Field Name="Field0" Type="int" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="float" ArraySize="3" />
		<Field Name="Field4" Type="float" ArraySize="3" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="ushort" />
		<Field Name="Field8" Type="ushort" />
		<Field Name="Field9" Type="ushort" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FieldD" Type="int" />
		<Field Name="FieldE" Type="int" />
	</Table>
	<Table Name="Spellxdescriptionvariables" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="SpellXSpellVisual" Build="25632">
		<Field Name="SpellVisualID" Type="uint" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Chance" Type="float" />
		<Field Name="CasterPlayerConditionID" Type="ushort" />
		<Field Name="CasterUnitConditionID" Type="ushort" />
		<Field Name="PlayerConditionID" Type="ushort" />
		<Field Name="UnitConditionID" Type="ushort" />
		<Field Name="IconFileDataID" Type="uint" />
		<Field Name="ActiveIconFileDataID" Type="uint" />
		<Field Name="Flags" Type="byte" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="Priority" Type="byte" />
	</Table>
	<Table Name="Startup_strings" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="string" />
	</Table>
	<Table Name="Startupfiles" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Stationery" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SummonProperties" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="uint" />
		<Field Name="Category" Type="uint" />
		<Field Name="Faction" Type="uint" />
		<Field Name="Type" Type="int" />
		<Field Name="Slot" Type="int" />
	</Table>
	<Table Name="TactKey" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Key" Type="byte" ArraySize="16" />
	</Table>
	<Table Name="Tactkeylookup" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" ArraySize="8" />
	</Table>
	<Table Name="Talent" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
		<Field Name="SpellID" Type="uint" />
		<Field Name="OverridesSpellID" Type="uint" />
		<Field Name="SpecID" Type="ushort" />
		<Field Name="TierID" Type="byte" />
		<Field Name="ColumnIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CategoryMask" Type="byte" ArraySize="2" />
		<Field Name="ClassID" Type="byte" />
	</Table>
	<Table Name="TaxiNodes" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="MountCreatureID" Type="uint" ArraySize="2" />
		<Field Name="MapOffset" Type="float" ArraySize="2" />
		<Field Name="Unk730" Type="float" />
		<Field Name="FlightMapOffset" Type="float" ArraySize="2" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="ConditionID" Type="ushort" />
		<Field Name="LearnableIndex" Type="ushort" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UiTextureKitPrefixID" Type="int" />
		<Field Name="SpecialAtlasIconPlayerConditionID" Type="uint" />
	</Table>
	<Table Name="TaxiPath" Build="25632">
		<Field Name="From" Type="ushort" />
		<Field Name="To" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Cost" Type="uint" />
	</Table>
	<Table Name="TaxiPathNode" Build="25632">
		<Field Name="Loc" Type="float" ArraySize="3" />
		<Field Name="PathID" Type="ushort" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="NodeIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Delay" Type="uint" />
		<Field Name="ArrivalEventID" Type="ushort" />
		<Field Name="DepartureEventID" Type="ushort" />
	</Table>
	<Table Name="Terrainmaterial" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Terraintype" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Terraintypesounds" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Textureblendset" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" ArraySize="3" />
		<Field Name="Field2" Type="float" ArraySize="3" />
		<Field Name="Field3" Type="float" ArraySize="3" />
		<Field Name="Field4" Type="float" ArraySize="3" />
		<Field Name="Field5" Type="float" ArraySize="3" />
		<Field Name="Field6" Type="float" ArraySize="4" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
	</Table>
	<Table Name="Texturefiledata" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="TotemCategory" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="CategoryMask" Type="uint" />
		<Field Name="CategoryType" Type="byte" />
	</Table>
	<Table Name="Toy" Build="25632">
		<Field Name="Description" Type="string" />
		<Field Name="ItemID" Type="uint" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CategoryFilter" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Tradeskillcategory" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Tradeskillitem" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Transformmatrix" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="3" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
	</Table>
	<Table Name="TransmogHoliday" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HolidayID" Type="int" />
	</Table>
	<Table Name="TransmogSet" Build="25632">
		<Field Name="Name" Type="string" />
		<Field Name="BaseSetID" Type="ushort" />
		<Field Name="UIOrder" Type="ushort" />
		<Field Name="ExpansionID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="QuestID" Type="int" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="ItemNameDescriptionID" Type="int" />
		<Field Name="TransmogSetGroupID" Type="uint" />
	</Table>
	<Table Name="TransmogSetGroup" Build="25632">
		<Field Name="Label" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="TransmogSetItem" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TransmogSetID" Type="uint" />
		<Field Name="ItemModifiedAppearanceID" Type="uint" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="TransportAnimation" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TimeIndex" Type="uint" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="SequenceID" Type="byte" />
	</Table>
	<Table Name="Transportphysics" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="float" />
		<Field Name="Field7" Type="float" />
		<Field Name="Field8" Type="float" />
		<Field Name="Field9" Type="float" />
		<Field Name="FieldA" Type="float" />
	</Table>
	<Table Name="TransportRotation" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TimeIndex" Type="uint" />
		<Field Name="X" Type="float" />
		<Field Name="Y" Type="float" />
		<Field Name="Z" Type="float" />
		<Field Name="W" Type="float" />
	</Table>
	<Table Name="Trophy" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="int" />
	</Table>
	<Table Name="Uicamera" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="float" ArraySize="3" />
		<Field Name="Field3" Type="float" ArraySize="3" />
		<Field Name="Field4" Type="float" ArraySize="3" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
	</Table>
	<Table Name="Uicameratype" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Uicamfbacktransmogchrrace" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Uicamfbacktransmogweapon" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Uiexpansiondisplayinfo" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Uiexpansiondisplayinfoicon" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Uimappoi" Build="25632">
		<Field Name="Field0" Type="int" />
		<Field Name="Field1" Type="float" ArraySize="3" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Uimodelscene" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Uimodelsceneactor" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="float" ArraySize="3" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field8" Type="byte" />
	</Table>
	<Table Name="Uimodelsceneactordisplay" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Uimodelscenecamera" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="Field1" Type="float" ArraySize="3" />
		<Field Name="Field2" Type="int" ArraySize="3" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="int" />
		<Field Name="Field6" Type="float" />
		<Field Name="Field7" Type="int" />
		<Field Name="Field8" Type="int" />
		<Field Name="Field9" Type="float" />
		<Field Name="FieldA" Type="float" />
		<Field Name="FieldB" Type="float" />
		<Field Name="FieldC" Type="byte" />
		<Field Name="FieldD" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Uitextureatlas" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Uitextureatlasmember" Build="25632">
		<Field Name="Field0" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="Uitexturekit" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Unitblood" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="uint" />
		<Field Name="Field6" Type="int" />
	</Table>
	<Table Name="Unitbloodlevels" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" ArraySize="3" />
	</Table>
	<Table Name="Unitcondition" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" ArraySize="8" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" ArraySize="8" />
		<Field Name="Field4" Type="byte" ArraySize="8" />
	</Table>
	<Table Name="UnitPowerBar" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Cost" Type="string" />
		<Field Name="OutOfError" Type="string" />
		<Field Name="ToolTip" Type="string" />
		<Field Name="RegenerationPeace" Type="float" />
		<Field Name="RegenerationCombat" Type="float" />
		<Field Name="FileDataID" Type="uint" ArraySize="6" />
		<Field Name="Color" Type="uint" ArraySize="6" />
		<Field Name="StartInset" Type="float" />
		<Field Name="EndInset" Type="float" />
		<Field Name="StartPower" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="CenterPower" Type="byte" />
		<Field Name="BarType" Type="byte" />
		<Field Name="MinPower" Type="uint" />
		<Field Name="MaxPower" Type="uint" />
	</Table>
	<Table Name="Vehicle" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="uint" />
		<Field Name="TurnSpeed" Type="float" />
		<Field Name="PitchSpeed" Type="float" />
		<Field Name="PitchMin" Type="float" />
		<Field Name="PitchMax" Type="float" />
		<Field Name="MouseLookOffsetPitch" Type="float" />
		<Field Name="CameraFadeDistScalarMin" Type="float" />
		<Field Name="CameraFadeDistScalarMax" Type="float" />
		<Field Name="CameraPitchOffset" Type="float" />
		<Field Name="FacingLimitRight" Type="float" />
		<Field Name="FacingLimitLeft" Type="float" />
		<Field Name="CameraYawOffset" Type="float" />
		<Field Name="SeatID" Type="ushort" ArraySize="8" />
		<Field Name="VehicleUIIndicatorID" Type="ushort" />
		<Field Name="PowerDisplayID" Type="ushort" ArraySize="3" />
		<Field Name="FlagsB" Type="byte" />
		<Field Name="UILocomotionType" Type="byte" />
		<Field Name="MissileTargetingID" Type="int" />
	</Table>
	<Table Name="VehicleSeat" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="uint" ArraySize="3" />
		<Field Name="AttachmentOffset" Type="float" ArraySize="3" />
		<Field Name="EnterPreDelay" Type="float" />
		<Field Name="EnterSpeed" Type="float" />
		<Field Name="EnterGravity" Type="float" />
		<Field Name="EnterMinDuration" Type="float" />
		<Field Name="EnterMaxDuration" Type="float" />
		<Field Name="EnterMinArcHeight" Type="float" />
		<Field Name="EnterMaxArcHeight" Type="float" />
		<Field Name="ExitPreDelay" Type="float" />
		<Field Name="ExitSpeed" Type="float" />
		<Field Name="ExitGravity" Type="float" />
		<Field Name="ExitMinDuration" Type="float" />
		<Field Name="ExitMaxDuration" Type="float" />
		<Field Name="ExitMinArcHeight" Type="float" />
		<Field Name="ExitMaxArcHeight" Type="float" />
		<Field Name="PassengerYaw" Type="float" />
		<Field Name="PassengerPitch" Type="float" />
		<Field Name="PassengerRoll" Type="float" />
		<Field Name="VehicleEnterAnimDelay" Type="float" />
		<Field Name="VehicleExitAnimDelay" Type="float" />
		<Field Name="CameraEnteringDelay" Type="float" />
		<Field Name="CameraEnteringDuration" Type="float" />
		<Field Name="CameraExitingDelay" Type="float" />
		<Field Name="CameraExitingDuration" Type="float" />
		<Field Name="CameraOffset" Type="float" ArraySize="3" />
		<Field Name="CameraPosChaseRate" Type="float" />
		<Field Name="CameraFacingChaseRate" Type="float" />
		<Field Name="CameraEnteringZoom" Type="float" />
		<Field Name="CameraSeatZoomMin" Type="float" />
		<Field Name="CameraSeatZoomMax" Type="float" />
		<Field Name="UISkinFileDataID" Type="uint" />
		<Field Name="EnterAnimStart" Type="short" />
		<Field Name="EnterAnimLoop" Type="short" />
		<Field Name="RideAnimStart" Type="short" />
		<Field Name="RideAnimLoop" Type="short" />
		<Field Name="RideUpperAnimStart" Type="short" />
		<Field Name="RideUpperAnimLoop" Type="short" />
		<Field Name="ExitAnimStart" Type="short" />
		<Field Name="ExitAnimLoop" Type="short" />
		<Field Name="ExitAnimEnd" Type="short" />
		<Field Name="VehicleEnterAnim" Type="short" />
		<Field Name="VehicleExitAnim" Type="short" />
		<Field Name="VehicleRideAnimLoop" Type="short" />
		<Field Name="EnterAnimKitID" Type="ushort" />
		<Field Name="RideAnimKitID" Type="ushort" />
		<Field Name="ExitAnimKitID" Type="ushort" />
		<Field Name="VehicleEnterAnimKitID" Type="ushort" />
		<Field Name="VehicleRideAnimKitID" Type="ushort" />
		<Field Name="VehicleExitAnimKitID" Type="ushort" />
		<Field Name="CameraModeID" Type="ushort" />
		<Field Name="AttachmentID" Type="byte" />
		<Field Name="PassengerAttachmentID" Type="byte" />
		<Field Name="VehicleEnterAnimBone" Type="byte" />
		<Field Name="VehicleExitAnimBone" Type="byte" />
		<Field Name="VehicleRideAnimLoopBone" Type="byte" />
		<Field Name="VehicleAbilityDisplay" Type="byte" />
		<Field Name="EnterUISoundID" Type="uint" />
		<Field Name="ExitUISoundID" Type="uint" />
	</Table>
	<Table Name="Vehicleuiindicator" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
	</Table>
	<Table Name="Vehicleuiindseat" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="Vignette" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="ushort" />
	</Table>
	<Table Name="Virtualattachment" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Virtualattachmentcustomization" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Vocaluisounds" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="int" ArraySize="2" />
	</Table>
	<Table Name="Wbaccesscontrollist" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
	</Table>
	<Table Name="Wbcertwhitelist" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="Weaponimpactsounds" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="int" ArraySize="11" />
		<Field Name="Field5" Type="int" ArraySize="11" />
		<Field Name="Field6" Type="int" ArraySize="11" />
		<Field Name="Field7" Type="int" ArraySize="11" />
	</Table>
	<Table Name="Weaponswingsounds2" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="byte" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="int" />
	</Table>
	<Table Name="Weapontrail" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="int" />
		<Field Name="Field4" Type="int" />
		<Field Name="Field5" Type="int" ArraySize="3" />
		<Field Name="Field6" Type="float" ArraySize="3" />
		<Field Name="Field7" Type="float" ArraySize="3" />
		<Field Name="Field8" Type="float" ArraySize="3" />
		<Field Name="Field9" Type="float" ArraySize="3" />
	</Table>
	<Table Name="Weapontrailmodeldef" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Weapontrailparam" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
	</Table>
	<Table Name="Weather" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="2" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" ArraySize="3" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" />
		<Field Name="Field6" Type="float" />
		<Field Name="Field7" Type="float" />
		<Field Name="Field8" Type="float" />
		<Field Name="Field9" Type="ushort" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="byte" />
		<Field Name="FieldC" Type="byte" />
		<Field Name="FieldD" Type="ushort" />
		<Field Name="FieldE" Type="int" />
	</Table>
	<Table Name="Windsettings" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" />
		<Field Name="Field2" Type="float" ArraySize="3" />
		<Field Name="Field3" Type="float" />
		<Field Name="Field4" Type="float" />
		<Field Name="Field5" Type="float" ArraySize="3" />
		<Field Name="Field6" Type="float" />
		<Field Name="Field7" Type="float" ArraySize="3" />
		<Field Name="Field8" Type="int" />
		<Field Name="Field9" Type="int" />
		<Field Name="FieldA" Type="byte" />
	</Table>
	<Table Name="WMOAreaTable" Build="25632">
		<Field Name="AreaName" Type="string" />
		<Field Name="WMOGroupID" Type="int" />
		<Field Name="AmbienceID" Type="ushort" />
		<Field Name="ZoneMusic" Type="ushort" />
		<Field Name="IntroSound" Type="ushort" />
		<Field Name="AreaTableID" Type="ushort" />
		<Field Name="UWIntroSound" Type="ushort" />
		<Field Name="UWAmbience" Type="ushort" />
		<Field Name="NameSet" Type="byte" />
		<Field Name="SoundProviderPref" Type="byte" />
		<Field Name="SoundProviderPrefUnderwater" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UWZoneMusic" Type="uint" />
	</Table>
	<Table Name="Wmominimaptexture" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
	</Table>
	<Table Name="World_pvp_area" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="byte" />
		<Field Name="Field7" Type="byte" />
	</Table>
	<Table Name="Worldbosslockout" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
	</Table>
	<Table Name="Worldchunksounds" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="ushort" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="byte" />
		<Field Name="Field5" Type="byte" />
		<Field Name="Field6" Type="byte" />
	</Table>
	<Table Name="WorldEffect" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TargetAsset" Type="uint" />
		<Field Name="CombatConditionID" Type="ushort" />
		<Field Name="TargetType" Type="byte" />
		<Field Name="WhenToDisplay" Type="byte" />
		<Field Name="QuestFeedbackEffectID" Type="uint" />
		<Field Name="PlayerConditionID" Type="uint" />
	</Table>
	<Table Name="Worldelapsedtimer" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="byte" />
		<Field Name="Field3" Type="byte" />
	</Table>
	<Table Name="WorldMapArea" Build="25632">
		<Field Name="AreaName" Type="string" />
		<Field Name="LocLeft" Type="float" />
		<Field Name="LocRight" Type="float" />
		<Field Name="LocTop" Type="float" />
		<Field Name="LocBottom" Type="float" />
		<Field Name="Flags" Type="uint" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="AreaID" Type="ushort" />
		<Field Name="DisplayMapID" Type="ushort" />
		<Field Name="DefaultDungeonFloor" Type="short" />
		<Field Name="ParentWorldMapID" Type="ushort" />
		<Field Name="LevelRangeMin" Type="byte" />
		<Field Name="LevelRangeMax" Type="byte" />
		<Field Name="BountySetID" Type="byte" />
		<Field Name="BountyBoardLocation" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionID" Type="uint" />
	</Table>
	<Table Name="Worldmapcontinent" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="2" />
		<Field Name="Field2" Type="float" />
		<Field Name="Field3" Type="float" ArraySize="2" />
		<Field Name="Field4" Type="float" ArraySize="2" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="byte" />
		<Field Name="Field8" Type="byte" />
		<Field Name="Field9" Type="byte" />
		<Field Name="FieldA" Type="byte" />
		<Field Name="FieldB" Type="byte" />
	</Table>
	<Table Name="WorldMapOverlay" Build="25632">
		<Field Name="TextureName" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TextureWidth" Type="ushort" />
		<Field Name="TextureHeight" Type="ushort" />
		<Field Name="MapAreaID" Type="uint" />
		<Field Name="AreaID" Type="uint" ArraySize="4" />
		<Field Name="OffsetX" Type="int" />
		<Field Name="OffsetY" Type="int" />
		<Field Name="HitRectTop" Type="int" />
		<Field Name="HitRectLeft" Type="int" />
		<Field Name="HitRectBottom" Type="int" />
		<Field Name="HitRectRight" Type="int" />
		<Field Name="PlayerConditionID" Type="uint" />
		<Field Name="Flags" Type="uint" />
	</Table>
	<Table Name="WorldMapTransforms" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RegionMin" Type="float" ArraySize="3" />
		<Field Name="RegionMax" Type="float" ArraySize="3" />
		<Field Name="RegionOffset" Type="float" ArraySize="2" />
		<Field Name="RegionScale" Type="float" />
		<Field Name="MapID" Type="ushort" />
		<Field Name="AreaID" Type="ushort" />
		<Field Name="NewMapID" Type="ushort" />
		<Field Name="NewDungeonMapID" Type="ushort" />
		<Field Name="NewAreaID" Type="ushort" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="WorldSafeLocs" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AreaName" Type="string" />
		<Field Name="Loc" Type="float" ArraySize="3" />
		<Field Name="Facing" Type="float" />
		<Field Name="MapID" Type="ushort" />
	</Table>
	<Table Name="Worldstate" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Worldstateexpression" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
	</Table>
	<Table Name="Worldstateui" Build="25632">
		<Field Name="Field00" Type="string" />
		<Field Name="Field01" Type="string" />
		<Field Name="Field02" Type="string" />
		<Field Name="Field03" Type="string" />
		<Field Name="Field04" Type="string" />
		<Field Name="Field05" Type="ushort" />
		<Field Name="Field06" Type="ushort" />
		<Field Name="Field07" Type="ushort" />
		<Field Name="Field08" Type="ushort" />
		<Field Name="Field09" Type="ushort" />
		<Field Name="Field0A" Type="ushort" ArraySize="3" />
		<Field Name="Field0B" Type="byte" />
		<Field Name="Field0C" Type="byte" />
		<Field Name="Field0D" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field0F" Type="int" />
		<Field Name="Field10" Type="int" />
	</Table>
	<Table Name="Worldstatezonesounds" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
		<Field Name="Field4" Type="ushort" />
		<Field Name="Field5" Type="ushort" />
		<Field Name="Field6" Type="ushort" />
		<Field Name="Field7" Type="ushort" />
		<Field Name="Field8" Type="byte" />
	</Table>
	<Table Name="Zoneintromusictable" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="byte" />
		<Field Name="Field4" Type="uint" />
	</Table>
	<Table Name="Zonelight" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="ushort" />
		<Field Name="Field3" Type="ushort" />
	</Table>
	<Table Name="Zonelightpoint" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="float" ArraySize="2" />
		<Field Name="Field2" Type="byte" />
	</Table>
	<Table Name="Zonemusic" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="string" />
		<Field Name="Field2" Type="int" ArraySize="2" />
		<Field Name="Field3" Type="int" ArraySize="2" />
		<Field Name="Field4" Type="int" ArraySize="2" />
	</Table>
	<Table Name="Zonestory" Build="25632">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field1" Type="int" />
		<Field Name="Field2" Type="int" />
		<Field Name="Field3" Type="byte" />
	</Table>
</Definition>