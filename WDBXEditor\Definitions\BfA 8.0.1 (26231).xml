<?xml version="1.0"?>
<Definition xmlns:xsi="http://www.w3.org/2001/XMLSchema-instanc" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<Table Name="Achievement" Build="26231">
		<Field Name="Title" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Reward" Type="string" />
		<Field Name="Flags" Type="int" />
		<Field Name="InstanceId" Type="ushort" />
		<Field Name="Supercedes" Type="ushort" />
		<Field Name="Category" Type="ushort" />
		<Field Name="UiOrder" Type="ushort" />
		<Field Name="SharesCriteria" Type="ushort" />
		<Field Name="Faction" Type="byte" />
		<Field Name="Points" Type="byte" />
		<Field Name="MinimumCriteria" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileId" Type="int" />
		<Field Name="CriteriaTree" Type="int" />
	</Table>
	<Table Name="Achievement_Category" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Parent" Type="ushort" />
		<Field Name="UiOrder" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="AdventureJournal" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="ButtonText" Type="string" />
		<Field Name="RewardDescription" Type="string" />
		<Field Name="ContinueDescription" Type="string" />
		<Field Name="TextureFileDataId" Type="int" />
		<Field Name="ItemId" Type="int" />
		<Field Name="LfgDungeonId" Type="ushort" />
		<Field Name="QuestId" Type="ushort" />
		<Field Name="BattleMasterListId" Type="ushort" />
		<Field Name="BonusPlayerConditionId" Type="ushort" ArraySize="2" />
		<Field Name="CurrencyType" Type="ushort" />
		<Field Name="WorldMapAreaId" Type="ushort" />
		<Field Name="Type" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ButtonActionType" Type="byte" />
		<Field Name="PriorityMin" Type="byte" />
		<Field Name="PriorityMax" Type="byte" />
		<Field Name="BonusValue" Type="byte" ArraySize="2" />
		<Field Name="CurrencyQuantity" Type="byte" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="ItemQuantity" Type="int" />
	</Table>
	<Table Name="AdventureMapPOI" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Title" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="WorldPosition" Type="float" ArraySize="2" />
		<Field Name="RewardItemId" Type="int" />
		<Field Name="Type" Type="byte" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="QuestId" Type="int" />
		<Field Name="LfgDungeonId" Type="int" />
		<Field Name="UiTextureAtlasMemberId" Type="int" />
		<Field Name="UiTextureKitId" Type="int" />
		<Field Name="WorldMapAreaId" Type="int" />
		<Field Name="DungeonMapId" Type="int" />
		<Field Name="AreaTableId" Type="int" />
	</Table>
	<Table Name="AlliedRace" Build="26231">
		<Field Name="BannerColor" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceId" Type="int" />
		<Field Name="CrestTextureId" Type="int" />
		<Field Name="ModelBackgroundTextureId" Type="int" />
		<Field Name="MaleCreatureDisplayId" Type="int" />
		<Field Name="FemaleCreatureDisplayId" Type="int" />
		<Field Name="UiUnlockAchievementId" Type="int" />
	</Table>
	<Table Name="AlliedRaceRacialAbility" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="IconFileDataId" Type="int" />
	</Table>
	<Table Name="AnimationData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="uint" />
		<Field Name="Fallback" Type="ushort" />
		<Field Name="BehaviorId" Type="ushort" />
		<Field Name="BehaviorTier" Type="byte" />
	</Table>
	<Table Name="AnimKit" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OneShotDuration" Type="int" />
		<Field Name="OneShotStopAnimKitId" Type="ushort" />
		<Field Name="LowDefAnimKitId" Type="ushort" />
	</Table>
	<Table Name="AnimKitBoneSet" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="BoneDataId" Type="byte" />
		<Field Name="ParentAnimKitBoneSetId" Type="byte" />
		<Field Name="ExtraBoneCount" Type="byte" />
		<Field Name="AltAnimKitBoneSetId" Type="byte" />
	</Table>
	<Table Name="AnimKitBoneSetAlias" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BoneDataId" Type="byte" />
		<Field Name="AnimKitBoneSetId" Type="byte" />
	</Table>
	<Table Name="AnimKitConfig" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ConfigFlags" Type="int" />
	</Table>
	<Table Name="AnimKitConfigBoneSet" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AnimKitPriorityId" Type="ushort" />
		<Field Name="AnimKitBoneSetId" Type="byte" />
	</Table>
	<Table Name="AnimKitPriority" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Priority" Type="byte" />
	</Table>
	<Table Name="AnimKitReplacement" Build="26231">
		<Field Name="SrcAnimKitId" Type="ushort" />
		<Field Name="DstAnimKitId" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="AnimKitSegment" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AnimStartTime" Type="int" />
		<Field Name="EndConditionParam" Type="int" />
		<Field Name="EndConditionDelay" Type="int" />
		<Field Name="Speed" Type="float" />
		<Field Name="OverrideConfigFlags" Type="uint" />
		<Field Name="ParentAnimKitId" Type="ushort" />
		<Field Name="AnimId" Type="ushort" />
		<Field Name="AnimKitConfigId" Type="ushort" />
		<Field Name="SegmentFlags" Type="ushort" />
		<Field Name="BlendInTimeMs" Type="ushort" />
		<Field Name="BlendOutTimeMs" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="StartCondition" Type="byte" />
		<Field Name="StartConditionParam" Type="byte" />
		<Field Name="EndCondition" Type="byte" />
		<Field Name="ForcedVariation" Type="byte" />
		<Field Name="LoopToSegmentIndex" Type="byte" />
		<Field Name="StartConditionDelay" Type="int" />
	</Table>
	<Table Name="AnimReplacement" Build="26231">
		<Field Name="SrcAnimId" Type="ushort" />
		<Field Name="DstAnimId" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="AnimReplacementSet" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ExecOrder" Type="byte" />
	</Table>
	<Table Name="AreaFarClipOverride" Build="26231">
		<Field Name="AreaId" Type="int" />
		<Field Name="MinFarClip" Type="float" />
		<Field Name="MinHorizonStart" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="AreaGroupMember" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AreaId" Type="ushort" />
	</Table>
	<Table Name="AreaPOI" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Flags" Type="int" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="PoiDataType" Type="int" />
		<Field Name="PoiData" Type="int" />
		<Field Name="ContinentId" Type="ushort" />
		<Field Name="AreaId" Type="ushort" />
		<Field Name="WorldStateId" Type="ushort" />
		<Field Name="Importance" Type="byte" />
		<Field Name="Icon" Type="byte" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="PortLocId" Type="int" />
		<Field Name="UiTextureAtlasMemberId" Type="int" />
		<Field Name="MapFloor" Type="int" />
		<Field Name="WmoGroupId" Type="int" />
	</Table>
	<Table Name="AreaPOIState" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
		<Field Name="WorldStateValue" Type="byte" />
		<Field Name="IconEnumValue" Type="byte" />
		<Field Name="UiTextureAtlasMemberId" Type="int" />
	</Table>
	<Table Name="AreaTable" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ZoneName" Type="string" />
		<Field Name="AreaName" Type="string" />
		<Field Name="Flags" Type="uint" ArraySize="2" />
		<Field Name="AmbientMultiplier" Type="float" />
		<Field Name="ContinentId" Type="ushort" />
		<Field Name="ParentAreaId" Type="ushort" />
		<Field Name="AreaBit" Type="ushort" />
		<Field Name="AmbienceId" Type="ushort" />
		<Field Name="ZoneMusic" Type="ushort" />
		<Field Name="IntroSound" Type="ushort" />
		<Field Name="LiquidTypeId" Type="ushort" ArraySize="4" />
		<Field Name="UwZoneMusic" Type="ushort" />
		<Field Name="UwAmbience" Type="ushort" />
		<Field Name="PvpCombatWorldStateId" Type="ushort" />
		<Field Name="SoundProviderPref" Type="byte" />
		<Field Name="SoundProviderPrefUnderwater" Type="byte" />
		<Field Name="ExplorationLevel" Type="byte" />
		<Field Name="FactionGroupMask" Type="byte" />
		<Field Name="MountFlags" Type="byte" />
		<Field Name="WildBattlePetLevelMin" Type="byte" />
		<Field Name="WildBattlePetLevelMax" Type="byte" />
		<Field Name="WindSettingsId" Type="byte" />
		<Field Name="UwIntroSound" Type="int" />
	</Table>
	<Table Name="AreaTrigger" Build="26231">
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="Radius" Type="float" />
		<Field Name="BoxLength" Type="float" />
		<Field Name="BoxWidth" Type="float" />
		<Field Name="BoxHeight" Type="float" />
		<Field Name="BoxYaw" Type="float" />
		<Field Name="ContinentId" Type="ushort" />
		<Field Name="PhaseId" Type="ushort" />
		<Field Name="PhaseGroupId" Type="ushort" />
		<Field Name="ShapeId" Type="ushort" />
		<Field Name="AreaTriggerActionSetId" Type="ushort" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="ShapeType" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="AreaTriggerActionSet" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="ushort" />
	</Table>
	<Table Name="AreaTriggerBox" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Extents" Type="float" ArraySize="3" />
	</Table>
	<Table Name="AreaTriggerCreateProperties" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StartShapeId" Type="ushort" />
		<Field Name="ShapeType" Type="byte" />
	</Table>
	<Table Name="AreaTriggerCylinder" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Radius" Type="float" />
		<Field Name="Height" Type="float" />
		<Field Name="ZOffset" Type="float" />
	</Table>
	<Table Name="AreaTriggerSphere" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaxRadius" Type="float" />
	</Table>
	<Table Name="ArmorLocation" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Clothmodifier" Type="float" />
		<Field Name="Leathermodifier" Type="float" />
		<Field Name="Chainmodifier" Type="float" />
		<Field Name="Platemodifier" Type="float" />
		<Field Name="Modifier" Type="float" />
	</Table>
	<Table Name="Artifact" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="UiBarOverlayColor" Type="uint" />
		<Field Name="UiBarBackgroundColor" Type="uint" />
		<Field Name="UiNameColor" Type="uint" />
		<Field Name="UiTextureKitId" Type="ushort" />
		<Field Name="ChrSpecializationId" Type="ushort" />
		<Field Name="ArtifactCategoryId" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UiModelSceneId" Type="int" />
		<Field Name="SpellVisualKitId" Type="int" />
	</Table>
	<Table Name="ArtifactAppearance" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="UiSwatchColor" Type="uint" />
		<Field Name="UiModelSaturation" Type="float" />
		<Field Name="UiModelOpacity" Type="float" />
		<Field Name="OverrideShapeshiftDisplayId" Type="int" />
		<Field Name="ArtifactAppearanceSetId" Type="ushort" />
		<Field Name="UiCameraId" Type="ushort" />
		<Field Name="DisplayIndex" Type="byte" />
		<Field Name="ItemAppearanceModifierId" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="OverrideShapeshiftFormId" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UnlockPlayerConditionId" Type="int" />
		<Field Name="UiItemAppearanceId" Type="int" />
		<Field Name="UiAltItemAppearanceId" Type="int" />
	</Table>
	<Table Name="ArtifactAppearanceSet" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="UiCameraId" Type="ushort" />
		<Field Name="AltHandUICameraId" Type="ushort" />
		<Field Name="DisplayIndex" Type="byte" />
		<Field Name="ForgeAttachmentOverride" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ArtifactCategory" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="XpMultCurrencyId" Type="ushort" />
		<Field Name="XpMultCurveId" Type="ushort" />
	</Table>
	<Table Name="ArtifactPower" Build="26231">
		<Field Name="DisplayPos" Type="float" ArraySize="2" />
		<Field Name="ArtifactId" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MaxPurchasableRank" Type="byte" />
		<Field Name="Tier" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Label" Type="int" />
	</Table>
	<Table Name="ArtifactPowerLink" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PowerA" Type="ushort" />
		<Field Name="PowerB" Type="ushort" />
	</Table>
	<Table Name="ArtifactPowerPicker" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionId" Type="int" />
	</Table>
	<Table Name="ArtifactPowerRank" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="AuraPointsOverride" Type="float" />
		<Field Name="ItemBonusListId" Type="ushort" />
		<Field Name="RankIndex" Type="byte" />
	</Table>
	<Table Name="ArtifactQuestXP" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="int" ArraySize="10" />
	</Table>
	<Table Name="ArtifactTier" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ArtifactTier" Type="int" />
		<Field Name="MaxNumTraits" Type="int" />
		<Field Name="MaxArtifactKnowledge" Type="int" />
		<Field Name="KnowledgePlayerCondition" Type="int" />
		<Field Name="MinimumEmpowerKnowledge" Type="int" />
	</Table>
	<Table Name="ArtifactUnlock" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemBonusListId" Type="ushort" />
		<Field Name="PowerRank" Type="byte" />
		<Field Name="PowerId" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
	</Table>
	<Table Name="AuctionHouse" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="FactionId" Type="ushort" />
		<Field Name="DepositRate" Type="byte" />
		<Field Name="ConsignmentRate" Type="byte" />
	</Table>
	<Table Name="AzeriteEmpoweredItem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="int" />
		<Field Name="AzeriteTierUnlockSetId" Type="int" />
		<Field Name="AzeritePowerSetId" Type="int" />
	</Table>
	<Table Name="AzeriteItem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="int" />
	</Table>
	<Table Name="AzeriteItemMilestonePower" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AzeritePowerId" Type="ushort" />
		<Field Name="RequiredLevel" Type="byte" />
	</Table>
	<Table Name="AzeritePower" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="ItemBonusListId" Type="int" />
	</Table>
	<Table Name="AzeritePowerSetMember" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AzeritePowerId" Type="ushort" />
		<Field Name="Class" Type="byte" />
		<Field Name="Tier" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="AzeriteTierUnlock" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemCreationContext" Type="byte" />
		<Field Name="Tier" Type="byte" />
		<Field Name="AzeriteLevel" Type="byte" />
	</Table>
	<Table Name="BankBagSlotPrices" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Cost" Type="int" />
	</Table>
	<Table Name="BannedAddOns" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Version" Type="string" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="BarberShopStyle" Build="26231">
		<Field Name="DisplayName" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="CostModifier" Type="float" />
		<Field Name="Type" Type="byte" />
		<Field Name="Race" Type="byte" />
		<Field Name="Sex" Type="byte" />
		<Field Name="Data" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="BattlemasterList" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="GameType" Type="string" />
		<Field Name="ShortDescription" Type="string" />
		<Field Name="LongDescription" Type="string" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="MapId" Type="ushort" ArraySize="16" />
		<Field Name="HolidayWorldState" Type="ushort" />
		<Field Name="RequiredPlayerConditionId" Type="ushort" />
		<Field Name="InstanceType" Type="byte" />
		<Field Name="GroupsAllowed" Type="byte" />
		<Field Name="MaxGroupSize" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
		<Field Name="RatedPlayers" Type="byte" />
		<Field Name="MinPlayers" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="BattlePetAbility" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="BattlePetVisualId" Type="ushort" />
		<Field Name="PetTypeEnum" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Cooldown" Type="int" />
	</Table>
	<Table Name="BattlePetAbilityEffect" Build="26231">
		<Field Name="BattlePetAbilityTurnId" Type="ushort" />
		<Field Name="BattlePetVisualId" Type="ushort" />
		<Field Name="AuraBattlePetAbilityId" Type="ushort" />
		<Field Name="BattlePetEffectPropertiesId" Type="ushort" />
		<Field Name="Param" Type="ushort" ArraySize="6" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="BattlePetAbilityState" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="uint" />
		<Field Name="BattlePetStateId" Type="byte" />
	</Table>
	<Table Name="BattlePetAbilityTurn" Build="26231">
		<Field Name="BattlePetAbilityId" Type="ushort" />
		<Field Name="BattlePetVisualId" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="TurnTypeEnum" Type="byte" />
		<Field Name="EventTypeEnum" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="BattlePetBreedQuality" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StateMultiplier" Type="float" />
		<Field Name="QualityEnum" Type="byte" />
	</Table>
	<Table Name="BattlePetBreedState" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="ushort" />
		<Field Name="BattlePetStateId" Type="byte" />
	</Table>
	<Table Name="BattlePetDisplayOverride" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BattlePetSpeciesId" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="CreatureDisplayInfoId" Type="int" />
		<Field Name="PriorityCategory" Type="byte" />
	</Table>
	<Table Name="BattlePetEffectProperties" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ParamLabel" Type="string" ArraySize="6" />
		<Field Name="BattlePetVisualId" Type="ushort" />
		<Field Name="ParamTypeEnum" Type="byte" ArraySize="6" />
	</Table>
	<Table Name="BattlePetNPCTeamMember" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="BattlePetSpecies" Build="26231">
		<Field Name="SourceText" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="CreatureId" Type="int" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="SummonSpellId" Type="int" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="PetTypeEnum" Type="byte" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CardUIModelSceneId" Type="int" />
		<Field Name="LoadoutUIModelSceneId" Type="int" />
	</Table>
	<Table Name="BattlePetSpeciesState" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="uint" />
		<Field Name="BattlePetStateId" Type="byte" />
	</Table>
	<Table Name="BattlePetSpeciesXAbility" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BattlePetAbilityId" Type="ushort" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="SlotEnum" Type="byte" />
	</Table>
	<Table Name="BattlePetState" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LuaName" Type="string" />
		<Field Name="BattlePetVisualId" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
	</Table>
	<Table Name="BattlePetVisual" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SceneScriptFunction" Type="string" />
		<Field Name="SpellVisualId" Type="int" />
		<Field Name="CastMilliSeconds" Type="ushort" />
		<Field Name="ImpactMilliSeconds" Type="ushort" />
		<Field Name="SceneScriptPackageId" Type="ushort" />
		<Field Name="RangeTypeEnum" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="BeamEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BeamId" Type="int" />
		<Field Name="SourceMinDistance" Type="float" />
		<Field Name="FixedLength" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="SourceOffset" Type="int" />
		<Field Name="DestOffset" Type="int" />
		<Field Name="SourceAttachId" Type="int" />
		<Field Name="DestAttachId" Type="int" />
		<Field Name="SourcePositionerId" Type="int" />
		<Field Name="DestPositionerId" Type="int" />
	</Table>
	<Table Name="BoneWindModifierModel" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="BoneWindModifierId" Type="int" />
	</Table>
	<Table Name="BoneWindModifiers" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Multiplier" Type="float" ArraySize="3" />
		<Field Name="PhaseMultiplier" Type="float" />
	</Table>
	<Table Name="Bounty" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="QuestId" Type="ushort" />
		<Field Name="FactionId" Type="ushort" />
		<Field Name="TurninPlayerConditionId" Type="int" />
	</Table>
	<Table Name="BountySet" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LockedQuestId" Type="ushort" />
		<Field Name="VisiblePlayerConditionId" Type="int" />
	</Table>
	<Table Name="BroadcastText" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text" Type="string" />
		<Field Name="Text1" Type="string" />
		<Field Name="EmoteId" Type="ushort" ArraySize="3" />
		<Field Name="EmoteDelay" Type="ushort" ArraySize="3" />
		<Field Name="EmotesId" Type="ushort" />
		<Field Name="LanguageId" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ConditionId" Type="int" />
		<Field Name="SoundEntriesId" Type="int" ArraySize="2" />
	</Table>
	<Table Name="CameraEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CameraEffectEntry" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Duration" Type="float" />
		<Field Name="Delay" Type="float" />
		<Field Name="Phase" Type="float" />
		<Field Name="Amplitude" Type="float" />
		<Field Name="AmplitudeB" Type="float" />
		<Field Name="Frequency" Type="float" />
		<Field Name="RadiusMin" Type="float" />
		<Field Name="RadiusMax" Type="float" />
		<Field Name="AmplitudeCurveId" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="EffectType" Type="byte" />
		<Field Name="DirectionType" Type="byte" />
		<Field Name="MovementType" Type="byte" />
		<Field Name="AttenuationType" Type="byte" />
	</Table>
	<Table Name="CameraMode" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PositionOffset" Type="float" ArraySize="3" />
		<Field Name="TargetOffset" Type="float" ArraySize="3" />
		<Field Name="PositionSmoothing" Type="float" />
		<Field Name="RotationSmoothing" Type="float" />
		<Field Name="FieldOfView" Type="float" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="Type" Type="byte" />
		<Field Name="LockedPositionOffsetBase" Type="byte" />
		<Field Name="LockedPositionOffsetDirection" Type="byte" />
		<Field Name="LockedTargetOffsetBase" Type="byte" />
		<Field Name="LockedTargetOffsetDirection" Type="byte" />
	</Table>
	<Table Name="CastableRaidBuffs" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CastingSpellId" Type="int" />
	</Table>
	<Table Name="CelestialBody" Build="26231">
		<Field Name="BaseFileDataId" Type="int" />
		<Field Name="LightMaskFileDataId" Type="int" />
		<Field Name="GlowMaskFileDataId" Type="int" ArraySize="2" />
		<Field Name="AtmosphericMaskFileDataId" Type="int" />
		<Field Name="AtmosphericModifiedFileDataId" Type="int" />
		<Field Name="GlowModifiedFileDataId" Type="int" ArraySize="2" />
		<Field Name="ScrollURate" Type="float" ArraySize="2" />
		<Field Name="ScrollVRate" Type="float" ArraySize="2" />
		<Field Name="RotateRate" Type="float" />
		<Field Name="GlowMaskScale" Type="float" ArraySize="2" />
		<Field Name="AtmosphericMaskScale" Type="float" />
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="BodyBaseScale" Type="float" />
		<Field Name="SkyArrayBand" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Cfg_Categories" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="LocaleMask" Type="ushort" />
		<Field Name="CreateCharsetMask" Type="byte" />
		<Field Name="ExistingCharsetMask" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Cfg_Configs" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaxDamageReductionPctPhysical" Type="float" />
		<Field Name="PlayerAttackSpeedBase" Type="ushort" />
		<Field Name="PlayerKillingAllowed" Type="byte" />
		<Field Name="Roleplaying" Type="byte" />
	</Table>
	<Table Name="Cfg_Regions" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Tag" Type="string" />
		<Field Name="Raidorigin" Type="int" />
		<Field Name="ChallengeOrigin" Type="int" />
		<Field Name="RegionId" Type="ushort" />
		<Field Name="RegionGroupMask" Type="byte" />
	</Table>
	<Table Name="CharacterFaceBoneSet" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BoneSetFileDataId" Type="int" />
		<Field Name="SexId" Type="byte" />
		<Field Name="FaceVariationIndex" Type="byte" />
		<Field Name="Resolution" Type="byte" />
	</Table>
	<Table Name="CharacterFacialHairStyles" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Geoset" Type="uint" ArraySize="5" />
		<Field Name="RaceId" Type="byte" />
		<Field Name="SexId" Type="byte" />
		<Field Name="VariationId" Type="byte" />
	</Table>
	<Table Name="CharacterLoadout" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="ulong" />
		<Field Name="ChrClassId" Type="byte" />
		<Field Name="Purpose" Type="byte" />
	</Table>
	<Table Name="CharacterLoadoutItem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="int" />
		<Field Name="CharacterLoadoutId" Type="ushort" />
	</Table>
	<Table Name="CharacterServiceInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FlowTitle" Type="string" />
		<Field Name="PopupTitle" Type="string" />
		<Field Name="PopupDescription" Type="string" />
		<Field Name="BoostType" Type="int" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="Priority" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="ProfessionLevel" Type="int" />
		<Field Name="BoostLevel" Type="int" />
		<Field Name="Expansion" Type="int" />
		<Field Name="PopupUITextureKitId" Type="int" />
	</Table>
	<Table Name="CharBaseInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceId" Type="byte" />
		<Field Name="ClassId" Type="byte" />
	</Table>
	<Table Name="CharBaseSection" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VariationEnum" Type="byte" />
		<Field Name="ResolutionVariationEnum" Type="byte" />
		<Field Name="LayoutResType" Type="byte" />
	</Table>
	<Table Name="CharComponentTextureLayouts" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Width" Type="ushort" />
		<Field Name="Height" Type="ushort" />
	</Table>
	<Table Name="CharComponentTextureSections" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OverlapSectionMask" Type="int" />
		<Field Name="X" Type="ushort" />
		<Field Name="Y" Type="ushort" />
		<Field Name="Width" Type="ushort" />
		<Field Name="Height" Type="ushort" />
		<Field Name="CharComponentTextureLayoutId" Type="byte" />
		<Field Name="SectionType" Type="byte" />
	</Table>
	<Table Name="CharHairGeosets" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HdCustomGeoFileDataId" Type="int" />
		<Field Name="RaceId" Type="byte" />
		<Field Name="SexId" Type="byte" />
		<Field Name="VariationId" Type="byte" />
		<Field Name="VariationType" Type="byte" />
		<Field Name="GeosetId" Type="byte" />
		<Field Name="GeosetType" Type="byte" />
		<Field Name="Showscalp" Type="byte" />
		<Field Name="ColorIndex" Type="byte" />
		<Field Name="CustomGeoFileDataId" Type="int" />
	</Table>
	<Table Name="CharSections" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaterialResourcesId" Type="int" ArraySize="3" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="RaceId" Type="byte" />
		<Field Name="SexId" Type="byte" />
		<Field Name="BaseSection" Type="byte" />
		<Field Name="VariationIndex" Type="byte" />
		<Field Name="ColorIndex" Type="byte" />
	</Table>
	<Table Name="CharShipment" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TreasureId" Type="int" />
		<Field Name="Duration" Type="int" />
		<Field Name="SpellId" Type="int" />
		<Field Name="DummyItemId" Type="int" />
		<Field Name="OnCompleteSpellId" Type="int" />
		<Field Name="ContainerId" Type="ushort" />
		<Field Name="GarrFollowerId" Type="ushort" />
		<Field Name="MaxShipments" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CharShipmentContainer" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PendingText" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="WorkingSpellVisualId" Type="int" />
		<Field Name="UiTextureKitId" Type="ushort" />
		<Field Name="WorkingDisplayInfoId" Type="ushort" />
		<Field Name="SmallDisplayInfoId" Type="ushort" />
		<Field Name="MediumDisplayInfoId" Type="ushort" />
		<Field Name="LargeDisplayInfoId" Type="ushort" />
		<Field Name="CrossFactionId" Type="ushort" />
		<Field Name="BaseCapacity" Type="byte" />
		<Field Name="GarrBuildingType" Type="byte" />
		<Field Name="GarrTypeId" Type="byte" />
		<Field Name="MediumThreshold" Type="byte" />
		<Field Name="LargeThreshold" Type="byte" />
		<Field Name="Faction" Type="byte" />
		<Field Name="CompleteSpellVisualId" Type="int" />
	</Table>
	<Table Name="CharStartOutfit" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="int" ArraySize="24" />
		<Field Name="PetDisplayId" Type="int" />
		<Field Name="ClassId" Type="byte" />
		<Field Name="SexId" Type="byte" />
		<Field Name="OutfitId" Type="byte" />
		<Field Name="PetFamilyId" Type="byte" />
	</Table>
	<Table Name="CharTitles" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Name1" Type="string" />
		<Field Name="MaskId" Type="ushort" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ChatChannels" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Shortcut" Type="string" />
		<Field Name="Flags" Type="int" />
		<Field Name="FactionGroup" Type="byte" />
	</Table>
	<Table Name="ChatProfanity" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text" Type="string" />
		<Field Name="Language" Type="byte" />
	</Table>
	<Table Name="ChrClasses" Build="26231">
		<Field Name="PetNameToken" Type="string" />
		<Field Name="Name" Type="string" />
		<Field Name="NameFemale" Type="string" />
		<Field Name="NameMale" Type="string" />
		<Field Name="Filename" Type="string" />
		<Field Name="CreateScreenFileDataId" Type="int" />
		<Field Name="SelectScreenFileDataId" Type="int" />
		<Field Name="LowResScreenFileDataId" Type="int" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="StartingLevel" Type="int" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="CinematicSequenceId" Type="ushort" />
		<Field Name="DefaultSpec" Type="ushort" />
		<Field Name="DisplayPower" Type="byte" />
		<Field Name="SpellClassSet" Type="byte" />
		<Field Name="AttackPowerPerStrength" Type="byte" />
		<Field Name="AttackPowerPerAgility" Type="byte" />
		<Field Name="RangedAttackPowerPerAgility" Type="byte" />
		<Field Name="PrimaryStatPriority" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ChrClassesXPowerTypes" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PowerType" Type="byte" />
	</Table>
	<Table Name="ChrClassRaceSex" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClassId" Type="byte" />
		<Field Name="RaceId" Type="byte" />
		<Field Name="Sex" Type="byte" />
		<Field Name="Flags" Type="int" />
		<Field Name="SoundId" Type="int" />
		<Field Name="VoiceSoundFilterId" Type="int" />
	</Table>
	<Table Name="ChrClassTitle" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="NameMale" Type="string" />
		<Field Name="NameFemale" Type="string" />
		<Field Name="ChrClassId" Type="byte" />
	</Table>
	<Table Name="ChrClassUIDisplay" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrClassesId" Type="byte" />
		<Field Name="AdvGuidePlayerConditionId" Type="int" />
		<Field Name="SplashPlayerConditionId" Type="int" />
	</Table>
	<Table Name="ChrClassVillain" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="ChrClassId" Type="byte" />
		<Field Name="Gender" Type="byte" />
	</Table>
	<Table Name="ChrCustomization" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Sex" Type="int" />
		<Field Name="BaseSection" Type="int" />
		<Field Name="UiCustomizationType" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="ComponentSection" Type="int" ArraySize="3" />
	</Table>
	<Table Name="ChrRaces" Build="26231">
		<Field Name="ClientPrefix" Type="string" />
		<Field Name="ClientFileString" Type="string" />
		<Field Name="Name" Type="string" />
		<Field Name="NameFemale" Type="string" />
		<Field Name="NameLowercase" Type="string" />
		<Field Name="NameFemaleLowercase" Type="string" />
		<Field Name="Flags" Type="int" />
		<Field Name="MaleDisplayId" Type="int" />
		<Field Name="FemaleDisplayId" Type="int" />
		<Field Name="CreateScreenFileDataId" Type="int" />
		<Field Name="SelectScreenFileDataId" Type="int" />
		<Field Name="MaleCustomizeOffset" Type="float" ArraySize="3" />
		<Field Name="FemaleCustomizeOffset" Type="float" ArraySize="3" />
		<Field Name="LowResScreenFileDataId" Type="int" />
		<Field Name="StartingLevel" Type="int" />
		<Field Name="UiDisplayOrder" Type="int" />
		<Field Name="MaleModelFallbackArmor2Scale" Type="float" />
		<Field Name="FemaleModelFallbackArmor2Scale" Type="float" />
		<Field Name="FactionId" Type="ushort" />
		<Field Name="ResSicknessSpellId" Type="ushort" />
		<Field Name="SplashSoundId" Type="ushort" />
		<Field Name="CinematicSequenceId" Type="ushort" />
		<Field Name="BaseLanguage" Type="byte" />
		<Field Name="CreatureType" Type="byte" />
		<Field Name="Alliance" Type="byte" />
		<Field Name="RaceRelated" Type="byte" />
		<Field Name="UnalteredVisualRaceId" Type="byte" />
		<Field Name="CharComponentTextureLayoutId" Type="byte" />
		<Field Name="DefaultClassId" Type="byte" />
		<Field Name="NeutralRaceId" Type="byte" />
		<Field Name="CharComponentTexLayoutHiResId" Type="byte" />
		<Field Name="MaleModelFallbackRaceId" Type="byte" />
		<Field Name="MaleModelFallbackSex" Type="byte" />
		<Field Name="FemaleModelFallbackRaceId" Type="byte" />
		<Field Name="FemaleModelFallbackSex" Type="byte" />
		<Field Name="MaleTextureFallbackRaceId" Type="byte" />
		<Field Name="MaleTextureFallbackSex" Type="byte" />
		<Field Name="FemaleTextureFallbackRaceId" Type="byte" />
		<Field Name="FemaleTextureFallbackSex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HighResMaleDisplayId" Type="int" />
		<Field Name="HighResFemaleDisplayId" Type="int" />
		<Field Name="HeritageArmorAchievementId" Type="int" />
		<Field Name="MaleSkeletonFileDataId" Type="int" />
		<Field Name="FemaleSkeletonFileDataId" Type="int" />
		<Field Name="AlteredFormStartVisualKitId" Type="int" ArraySize="3" />
		<Field Name="AlteredFormFinishVisualKitId" Type="int" ArraySize="3" />
	</Table>
	<Table Name="ChrSpecialization" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="FemaleName" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="MasterySpellId" Type="int" ArraySize="2" />
		<Field Name="ClassId" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PetTalentType" Type="byte" />
		<Field Name="Role" Type="byte" />
		<Field Name="PrimaryStatPriority" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellIconFileId" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="AnimReplacements" Type="int" />
	</Table>
	<Table Name="ChrUpgradeBucket" Build="26231">
		<Field Name="ChrSpecializationId" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ChrUpgradeBucketSpell" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
	</Table>
	<Table Name="ChrUpgradeTier" Build="26231">
		<Field Name="DisplayName" Type="string" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="NumTalents" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="CinematicCamera" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundId" Type="int" />
		<Field Name="Origin" Type="float" ArraySize="3" />
		<Field Name="OriginFacing" Type="float" />
		<Field Name="FileDataId" Type="int" />
	</Table>
	<Table Name="CinematicSequences" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundId" Type="int" />
		<Field Name="Camera" Type="ushort" ArraySize="8" />
	</Table>
	<Table Name="ClientSceneEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SceneScriptPackageId" Type="int" />
	</Table>
	<Table Name="CloakDampening" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Angle" Type="float" ArraySize="5" />
		<Field Name="Dampening" Type="float" ArraySize="5" />
		<Field Name="TailAngle" Type="float" ArraySize="2" />
		<Field Name="TailDampening" Type="float" ArraySize="2" />
		<Field Name="TabardAngle" Type="float" />
		<Field Name="TabardDampening" Type="float" />
		<Field Name="ExpectedWeaponSize" Type="float" />
	</Table>
	<Table Name="CombatCondition" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WorldStateExpressionId" Type="ushort" />
		<Field Name="SelfConditionId" Type="ushort" />
		<Field Name="TargetConditionId" Type="ushort" />
		<Field Name="FriendConditionId" Type="ushort" ArraySize="2" />
		<Field Name="EnemyConditionId" Type="ushort" ArraySize="2" />
		<Field Name="FriendConditionOp" Type="byte" ArraySize="2" />
		<Field Name="FriendConditionCount" Type="byte" ArraySize="2" />
		<Field Name="FriendConditionLogic" Type="byte" />
		<Field Name="EnemyConditionOp" Type="byte" ArraySize="2" />
		<Field Name="EnemyConditionCount" Type="byte" ArraySize="2" />
		<Field Name="EnemyConditionLogic" Type="byte" />
	</Table>
	<Table Name="CommentatorStartLocation" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="MapId" Type="int" />
	</Table>
	<Table Name="CommentatorTrackedCooldown" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Priority" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="SpellId" Type="int" />
	</Table>
	<Table Name="ComponentModelFileData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GenderIndex" Type="byte" />
		<Field Name="ClassId" Type="byte" />
		<Field Name="RaceId" Type="byte" />
		<Field Name="PositionIndex" Type="byte" />
	</Table>
	<Table Name="ComponentTextureFileData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GenderIndex" Type="byte" />
		<Field Name="ClassId" Type="byte" />
		<Field Name="RaceId" Type="byte" />
	</Table>
	<Table Name="ConfigurationWarning" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Warning" Type="string" />
		<Field Name="Type" Type="int" />
	</Table>
	<Table Name="ContentTuning" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinLevel" Type="int" />
		<Field Name="MaxLevel" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="ExpectedStatModId" Type="int" />
	</Table>
	<Table Name="Contribution" Build="26231">
		<Field Name="Description" Type="string" />
		<Field Name="Name" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ManagedWorldStateInputId" Type="int" />
		<Field Name="UiTextureAtlasMemberId" Type="int" ArraySize="4" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="ConversationLine" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BroadcastTextId" Type="int" />
		<Field Name="SpellVisualKitId" Type="int" />
		<Field Name="AdditionalDuration" Type="uint" />
		<Field Name="NextConversationLineId" Type="ushort" />
		<Field Name="AnimKitId" Type="ushort" />
		<Field Name="SpeechType" Type="byte" />
		<Field Name="StartAnimation" Type="byte" />
		<Field Name="EndAnimation" Type="byte" />
	</Table>
	<Table Name="Creature" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="NameAlt" Type="string" />
		<Field Name="Title" Type="string" />
		<Field Name="TitleAlt" Type="string" />
		<Field Name="AlwaysItem" Type="int" ArraySize="3" />
		<Field Name="MountCreatureId" Type="int" />
		<Field Name="DisplayId" Type="int" ArraySize="4" />
		<Field Name="DisplayProbability" Type="float" ArraySize="4" />
		<Field Name="CreatureType" Type="byte" />
		<Field Name="CreatureFamily" Type="byte" />
		<Field Name="Classification" Type="byte" />
		<Field Name="StartAnimState" Type="byte" />
	</Table>
	<Table Name="CreatureDifficulty" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="uint" ArraySize="7" />
		<Field Name="FactionId" Type="ushort" />
		<Field Name="ExpansionId" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
		<Field Name="ContentTuningId" Type="int" />
	</Table>
	<Table Name="CreatureDisplayInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureModelScale" Type="float" />
		<Field Name="ModelId" Type="ushort" />
		<Field Name="NPCSoundId" Type="ushort" />
		<Field Name="SizeClass" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Gender" Type="byte" />
		<Field Name="ExtendedDisplayInfoId" Type="int" />
		<Field Name="PortraitTextureFileDataId" Type="int" />
		<Field Name="CreatureModelAlpha" Type="byte" />
		<Field Name="SoundId" Type="ushort" />
		<Field Name="PlayerOverrideScale" Type="float" />
		<Field Name="PortraitCreatureDisplayInfoId" Type="int" />
		<Field Name="BloodId" Type="byte" />
		<Field Name="ParticleColorId" Type="ushort" />
		<Field Name="ObjectEffectPackageId" Type="ushort" />
		<Field Name="AnimReplacementSetId" Type="ushort" />
		<Field Name="UnarmedWeaponType" Type="byte" />
		<Field Name="StateSpellVisualKitId" Type="int" />
		<Field Name="PetInstanceScale" Type="float" />
		<Field Name="MountPoofSpellVisualKitId" Type="int" />
		<Field Name="DissolveEffectId" Type="int" />
		<Field Name="TextureVariationFileDataId" Type="int" ArraySize="3" />
	</Table>
	<Table Name="CreatureDisplayInfoCond" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="ulong" />
		<Field Name="CustomOption0Mask" Type="uint" ArraySize="2" />
		<Field Name="CustomOption1Mask" Type="int" ArraySize="2" />
		<Field Name="CustomOption2Mask" Type="int" ArraySize="2" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Gender" Type="byte" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="SkinColorMask" Type="int" />
		<Field Name="HairColorMask" Type="int" />
		<Field Name="HairStyleMask" Type="int" />
		<Field Name="FaceStyleMask" Type="int" />
		<Field Name="FacialHairStyleMask" Type="int" />
		<Field Name="CreatureModelDataId" Type="int" />
		<Field Name="TextureVariationFileDataId" Type="int" ArraySize="3" />
	</Table>
	<Table Name="CreatureDisplayInfoEvt" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Fourcc" Type="int" />
		<Field Name="SpellVisualKitId" Type="int" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CreatureDisplayInfoExtra" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BakeMaterialResourcesId" Type="int" />
		<Field Name="HDBakeMaterialResourcesId" Type="int" />
		<Field Name="DisplayRaceId" Type="byte" />
		<Field Name="DisplaySexId" Type="byte" />
		<Field Name="DisplayClassId" Type="byte" />
		<Field Name="SkinId" Type="byte" />
		<Field Name="FaceId" Type="byte" />
		<Field Name="HairStyleId" Type="byte" />
		<Field Name="HairColorId" Type="byte" />
		<Field Name="FacialHairId" Type="byte" />
		<Field Name="CustomDisplayOption" Type="byte" ArraySize="3" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CreatureDisplayInfoGeosetData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GeosetIndex" Type="byte" />
		<Field Name="GeosetValue" Type="byte" />
	</Table>
	<Table Name="CreatureDisplayInfoTrn" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DstCreatureDisplayInfoId" Type="int" />
		<Field Name="MaxTime" Type="float" />
		<Field Name="DissolveEffectId" Type="int" />
		<Field Name="StartVisualKitId" Type="int" />
		<Field Name="FinishVisualKitId" Type="int" />
	</Table>
	<Table Name="CreatureDispXUiCamera" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureDisplayInfoId" Type="int" />
		<Field Name="UiCameraId" Type="ushort" />
	</Table>
	<Table Name="CreatureFamily" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="MinScale" Type="float" />
		<Field Name="MaxScale" Type="float" />
		<Field Name="IconFileId" Type="int" />
		<Field Name="SkillLine" Type="ushort" ArraySize="2" />
		<Field Name="PetFoodMask" Type="ushort" />
		<Field Name="MinScaleLevel" Type="byte" />
		<Field Name="MaxScaleLevel" Type="byte" />
		<Field Name="PetTalentType" Type="byte" />
	</Table>
	<Table Name="CreatureImmunities" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Mechanic" Type="int" ArraySize="2" />
		<Field Name="School" Type="byte" />
		<Field Name="MechanicsAllowed" Type="byte" />
		<Field Name="EffectsAllowed" Type="byte" />
		<Field Name="StatesAllowed" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="DispelType" Type="int" />
		<Field Name="Effect" Type="int" ArraySize="9" />
		<Field Name="State" Type="int" ArraySize="16" />
	</Table>
	<Table Name="CreatureModelData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelScale" Type="float" />
		<Field Name="FootprintTextureLength" Type="float" />
		<Field Name="FootprintTextureWidth" Type="float" />
		<Field Name="FootprintParticleScale" Type="float" />
		<Field Name="CollisionWidth" Type="float" />
		<Field Name="CollisionHeight" Type="float" />
		<Field Name="MountHeight" Type="float" />
		<Field Name="GeoBox" Type="float" ArraySize="6" />
		<Field Name="WorldEffectScale" Type="float" />
		<Field Name="AttachedEffectScale" Type="float" />
		<Field Name="MissileCollisionRadius" Type="float" />
		<Field Name="MissileCollisionPush" Type="float" />
		<Field Name="MissileCollisionRaise" Type="float" />
		<Field Name="OverrideLootEffectScale" Type="float" />
		<Field Name="OverrideNameScale" Type="float" />
		<Field Name="OverrideSelectionRadius" Type="float" />
		<Field Name="TamedPetBaseScale" Type="float" />
		<Field Name="HoverHeight" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="SizeClass" Type="int" />
		<Field Name="BloodId" Type="int" />
		<Field Name="FootprintTextureId" Type="int" />
		<Field Name="FoleyMaterialId" Type="int" />
		<Field Name="FootstepCameraEffectId" Type="int" />
		<Field Name="DeathThudCameraEffectId" Type="int" />
		<Field Name="SoundId" Type="int" />
		<Field Name="CreatureGeosetDataId" Type="int" />
	</Table>
	<Table Name="CreatureMovementInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SmoothFacingChaseRate" Type="float" />
	</Table>
	<Table Name="CreatureSoundData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FidgetDelaySecondsMin" Type="float" />
		<Field Name="FidgetDelaySecondsMax" Type="float" />
		<Field Name="CreatureImpactType" Type="byte" />
		<Field Name="SoundExertionId" Type="int" />
		<Field Name="SoundExertionCriticalId" Type="int" />
		<Field Name="SoundInjuryId" Type="int" />
		<Field Name="SoundInjuryCriticalId" Type="int" />
		<Field Name="SoundInjuryCrushingBlowId" Type="int" />
		<Field Name="SoundDeathId" Type="int" />
		<Field Name="SoundStunId" Type="int" />
		<Field Name="SoundStandId" Type="int" />
		<Field Name="SoundFootstepId" Type="int" />
		<Field Name="SoundAggroId" Type="int" />
		<Field Name="SoundWingFlapId" Type="int" />
		<Field Name="SoundWingGlideId" Type="int" />
		<Field Name="SoundAlertId" Type="int" />
		<Field Name="NPCSoundId" Type="int" />
		<Field Name="LoopSoundId" Type="int" />
		<Field Name="SoundJumpStartId" Type="int" />
		<Field Name="SoundJumpEndId" Type="int" />
		<Field Name="SoundPetAttackId" Type="int" />
		<Field Name="SoundPetOrderId" Type="int" />
		<Field Name="SoundPetDismissId" Type="int" />
		<Field Name="BirthSoundId" Type="int" />
		<Field Name="SpellCastDirectedSoundId" Type="int" />
		<Field Name="SubmergeSoundId" Type="int" />
		<Field Name="SubmergedSoundId" Type="int" />
		<Field Name="CreatureSoundDataIDPet" Type="int" />
		<Field Name="WindupSoundId" Type="int" />
		<Field Name="WindupCriticalSoundId" Type="int" />
		<Field Name="ChargeSoundId" Type="int" />
		<Field Name="ChargeCriticalSoundId" Type="int" />
		<Field Name="BattleShoutSoundId" Type="int" />
		<Field Name="BattleShoutCriticalSoundId" Type="int" />
		<Field Name="TauntSoundId" Type="int" />
		<Field Name="SoundFidget" Type="int" ArraySize="5" />
		<Field Name="CustomAttack" Type="int" ArraySize="4" />
	</Table>
	<Table Name="CreatureType" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CreatureXContribution" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ContributionId" Type="int" />
	</Table>
	<Table Name="CreatureXDisplayInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureDisplayInfoId" Type="int" />
		<Field Name="Probability" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="Criteria" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Asset" Type="int" />
		<Field Name="StartAsset" Type="int" />
		<Field Name="FailAsset" Type="uint" />
		<Field Name="ModifierTreeId" Type="int" />
		<Field Name="StartTimer" Type="ushort" />
		<Field Name="EligibilityWorldStateId" Type="ushort" />
		<Field Name="Type" Type="byte" />
		<Field Name="StartEvent" Type="byte" />
		<Field Name="FailEvent" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="EligibilityWorldStateValue" Type="byte" />
	</Table>
	<Table Name="CriteriaTree" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
		<Field Name="Amount" Type="int" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="Operator" Type="byte" />
		<Field Name="CriteriaId" Type="int" />
		<Field Name="Parent" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="CriteriaTreeXEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WorldEffectId" Type="ushort" />
	</Table>
	<Table Name="CurrencyCategory" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ExpansionId" Type="byte" />
	</Table>
	<Table Name="CurrencyContainer" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ContainerName" Type="string" />
		<Field Name="ContainerDescription" Type="string" />
		<Field Name="MinAmount" Type="int" />
		<Field Name="MaxAmount" Type="int" />
		<Field Name="ContainerIconId" Type="int" />
		<Field Name="ContainerQuality" Type="int" />
	</Table>
	<Table Name="CurrencyTypes" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="MaxQty" Type="int" />
		<Field Name="MaxEarnablePerWeek" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="CategoryId" Type="byte" />
		<Field Name="SpellCategory" Type="byte" />
		<Field Name="Quality" Type="byte" />
		<Field Name="InventoryIconFileId" Type="int" />
		<Field Name="SpellWeight" Type="int" />
	</Table>
	<Table Name="Curve" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CurvePoint" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="2" />
		<Field Name="CurveId" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="DeathThudLookups" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SizeClass" Type="byte" />
		<Field Name="TerrainTypeSoundId" Type="byte" />
		<Field Name="SoundEntryId" Type="int" />
		<Field Name="SoundEntryIDWater" Type="int" />
	</Table>
	<Table Name="DecalProperties" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="InnerRadius" Type="float" />
		<Field Name="OuterRadius" Type="float" />
		<Field Name="Rim" Type="float" />
		<Field Name="Gain" Type="float" />
		<Field Name="ModX" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="FadeIn" Type="float" />
		<Field Name="FadeOut" Type="float" />
		<Field Name="Priority" Type="byte" />
		<Field Name="BlendMode" Type="byte" />
		<Field Name="TopTextureBlendSetId" Type="int" />
		<Field Name="BotTextureBlendSetId" Type="int" />
		<Field Name="GameFlags" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="CasterDecalPropertiesId" Type="int" />
	</Table>
	<Table Name="DeclinedWord" Build="26231">
		<Field Name="Word" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="DeclinedWordCases" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DeclinedWord" Type="string" />
		<Field Name="CaseIndex" Type="byte" />
	</Table>
	<Table Name="DestructibleModelData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="State0Wmo" Type="ushort" />
		<Field Name="State1Wmo" Type="ushort" />
		<Field Name="State2Wmo" Type="ushort" />
		<Field Name="State3Wmo" Type="ushort" />
		<Field Name="HealEffectSpeed" Type="ushort" />
		<Field Name="State0ImpactEffectDoodadSet" Type="byte" />
		<Field Name="State0AmbientDoodadSet" Type="byte" />
		<Field Name="State0NameSet" Type="byte" />
		<Field Name="State1DestructionDoodadSet" Type="byte" />
		<Field Name="State1ImpactEffectDoodadSet" Type="byte" />
		<Field Name="State1AmbientDoodadSet" Type="byte" />
		<Field Name="State1NameSet" Type="byte" />
		<Field Name="State2DestructionDoodadSet" Type="byte" />
		<Field Name="State2ImpactEffectDoodadSet" Type="byte" />
		<Field Name="State2AmbientDoodadSet" Type="byte" />
		<Field Name="State2NameSet" Type="byte" />
		<Field Name="State3InitDoodadSet" Type="byte" />
		<Field Name="State3AmbientDoodadSet" Type="byte" />
		<Field Name="State3NameSet" Type="byte" />
		<Field Name="EjectDirection" Type="byte" />
		<Field Name="DoNotHighlight" Type="byte" />
		<Field Name="HealEffect" Type="byte" />
	</Table>
	<Table Name="DeviceBlacklist" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VendorId" Type="ushort" />
		<Field Name="DeviceId" Type="ushort" />
	</Table>
	<Table Name="DeviceDefaultSettings" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VendorId" Type="ushort" />
		<Field Name="DeviceId" Type="ushort" />
		<Field Name="DefaultSetting" Type="byte" />
	</Table>
	<Table Name="Difficulty" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="GroupSizeHealthCurveId" Type="ushort" />
		<Field Name="GroupSizeDmgCurveId" Type="ushort" />
		<Field Name="GroupSizeSpellPointsCurveId" Type="ushort" />
		<Field Name="FallbackDifficultyId" Type="byte" />
		<Field Name="InstanceType" Type="byte" />
		<Field Name="MinPlayers" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="OldEnumValue" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ToggleDifficultyId" Type="byte" />
		<Field Name="ItemContext" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="DissolveEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Ramp" Type="float" />
		<Field Name="StartValue" Type="float" />
		<Field Name="EndValue" Type="float" />
		<Field Name="FadeInTime" Type="float" />
		<Field Name="FadeOutTime" Type="float" />
		<Field Name="Duration" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="FresnelIntensity" Type="float" />
		<Field Name="AttachId" Type="byte" />
		<Field Name="ProjectionType" Type="byte" />
		<Field Name="TextureBlendSetId" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="CurveId" Type="int" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="DriverBlacklist" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DriverVersionHi" Type="int" />
		<Field Name="DriverVersionLow" Type="int" />
		<Field Name="VendorId" Type="ushort" />
		<Field Name="DeviceId" Type="byte" />
		<Field Name="OsVersion" Type="byte" />
		<Field Name="OsBits" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="DungeonEncounter" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="CreatureDisplayId" Type="int" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="Bit" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OrderIndex" Type="int" />
		<Field Name="SpellIconFileId" Type="int" />
	</Table>
	<Table Name="DungeonMap" Build="26231">
		<Field Name="Min" Type="float" ArraySize="2" />
		<Field Name="Max" Type="float" ArraySize="2" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="ParentWorldMapId" Type="ushort" />
		<Field Name="FloorIndex" Type="byte" />
		<Field Name="RelativeHeightIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="DungeonMapChunk" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinZ" Type="float" />
		<Field Name="DoodadPlacementId" Type="int" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="WmoGroupId" Type="ushort" />
		<Field Name="DungeonMapId" Type="ushort" />
	</Table>
	<Table Name="DurabilityCosts" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WeaponSubClassCost" Type="ushort" ArraySize="21" />
		<Field Name="ArmorSubClassCost" Type="ushort" ArraySize="8" />
	</Table>
	<Table Name="DurabilityQuality" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="EdgeGlowEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Duration" Type="float" />
		<Field Name="FadeIn" Type="float" />
		<Field Name="FadeOut" Type="float" />
		<Field Name="FresnelCoefficient" Type="float" />
		<Field Name="GlowRed" Type="float" />
		<Field Name="GlowGreen" Type="float" />
		<Field Name="GlowBlue" Type="float" />
		<Field Name="GlowAlpha" Type="float" />
		<Field Name="GlowMultiplier" Type="float" />
		<Field Name="InitialDelay" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CurveId" Type="int" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="Emotes" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="ulong" />
		<Field Name="EmoteSlashCommand" Type="string" />
		<Field Name="EmoteFlags" Type="int" />
		<Field Name="SpellVisualKitId" Type="int" />
		<Field Name="AnimId" Type="ushort" />
		<Field Name="EmoteSpecProc" Type="byte" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="EmoteSpecProcParam" Type="int" />
		<Field Name="EventSoundId" Type="int" />
	</Table>
	<Table Name="EmotesText" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="EmoteId" Type="ushort" />
	</Table>
	<Table Name="EmotesTextData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text" Type="string" />
		<Field Name="RelationshipFlags" Type="byte" />
	</Table>
	<Table Name="EmotesTextSound" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceId" Type="byte" />
		<Field Name="SexId" Type="byte" />
		<Field Name="ClassId" Type="byte" />
		<Field Name="SoundId" Type="int" />
	</Table>
	<Table Name="EnvironmentalDamage" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VisualKitId" Type="ushort" />
		<Field Name="EnumId" Type="byte" />
	</Table>
	<Table Name="Exhaustion" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="CombatLogText" Type="string" />
		<Field Name="Xp" Type="uint" />
		<Field Name="Factor" Type="float" />
		<Field Name="OutdoorHours" Type="float" />
		<Field Name="InnHours" Type="float" />
		<Field Name="Threshold" Type="float" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ExpectedStat" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ExpansionId" Type="uint" />
		<Field Name="CreatureHealth" Type="float" />
		<Field Name="PlayerHealth" Type="float" />
		<Field Name="CreatureAutoAttackDps" Type="float" />
		<Field Name="CreatureArmor" Type="float" />
		<Field Name="PlayerMana" Type="float" />
		<Field Name="PlayerPrimaryStat" Type="float" />
		<Field Name="PlayerSecondaryStat" Type="float" />
		<Field Name="ArmorConstant" Type="float" />
		<Field Name="CreatureSpellDamage" Type="float" />
	</Table>
	<Table Name="ExpectedStatMod" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureHealthMod" Type="float" />
		<Field Name="PlayerHealthMod" Type="float" />
		<Field Name="CreatureAutoAttackDPSMod" Type="float" />
		<Field Name="CreatureArmorMod" Type="float" />
		<Field Name="PlayerManaMod" Type="float" />
		<Field Name="PlayerPrimaryStatMod" Type="float" />
		<Field Name="PlayerSecondaryStatMod" Type="float" />
		<Field Name="ArmorConstantMod" Type="float" />
		<Field Name="CreatureSpellDamageMod" Type="float" />
	</Table>
	<Table Name="Faction" Build="26231">
		<Field Name="ReputationRaceMask" Type="ulong" ArraySize="4" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ReputationBase" Type="uint" ArraySize="4" />
		<Field Name="ParentFactionMod" Type="float" ArraySize="2" />
		<Field Name="ReputationMax" Type="uint" ArraySize="4" />
		<Field Name="ReputationIndex" Type="ushort" />
		<Field Name="ReputationClassMask" Type="ushort" ArraySize="4" />
		<Field Name="ReputationFlags" Type="ushort" ArraySize="4" />
		<Field Name="ParentFactionId" Type="ushort" />
		<Field Name="ParagonFactionId" Type="ushort" />
		<Field Name="ParentFactionCap" Type="byte" ArraySize="2" />
		<Field Name="Expansion" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="FriendshipRepId" Type="byte" />
	</Table>
	<Table Name="FactionGroup" Build="26231">
		<Field Name="InternalName" Type="string" />
		<Field Name="Name" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaskId" Type="byte" />
		<Field Name="HonorCurrencyTextureFileId" Type="int" />
		<Field Name="ConquestCurrencyTextureFileId" Type="int" />
	</Table>
	<Table Name="FactionTemplate" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Faction" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="Enemies" Type="ushort" ArraySize="4" />
		<Field Name="Friend" Type="ushort" ArraySize="4" />
		<Field Name="FactionGroup" Type="byte" />
		<Field Name="FriendGroup" Type="byte" />
		<Field Name="EnemyGroup" Type="byte" />
	</Table>
	<Table Name="FootprintTextures" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TextureBlendsetId" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="FileDataId" Type="int" />
	</Table>
	<Table Name="FootstepTerrainLookup" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureFootstepId" Type="ushort" />
		<Field Name="TerrainSoundId" Type="byte" />
		<Field Name="SoundId" Type="int" />
		<Field Name="SoundIDSplash" Type="int" />
	</Table>
	<Table Name="FriendshipRepReaction" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Reaction" Type="string" />
		<Field Name="ReactionThreshold" Type="ushort" />
		<Field Name="FriendshipRepId" Type="byte" />
	</Table>
	<Table Name="FriendshipReputation" Build="26231">
		<Field Name="Description" Type="string" />
		<Field Name="TextureFileId" Type="int" />
		<Field Name="FactionId" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="FullScreenEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Saturation" Type="float" />
		<Field Name="GammaRed" Type="float" />
		<Field Name="GammaGreen" Type="float" />
		<Field Name="GammaBlue" Type="float" />
		<Field Name="MaskOffsetY" Type="float" />
		<Field Name="MaskSizeMultiplier" Type="float" />
		<Field Name="MaskPower" Type="float" />
		<Field Name="ColorMultiplyRed" Type="float" />
		<Field Name="ColorMultiplyGreen" Type="float" />
		<Field Name="ColorMultiplyBlue" Type="float" />
		<Field Name="ColorMultiplyOffsetY" Type="float" />
		<Field Name="ColorMultiplyMultiplier" Type="float" />
		<Field Name="ColorMultiplyPower" Type="float" />
		<Field Name="ColorAdditionRed" Type="float" />
		<Field Name="ColorAdditionGreen" Type="float" />
		<Field Name="ColorAdditionBlue" Type="float" />
		<Field Name="ColorAdditionOffsetY" Type="float" />
		<Field Name="ColorAdditionMultiplier" Type="float" />
		<Field Name="ColorAdditionPower" Type="float" />
		<Field Name="BlurIntensity" Type="float" />
		<Field Name="BlurOffsetY" Type="float" />
		<Field Name="BlurMultiplier" Type="float" />
		<Field Name="BlurPower" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="TextureBlendSetId" Type="int" />
		<Field Name="EffectFadeInMs" Type="int" />
		<Field Name="EffectFadeOutMs" Type="int" />
	</Table>
	<Table Name="GameObjectArtKit" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AttachModelFileId" Type="int" />
		<Field Name="TextureVariationFileId" Type="int" ArraySize="3" />
	</Table>
	<Table Name="GameObjectDiffAnimMap" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AttachmentDisplayId" Type="ushort" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="Animation" Type="byte" />
	</Table>
	<Table Name="GameObjectDisplayInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="GeoBox" Type="float" ArraySize="6" />
		<Field Name="OverrideLootEffectScale" Type="float" />
		<Field Name="OverrideNameScale" Type="float" />
		<Field Name="ObjectEffectPackageId" Type="ushort" />
	</Table>
	<Table Name="GameObjectDisplayInfoXSoundKit" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EventIndex" Type="byte" />
		<Field Name="SoundKitId" Type="int" />
	</Table>
	<Table Name="GameObjects" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="Rot" Type="float" ArraySize="4" />
		<Field Name="Scale" Type="float" />
		<Field Name="PropValue" Type="uint" ArraySize="8" />
		<Field Name="OwnerId" Type="ushort" />
		<Field Name="DisplayId" Type="ushort" />
		<Field Name="PhaseId" Type="ushort" />
		<Field Name="PhaseGroupId" Type="ushort" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="TypeId" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GameTips" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text" Type="string" />
		<Field Name="MinLevel" Type="ushort" />
		<Field Name="MaxLevel" Type="ushort" />
		<Field Name="SortIndex" Type="byte" />
	</Table>
	<Table Name="GarrAbility" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="FactionChangeGarrAbilityId" Type="ushort" />
		<Field Name="GarrAbilityCategoryId" Type="byte" />
		<Field Name="GarrFollowerTypeId" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrAbilityCategory" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="GarrAbilityEffect" Build="26231">
		<Field Name="CombatWeightBase" Type="float" />
		<Field Name="CombatWeightMax" Type="float" />
		<Field Name="ActionValueFlat" Type="float" />
		<Field Name="ActionRecordId" Type="int" />
		<Field Name="GarrAbilityId" Type="ushort" />
		<Field Name="AbilityAction" Type="byte" />
		<Field Name="AbilityTargetType" Type="byte" />
		<Field Name="GarrMechanicTypeId" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ActionRace" Type="byte" />
		<Field Name="ActionHours" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrBuilding" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AllianceName" Type="string" />
		<Field Name="HordeName" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Tooltip" Type="string" />
		<Field Name="HordeGameObjectId" Type="int" />
		<Field Name="AllianceGameObjectId" Type="int" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="CurrencyTypeId" Type="ushort" />
		<Field Name="HordeUiTextureKitId" Type="ushort" />
		<Field Name="AllianceUiTextureKitId" Type="ushort" />
		<Field Name="AllianceSceneScriptPackageId" Type="ushort" />
		<Field Name="HordeSceneScriptPackageId" Type="ushort" />
		<Field Name="GarrAbilityId" Type="ushort" />
		<Field Name="BonusGarrAbilityId" Type="ushort" />
		<Field Name="GoldCost" Type="ushort" />
		<Field Name="GarrSiteId" Type="byte" />
		<Field Name="BuildingType" Type="byte" />
		<Field Name="UpgradeLevel" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ShipmentCapacity" Type="byte" />
		<Field Name="GarrTypeId" Type="byte" />
		<Field Name="BuildSeconds" Type="int" />
		<Field Name="CurrencyQty" Type="int" />
		<Field Name="MaxAssignments" Type="int" />
	</Table>
	<Table Name="GarrBuildingDoodadSet" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrBuildingId" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="AllianceDoodadSet" Type="byte" />
		<Field Name="HordeDoodadSet" Type="byte" />
		<Field Name="SpecializationId" Type="byte" />
	</Table>
	<Table Name="GarrBuildingPlotInst" Build="26231">
		<Field Name="MapOffset" Type="float" ArraySize="2" />
		<Field Name="UiTextureAtlasMemberId" Type="ushort" />
		<Field Name="GarrSiteLevelPlotInstId" Type="ushort" />
		<Field Name="GarrBuildingId" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrClassSpec" Build="26231">
		<Field Name="ClassSpec" Type="string" />
		<Field Name="ClassSpecMale" Type="string" />
		<Field Name="ClassSpecFemale" Type="string" />
		<Field Name="UiTextureAtlasMemberId" Type="ushort" />
		<Field Name="GarrFollItemSetId" Type="ushort" />
		<Field Name="FollowerClassLimit" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrClassSpecPlayerCond" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="GarrClassSpecId" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="FlavorGarrStringId" Type="int" />
	</Table>
	<Table Name="GarrEncounter" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="CreatureId" Type="int" />
		<Field Name="UiAnimScale" Type="float" />
		<Field Name="UiAnimHeight" Type="float" />
		<Field Name="PortraitFileDataId" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiTextureKitId" Type="int" />
	</Table>
	<Table Name="GarrEncounterSetXEncounter" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrEncounterId" Type="int" />
	</Table>
	<Table Name="GarrEncounterXMechanic" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrMechanicId" Type="byte" />
		<Field Name="GarrMechanicSetId" Type="byte" />
	</Table>
	<Table Name="GarrFollItemSetMember" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="int" />
		<Field Name="MinItemLevel" Type="ushort" />
		<Field Name="ItemSlot" Type="byte" />
	</Table>
	<Table Name="GarrFollower" Build="26231">
		<Field Name="HordeSourceText" Type="string" />
		<Field Name="AllianceSourceText" Type="string" />
		<Field Name="TitleName" Type="string" />
		<Field Name="HordeCreatureId" Type="int" />
		<Field Name="AllianceCreatureId" Type="int" />
		<Field Name="HordeIconFileDataId" Type="int" />
		<Field Name="AllianceIconFileDataId" Type="int" />
		<Field Name="HordeSlottingBroadcastTextId" Type="int" />
		<Field Name="AllySlottingBroadcastTextId" Type="int" />
		<Field Name="HordeGarrFollItemSetId" Type="ushort" />
		<Field Name="AllianceGarrFollItemSetId" Type="ushort" />
		<Field Name="ItemLevelWeapon" Type="ushort" />
		<Field Name="ItemLevelArmor" Type="ushort" />
		<Field Name="HordeUITextureKitId" Type="ushort" />
		<Field Name="AllianceUITextureKitId" Type="ushort" />
		<Field Name="GarrFollowerTypeId" Type="byte" />
		<Field Name="HordeGarrFollRaceId" Type="byte" />
		<Field Name="AllianceGarrFollRaceId" Type="byte" />
		<Field Name="Quality" Type="byte" />
		<Field Name="HordeGarrClassSpecId" Type="byte" />
		<Field Name="AllianceGarrClassSpecId" Type="byte" />
		<Field Name="FollowerLevel" Type="byte" />
		<Field Name="Gender" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="HordeSourceTypeEnum" Type="byte" />
		<Field Name="AllianceSourceTypeEnum" Type="byte" />
		<Field Name="GarrTypeId" Type="byte" />
		<Field Name="Vitality" Type="byte" />
		<Field Name="ChrClassId" Type="byte" />
		<Field Name="HordeFlavorGarrStringId" Type="byte" />
		<Field Name="AllianceFlavorGarrStringId" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrFollowerLevelXP" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="XpToNextLevel" Type="ushort" />
		<Field Name="ShipmentXP" Type="ushort" />
		<Field Name="FollowerLevel" Type="byte" />
		<Field Name="GarrFollowerTypeId" Type="byte" />
	</Table>
	<Table Name="GarrFollowerQuality" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="XpToNextQuality" Type="int" />
		<Field Name="ShipmentXP" Type="ushort" />
		<Field Name="Quality" Type="byte" />
		<Field Name="AbilityCount" Type="byte" />
		<Field Name="TraitCount" Type="byte" />
		<Field Name="GarrFollowerTypeId" Type="byte" />
		<Field Name="ClassSpecId" Type="int" />
	</Table>
	<Table Name="GarrFollowerSetXFollower" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrFollowerId" Type="int" />
	</Table>
	<Table Name="GarrFollowerType" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaxItemLevel" Type="ushort" />
		<Field Name="MaxFollowers" Type="byte" />
		<Field Name="MaxFollowerBuildingType" Type="byte" />
		<Field Name="GarrTypeId" Type="byte" />
		<Field Name="LevelRangeBias" Type="byte" />
		<Field Name="ItemLevelRangeBias" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="GarrFollowerUICreature" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureId" Type="int" />
		<Field Name="Scale" Type="float" />
		<Field Name="FactionIndex" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="GarrFollowerXAbility" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrAbilityId" Type="ushort" />
		<Field Name="FactionIndex" Type="byte" />
	</Table>
	<Table Name="GarrFollSupportSpell" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AllianceSpellId" Type="int" />
		<Field Name="HordeSpellId" Type="int" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="GarrItemLevelUpgradeData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Operation" Type="int" />
		<Field Name="MinItemLevel" Type="int" />
		<Field Name="MaxItemLevel" Type="int" />
		<Field Name="FollowerTypeId" Type="int" />
	</Table>
	<Table Name="GarrMechanic" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Factor" Type="float" />
		<Field Name="GarrMechanicTypeId" Type="byte" />
		<Field Name="GarrAbilityId" Type="int" />
	</Table>
	<Table Name="GarrMechanicSetXMechanic" Build="26231">
		<Field Name="GarrMechanicId" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrMechanicType" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="Category" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrMission" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Location" Type="string" />
		<Field Name="MissionDuration" Type="int" />
		<Field Name="OfferDuration" Type="int" />
		<Field Name="MapPos" Type="float" ArraySize="2" />
		<Field Name="WorldPos" Type="float" ArraySize="2" />
		<Field Name="TargetItemLevel" Type="ushort" />
		<Field Name="UiTextureKitId" Type="ushort" />
		<Field Name="MissionCostCurrencyTypesId" Type="ushort" />
		<Field Name="TargetLevel" Type="byte" />
		<Field Name="EnvGarrMechanicTypeId" Type="byte" />
		<Field Name="MaxFollowers" Type="byte" />
		<Field Name="OfferedGarrMissionTextureId" Type="byte" />
		<Field Name="GarrMissionTypeId" Type="byte" />
		<Field Name="GarrFollowerTypeId" Type="byte" />
		<Field Name="BaseCompletionChance" Type="byte" />
		<Field Name="FollowerDeathChance" Type="byte" />
		<Field Name="GarrTypeId" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TravelDuration" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="MissionCost" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="BaseFollowerXP" Type="int" />
		<Field Name="AreaId" Type="int" />
		<Field Name="OvermaxRewardPackId" Type="int" />
		<Field Name="EnvGarrMechanicId" Type="int" />
	</Table>
	<Table Name="GarrMissionTexture" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="2" />
		<Field Name="UiTextureKitId" Type="ushort" />
	</Table>
	<Table Name="GarrMissionType" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="UiTextureAtlasMemberId" Type="ushort" />
		<Field Name="UiTextureKitId" Type="ushort" />
	</Table>
	<Table Name="GarrMissionXEncounter" Build="26231">
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrEncounterId" Type="int" />
		<Field Name="GarrEncounterSetId" Type="int" />
	</Table>
	<Table Name="GarrMissionXFollower" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrFollowerId" Type="int" />
		<Field Name="GarrFollowerSetId" Type="int" />
	</Table>
	<Table Name="GarrMssnBonusAbility" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Radius" Type="float" />
		<Field Name="DurationSecs" Type="int" />
		<Field Name="GarrAbilityId" Type="ushort" />
		<Field Name="GarrFollowerTypeId" Type="byte" />
		<Field Name="GarrMissionTextureId" Type="byte" />
	</Table>
	<Table Name="GarrPlot" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="AllianceConstructObjId" Type="int" />
		<Field Name="HordeConstructObjId" Type="int" />
		<Field Name="UiCategoryId" Type="byte" />
		<Field Name="PlotType" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UpgradeRequirement" Type="int" ArraySize="2" />
	</Table>
	<Table Name="GarrPlotBuilding" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrPlotId" Type="byte" />
		<Field Name="GarrBuildingId" Type="byte" />
	</Table>
	<Table Name="GarrPlotInstance" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="GarrPlotId" Type="byte" />
	</Table>
	<Table Name="GarrPlotUICategory" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CategoryName" Type="string" />
		<Field Name="PlotType" Type="byte" />
	</Table>
	<Table Name="GarrSiteLevel" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TownHallUiPos" Type="float" ArraySize="2" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="UiTextureKitId" Type="ushort" />
		<Field Name="UpgradeMovieId" Type="ushort" />
		<Field Name="UpgradeCost" Type="ushort" />
		<Field Name="UpgradeGoldCost" Type="ushort" />
		<Field Name="GarrLevel" Type="byte" />
		<Field Name="GarrSiteId" Type="byte" />
		<Field Name="MaxBuildingLevel" Type="byte" />
	</Table>
	<Table Name="GarrSiteLevelPlotInst" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiMarkerPos" Type="float" ArraySize="2" />
		<Field Name="GarrSiteLevelId" Type="ushort" />
		<Field Name="GarrPlotInstanceId" Type="byte" />
		<Field Name="UiMarkerSize" Type="byte" />
	</Table>
	<Table Name="GarrSpecialization" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Tooltip" Type="string" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="Param" Type="float" ArraySize="2" />
		<Field Name="BuildingType" Type="byte" />
		<Field Name="SpecType" Type="byte" />
		<Field Name="RequiredUpgradeLevel" Type="byte" />
	</Table>
	<Table Name="GarrString" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text" Type="string" />
	</Table>
	<Table Name="GarrTalent" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="ResearchDurationSecs" Type="int" />
		<Field Name="Tier" Type="byte" />
		<Field Name="UiOrder" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrTalentTreeId" Type="int" />
		<Field Name="GarrAbilityId" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="ResearchCost" Type="int" />
		<Field Name="ResearchCostCurrencyTypesId" Type="int" />
		<Field Name="ResearchGoldCost" Type="int" />
		<Field Name="PerkSpellId" Type="int" />
		<Field Name="PerkPlayerConditionId" Type="int" />
		<Field Name="RespecCost" Type="int" />
		<Field Name="RespecCostCurrencyTypesId" Type="int" />
		<Field Name="RespecDurationSecs" Type="int" />
		<Field Name="RespecGoldCost" Type="int" />
	</Table>
	<Table Name="GarrTalentTree" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiTextureKitId" Type="ushort" />
		<Field Name="MaxTiers" Type="byte" />
		<Field Name="UiOrder" Type="byte" />
		<Field Name="ClassId" Type="int" />
		<Field Name="GarrTypeId" Type="int" />
	</Table>
	<Table Name="GarrType" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="PrimaryCurrencyTypeId" Type="int" />
		<Field Name="SecondaryCurrencyTypeId" Type="int" />
		<Field Name="ExpansionId" Type="int" />
		<Field Name="MapIDs" Type="int" ArraySize="2" />
	</Table>
	<Table Name="GarrUiAnimClassInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ImpactDelaySecs" Type="float" />
		<Field Name="GarrClassSpecId" Type="byte" />
		<Field Name="MovementType" Type="byte" />
		<Field Name="CastKit" Type="int" />
		<Field Name="ImpactKit" Type="int" />
		<Field Name="TargetImpactKit" Type="int" />
	</Table>
	<Table Name="GarrUiAnimRaceInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaleScale" Type="float" />
		<Field Name="MaleHeight" Type="float" />
		<Field Name="MaleSingleModelScale" Type="float" />
		<Field Name="MaleSingleModelHeight" Type="float" />
		<Field Name="MaleFollowerPageScale" Type="float" />
		<Field Name="MaleFollowerPageHeight" Type="float" />
		<Field Name="FemaleScale" Type="float" />
		<Field Name="FemaleHeight" Type="float" />
		<Field Name="FemaleSingleModelScale" Type="float" />
		<Field Name="FemaleSingleModelHeight" Type="float" />
		<Field Name="FemaleFollowerPageScale" Type="float" />
		<Field Name="FemaleFollowerPageHeight" Type="float" />
		<Field Name="GarrFollRaceId" Type="byte" />
	</Table>
	<Table Name="GemProperties" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="int" />
		<Field Name="EnchantId" Type="ushort" />
		<Field Name="MinItemLevel" Type="ushort" />
	</Table>
	<Table Name="GlobalStrings" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseTag" Type="string" />
		<Field Name="TagText" Type="string" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="GlyphBindableSpell" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
	</Table>
	<Table Name="GlyphExclusiveCategory" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="GlyphProperties" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="SpellIconId" Type="ushort" />
		<Field Name="GlyphType" Type="byte" />
		<Field Name="GlyphExclusiveCategoryId" Type="byte" />
	</Table>
	<Table Name="GlyphRequiredSpec" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrSpecializationId" Type="ushort" />
	</Table>
	<Table Name="GMSurveyAnswers" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Answer" Type="string" />
		<Field Name="SortIndex" Type="byte" />
	</Table>
	<Table Name="GMSurveyCurrentSurvey" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GmsurveyId" Type="byte" />
	</Table>
	<Table Name="GMSurveyQuestions" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Question" Type="string" />
	</Table>
	<Table Name="GMSurveySurveys" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Q" Type="byte" ArraySize="15" />
	</Table>
	<Table Name="GroundEffectDoodad" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Animscale" Type="float" />
		<Field Name="PushScale" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ModelFileId" Type="int" />
	</Table>
	<Table Name="GroundEffectTexture" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DoodadId" Type="ushort" ArraySize="4" />
		<Field Name="DoodadWeight" Type="byte" ArraySize="4" />
		<Field Name="Sound" Type="byte" />
		<Field Name="Density" Type="int" />
	</Table>
	<Table Name="GroupFinderActivity" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FullName" Type="string" />
		<Field Name="ShortName" Type="string" />
		<Field Name="MinGearLevelSuggestion" Type="ushort" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="AreaId" Type="ushort" />
		<Field Name="GroupFinderCategoryId" Type="byte" />
		<Field Name="GroupFinderActivityGrpId" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevelSuggestion" Type="byte" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="DisplayType" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
	</Table>
	<Table Name="GroupFinderActivityGrp" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="GroupFinderCategory" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="GuildColorBackground" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="GuildColorBorder" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="GuildColorEmblem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="GuildPerkSpells" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
	</Table>
	<Table Name="Heirloom" Build="26231">
		<Field Name="SourceText" Type="string" />
		<Field Name="ItemId" Type="int" />
		<Field Name="LegacyItemId" Type="int" />
		<Field Name="LegacyUpgradedItemId" Type="int" />
		<Field Name="StaticUpgradedItemId" Type="int" />
		<Field Name="UpgradeItemId" Type="int" ArraySize="3" />
		<Field Name="UpgradeItemBonusListId" Type="ushort" ArraySize="3" />
		<Field Name="Flags" Type="byte" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="HelmetAnimScaling" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Amount" Type="float" />
		<Field Name="RaceId" Type="int" />
	</Table>
	<Table Name="HelmetGeosetVisData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HideGeoset" Type="uint" ArraySize="9" />
	</Table>
	<Table Name="HighlightColor" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StartColor" Type="uint" />
		<Field Name="MidColor" Type="uint" />
		<Field Name="EndColor" Type="uint" />
		<Field Name="Type" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="HolidayDescriptions" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
	</Table>
	<Table Name="HolidayNames" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="Holidays" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Date" Type="int" ArraySize="16" />
		<Field Name="Duration" Type="ushort" ArraySize="10" />
		<Field Name="Region" Type="ushort" />
		<Field Name="Looping" Type="byte" />
		<Field Name="CalendarFlags" Type="byte" ArraySize="10" />
		<Field Name="Priority" Type="byte" />
		<Field Name="CalendarFilterType" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="HolidayNameId" Type="int" />
		<Field Name="HolidayDescriptionId" Type="int" />
		<Field Name="TextureFileDataId" Type="int" ArraySize="3" />
	</Table>
	<Table Name="Hotfix" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Tablename" Type="string" />
		<Field Name="ObjectId" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="ImportPriceArmor" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClothModifier" Type="float" />
		<Field Name="LeatherModifier" Type="float" />
		<Field Name="ChainModifier" Type="float" />
		<Field Name="PlateModifier" Type="float" />
	</Table>
	<Table Name="ImportPriceQuality" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="ImportPriceShield" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="ImportPriceWeapon" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="InvasionClientData" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="IconLocation" Type="float" ArraySize="2" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WorldStateId" Type="int" />
		<Field Name="UiTextureAtlasMemberId" Type="int" />
		<Field Name="ScenarioId" Type="int" />
		<Field Name="WorldQuestId" Type="int" />
		<Field Name="WorldStateValue" Type="int" />
		<Field Name="InvasionEnabledWorldStateId" Type="int" />
	</Table>
	<Table Name="Item" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="ClassId" Type="byte" />
		<Field Name="SubclassId" Type="byte" />
		<Field Name="SoundOverrideSubclassId" Type="byte" />
		<Field Name="Material" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="SheatheType" Type="byte" />
		<Field Name="ItemGroupSoundsId" Type="byte" />
	</Table>
	<Table Name="ItemAppearance" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemDisplayInfoId" Type="int" />
		<Field Name="DefaultIconFileDataId" Type="int" />
		<Field Name="UiOrder" Type="int" />
		<Field Name="DisplayType" Type="byte" />
	</Table>
	<Table Name="ItemAppearanceXUiCamera" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemAppearanceId" Type="ushort" />
		<Field Name="UiCameraId" Type="ushort" />
	</Table>
	<Table Name="ItemArmorQuality" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Qualitymod" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemArmorShield" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemArmorTotal" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Cloth" Type="float" />
		<Field Name="Leather" Type="float" />
		<Field Name="Mail" Type="float" />
		<Field Name="Plate" Type="float" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemBagFamily" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="ItemBonus" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="uint" ArraySize="3" />
		<Field Name="ParentItemBonusListId" Type="ushort" />
		<Field Name="Type" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="ItemBonusListLevelDelta" Build="26231">
		<Field Name="ItemLevelDelta" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ItemBonusTreeNode" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChildItemBonusTreeId" Type="ushort" />
		<Field Name="ChildItemBonusListId" Type="ushort" />
		<Field Name="ChildItemLevelSelectorId" Type="ushort" />
		<Field Name="ItemContext" Type="byte" />
	</Table>
	<Table Name="ItemChildEquipment" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChildItemId" Type="int" />
		<Field Name="ChildItemEquipSlot" Type="byte" />
	</Table>
	<Table Name="ItemClass" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClassName" Type="string" />
		<Field Name="PriceModifier" Type="float" />
		<Field Name="ClassId" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ItemContextPickerEntry" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemCreationContext" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PVal" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
	</Table>
	<Table Name="ItemCurrencyCost" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="int" />
	</Table>
	<Table Name="ItemDamageAmmo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemDamageOneHand" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemDamageOneHandCaster" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemDamageTwoHand" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemDamageTwoHandCaster" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemDisenchantLoot" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinLevel" Type="ushort" />
		<Field Name="MaxLevel" Type="ushort" />
		<Field Name="SkillRequired" Type="ushort" />
		<Field Name="Subclass" Type="byte" />
		<Field Name="Quality" Type="byte" />
		<Field Name="ExpansionId" Type="byte" />
	</Table>
	<Table Name="ItemDisplayInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="ItemRangedDisplayInfoId" Type="int" />
		<Field Name="ItemVisual" Type="int" />
		<Field Name="ParticleColorId" Type="int" />
		<Field Name="OverrideSwooshSoundKitId" Type="int" />
		<Field Name="SheatheTransformMatrixId" Type="int" />
		<Field Name="ModelType1" Type="int" />
		<Field Name="StateSpellVisualKitId" Type="int" />
		<Field Name="SheathedSpellVisualKitId" Type="int" />
		<Field Name="UnsheathedSpellVisualKitId" Type="int" />
		<Field Name="ModelResourcesId" Type="int" ArraySize="2" />
		<Field Name="ModelMaterialResourcesId" Type="int" ArraySize="2" />
		<Field Name="GeosetGroup" Type="int" ArraySize="4" />
		<Field Name="AttachmentGeosetGroup" Type="int" ArraySize="4" />
		<Field Name="HelmetGeosetVis" Type="int" ArraySize="2" />
	</Table>
	<Table Name="ItemDisplayInfoMaterialRes" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaterialResourcesId" Type="int" />
		<Field Name="ComponentSection" Type="byte" />
	</Table>
	<Table Name="ItemDisplayXUiCamera" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemDisplayInfoId" Type="int" />
		<Field Name="UiCameraId" Type="ushort" />
	</Table>
	<Table Name="ItemEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="CoolDownMSec" Type="uint" />
		<Field Name="CategoryCoolDownMSec" Type="uint" />
		<Field Name="Charges" Type="ushort" />
		<Field Name="SpellCategoryId" Type="ushort" />
		<Field Name="ChrSpecializationId" Type="ushort" />
		<Field Name="LegacySlotIndex" Type="byte" />
		<Field Name="TriggerType" Type="byte" />
	</Table>
	<Table Name="ItemExtendedCost" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="int" ArraySize="5" />
		<Field Name="CurrencyCount" Type="int" ArraySize="5" />
		<Field Name="ItemCount" Type="ushort" ArraySize="5" />
		<Field Name="RequiredArenaRating" Type="ushort" />
		<Field Name="CurrencyId" Type="ushort" ArraySize="5" />
		<Field Name="ArenaBracket" Type="byte" />
		<Field Name="MinFactionId" Type="byte" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="RequiredAchievement" Type="byte" />
	</Table>
	<Table Name="ItemGroupSounds" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Sound" Type="int" ArraySize="4" />
	</Table>
	<Table Name="ItemLevelSelector" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinItemLevel" Type="ushort" />
		<Field Name="ItemLevelSelectorQualitySetId" Type="ushort" />
	</Table>
	<Table Name="ItemLevelSelectorQuality" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QualityItemBonusListId" Type="int" />
		<Field Name="Quality" Type="byte" />
	</Table>
	<Table Name="ItemLevelSelectorQualitySet" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IlvlRare" Type="ushort" />
		<Field Name="IlvlEpic" Type="ushort" />
	</Table>
	<Table Name="ItemLimitCategory" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Quantity" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ItemLimitCategoryCondition" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AddQuantity" Type="byte" />
		<Field Name="PlayerConditionId" Type="int" />
	</Table>
	<Table Name="ItemModifiedAppearance" Build="26231">
		<Field Name="ItemId" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemAppearanceModifierId" Type="byte" />
		<Field Name="ItemAppearanceId" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="TransmogSourceTypeEnum" Type="byte" />
	</Table>
	<Table Name="ItemModifiedAppearanceExtra" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="UnequippedIconFileDataId" Type="int" />
		<Field Name="SheatheType" Type="byte" />
		<Field Name="DisplayWeaponSubclassId" Type="byte" />
		<Field Name="DisplayInventoryType" Type="byte" />
	</Table>
	<Table Name="ItemNameDescription" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
		<Field Name="Color" Type="uint" />
	</Table>
	<Table Name="ItemPetFood" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="ItemPriceBase" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Armor" Type="float" />
		<Field Name="Weapon" Type="float" />
		<Field Name="ItemLevel" Type="ushort" />
	</Table>
	<Table Name="ItemRandomProperties" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Enchantment" Type="ushort" ArraySize="5" />
	</Table>
	<Table Name="ItemRandomSuffix" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Enchantment" Type="ushort" ArraySize="5" />
		<Field Name="AllocationPct" Type="ushort" ArraySize="5" />
	</Table>
	<Table Name="ItemRangedDisplayInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MissileSpellVisualEffectNameId" Type="int" />
		<Field Name="QuiverFileDataId" Type="int" />
		<Field Name="CastSpellVisualId" Type="int" />
		<Field Name="AutoAttackSpellVisualId" Type="int" />
	</Table>
	<Table Name="ItemSearchName" Build="26231">
		<Field Name="AllowableRace" Type="ulong" />
		<Field Name="Display" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="uint" ArraySize="3" />
		<Field Name="ItemLevel" Type="ushort" />
		<Field Name="OverallQualityId" Type="byte" />
		<Field Name="ExpansionId" Type="byte" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="MinFactionId" Type="ushort" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="AllowableClass" Type="int" />
		<Field Name="RequiredSkill" Type="ushort" />
		<Field Name="RequiredSkillRank" Type="ushort" />
		<Field Name="RequiredAbility" Type="int" />
	</Table>
	<Table Name="ItemSet" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="ItemId" Type="int" ArraySize="17" />
		<Field Name="RequiredSkillRank" Type="ushort" />
		<Field Name="RequiredSkill" Type="int" />
		<Field Name="SetFlags" Type="int" />
	</Table>
	<Table Name="ItemSetSpell" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="ChrSpecId" Type="ushort" />
		<Field Name="Threshold" Type="byte" />
	</Table>
	<Table Name="ItemSparse" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AllowableRace" Type="ulong" />
		<Field Name="Display" Type="string" />
		<Field Name="Display1" Type="string" />
		<Field Name="Display2" Type="string" />
		<Field Name="Display3" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Flags" Type="uint" ArraySize="4" />
		<Field Name="PriceRandomValue" Type="float" />
		<Field Name="PriceVariance" Type="float" />
		<Field Name="VendorStackCount" Type="int" />
		<Field Name="BuyPrice" Type="int" />
		<Field Name="SellPrice" Type="int" />
		<Field Name="RequiredAbility" Type="int" />
		<Field Name="MaxCount" Type="int" />
		<Field Name="Stackable" Type="int" />
		<Field Name="StatPercentEditor" Type="int" ArraySize="10" />
		<Field Name="StatPercentageOfSocket" Type="float" ArraySize="10" />
		<Field Name="ItemRange" Type="float" />
		<Field Name="BagFamily" Type="int" />
		<Field Name="QualityModifier" Type="float" />
		<Field Name="DurationInInventory" Type="int" />
		<Field Name="DmgVariance" Type="float" />
		<Field Name="AllowableClass" Type="ushort" />
		<Field Name="ItemLevel" Type="ushort" />
		<Field Name="RequiredSkill" Type="ushort" />
		<Field Name="RequiredSkillRank" Type="ushort" />
		<Field Name="MinFactionId" Type="ushort" />
		<Field Name="ScalingStatDistributionId" Type="ushort" />
		<Field Name="ItemDelay" Type="ushort" />
		<Field Name="PageId" Type="ushort" />
		<Field Name="StartQuestId" Type="ushort" />
		<Field Name="LockId" Type="ushort" />
		<Field Name="RandomSelect" Type="ushort" />
		<Field Name="ItemRandomSuffixGroupId" Type="ushort" />
		<Field Name="ItemSet" Type="ushort" />
		<Field Name="ZoneBound" Type="ushort" />
		<Field Name="InstanceBound" Type="ushort" />
		<Field Name="TotemCategoryId" Type="ushort" />
		<Field Name="SocketMatchEnchantmentId" Type="ushort" />
		<Field Name="GemProperties" Type="ushort" />
		<Field Name="LimitCategory" Type="ushort" />
		<Field Name="RequiredHoliday" Type="ushort" />
		<Field Name="RequiredTransmogHoliday" Type="ushort" />
		<Field Name="ItemNameDescriptionId" Type="ushort" />
		<Field Name="OverallQualityId" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="RequiredPVPRank" Type="byte" />
		<Field Name="RequiredPVPMedal" Type="byte" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="ContainerSlots" Type="byte" />
		<Field Name="StatModifierBonusStat" Type="byte" ArraySize="10" />
		<Field Name="DamageDamageType" Type="byte" />
		<Field Name="Bonding" Type="byte" />
		<Field Name="LanguageId" Type="byte" />
		<Field Name="PageMaterialId" Type="byte" />
		<Field Name="Material" Type="byte" />
		<Field Name="SheatheType" Type="byte" />
		<Field Name="SocketType" Type="byte" ArraySize="3" />
		<Field Name="SpellWeightCategory" Type="byte" />
		<Field Name="SpellWeight" Type="byte" />
		<Field Name="ArtifactId" Type="byte" />
		<Field Name="ExpansionId" Type="byte" />
	</Table>
	<Table Name="ItemSpec" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpecializationId" Type="ushort" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
		<Field Name="ItemType" Type="byte" />
		<Field Name="PrimaryStat" Type="byte" />
		<Field Name="SecondaryStat" Type="byte" />
	</Table>
	<Table Name="ItemSpecOverride" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpecId" Type="ushort" />
	</Table>
	<Table Name="ItemSubClass" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayName" Type="string" />
		<Field Name="VerboseName" Type="string" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="ClassId" Type="byte" />
		<Field Name="SubClassId" Type="byte" />
		<Field Name="PrerequisiteProficiency" Type="byte" />
		<Field Name="PostrequisiteProficiency" Type="byte" />
		<Field Name="DisplayFlags" Type="byte" />
		<Field Name="WeaponSwingSize" Type="byte" />
		<Field Name="AuctionHouseSortOrder" Type="byte" />
	</Table>
	<Table Name="ItemSubClassMask" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Mask" Type="int" />
		<Field Name="ClassId" Type="byte" />
	</Table>
	<Table Name="ItemUpgrade" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CurrencyAmount" Type="int" />
		<Field Name="PrerequisiteId" Type="ushort" />
		<Field Name="CurrencyType" Type="ushort" />
		<Field Name="ItemUpgradePathId" Type="byte" />
		<Field Name="ItemLevelIncrement" Type="byte" />
	</Table>
	<Table Name="ItemVisuals" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelFileId" Type="int" ArraySize="5" />
	</Table>
	<Table Name="ItemXBonusTree" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemBonusTreeId" Type="ushort" />
	</Table>
	<Table Name="JournalEncounter" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Map" Type="float" ArraySize="2" />
		<Field Name="DungeonMapId" Type="ushort" />
		<Field Name="WorldMapAreaId" Type="ushort" />
		<Field Name="FirstSectionId" Type="ushort" />
		<Field Name="JournalInstanceId" Type="ushort" />
		<Field Name="DifficultyMask" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="OrderIndex" Type="int" />
		<Field Name="MapDisplayConditionId" Type="int" />
	</Table>
	<Table Name="JournalEncounterCreature" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="CreatureDisplayInfoId" Type="int" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="JournalEncounterId" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiModelSceneId" Type="int" />
	</Table>
	<Table Name="JournalEncounterItem" Build="26231">
		<Field Name="ItemId" Type="int" />
		<Field Name="JournalEncounterId" Type="ushort" />
		<Field Name="DifficultyMask" Type="byte" />
		<Field Name="FactionMask" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="JournalEncounterSection" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Title" Type="string" />
		<Field Name="BodyText" Type="string" />
		<Field Name="IconCreatureDisplayInfoId" Type="int" />
		<Field Name="SpellId" Type="int" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="JournalEncounterId" Type="ushort" />
		<Field Name="NextSiblingSectionId" Type="ushort" />
		<Field Name="FirstChildSectionId" Type="ushort" />
		<Field Name="ParentSectionId" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="IconFlags" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="DifficultyMask" Type="byte" />
		<Field Name="UiModelSceneId" Type="int" />
	</Table>
	<Table Name="JournalEncounterXDifficulty" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyId" Type="byte" />
	</Table>
	<Table Name="JournalEncounterXMapLoc" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Map" Type="float" ArraySize="2" />
		<Field Name="Flags" Type="byte" />
		<Field Name="JournalEncounterId" Type="int" />
		<Field Name="DungeonMapId" Type="int" />
		<Field Name="MapDisplayConditionId" Type="int" />
	</Table>
	<Table Name="JournalInstance" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="ButtonFileDataId" Type="int" />
		<Field Name="ButtonSmallFileDataId" Type="int" />
		<Field Name="BackgroundFileDataId" Type="int" />
		<Field Name="LoreFileDataId" Type="int" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="AreaId" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="JournalItemXDifficulty" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyId" Type="byte" />
	</Table>
	<Table Name="JournalSectionXDifficulty" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyId" Type="byte" />
	</Table>
	<Table Name="JournalTier" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="JournalTierXInstance" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="JournalTierId" Type="ushort" />
		<Field Name="JournalInstanceId" Type="ushort" />
	</Table>
	<Table Name="KeyChain" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Key" Type="byte" ArraySize="32" />
	</Table>
	<Table Name="KeystoneAffix" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Filedataid" Type="int" />
	</Table>
	<Table Name="Languages" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="LanguageWords" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Word" Type="string" />
		<Field Name="LanguageId" Type="byte" />
	</Table>
	<Table Name="LfgDungeonExpansion" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RandomId" Type="ushort" />
		<Field Name="ExpansionLevel" Type="byte" />
		<Field Name="HardLevelMin" Type="byte" />
		<Field Name="HardLevelMax" Type="byte" />
		<Field Name="TargetLevelMin" Type="int" />
		<Field Name="TargetLevelMax" Type="int" />
	</Table>
	<Table Name="LfgDungeonGroup" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="OrderIndex" Type="ushort" />
		<Field Name="ParentGroupId" Type="byte" />
		<Field Name="Typeid" Type="byte" />
	</Table>
	<Table Name="LfgDungeons" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Flags" Type="uint" />
		<Field Name="MinGear" Type="float" />
		<Field Name="MaxLevel" Type="ushort" />
		<Field Name="TargetLevelMax" Type="ushort" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="RandomId" Type="ushort" />
		<Field Name="ScenarioId" Type="ushort" />
		<Field Name="FinalEncounterId" Type="ushort" />
		<Field Name="BonusReputationAmount" Type="ushort" />
		<Field Name="MentorItemLevel" Type="ushort" />
		<Field Name="RequiredPlayerConditionId" Type="ushort" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="TargetLevel" Type="byte" />
		<Field Name="TargetLevelMin" Type="byte" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="TypeId" Type="byte" />
		<Field Name="Faction" Type="byte" />
		<Field Name="ExpansionLevel" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="GroupId" Type="byte" />
		<Field Name="CountTank" Type="byte" />
		<Field Name="CountHealer" Type="byte" />
		<Field Name="CountDamage" Type="byte" />
		<Field Name="MinCountTank" Type="byte" />
		<Field Name="MinCountHealer" Type="byte" />
		<Field Name="MinCountDamage" Type="byte" />
		<Field Name="Subtype" Type="byte" />
		<Field Name="MentorCharLevel" Type="byte" />
		<Field Name="IconTextureFileId" Type="int" />
		<Field Name="RewardsBgTextureFileId" Type="int" />
		<Field Name="PopupBgTextureFileId" Type="int" />
	</Table>
	<Table Name="LfgDungeonsGroupingMap" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RandomLfgDungeonsId" Type="ushort" />
		<Field Name="GroupId" Type="byte" />
	</Table>
	<Table Name="LfgRoleRequirement" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RoleType" Type="byte" />
		<Field Name="PlayerConditionId" Type="int" />
	</Table>
	<Table Name="Light" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GameCoords" Type="float" ArraySize="3" />
		<Field Name="GameFalloffStart" Type="float" />
		<Field Name="GameFalloffEnd" Type="float" />
		<Field Name="ContinentId" Type="ushort" />
		<Field Name="LightParamsId" Type="ushort" ArraySize="8" />
	</Table>
	<Table Name="LightData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DirectColor" Type="int" />
		<Field Name="AmbientColor" Type="int" />
		<Field Name="SkyTopColor" Type="uint" />
		<Field Name="SkyMiddleColor" Type="int" />
		<Field Name="SkyBand1Color" Type="int" />
		<Field Name="SkyBand2Color" Type="uint" />
		<Field Name="SkySmogColor" Type="int" />
		<Field Name="SkyFogColor" Type="int" />
		<Field Name="SunColor" Type="int" />
		<Field Name="CloudSunColor" Type="int" />
		<Field Name="CloudEmissiveColor" Type="int" />
		<Field Name="CloudLayer1AmbientColor" Type="int" />
		<Field Name="CloudLayer2AmbientColor" Type="int" />
		<Field Name="OceanCloseColor" Type="int" />
		<Field Name="OceanFarColor" Type="int" />
		<Field Name="RiverCloseColor" Type="int" />
		<Field Name="RiverFarColor" Type="int" />
		<Field Name="ShadowOpacity" Type="uint" />
		<Field Name="FogEnd" Type="float" />
		<Field Name="FogScaler" Type="float" />
		<Field Name="CloudDensity" Type="float" />
		<Field Name="FogDensity" Type="float" />
		<Field Name="FogHeight" Type="float" />
		<Field Name="FogHeightScaler" Type="float" />
		<Field Name="FogHeightDensity" Type="float" />
		<Field Name="SunFogAngle" Type="float" />
		<Field Name="EndFogColorDistance" Type="float" />
		<Field Name="SunFogColor" Type="int" />
		<Field Name="EndFogColor" Type="int" />
		<Field Name="FogHeightColor" Type="int" />
		<Field Name="ColorGradingFileDataId" Type="int" />
		<Field Name="HorizonAmbientColor" Type="int" />
		<Field Name="GroundAmbientColor" Type="int" />
		<Field Name="LightParamId" Type="ushort" />
		<Field Name="Time" Type="ushort" />
	</Table>
	<Table Name="LightParams" Build="26231">
		<Field Name="Glow" Type="float" />
		<Field Name="WaterShallowAlpha" Type="float" />
		<Field Name="WaterDeepAlpha" Type="float" />
		<Field Name="OceanShallowAlpha" Type="float" />
		<Field Name="OceanDeepAlpha" Type="float" />
		<Field Name="OverrideCelestialSphere" Type="float" ArraySize="3" />
		<Field Name="LightSkyboxId" Type="ushort" />
		<Field Name="HighlightSky" Type="byte" />
		<Field Name="CloudTypeId" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="LightSkybox" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="CelestialSkyboxFileDataId" Type="int" />
		<Field Name="SkyboxFileDataId" Type="int" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="LiquidMaterial" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LVF" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="LiquidObject" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FlowDirection" Type="float" />
		<Field Name="FlowSpeed" Type="float" />
		<Field Name="LiquidTypeId" Type="ushort" />
		<Field Name="Fishable" Type="byte" />
		<Field Name="Reflection" Type="byte" />
	</Table>
	<Table Name="LiquidType" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Texture" Type="string" ArraySize="6" />
		<Field Name="SpellId" Type="int" />
		<Field Name="MaxDarkenDepth" Type="float" />
		<Field Name="FogDarkenIntensity" Type="float" />
		<Field Name="AmbDarkenIntensity" Type="float" />
		<Field Name="DirDarkenIntensity" Type="float" />
		<Field Name="ParticleScale" Type="float" />
		<Field Name="MinimapStaticCol" Type="uint" />
		<Field Name="Color" Type="uint" ArraySize="2" />
		<Field Name="Float" Type="float" ArraySize="18" />
		<Field Name="Int" Type="int" ArraySize="4" />
		<Field Name="Coefficient" Type="float" ArraySize="4" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="LightId" Type="ushort" />
		<Field Name="SoundBank" Type="byte" />
		<Field Name="ParticleMovement" Type="byte" />
		<Field Name="ParticleTexSlots" Type="byte" />
		<Field Name="MaterialId" Type="byte" />
		<Field Name="FrameCountTexture" Type="byte" ArraySize="6" />
		<Field Name="SoundId" Type="int" />
	</Table>
	<Table Name="LoadingScreens" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="NarrowScreenFileDataId" Type="int" />
		<Field Name="WideScreenFileDataId" Type="int" />
		<Field Name="WideScreen169FileDataId" Type="int" />
	</Table>
	<Table Name="LoadingScreenTaxiSplines" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LocX" Type="float" ArraySize="10" />
		<Field Name="LocY" Type="float" ArraySize="10" />
		<Field Name="PathId" Type="ushort" />
		<Field Name="LoadingScreenId" Type="ushort" />
		<Field Name="LegIndex" Type="byte" />
	</Table>
	<Table Name="Locale" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FontFileDataId" Type="int" />
		<Field Name="WowLocale" Type="byte" />
		<Field Name="Secondary" Type="byte" />
		<Field Name="ClientDisplayExpansion" Type="byte" />
	</Table>
	<Table Name="Location" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="Rot" Type="float" ArraySize="3" />
	</Table>
	<Table Name="Lock" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Index" Type="int" ArraySize="8" />
		<Field Name="Skill" Type="ushort" ArraySize="8" />
		<Field Name="Type" Type="byte" ArraySize="8" />
		<Field Name="Action" Type="byte" ArraySize="8" />
	</Table>
	<Table Name="LockType" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="ResourceName" Type="string" />
		<Field Name="Verb" Type="string" />
		<Field Name="CursorName" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="LookAtController" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ReactionEnableDistance" Type="float" />
		<Field Name="ReactionGiveupDistance" Type="float" />
		<Field Name="TorsoSpeedFactor" Type="float" />
		<Field Name="HeadSpeedFactor" Type="float" />
		<Field Name="ReactionEnableFOVDeg" Type="ushort" />
		<Field Name="ReactionGiveupTimeMS" Type="ushort" />
		<Field Name="ReactionIgnoreTimeMinMS" Type="ushort" />
		<Field Name="ReactionIgnoreTimeMaxMS" Type="ushort" />
		<Field Name="MaxTorsoYaw" Type="byte" />
		<Field Name="MaxTorsoYawWhileMoving" Type="byte" />
		<Field Name="MaxHeadYaw" Type="byte" />
		<Field Name="MaxHeadPitch" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ReactionWarmUpTimeMSMin" Type="int" />
		<Field Name="ReactionWarmUpTimeMSMax" Type="int" />
		<Field Name="ReactionGiveupFOVDeg" Type="int" />
		<Field Name="MaxTorsoPitchUp" Type="int" />
		<Field Name="MaxTorsoPitchDown" Type="int" />
	</Table>
	<Table Name="MailTemplate" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Body" Type="string" />
	</Table>
	<Table Name="ManagedWorldState" Build="26231">
		<Field Name="CurrentStageWorldStateId" Type="int" />
		<Field Name="ProgressWorldStateId" Type="int" />
		<Field Name="UpTimeSecs" Type="int" />
		<Field Name="DownTimeSecs" Type="int" />
		<Field Name="OccurrencesWorldStateId" Type="int" />
		<Field Name="AccumulationStateTargetValue" Type="int" />
		<Field Name="DepletionStateTargetValue" Type="int" />
		<Field Name="AccumulationAmountPerMinute" Type="int" />
		<Field Name="DepletionAmountPerMinute" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ManagedWorldStateBuff" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OccurrenceValue" Type="int" />
		<Field Name="BuffSpellId" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
	</Table>
	<Table Name="ManagedWorldStateInput" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ManagedWorldStateId" Type="int" />
		<Field Name="QuestId" Type="int" />
		<Field Name="ValidInputConditionId" Type="int" />
	</Table>
	<Table Name="ManifestInterfaceActionIcon" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ManifestInterfaceData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FilePath" Type="string" />
		<Field Name="FileName" Type="string" />
	</Table>
	<Table Name="ManifestInterfaceItemIcon" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ManifestInterfaceTOCData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FilePath" Type="string" />
	</Table>
	<Table Name="ManifestMP3" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Map" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Directory" Type="string" />
		<Field Name="MapName" Type="string" />
		<Field Name="MapDescription0" Type="string" />
		<Field Name="MapDescription1" Type="string" />
		<Field Name="PvpShortDescription" Type="string" />
		<Field Name="PvpLongDescription" Type="string" />
		<Field Name="Flags" Type="uint" ArraySize="2" />
		<Field Name="MinimapIconScale" Type="float" />
		<Field Name="Corpse" Type="float" ArraySize="2" />
		<Field Name="AreaTableId" Type="ushort" />
		<Field Name="LoadingScreenId" Type="ushort" />
		<Field Name="CorpseMapId" Type="ushort" />
		<Field Name="TimeOfDayOverride" Type="ushort" />
		<Field Name="ParentMapId" Type="ushort" />
		<Field Name="CosmeticParentMapId" Type="ushort" />
		<Field Name="WindSettingsId" Type="ushort" />
		<Field Name="InstanceType" Type="byte" />
		<Field Name="MapType" Type="byte" />
		<Field Name="ExpansionId" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="TimeOffset" Type="byte" />
	</Table>
	<Table Name="MapCelestialBody" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CelestialBodyId" Type="ushort" />
		<Field Name="PlayerConditionId" Type="int" />
	</Table>
	<Table Name="MapChallengeMode" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="CriteriaCount" Type="ushort" ArraySize="3" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="MapDifficulty" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Message" Type="string" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="ResetInterval" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="LockId" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ItemContext" Type="byte" />
		<Field Name="ItemContextPickerId" Type="int" />
	</Table>
	<Table Name="MapDifficultyXCondition" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FailureDescription" Type="string" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="MapLoadingScreen" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Min" Type="float" ArraySize="2" />
		<Field Name="Max" Type="float" ArraySize="2" />
		<Field Name="LoadingScreenId" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="MarketingPromotionsXLocale" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AcceptURL" Type="string" />
		<Field Name="AdTexture" Type="int" />
		<Field Name="LogoTexture" Type="int" />
		<Field Name="AcceptButtonTexture" Type="int" />
		<Field Name="DeclineButtonTexture" Type="int" />
		<Field Name="PromotionId" Type="byte" />
		<Field Name="LocaleId" Type="byte" />
	</Table>
	<Table Name="Material" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="FoleySoundId" Type="int" />
		<Field Name="SheatheSoundId" Type="int" />
		<Field Name="UnsheatheSoundId" Type="int" />
	</Table>
	<Table Name="MinorTalent" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="MissileTargeting" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TurnLingering" Type="float" />
		<Field Name="PitchLingering" Type="float" />
		<Field Name="MouseLingering" Type="float" />
		<Field Name="EndOpacity" Type="float" />
		<Field Name="ArcSpeed" Type="float" />
		<Field Name="ArcRepeat" Type="float" />
		<Field Name="ArcWidth" Type="float" />
		<Field Name="ImpactRadius" Type="float" ArraySize="2" />
		<Field Name="ImpactTexRadius" Type="float" />
		<Field Name="ArcTextureFileId" Type="int" />
		<Field Name="ImpactTextureFileId" Type="int" />
		<Field Name="ImpactModelFileId" Type="int" ArraySize="2" />
	</Table>
	<Table Name="ModelAnimCloakDampening" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AnimationDataId" Type="int" />
		<Field Name="CloakDampeningId" Type="int" />
	</Table>
	<Table Name="ModelFileData" Build="26231">
		<Field Name="Flags" Type="byte" />
		<Field Name="LodCount" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelResourcesId" Type="int" />
	</Table>
	<Table Name="ModelRibbonQuality" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RibbonQualityId" Type="byte" />
	</Table>
	<Table Name="ModifierTree" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Asset" Type="uint" />
		<Field Name="SecondaryAsset" Type="int" />
		<Field Name="Parent" Type="int" />
		<Field Name="Type" Type="byte" />
		<Field Name="TertiaryAsset" Type="byte" />
		<Field Name="Operator" Type="byte" />
		<Field Name="Amount" Type="byte" />
	</Table>
	<Table Name="Mount" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="SourceText" Type="string" />
		<Field Name="SourceSpellId" Type="int" />
		<Field Name="MountFlyRideHeight" Type="float" />
		<Field Name="MountTypeId" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="UiModelSceneId" Type="int" />
	</Table>
	<Table Name="MountCapability" Build="26231">
		<Field Name="ReqSpellKnownId" Type="int" />
		<Field Name="ModSpellAuraId" Type="int" />
		<Field Name="ReqRidingSkill" Type="ushort" />
		<Field Name="ReqAreaId" Type="ushort" />
		<Field Name="ReqMapId" Type="ushort" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ReqSpellAuraId" Type="int" />
	</Table>
	<Table Name="MountTypeXCapability" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MountTypeId" Type="ushort" />
		<Field Name="MountCapabilityId" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="MountXDisplay" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureDisplayInfoId" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
	</Table>
	<Table Name="Movie" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AudioFileDataId" Type="int" />
		<Field Name="SubtitleFileDataId" Type="int" />
		<Field Name="Volume" Type="byte" />
		<Field Name="KeyId" Type="byte" />
	</Table>
	<Table Name="MovieFileData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Resolution" Type="ushort" />
	</Table>
	<Table Name="MovieVariation" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="OverlayFileDataId" Type="int" />
	</Table>
	<Table Name="MultiStateProperties" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Offset" Type="float" ArraySize="3" />
		<Field Name="StateIndex" Type="byte" />
		<Field Name="GameObjectId" Type="int" />
		<Field Name="GameEventId" Type="int" />
		<Field Name="Facing" Type="float" />
		<Field Name="TransitionInId" Type="int" />
		<Field Name="TransitionOutId" Type="int" />
		<Field Name="CollisionHull" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="MultiTransitionProperties" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TransitionType" Type="int" />
		<Field Name="DurationMS" Type="int" />
	</Table>
	<Table Name="NameGen" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="RaceId" Type="byte" />
		<Field Name="Sex" Type="byte" />
	</Table>
	<Table Name="NamesProfanity" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Language" Type="byte" />
	</Table>
	<Table Name="NamesReserved" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="NamesReservedLocale" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="LocaleMask" Type="byte" />
	</Table>
	<Table Name="NpcModelItemSlotDisplayInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemDisplayInfoId" Type="int" />
		<Field Name="ItemSlot" Type="byte" />
	</Table>
	<Table Name="NPCSounds" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundId" Type="int" ArraySize="4" />
	</Table>
	<Table Name="NumTalentsAtLevel" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="NumTalents" Type="int" />
		<Field Name="NumTalentsDeathKnight" Type="int" />
		<Field Name="NumTalentsDemonHunter" Type="int" />
	</Table>
	<Table Name="ObjectEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Offset" Type="float" ArraySize="3" />
		<Field Name="ObjectEffectGroupId" Type="ushort" />
		<Field Name="TriggerType" Type="byte" />
		<Field Name="EventType" Type="byte" />
		<Field Name="EffectRecType" Type="byte" />
		<Field Name="Attachment" Type="byte" />
		<Field Name="EffectRecId" Type="int" />
		<Field Name="ObjectEffectModifierId" Type="int" />
	</Table>
	<Table Name="ObjectEffectModifier" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Param" Type="float" ArraySize="4" />
		<Field Name="InputType" Type="byte" />
		<Field Name="MapType" Type="byte" />
		<Field Name="OutputType" Type="byte" />
	</Table>
	<Table Name="ObjectEffectPackageElem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ObjectEffectPackageId" Type="ushort" />
		<Field Name="ObjectEffectGroupId" Type="ushort" />
		<Field Name="StateType" Type="ushort" />
	</Table>
	<Table Name="OutlineEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PassiveHighlightColorId" Type="int" />
		<Field Name="HighlightColorId" Type="int" />
		<Field Name="Priority" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="Range" Type="float" />
		<Field Name="UnitConditionId" Type="int" ArraySize="2" />
	</Table>
	<Table Name="OverrideSpellData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Spells" Type="int" ArraySize="10" />
		<Field Name="PlayerActionBarFileDataId" Type="uint" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="PageTextMaterial" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="PaperDollItemFrame" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemButtonName" Type="string" />
		<Field Name="SlotNumber" Type="byte" />
		<Field Name="SlotIconFileId" Type="int" />
	</Table>
	<Table Name="ParagonReputation" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LevelThreshold" Type="int" />
		<Field Name="QuestId" Type="int" />
		<Field Name="FactionId" Type="int" />
	</Table>
	<Table Name="ParticleColor" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Start" Type="uint" ArraySize="3" />
		<Field Name="Mid" Type="uint" ArraySize="3" />
		<Field Name="End" Type="uint" ArraySize="3" />
	</Table>
	<Table Name="Path" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="byte" />
		<Field Name="SplineType" Type="byte" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
		<Field Name="Alpha" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="PathNode" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LocationId" Type="int" />
		<Field Name="PathId" Type="ushort" />
		<Field Name="Sequence" Type="ushort" />
	</Table>
	<Table Name="PathNodeProperty" Build="26231">
		<Field Name="PathId" Type="ushort" />
		<Field Name="Sequence" Type="ushort" />
		<Field Name="PropertyIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="int" />
	</Table>
	<Table Name="PathProperty" Build="26231">
		<Field Name="Value" Type="int" />
		<Field Name="PathId" Type="ushort" />
		<Field Name="PropertyIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Phase" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="ushort" />
	</Table>
	<Table Name="PhaseShiftZoneSounds" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AreaId" Type="ushort" />
		<Field Name="PhaseId" Type="ushort" />
		<Field Name="PhaseGroupId" Type="ushort" />
		<Field Name="SoundAmbienceId" Type="ushort" />
		<Field Name="UwSoundAmbienceId" Type="ushort" />
		<Field Name="WmoAreaId" Type="byte" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="SoundProviderPreferencesId" Type="byte" />
		<Field Name="UwSoundProviderPreferencesId" Type="byte" />
		<Field Name="ZoneIntroMusicId" Type="int" />
		<Field Name="ZoneMusicId" Type="int" />
		<Field Name="UwZoneIntroMusicId" Type="int" />
		<Field Name="UwZoneMusicId" Type="int" />
	</Table>
	<Table Name="PhaseXPhaseGroup" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PhaseId" Type="ushort" />
	</Table>
	<Table Name="PlayerCondition" Build="26231">
		<Field Name="RaceMask" Type="ulong" />
		<Field Name="FailureDescription" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MinLevel" Type="ushort" />
		<Field Name="MaxLevel" Type="ushort" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="Gender" Type="byte" />
		<Field Name="NativeGender" Type="byte" />
		<Field Name="SkillLogic" Type="int" />
		<Field Name="LanguageId" Type="byte" />
		<Field Name="MinLanguage" Type="byte" />
		<Field Name="MaxLanguage" Type="int" />
		<Field Name="MaxFactionId" Type="ushort" />
		<Field Name="MaxReputation" Type="byte" />
		<Field Name="ReputationLogic" Type="int" />
		<Field Name="CurrentPvpFaction" Type="byte" />
		<Field Name="MinPVPRank" Type="byte" />
		<Field Name="MaxPVPRank" Type="byte" />
		<Field Name="PvpMedal" Type="byte" />
		<Field Name="PrevQuestLogic" Type="int" />
		<Field Name="CurrQuestLogic" Type="int" />
		<Field Name="CurrentCompletedQuestLogic" Type="int" />
		<Field Name="SpellLogic" Type="int" />
		<Field Name="ItemLogic" Type="int" />
		<Field Name="ItemFlags" Type="byte" />
		<Field Name="AuraSpellLogic" Type="int" />
		<Field Name="WorldStateExpressionId" Type="ushort" />
		<Field Name="WeatherId" Type="byte" />
		<Field Name="PartyStatus" Type="byte" />
		<Field Name="LifetimeMaxPVPRank" Type="byte" />
		<Field Name="AchievementLogic" Type="int" />
		<Field Name="LfgLogic" Type="int" />
		<Field Name="AreaLogic" Type="int" />
		<Field Name="CurrencyLogic" Type="int" />
		<Field Name="QuestKillId" Type="ushort" />
		<Field Name="QuestKillLogic" Type="int" />
		<Field Name="MinExpansionLevel" Type="byte" />
		<Field Name="MaxExpansionLevel" Type="byte" />
		<Field Name="MinExpansionTier" Type="byte" />
		<Field Name="MaxExpansionTier" Type="byte" />
		<Field Name="MinGuildLevel" Type="byte" />
		<Field Name="MaxGuildLevel" Type="byte" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="PhaseId" Type="ushort" />
		<Field Name="PhaseGroupId" Type="int" />
		<Field Name="MinAvgItemLevel" Type="int" />
		<Field Name="MaxAvgItemLevel" Type="int" />
		<Field Name="MinAvgEquippedItemLevel" Type="ushort" />
		<Field Name="MaxAvgEquippedItemLevel" Type="ushort" />
		<Field Name="ChrSpecializationIndex" Type="byte" />
		<Field Name="ChrSpecializationRole" Type="byte" />
		<Field Name="PowerType" Type="byte" />
		<Field Name="PowerTypeComp" Type="byte" />
		<Field Name="PowerTypeValue" Type="byte" />
		<Field Name="ModifierTreeId" Type="int" />
		<Field Name="WeaponSubclassMask" Type="int" />
		<Field Name="SkillId" Type="ushort" ArraySize="4" />
		<Field Name="MinSkill" Type="ushort" ArraySize="4" />
		<Field Name="MaxSkill" Type="ushort" ArraySize="4" />
		<Field Name="MinFactionId" Type="int" ArraySize="3" />
		<Field Name="MinReputation" Type="byte" ArraySize="3" />
		<Field Name="PrevQuestId" Type="ushort" ArraySize="4" />
		<Field Name="CurrQuestId" Type="ushort" ArraySize="4" />
		<Field Name="CurrentCompletedQuestId" Type="ushort" ArraySize="4" />
		<Field Name="SpellId" Type="int" ArraySize="4" />
		<Field Name="ItemId" Type="int" ArraySize="4" />
		<Field Name="ItemCount" Type="int" ArraySize="4" />
		<Field Name="Explored" Type="ushort" ArraySize="2" />
		<Field Name="Time" Type="int" ArraySize="2" />
		<Field Name="AuraSpellId" Type="int" ArraySize="4" />
		<Field Name="AuraStacks" Type="byte" ArraySize="4" />
		<Field Name="Achievement" Type="ushort" ArraySize="4" />
		<Field Name="LfgStatus" Type="byte" ArraySize="4" />
		<Field Name="LfgCompare" Type="byte" ArraySize="4" />
		<Field Name="LfgValue" Type="int" ArraySize="4" />
		<Field Name="AreaId" Type="ushort" ArraySize="4" />
		<Field Name="CurrencyId" Type="int" ArraySize="4" />
		<Field Name="CurrencyCount" Type="int" ArraySize="4" />
		<Field Name="QuestKillMonster" Type="int" ArraySize="6" />
		<Field Name="MovementFlags" Type="int" ArraySize="2" />
	</Table>
	<Table Name="Positioner" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StartLife" Type="float" />
		<Field Name="FirstStateId" Type="ushort" />
		<Field Name="Flags" Type="byte" />
		<Field Name="StartLifePercent" Type="byte" />
	</Table>
	<Table Name="PositionerState" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EndLife" Type="float" />
		<Field Name="EndLifePercent" Type="byte" />
		<Field Name="NextStateId" Type="int" />
		<Field Name="TransformMatrixId" Type="int" />
		<Field Name="PosEntryId" Type="int" />
		<Field Name="RotEntryId" Type="int" />
		<Field Name="ScaleEntryId" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="PositionerStateEntry" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ParamA" Type="float" />
		<Field Name="ParamB" Type="float" />
		<Field Name="SrcValType" Type="ushort" />
		<Field Name="SrcVal" Type="ushort" />
		<Field Name="DstValType" Type="ushort" />
		<Field Name="DstVal" Type="ushort" />
		<Field Name="EntryType" Type="byte" />
		<Field Name="Style" Type="byte" />
		<Field Name="SrcType" Type="byte" />
		<Field Name="DstType" Type="byte" />
		<Field Name="CurveId" Type="int" />
	</Table>
	<Table Name="PowerDisplay" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GlobalStringBaseTag" Type="string" />
		<Field Name="ActualType" Type="byte" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="PowerType" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="NameGlobalStringTag" Type="string" />
		<Field Name="CostGlobalStringTag" Type="string" />
		<Field Name="RegenPeace" Type="float" />
		<Field Name="RegenCombat" Type="float" />
		<Field Name="MaxBasePower" Type="ushort" />
		<Field Name="RegenInterruptTimeMS" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="PowerTypeEnum" Type="byte" />
		<Field Name="MinPower" Type="byte" />
		<Field Name="CenterPower" Type="byte" />
		<Field Name="DefaultPower" Type="byte" />
		<Field Name="DisplayModifier" Type="byte" />
	</Table>
	<Table Name="PrestigeLevelInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="BadgeTextureFileDataId" Type="int" />
		<Field Name="PrestigeLevel" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="PvpBracketTypes" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BracketId" Type="byte" />
		<Field Name="WeeklyQuestId" Type="int" ArraySize="4" />
	</Table>
	<Table Name="PvpDifficulty" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RangeIndex" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
	</Table>
	<Table Name="PvpItem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="int" />
		<Field Name="ItemLevelDelta" Type="byte" />
	</Table>
	<Table Name="PvpReward" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HonorLevel" Type="int" />
		<Field Name="PrestigeLevel" Type="int" />
		<Field Name="RewardPackId" Type="int" />
	</Table>
	<Table Name="PvpScalingEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="float" />
		<Field Name="PvpScalingEffectTypeId" Type="int" />
		<Field Name="SpecializationId" Type="int" />
	</Table>
	<Table Name="PvpScalingEffectType" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="PvpTalent" Build="26231">
		<Field Name="Description" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="SpecId" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="OverridesSpellId" Type="int" />
		<Field Name="ActionBarSpellId" Type="int" />
		<Field Name="PvpTalentCategoryId" Type="int" />
		<Field Name="LevelRequired" Type="int" />
	</Table>
	<Table Name="PvpTalentCategory" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TalentSlotMask" Type="byte" />
	</Table>
	<Table Name="PvpTalentSlotUnlock" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Slot" Type="byte" />
		<Field Name="LevelRequired" Type="int" />
		<Field Name="DeathKnightLevelRequired" Type="int" />
		<Field Name="DemonHunterLevelRequired" Type="int" />
	</Table>
	<Table Name="QuestFactionReward" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="ushort" ArraySize="10" />
	</Table>
	<Table Name="QuestFeedbackEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="MinimapAtlasMemberId" Type="ushort" />
		<Field Name="AttachPoint" Type="byte" />
		<Field Name="PassiveHighlightColorType" Type="byte" />
		<Field Name="Priority" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="QuestInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="InfoName" Type="string" />
		<Field Name="Profession" Type="ushort" />
		<Field Name="Type" Type="byte" />
		<Field Name="Modifiers" Type="byte" />
	</Table>
	<Table Name="QuestLine" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="QuestLineXQuest" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QuestLineId" Type="int" />
		<Field Name="QuestId" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="QuestMoneyReward" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="int" ArraySize="10" />
	</Table>
	<Table Name="QuestObjective" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
		<Field Name="Amount" Type="int" />
		<Field Name="ObjectId" Type="int" />
		<Field Name="Type" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="StorageIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="QuestPackageItem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="int" />
		<Field Name="PackageId" Type="ushort" />
		<Field Name="DisplayType" Type="byte" />
		<Field Name="ItemQuantity" Type="int" />
	</Table>
	<Table Name="QuestPOIBlob" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="WorldMapAreaId" Type="ushort" />
		<Field Name="NumPoints" Type="byte" />
		<Field Name="Floor" Type="byte" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="QuestId" Type="int" />
		<Field Name="ObjectiveIndex" Type="int" />
	</Table>
	<Table Name="QuestPOIPoint" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="X" Type="ushort" />
		<Field Name="Y" Type="ushort" />
	</Table>
	<Table Name="QuestSort" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SortName" Type="string" />
		<Field Name="UiOrderIndex" Type="byte" />
	</Table>
	<Table Name="QuestV2" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UniqueBitFlag" Type="ushort" />
	</Table>
	<Table Name="QuestV2CliTask" Build="26231">
		<Field Name="FiltRaces" Type="ulong" />
		<Field Name="QuestTitle" Type="string" />
		<Field Name="BulletText" Type="string" />
		<Field Name="StartItem" Type="int" />
		<Field Name="UniqueBitFlag" Type="ushort" />
		<Field Name="ConditionId" Type="ushort" />
		<Field Name="FiltClasses" Type="ushort" />
		<Field Name="FiltCompletedQuest" Type="ushort" ArraySize="3" />
		<Field Name="FiltMinSkillId" Type="ushort" />
		<Field Name="WorldStateExpressionId" Type="ushort" />
		<Field Name="FiltActiveQuest" Type="byte" />
		<Field Name="FiltCompletedQuestLogic" Type="byte" />
		<Field Name="FiltMaxFactionId" Type="byte" />
		<Field Name="FiltMaxFactionValue" Type="byte" />
		<Field Name="FiltMaxLevel" Type="byte" />
		<Field Name="FiltMinFactionId" Type="byte" />
		<Field Name="FiltMinFactionValue" Type="byte" />
		<Field Name="FiltMinLevel" Type="byte" />
		<Field Name="FiltMinSkillValue" Type="byte" />
		<Field Name="FiltNonActiveQuest" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BreadCrumbId" Type="int" />
		<Field Name="QuestInfoId" Type="int" />
		<Field Name="ContentTuningId" Type="int" />
	</Table>
	<Table Name="QuestXGroupActivity" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QuestId" Type="int" />
		<Field Name="GroupFinderActivityId" Type="int" />
	</Table>
	<Table Name="QuestXP" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="ushort" ArraySize="10" />
	</Table>
	<Table Name="RandPropPoints" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Epic" Type="int" ArraySize="5" />
		<Field Name="Superior" Type="int" ArraySize="5" />
		<Field Name="Good" Type="int" ArraySize="5" />
	</Table>
	<Table Name="RelicSlotTierRequirement" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="RelicIndex" Type="byte" />
		<Field Name="RelicTier" Type="byte" />
	</Table>
	<Table Name="RelicTalent" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ArtifactPowerId" Type="ushort" />
		<Field Name="ArtifactPowerLabel" Type="byte" />
		<Field Name="Type" Type="int" />
		<Field Name="PVal" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="ResearchBranch" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="ItemId" Type="int" />
		<Field Name="CurrencyId" Type="ushort" />
		<Field Name="ResearchFieldId" Type="byte" />
		<Field Name="TextureFileId" Type="int" />
		<Field Name="BigTextureFileId" Type="int" />
	</Table>
	<Table Name="ResearchField" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Slot" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ResearchProject" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="SpellId" Type="int" />
		<Field Name="ResearchBranchId" Type="ushort" />
		<Field Name="Rarity" Type="byte" />
		<Field Name="NumSockets" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TextureFileId" Type="int" />
		<Field Name="RequiredWeight" Type="int" />
	</Table>
	<Table Name="ResearchSite" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="QuestPoiBlobId" Type="int" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="AreaPOIIconEnum" Type="int" />
	</Table>
	<Table Name="Resistances" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="FizzleSoundId" Type="int" />
	</Table>
	<Table Name="RewardPack" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Money" Type="int" />
		<Field Name="ArtifactXPMultiplier" Type="float" />
		<Field Name="ArtifactXPDifficulty" Type="byte" />
		<Field Name="ArtifactXPCategoryId" Type="byte" />
		<Field Name="CharTitleId" Type="int" />
		<Field Name="TreasurePickerId" Type="int" />
	</Table>
	<Table Name="RewardPackXCurrencyType" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CurrencyTypeId" Type="int" />
		<Field Name="Quantity" Type="int" />
	</Table>
	<Table Name="RewardPackXItem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="int" />
		<Field Name="ItemQuantity" Type="int" />
	</Table>
	<Table Name="RibbonQuality" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaxSampleTimeDelta" Type="float" />
		<Field Name="AngleThreshold" Type="float" />
		<Field Name="MinDistancePerSlice" Type="float" />
		<Field Name="NumStrips" Type="byte" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="RulesetItemUpgrade" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemId" Type="int" />
		<Field Name="ItemUpgradeId" Type="ushort" />
	</Table>
	<Table Name="ScalingStatDistribution" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerLevelToItemLevelCurveId" Type="ushort" />
		<Field Name="MinLevel" Type="int" />
		<Field Name="MaxLevel" Type="int" />
	</Table>
	<Table Name="Scenario" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="AreaTableId" Type="ushort" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Type" Type="byte" />
	</Table>
	<Table Name="ScenarioEventEntry" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TriggerAsset" Type="int" />
		<Field Name="TriggerType" Type="byte" />
	</Table>
	<Table Name="ScenarioStep" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
		<Field Name="Title" Type="string" />
		<Field Name="ScenarioId" Type="ushort" />
		<Field Name="Supersedes" Type="ushort" />
		<Field Name="RewardQuestId" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Criteriatreeid" Type="int" />
		<Field Name="RelatedStep" Type="int" />
		<Field Name="VisibilityPlayerConditionId" Type="int" />
	</Table>
	<Table Name="SceneScript" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FirstSceneScriptId" Type="ushort" />
		<Field Name="NextSceneScriptId" Type="ushort" />
	</Table>
	<Table Name="SceneScriptGlobalText" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Script" Type="string" />
	</Table>
	<Table Name="SceneScriptPackage" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SceneScriptPackageMember" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SceneScriptPackageId" Type="ushort" />
		<Field Name="SceneScriptId" Type="ushort" />
		<Field Name="ChildSceneScriptPackageId" Type="ushort" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="SceneScriptText" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Script" Type="string" />
	</Table>
	<Table Name="ScheduledInterval" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="RepeatType" Type="int" />
		<Field Name="DurationSecs" Type="int" />
		<Field Name="OffsetSecs" Type="int" />
		<Field Name="DateAlignmentType" Type="int" />
	</Table>
	<Table Name="ScheduledWorldState" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ScheduledWorldStateGroupId" Type="int" />
		<Field Name="WorldStateId" Type="int" />
		<Field Name="Value" Type="int" />
		<Field Name="DurationSecs" Type="int" />
		<Field Name="Weight" Type="int" />
		<Field Name="UniqueCategory" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="ScheduledWorldStateGroup" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="ScheduledIntervalId" Type="int" />
		<Field Name="SelectionType" Type="int" />
		<Field Name="SelectionCount" Type="int" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="ScheduledWorldStateXUniqCat" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ScheduledUniqueCategoryId" Type="int" />
	</Table>
	<Table Name="ScreenEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Param" Type="uint" ArraySize="4" />
		<Field Name="LightParamsId" Type="ushort" />
		<Field Name="LightParamsFadeIn" Type="ushort" />
		<Field Name="LightParamsFadeOut" Type="ushort" />
		<Field Name="TimeOfDayOverride" Type="ushort" />
		<Field Name="Effect" Type="byte" />
		<Field Name="LightFlags" Type="byte" />
		<Field Name="EffectMask" Type="byte" />
		<Field Name="FullScreenEffectId" Type="int" />
		<Field Name="SoundAmbienceId" Type="int" />
		<Field Name="ZoneMusicId" Type="int" />
	</Table>
	<Table Name="ScreenLocation" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SDReplacementModel" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SdFileDataId" Type="int" />
	</Table>
	<Table Name="SeamlessSite" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapId" Type="int" />
	</Table>
	<Table Name="ServerMessages" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text" Type="string" />
	</Table>
	<Table Name="ShadowyEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PrimaryColor" Type="uint" />
		<Field Name="SecondaryColor" Type="uint" />
		<Field Name="Duration" Type="float" />
		<Field Name="Value" Type="float" />
		<Field Name="FadeInTime" Type="float" />
		<Field Name="FadeOutTime" Type="float" />
		<Field Name="InnerStrength" Type="float" />
		<Field Name="OuterStrength" Type="float" />
		<Field Name="InitialDelay" Type="float" />
		<Field Name="AttachPos" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CurveId" Type="int" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="SiegeableProperties" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Health" Type="int" />
		<Field Name="DamageSpellVisualKitId" Type="int" />
		<Field Name="HealingSpellVisualKitId" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="SkillLine" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayName" Type="string" />
		<Field Name="HordeDisplayName" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="AlternateVerb" Type="string" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="CategoryId" Type="byte" />
		<Field Name="CanLink" Type="byte" />
		<Field Name="SpellIconFileId" Type="int" />
		<Field Name="ParentSkillLineId" Type="int" />
		<Field Name="ParentTierIndex" Type="int" />
	</Table>
	<Table Name="SkillLineAbility" Build="26231">
		<Field Name="RaceMask" Type="ulong" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Spell" Type="int" />
		<Field Name="SupercedesSpell" Type="int" />
		<Field Name="SkillLine" Type="ushort" />
		<Field Name="TrivialSkillLineRankHigh" Type="ushort" />
		<Field Name="TrivialSkillLineRankLow" Type="ushort" />
		<Field Name="UniqueBit" Type="ushort" />
		<Field Name="TradeSkillCategoryId" Type="ushort" />
		<Field Name="NumSkillUps" Type="byte" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="MinSkillLineRank" Type="ushort" />
		<Field Name="AcquireMethod" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="SkillupSkillLineId" Type="ushort" />
	</Table>
	<Table Name="SkillRaceClassInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="ulong" />
		<Field Name="SkillId" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="SkillTierId" Type="ushort" />
		<Field Name="Availability" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="ClassMask" Type="int" />
	</Table>
	<Table Name="SoundAmbience" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="SoundFilterId" Type="int" />
		<Field Name="FlavorSoundFilterId" Type="int" />
		<Field Name="AmbienceId" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SoundAmbienceFlavor" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundEntriesIDDay" Type="int" />
		<Field Name="SoundEntriesIDNight" Type="int" />
	</Table>
	<Table Name="SoundBus" Build="26231">
		<Field Name="DefaultVolume" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="DefaultPlaybackLimit" Type="byte" />
		<Field Name="DefaultPriority" Type="byte" />
		<Field Name="DefaultPriorityPenalty" Type="byte" />
		<Field Name="BusEnumId" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="SoundBusOverride" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Volume" Type="float" />
		<Field Name="PlaybackLimit" Type="byte" />
		<Field Name="Priority" Type="byte" />
		<Field Name="PriorityPenalty" Type="byte" />
		<Field Name="SoundBusId" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
	</Table>
	<Table Name="SoundEmitterPillPoints" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="SoundEmittersId" Type="ushort" />
	</Table>
	<Table Name="SoundEmitters" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="Direction" Type="float" ArraySize="3" />
		<Field Name="WorldStateExpressionId" Type="ushort" />
		<Field Name="PhaseId" Type="ushort" />
		<Field Name="EmitterType" Type="byte" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundEntriesId" Type="int" />
		<Field Name="PhaseGroupId" Type="int" />
	</Table>
	<Table Name="SoundEnvelope" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundKitId" Type="int" />
		<Field Name="CurveId" Type="int" />
		<Field Name="DecayIndex" Type="ushort" />
		<Field Name="SustainIndex" Type="ushort" />
		<Field Name="ReleaseIndex" Type="ushort" />
		<Field Name="EnvelopeType" Type="byte" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="SoundFilter" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SoundFilterElem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Params" Type="float" ArraySize="9" />
		<Field Name="FilterType" Type="byte" />
	</Table>
	<Table Name="SoundKit" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VolumeFloat" Type="float" />
		<Field Name="MinDistance" Type="float" />
		<Field Name="DistanceCutoff" Type="float" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="SoundType" Type="byte" />
		<Field Name="DialogType" Type="byte" />
		<Field Name="EAXDef" Type="byte" />
		<Field Name="SoundKitAdvancedId" Type="int" />
		<Field Name="VolumeVariationPlus" Type="float" />
		<Field Name="VolumeVariationMinus" Type="float" />
		<Field Name="PitchVariationPlus" Type="float" />
		<Field Name="PitchVariationMinus" Type="float" />
		<Field Name="PitchAdjust" Type="float" />
		<Field Name="BusOverwriteId" Type="ushort" />
		<Field Name="MaxInstances" Type="byte" />
	</Table>
	<Table Name="SoundKitAdvanced" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="InnerRadius2D" Type="float" />
		<Field Name="DuckToSFX" Type="float" />
		<Field Name="DuckToMusic" Type="float" />
		<Field Name="InnerRadiusOfInfluence" Type="float" />
		<Field Name="OuterRadiusOfInfluence" Type="float" />
		<Field Name="TimeToDuck" Type="int" />
		<Field Name="TimeToUnduck" Type="int" />
		<Field Name="OuterRadius2D" Type="float" />
		<Field Name="Usage" Type="byte" />
		<Field Name="SoundKitId" Type="int" />
		<Field Name="TimeA" Type="int" />
		<Field Name="TimeB" Type="int" />
		<Field Name="TimeC" Type="int" />
		<Field Name="TimeD" Type="int" />
		<Field Name="RandomOffsetRange" Type="int" />
		<Field Name="TimeIntervalMin" Type="int" />
		<Field Name="TimeIntervalMax" Type="int" />
		<Field Name="DelayMin" Type="int" />
		<Field Name="DelayMax" Type="int" />
		<Field Name="VolumeSliderCategory" Type="byte" />
		<Field Name="DuckToAmbience" Type="float" />
		<Field Name="InsideAngle" Type="float" />
		<Field Name="OutsideAngle" Type="float" />
		<Field Name="OutsideVolume" Type="float" />
		<Field Name="MinRandomPosOffset" Type="byte" />
		<Field Name="MaxRandomPosOffset" Type="ushort" />
		<Field Name="DuckToDialog" Type="float" />
		<Field Name="DuckToSuppressors" Type="float" />
		<Field Name="MsOffset" Type="int" />
		<Field Name="TimeCooldownMin" Type="int" />
		<Field Name="TimeCooldownMax" Type="int" />
		<Field Name="MaxInstancesBehavior" Type="byte" />
		<Field Name="VolumeControlType" Type="byte" />
		<Field Name="VolumeFadeInTimeMin" Type="int" />
		<Field Name="VolumeFadeInTimeMax" Type="int" />
		<Field Name="VolumeFadeInCurveId" Type="int" />
		<Field Name="VolumeFadeOutTimeMin" Type="int" />
		<Field Name="VolumeFadeOutTimeMax" Type="int" />
		<Field Name="VolumeFadeOutCurveId" Type="int" />
		<Field Name="ChanceToPlay" Type="float" />
	</Table>
	<Table Name="SoundKitChild" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ParentSoundKitId" Type="int" />
		<Field Name="SoundKitId" Type="int" />
	</Table>
	<Table Name="SoundKitEntry" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundKitId" Type="int" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="Frequency" Type="byte" />
		<Field Name="Volume" Type="float" />
	</Table>
	<Table Name="SoundKitFallback" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundKitId" Type="int" />
		<Field Name="FallbackSoundKitId" Type="int" />
	</Table>
	<Table Name="SoundKitName" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SoundOverride" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ZoneIntroMusicId" Type="ushort" />
		<Field Name="ZoneMusicId" Type="ushort" />
		<Field Name="SoundAmbienceId" Type="ushort" />
		<Field Name="SoundProviderPreferencesId" Type="byte" />
	</Table>
	<Table Name="SoundProviderPreferences" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
		<Field Name="EAXDecayTime" Type="float" />
		<Field Name="EAX2EnvironmentSize" Type="float" />
		<Field Name="EAX2EnvironmentDiffusion" Type="float" />
		<Field Name="EAX2DecayHFRatio" Type="float" />
		<Field Name="EAX2ReflectionsDelay" Type="float" />
		<Field Name="EAX2ReverbDelay" Type="float" />
		<Field Name="EAX2RoomRolloff" Type="float" />
		<Field Name="EAX2AirAbsorption" Type="float" />
		<Field Name="EAX3DecayLFRatio" Type="float" />
		<Field Name="EAX3EchoTime" Type="float" />
		<Field Name="EAX3EchoDepth" Type="float" />
		<Field Name="EAX3ModulationTime" Type="float" />
		<Field Name="EAX3ModulationDepth" Type="float" />
		<Field Name="EAX3HFReference" Type="float" />
		<Field Name="EAX3LFReference" Type="float" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="EAX2Room" Type="ushort" />
		<Field Name="EAX2RoomHF" Type="ushort" />
		<Field Name="EAX2Reflections" Type="ushort" />
		<Field Name="EAX2Reverb" Type="ushort" />
		<Field Name="EAXEnvironmentSelection" Type="byte" />
		<Field Name="EAX3RoomLF" Type="byte" />
	</Table>
	<Table Name="SourceInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SourceText" Type="string" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="PvpFaction" Type="byte" />
	</Table>
	<Table Name="SpamMessages" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text" Type="string" />
	</Table>
	<Table Name="SpecializationSpells" Build="26231">
		<Field Name="Description" Type="string" />
		<Field Name="SpellId" Type="int" />
		<Field Name="OverridesSpellId" Type="int" />
		<Field Name="SpecId" Type="ushort" />
		<Field Name="DisplayOrder" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Spell" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="NameSubtext" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="AuraDescription" Type="string" />
	</Table>
	<Table Name="SpellActionBarPref" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="PreferredActionBarMask" Type="ushort" />
	</Table>
	<Table Name="SpellActivationOverlay" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="OverlayFileDataId" Type="int" />
		<Field Name="Color" Type="uint" />
		<Field Name="Scale" Type="float" />
		<Field Name="IconHighlightSpellClassMask" Type="uint" ArraySize="4" />
		<Field Name="ScreenLocationId" Type="byte" />
		<Field Name="TriggerType" Type="byte" />
		<Field Name="SoundEntriesId" Type="int" />
	</Table>
	<Table Name="SpellAuraOptions" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ProcCharges" Type="uint" />
		<Field Name="ProcTypeMask" Type="uint" ArraySize="2" />
		<Field Name="ProcCategoryRecovery" Type="int" />
		<Field Name="CumulativeAura" Type="ushort" />
		<Field Name="SpellProcsPerMinuteId" Type="ushort" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="ProcChance" Type="byte" />
	</Table>
	<Table Name="SpellAuraRestrictions" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CasterAuraSpell" Type="int" />
		<Field Name="TargetAuraSpell" Type="int" />
		<Field Name="ExcludeCasterAuraSpell" Type="int" />
		<Field Name="ExcludeTargetAuraSpell" Type="int" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="CasterAuraState" Type="byte" />
		<Field Name="TargetAuraState" Type="byte" />
		<Field Name="ExcludeCasterAuraState" Type="byte" />
		<Field Name="ExcludeTargetAuraState" Type="byte" />
	</Table>
	<Table Name="SpellAuraVisibility" Build="26231">
		<Field Name="Type" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="SpellAuraVisXChrSpec" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrSpecializationId" Type="ushort" />
	</Table>
	<Table Name="SpellCastingRequirements" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="MinFactionId" Type="ushort" />
		<Field Name="RequiredAreasId" Type="ushort" />
		<Field Name="RequiresSpellFocus" Type="ushort" />
		<Field Name="FacingCasterFlags" Type="byte" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="RequiredAuraVision" Type="byte" />
	</Table>
	<Table Name="SpellCastTimes" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Base" Type="uint" />
		<Field Name="Minimum" Type="uint" />
		<Field Name="PerLevel" Type="ushort" />
	</Table>
	<Table Name="SpellCategories" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Category" Type="ushort" />
		<Field Name="StartRecoveryCategory" Type="ushort" />
		<Field Name="ChargeCategory" Type="ushort" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="DefenseType" Type="byte" />
		<Field Name="DispelType" Type="byte" />
		<Field Name="Mechanic" Type="byte" />
		<Field Name="PreventionType" Type="byte" />
	</Table>
	<Table Name="SpellCategory" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="ChargeRecoveryTime" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UsesPerWeek" Type="byte" />
		<Field Name="MaxCharges" Type="byte" />
		<Field Name="TypeMask" Type="int" />
	</Table>
	<Table Name="SpellChainEffects" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AvgSegLen" Type="float" />
		<Field Name="NoiseScale" Type="float" />
		<Field Name="TexCoordScale" Type="float" />
		<Field Name="SegDuration" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="JointOffsetRadius" Type="float" />
		<Field Name="MinorJointScale" Type="float" />
		<Field Name="MajorJointScale" Type="float" />
		<Field Name="JointMoveSpeed" Type="float" />
		<Field Name="JointSmoothness" Type="float" />
		<Field Name="MinDurationBetweenJointJumps" Type="float" />
		<Field Name="MaxDurationBetweenJointJumps" Type="float" />
		<Field Name="WaveHeight" Type="float" />
		<Field Name="WaveFreq" Type="float" />
		<Field Name="WaveSpeed" Type="float" />
		<Field Name="MinWaveAngle" Type="float" />
		<Field Name="MaxWaveAngle" Type="float" />
		<Field Name="MinWaveSpin" Type="float" />
		<Field Name="MaxWaveSpin" Type="float" />
		<Field Name="ArcHeight" Type="float" />
		<Field Name="MinArcAngle" Type="float" />
		<Field Name="MaxArcAngle" Type="float" />
		<Field Name="MinArcSpin" Type="float" />
		<Field Name="MaxArcSpin" Type="float" />
		<Field Name="DelayBetweenEffects" Type="float" />
		<Field Name="MinFlickerOnDuration" Type="float" />
		<Field Name="MaxFlickerOnDuration" Type="float" />
		<Field Name="MinFlickerOffDuration" Type="float" />
		<Field Name="MaxFlickerOffDuration" Type="float" />
		<Field Name="PulseSpeed" Type="float" />
		<Field Name="PulseOnLength" Type="float" />
		<Field Name="PulseFadeLength" Type="float" />
		<Field Name="WavePhase" Type="float" />
		<Field Name="TimePerFlipFrame" Type="float" />
		<Field Name="VariancePerFlipFrame" Type="float" />
		<Field Name="TextureCoordScaleU" Type="float" ArraySize="3" />
		<Field Name="TextureCoordScaleV" Type="float" ArraySize="3" />
		<Field Name="TextureRepeatLengthU" Type="float" ArraySize="3" />
		<Field Name="TextureRepeatLengthV" Type="float" ArraySize="3" />
		<Field Name="TextureParticleFileDataId" Type="int" />
		<Field Name="StartWidth" Type="float" />
		<Field Name="EndWidth" Type="float" />
		<Field Name="ParticleScaleMultiplier" Type="float" />
		<Field Name="ParticleEmissionRateMultiplier" Type="float" />
		<Field Name="SegDelay" Type="ushort" />
		<Field Name="JointCount" Type="ushort" />
		<Field Name="SpellChainEffectId" Type="ushort" ArraySize="11" />
		<Field Name="WidthScaleCurveId" Type="ushort" />
		<Field Name="JointsPerMinorJoint" Type="byte" />
		<Field Name="MinorJointsPerMajorJoint" Type="byte" />
		<Field Name="Alpha" Type="byte" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
		<Field Name="BlendMode" Type="byte" />
		<Field Name="RenderLayer" Type="byte" />
		<Field Name="NumFlipFramesU" Type="byte" />
		<Field Name="NumFlipFramesV" Type="byte" />
		<Field Name="SoundKitId" Type="int" />
		<Field Name="TextureFileDataId" Type="int" ArraySize="3" />
	</Table>
	<Table Name="SpellClassOptions" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="SpellClassMask" Type="uint" ArraySize="4" />
		<Field Name="SpellClassSet" Type="byte" />
		<Field Name="ModalNextSpell" Type="int" />
	</Table>
	<Table Name="SpellCooldowns" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CategoryRecoveryTime" Type="int" />
		<Field Name="RecoveryTime" Type="int" />
		<Field Name="StartRecoveryTime" Type="int" />
		<Field Name="DifficultyId" Type="byte" />
	</Table>
	<Table Name="SpellDescriptionVariables" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Variables" Type="string" />
	</Table>
	<Table Name="SpellDispelType" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="InternalName" Type="string" />
		<Field Name="Mask" Type="byte" />
		<Field Name="ImmunityPossible" Type="byte" />
	</Table>
	<Table Name="SpellDuration" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Duration" Type="uint" />
		<Field Name="MaxDuration" Type="uint" />
		<Field Name="DurationPerLevel" Type="uint" />
	</Table>
	<Table Name="SpellEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Effect" Type="int" />
		<Field Name="EffectBasePoints" Type="int" />
		<Field Name="EffectIndex" Type="int" />
		<Field Name="EffectAura" Type="int" />
		<Field Name="DifficultyId" Type="int" />
		<Field Name="EffectAmplitude" Type="float" />
		<Field Name="EffectAuraPeriod" Type="int" />
		<Field Name="EffectBonusCoefficient" Type="float" />
		<Field Name="EffectChainAmplitude" Type="float" />
		<Field Name="EffectChainTargets" Type="int" />
		<Field Name="EffectDieSides" Type="int" />
		<Field Name="EffectItemType" Type="uint" />
		<Field Name="EffectMechanic" Type="int" />
		<Field Name="EffectPointsPerResource" Type="float" />
		<Field Name="EffectRealPointsPerLevel" Type="float" />
		<Field Name="EffectTriggerSpell" Type="int" />
		<Field Name="EffectPosFacing" Type="float" />
		<Field Name="EffectAttributes" Type="int" />
		<Field Name="BonusCoefficientFromAP" Type="float" />
		<Field Name="PvpMultiplier" Type="float" />
		<Field Name="Coefficient" Type="float" />
		<Field Name="Variance" Type="float" />
		<Field Name="ResourceCoefficient" Type="float" />
		<Field Name="GroupSizeBasePointsCoefficient" Type="float" />
		<Field Name="EffectSpellClassMask" Type="int" ArraySize="4" />
		<Field Name="EffectMiscValue" Type="int" ArraySize="2" />
		<Field Name="EffectRadiusIndex" Type="int" ArraySize="2" />
		<Field Name="ImplicitTarget" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SpellEffectAutoDescription" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EffectDescription" Type="string" />
		<Field Name="AuraDescription" Type="string" />
		<Field Name="SpellEffectType" Type="int" />
		<Field Name="AuraEffectType" Type="int" />
		<Field Name="EffectOrderIndex" Type="int" />
		<Field Name="AuraOrderIndex" Type="int" />
		<Field Name="PointsSign" Type="byte" />
		<Field Name="TargetType" Type="byte" />
		<Field Name="SchoolMask" Type="byte" />
	</Table>
	<Table Name="SpellEffectEmission" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EmissionRate" Type="float" />
		<Field Name="ModelScale" Type="float" />
		<Field Name="AreaModelId" Type="ushort" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellEquippedItems" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="EquippedItemInvTypes" Type="int" />
		<Field Name="EquippedItemSubclass" Type="int" />
		<Field Name="EquippedItemClass" Type="byte" />
	</Table>
	<Table Name="SpellFlyout" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="ulong" />
		<Field Name="Name" Type="string" />
		<Field Name="Description" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="SpellIconFileId" Type="int" />
	</Table>
	<Table Name="SpellFlyoutItem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="Slot" Type="byte" />
	</Table>
	<Table Name="SpellFocusObject" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SpellInterrupts" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="InterruptFlags" Type="ushort" />
		<Field Name="AuraInterruptFlags" Type="int" ArraySize="2" />
		<Field Name="ChannelInterruptFlags" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SpellItemEnchantment" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="HordeName" Type="string" />
		<Field Name="EffectArg" Type="int" ArraySize="3" />
		<Field Name="EffectScalingPoints" Type="float" ArraySize="3" />
		<Field Name="TransmogCost" Type="int" />
		<Field Name="IconFileDataId" Type="int" />
		<Field Name="EffectPointsMin" Type="ushort" ArraySize="3" />
		<Field Name="ItemVisual" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="RequiredSkillId" Type="ushort" />
		<Field Name="RequiredSkillRank" Type="ushort" />
		<Field Name="ItemLevel" Type="ushort" />
		<Field Name="Charges" Type="byte" />
		<Field Name="Effect" Type="byte" ArraySize="3" />
		<Field Name="ConditionId" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
		<Field Name="ScalingClass" Type="byte" />
		<Field Name="ScalingClassRestricted" Type="byte" />
		<Field Name="TransmogPlayerConditionId" Type="int" />
	</Table>
	<Table Name="SpellItemEnchantmentCondition" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LtOperand" Type="int" ArraySize="5" />
		<Field Name="LtOperandType" Type="byte" ArraySize="5" />
		<Field Name="Operator" Type="byte" ArraySize="5" />
		<Field Name="RtOperandType" Type="byte" ArraySize="5" />
		<Field Name="RtOperand" Type="byte" ArraySize="5" />
		<Field Name="Logic" Type="byte" ArraySize="5" />
	</Table>
	<Table Name="SpellKeyboundOverride" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Function" Type="string" />
		<Field Name="Data" Type="int" />
		<Field Name="Type" Type="byte" />
	</Table>
	<Table Name="SpellLabel" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LabelId" Type="int" />
	</Table>
	<Table Name="SpellLearnSpell" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="LearnSpellId" Type="int" />
		<Field Name="OverridesSpellId" Type="int" />
	</Table>
	<Table Name="SpellLevels" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseLevel" Type="ushort" />
		<Field Name="MaxLevel" Type="ushort" />
		<Field Name="SpellLevel" Type="ushort" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="MaxPassiveAuraLevel" Type="byte" />
	</Table>
	<Table Name="SpellMechanic" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StateName" Type="string" />
	</Table>
	<Table Name="SpellMisc" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CastingTimeIndex" Type="ushort" />
		<Field Name="DurationIndex" Type="ushort" />
		<Field Name="RangeIndex" Type="ushort" />
		<Field Name="SchoolMask" Type="byte" />
		<Field Name="SpellIconFileDataId" Type="int" />
		<Field Name="Speed" Type="float" />
		<Field Name="ActiveIconFileDataId" Type="int" />
		<Field Name="LaunchDelay" Type="float" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="Attributes" Type="int" ArraySize="14" />
	</Table>
	<Table Name="SpellMissile" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="DefaultPitchMin" Type="float" />
		<Field Name="DefaultPitchMax" Type="float" />
		<Field Name="DefaultSpeedMin" Type="float" />
		<Field Name="DefaultSpeedMax" Type="float" />
		<Field Name="RandomizeFacingMin" Type="float" />
		<Field Name="RandomizeFacingMax" Type="float" />
		<Field Name="RandomizePitchMin" Type="float" />
		<Field Name="RandomizePitchMax" Type="float" />
		<Field Name="RandomizeSpeedMin" Type="float" />
		<Field Name="RandomizeSpeedMax" Type="float" />
		<Field Name="Gravity" Type="float" />
		<Field Name="MaxDuration" Type="float" />
		<Field Name="CollisionRadius" Type="float" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellMissileMotion" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="ScriptBody" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MissileCount" Type="byte" />
	</Table>
	<Table Name="SpellPower" Build="26231">
		<Field Name="ManaCost" Type="uint" />
		<Field Name="PowerCostPct" Type="float" />
		<Field Name="PowerPctPerSecond" Type="float" />
		<Field Name="RequiredAuraSpellId" Type="int" />
		<Field Name="PowerCostMaxPct" Type="float" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PowerType" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ManaCostPerLevel" Type="uint" />
		<Field Name="ManaPerSecond" Type="int" />
		<Field Name="OptionalCost" Type="int" />
		<Field Name="PowerDisplayId" Type="int" />
		<Field Name="AltPowerBarId" Type="int" />
	</Table>
	<Table Name="SpellPowerDifficulty" Build="26231">
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="SpellProceduralEffect" Build="26231">
		<Field Name="Value" Type="float" ArraySize="4" />
		<Field Name="Type" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="SpellProcsPerMinute" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseProcRate" Type="float" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellProcsPerMinuteMod" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Coeff" Type="float" />
		<Field Name="Param" Type="ushort" />
		<Field Name="Type" Type="byte" />
	</Table>
	<Table Name="SpellRadius" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Radius" Type="float" />
		<Field Name="RadiusPerLevel" Type="float" />
		<Field Name="RadiusMin" Type="float" />
		<Field Name="RadiusMax" Type="float" />
	</Table>
	<Table Name="SpellRange" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayName" Type="string" />
		<Field Name="DisplayNameShort" Type="string" />
		<Field Name="RangeMin" Type="float" ArraySize="2" />
		<Field Name="RangeMax" Type="float" ArraySize="2" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellReagents" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="Reagent" Type="int" ArraySize="8" />
		<Field Name="ReagentCount" Type="ushort" ArraySize="8" />
	</Table>
	<Table Name="SpellReagentsCurrency" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="CurrencyTypesId" Type="ushort" />
		<Field Name="CurrencyCount" Type="ushort" />
	</Table>
	<Table Name="SpellScaling" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="ScalesFromItemLevel" Type="ushort" />
		<Field Name="Class" Type="int" />
		<Field Name="MinScalingLevel" Type="int" />
		<Field Name="MaxScalingLevel" Type="int" />
	</Table>
	<Table Name="SpellShapeshift" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="ShapeshiftExclude" Type="uint" ArraySize="2" />
		<Field Name="ShapeshiftMask" Type="uint" ArraySize="2" />
		<Field Name="StanceBarOrder" Type="byte" />
	</Table>
	<Table Name="SpellShapeshiftForm" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="DamageVariance" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="CombatRoundTime" Type="ushort" />
		<Field Name="MountTypeId" Type="ushort" />
		<Field Name="CreatureType" Type="byte" />
		<Field Name="BonusActionBar" Type="byte" />
		<Field Name="AttackIconFileId" Type="int" />
		<Field Name="CreatureDisplayId" Type="int" ArraySize="4" />
		<Field Name="PresetSpellId" Type="int" ArraySize="8" />
	</Table>
	<Table Name="SpellSpecialUnitEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellVisualEffectNameId" Type="ushort" />
		<Field Name="PositionerId" Type="int" />
	</Table>
	<Table Name="SpellTargetRestrictions" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ConeDegrees" Type="float" />
		<Field Name="Width" Type="float" />
		<Field Name="Targets" Type="int" />
		<Field Name="TargetCreatureType" Type="ushort" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="MaxTargets" Type="byte" />
		<Field Name="MaxTargetLevel" Type="int" />
	</Table>
	<Table Name="SpellTotems" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="Totem" Type="int" ArraySize="2" />
		<Field Name="RequiredTotemCategoryId" Type="ushort" ArraySize="2" />
	</Table>
	<Table Name="SpellVisual" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MissileCastOffset" Type="float" ArraySize="3" />
		<Field Name="MissileImpactOffset" Type="float" ArraySize="3" />
		<Field Name="Flags" Type="int" />
		<Field Name="SpellVisualMissileSetId" Type="ushort" />
		<Field Name="MissileDestinationAttachment" Type="byte" />
		<Field Name="MissileAttachment" Type="byte" />
		<Field Name="MissileCastPositionerId" Type="int" />
		<Field Name="MissileImpactPositionerId" Type="int" />
		<Field Name="MissileTargetingKit" Type="int" />
		<Field Name="AnimEventSoundId" Type="int" />
		<Field Name="DamageNumberDelay" Type="ushort" />
		<Field Name="HostileSpellVisualId" Type="int" />
		<Field Name="CasterSpellVisualId" Type="int" />
		<Field Name="LowViolenceSpellVisualId" Type="int" />
	</Table>
	<Table Name="SpellVisualAnim" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="InitialAnimId" Type="ushort" />
		<Field Name="LoopAnimId" Type="ushort" />
		<Field Name="AnimKitId" Type="ushort" />
	</Table>
	<Table Name="SpellVisualColorEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Duration" Type="float" />
		<Field Name="Color" Type="uint" />
		<Field Name="ColorMultiplier" Type="float" />
		<Field Name="RedCurveId" Type="ushort" />
		<Field Name="GreenCurveId" Type="ushort" />
		<Field Name="BlueCurveId" Type="ushort" />
		<Field Name="AlphaCurveId" Type="ushort" />
		<Field Name="OpacityCurveId" Type="ushort" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="PositionerId" Type="int" />
	</Table>
	<Table Name="SpellVisualEffectName" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelFileDataId" Type="int" />
		<Field Name="EffectRadius" Type="float" />
		<Field Name="BaseMissileSpeed" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="MinAllowedScale" Type="float" />
		<Field Name="MaxAllowedScale" Type="float" />
		<Field Name="Alpha" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="Type" Type="int" />
		<Field Name="GenericId" Type="int" />
		<Field Name="TextureFileDataId" Type="int" />
		<Field Name="RibbonQualityId" Type="int" />
		<Field Name="DissolveEffectId" Type="int" />
		<Field Name="ModelPosition" Type="int" />
	</Table>
	<Table Name="SpellVisualEvent" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StartEvent" Type="int" />
		<Field Name="StartMinOffsetMs" Type="int" />
		<Field Name="StartMaxOffsetMs" Type="int" />
		<Field Name="EndEvent" Type="int" />
		<Field Name="EndMinOffsetMs" Type="int" />
		<Field Name="EndMaxOffsetMs" Type="int" />
		<Field Name="TargetType" Type="int" />
		<Field Name="SpellVisualKitId" Type="int" />
	</Table>
	<Table Name="SpellVisualKit" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="FallbackPriority" Type="float" />
		<Field Name="FallbackSpellVisualKitId" Type="int" />
		<Field Name="DelayMin" Type="ushort" />
		<Field Name="DelayMax" Type="ushort" />
	</Table>
	<Table Name="SpellVisualKitAreaModel" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelFileDataId" Type="int" />
		<Field Name="EmissionRate" Type="float" />
		<Field Name="Spacing" Type="float" />
		<Field Name="ModelScale" Type="float" />
		<Field Name="LifeTime" Type="ushort" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellVisualKitEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EffectType" Type="int" />
		<Field Name="Effect" Type="int" />
	</Table>
	<Table Name="SpellVisualKitModelAttach" Build="26231">
		<Field Name="Offset" Type="float" ArraySize="3" />
		<Field Name="OffsetVariation" Type="float" ArraySize="3" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellVisualEffectNameId" Type="ushort" />
		<Field Name="AttachmentId" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="PositionerId" Type="ushort" />
		<Field Name="Yaw" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Roll" Type="float" />
		<Field Name="YawVariation" Type="float" />
		<Field Name="PitchVariation" Type="float" />
		<Field Name="RollVariation" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="ScaleVariation" Type="float" />
		<Field Name="StartAnimId" Type="ushort" />
		<Field Name="AnimId" Type="ushort" />
		<Field Name="EndAnimId" Type="ushort" />
		<Field Name="AnimKitId" Type="ushort" />
		<Field Name="LowDefModelAttachId" Type="int" />
		<Field Name="StartDelay" Type="float" />
	</Table>
	<Table Name="SpellVisualMissile" Build="26231">
		<Field Name="FollowGroundHeight" Type="uint" />
		<Field Name="FollowGroundDropSpeed" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="CastOffset" Type="float" ArraySize="3" />
		<Field Name="ImpactOffset" Type="float" ArraySize="3" />
		<Field Name="SpellVisualEffectNameId" Type="ushort" />
		<Field Name="CastPositionerId" Type="ushort" />
		<Field Name="ImpactPositionerId" Type="ushort" />
		<Field Name="FollowGroundApproach" Type="ushort" />
		<Field Name="SpellMissileMotionId" Type="ushort" />
		<Field Name="Attachment" Type="byte" />
		<Field Name="DestinationAttachment" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundEntriesId" Type="int" />
		<Field Name="AnimKitId" Type="int" />
	</Table>
	<Table Name="SpellXDescriptionVariables" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellId" Type="int" />
		<Field Name="SpellDescriptionVariablesId" Type="int" />
	</Table>
	<Table Name="SpellXSpellVisual" Build="26231">
		<Field Name="SpellVisualId" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Probability" Type="float" />
		<Field Name="CasterPlayerConditionId" Type="ushort" />
		<Field Name="CasterUnitConditionId" Type="ushort" />
		<Field Name="ViewerPlayerConditionId" Type="ushort" />
		<Field Name="ViewerUnitConditionId" Type="ushort" />
		<Field Name="SpellIconFileId" Type="int" />
		<Field Name="ActiveIconFileId" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="DifficultyId" Type="byte" />
		<Field Name="Priority" Type="byte" />
	</Table>
	<Table Name="StartupFiles" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="Locale" Type="int" />
		<Field Name="BytesRequired" Type="int" />
	</Table>
	<Table Name="Startup_Strings" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Message" Type="string" />
	</Table>
	<Table Name="Stationery" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ItemId" Type="int" />
		<Field Name="TextureFileDataId" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SummonProperties" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="uint" />
		<Field Name="Control" Type="int" />
		<Field Name="Faction" Type="int" />
		<Field Name="Title" Type="int" />
		<Field Name="Slot" Type="int" />
	</Table>
	<Table Name="TactKey" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Key" Type="byte" ArraySize="16" />
	</Table>
	<Table Name="TactKeyLookup" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TACTId" Type="byte" ArraySize="8" />
	</Table>
	<Table Name="Talent" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
		<Field Name="SpellId" Type="int" />
		<Field Name="OverridesSpellId" Type="int" />
		<Field Name="SpecId" Type="ushort" />
		<Field Name="TierId" Type="byte" />
		<Field Name="ColumnIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CategoryMask" Type="byte" ArraySize="2" />
		<Field Name="ClassId" Type="byte" />
	</Table>
	<Table Name="TaxiNodes" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="MountCreatureId" Type="int" ArraySize="2" />
		<Field Name="MapOffset" Type="float" ArraySize="2" />
		<Field Name="Facing" Type="float" />
		<Field Name="FlightMapOffset" Type="float" ArraySize="2" />
		<Field Name="ContinentId" Type="ushort" />
		<Field Name="ConditionId" Type="ushort" />
		<Field Name="CharacterBitNumber" Type="ushort" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UiTextureKitId" Type="int" />
		<Field Name="SpecialIconConditionId" Type="int" />
	</Table>
	<Table Name="TaxiPath" Build="26231">
		<Field Name="FromTaxiNode" Type="ushort" />
		<Field Name="ToTaxiNode" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Cost" Type="int" />
	</Table>
	<Table Name="TaxiPathNode" Build="26231">
		<Field Name="Loc" Type="float" ArraySize="3" />
		<Field Name="PathId" Type="ushort" />
		<Field Name="ContinentId" Type="ushort" />
		<Field Name="NodeIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Delay" Type="int" />
		<Field Name="ArrivalEventId" Type="ushort" />
		<Field Name="DepartureEventId" Type="ushort" />
	</Table>
	<Table Name="TerrainMaterial" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Shader" Type="byte" />
		<Field Name="EnvMapDiffuseFileId" Type="int" />
		<Field Name="EnvMapSpecularFileId" Type="int" />
	</Table>
	<Table Name="TerrainType" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TerrainDesc" Type="string" />
		<Field Name="FootstepSprayRun" Type="ushort" />
		<Field Name="FootstepSprayWalk" Type="ushort" />
		<Field Name="SoundId" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="TerrainTypeSounds" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="TextureBlendSet" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TextureFileDataId" Type="int" ArraySize="3" />
		<Field Name="TextureScrollRateU" Type="float" ArraySize="3" />
		<Field Name="TextureScrollRateV" Type="float" ArraySize="3" />
		<Field Name="TextureScaleU" Type="float" ArraySize="3" />
		<Field Name="TextureScaleV" Type="float" ArraySize="3" />
		<Field Name="ModX" Type="float" ArraySize="4" />
		<Field Name="SwizzleRed" Type="byte" />
		<Field Name="SwizzleGreen" Type="byte" />
		<Field Name="SwizzleBlue" Type="byte" />
		<Field Name="SwizzleAlpha" Type="byte" />
	</Table>
	<Table Name="TextureFileData" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaterialResourcesId" Type="int" />
		<Field Name="UsageType" Type="byte" />
	</Table>
	<Table Name="TotemCategory" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="TotemCategoryMask" Type="uint" />
		<Field Name="TotemCategoryType" Type="byte" />
	</Table>
	<Table Name="Toy" Build="26231">
		<Field Name="SourceText" Type="string" />
		<Field Name="ItemId" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="TradeSkillCategory" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="HordeName" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SkillLineId" Type="ushort" />
		<Field Name="ParentTradeSkillCategoryId" Type="ushort" />
		<Field Name="OrderIndex" Type="ushort" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="TradeSkillItem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevel" Type="ushort" />
		<Field Name="RequiredLevel" Type="byte" />
	</Table>
	<Table Name="TransformMatrix" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="Yaw" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Roll" Type="float" />
		<Field Name="Scale" Type="float" />
	</Table>
	<Table Name="TransmogHoliday" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RequiredTransmogHoliday" Type="int" />
	</Table>
	<Table Name="TransmogSet" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="ParentTransmogSetId" Type="ushort" />
		<Field Name="UiOrder" Type="ushort" />
		<Field Name="ExpansionId" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="TrackingQuestId" Type="int" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="ItemNameDescriptionId" Type="int" />
		<Field Name="TransmogSetGroupId" Type="int" />
	</Table>
	<Table Name="TransmogSetGroup" Build="26231">
		<Field Name="Name" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="TransmogSetItem" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TransmogSetId" Type="int" />
		<Field Name="ItemModifiedAppearanceId" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="TransportAnimation" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TimeIndex" Type="int" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="SequenceId" Type="byte" />
	</Table>
	<Table Name="TransportPhysics" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WaveAmp" Type="float" />
		<Field Name="WaveTimeScale" Type="float" />
		<Field Name="RollAmp" Type="float" />
		<Field Name="RollTimeScale" Type="float" />
		<Field Name="PitchAmp" Type="float" />
		<Field Name="PitchTimeScale" Type="float" />
		<Field Name="MaxBank" Type="float" />
		<Field Name="MaxBankTurnSpeed" Type="float" />
		<Field Name="SpeedDampThresh" Type="float" />
		<Field Name="SpeedDamp" Type="float" />
	</Table>
	<Table Name="TransportRotation" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TimeIndex" Type="int" />
		<Field Name="Rot" Type="float" ArraySize="4" />
	</Table>
	<Table Name="Trophy" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="GameObjectDisplayInfoId" Type="ushort" />
		<Field Name="TrophyTypeId" Type="byte" />
		<Field Name="PlayerConditionId" Type="int" />
	</Table>
	<Table Name="UiCamera" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="LookAt" Type="float" ArraySize="3" />
		<Field Name="Up" Type="float" ArraySize="3" />
		<Field Name="AnimFrame" Type="ushort" />
		<Field Name="UiCameraTypeId" Type="byte" />
		<Field Name="AnimVariation" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="AnimId" Type="int" />
	</Table>
	<Table Name="UiCameraType" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Width" Type="int" />
		<Field Name="Height" Type="int" />
	</Table>
	<Table Name="UiCamFbackTransmogChrRace" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiCameraId" Type="ushort" />
		<Field Name="ChrRaceId" Type="byte" />
		<Field Name="Gender" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="Variation" Type="byte" />
	</Table>
	<Table Name="UiCamFbackTransmogWeapon" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiCameraId" Type="ushort" />
		<Field Name="ItemClass" Type="byte" />
		<Field Name="ItemSubclass" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
	</Table>
	<Table Name="UIExpansionDisplayInfo" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ExpansionLogo" Type="int" />
		<Field Name="ExpansionBanner" Type="int" />
		<Field Name="ExpansionLevel" Type="int" />
	</Table>
	<Table Name="UIExpansionDisplayInfoIcon" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FeatureDescription" Type="string" />
		<Field Name="ParentId" Type="int" />
		<Field Name="FeatureIcon" Type="int" />
	</Table>
	<Table Name="UiMapPOI" Build="26231">
		<Field Name="ContinentId" Type="int" />
		<Field Name="WorldLoc" Type="float" ArraySize="3" />
		<Field Name="UiTextureAtlasMemberId" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="PoiDataType" Type="ushort" />
		<Field Name="PoiData" Type="ushort" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="UiModelScene" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiSystemType" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="UiModelSceneActor" Build="26231">
		<Field Name="ScriptTag" Type="string" />
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="OrientationYaw" Type="float" />
		<Field Name="OrientationPitch" Type="float" />
		<Field Name="OrientationRoll" Type="float" />
		<Field Name="NormalizedScale" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiModelSceneActorDisplayId" Type="int" />
	</Table>
	<Table Name="UiModelSceneActorDisplay" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AnimSpeed" Type="float" />
		<Field Name="Alpha" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="AnimationId" Type="int" />
		<Field Name="SequenceVariation" Type="int" />
	</Table>
	<Table Name="UiModelSceneCamera" Build="26231">
		<Field Name="ScriptTag" Type="string" />
		<Field Name="Target" Type="float" ArraySize="3" />
		<Field Name="ZoomedTargetOffset" Type="float" ArraySize="3" />
		<Field Name="Yaw" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Roll" Type="float" />
		<Field Name="ZoomedYawOffset" Type="float" />
		<Field Name="ZoomedPitchOffset" Type="float" />
		<Field Name="ZoomedRollOffset" Type="float" />
		<Field Name="ZoomDistance" Type="float" />
		<Field Name="MinZoomDistance" Type="float" />
		<Field Name="MaxZoomDistance" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CameraType" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="UiTextureAtlas" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="AtlasHeight" Type="ushort" />
		<Field Name="AtlasWidth" Type="ushort" />
	</Table>
	<Table Name="UiTextureAtlasMember" Build="26231">
		<Field Name="CommittedName" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiTextureAtlasId" Type="ushort" />
		<Field Name="CommittedLeft" Type="ushort" />
		<Field Name="CommittedRight" Type="ushort" />
		<Field Name="CommittedTop" Type="ushort" />
		<Field Name="CommittedBottom" Type="ushort" />
		<Field Name="CommittedFlags" Type="byte" />
	</Table>
	<Table Name="UiTextureKit" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="KitPrefix" Type="string" />
	</Table>
	<Table Name="UnitBlood" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerCritBloodSpurtId" Type="int" />
		<Field Name="PlayerHitBloodSpurtId" Type="int" />
		<Field Name="DefaultBloodSpurtId" Type="int" />
		<Field Name="PlayerOmniCritBloodSpurtId" Type="int" />
		<Field Name="PlayerOmniHitBloodSpurtId" Type="int" />
		<Field Name="DefaultOmniBloodSpurtId" Type="int" />
	</Table>
	<Table Name="UnitBloodLevels" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Violencelevel" Type="byte" ArraySize="3" />
	</Table>
	<Table Name="UnitCondition" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="int" ArraySize="8" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Variable" Type="byte" ArraySize="8" />
		<Field Name="Op" Type="byte" ArraySize="8" />
	</Table>
	<Table Name="UnitPowerBar" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Cost" Type="string" />
		<Field Name="OutOfError" Type="string" />
		<Field Name="ToolTip" Type="string" />
		<Field Name="RegenerationPeace" Type="float" />
		<Field Name="RegenerationCombat" Type="float" />
		<Field Name="FileDataId" Type="int" ArraySize="6" />
		<Field Name="Color" Type="uint" ArraySize="6" />
		<Field Name="StartInset" Type="float" />
		<Field Name="EndInset" Type="float" />
		<Field Name="StartPower" Type="ushort" />
		<Field Name="Flags" Type="ushort" />
		<Field Name="CenterPower" Type="byte" />
		<Field Name="BarType" Type="byte" />
		<Field Name="MinPower" Type="int" />
		<Field Name="MaxPower" Type="int" />
	</Table>
	<Table Name="Vehicle" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="uint" />
		<Field Name="TurnSpeed" Type="float" />
		<Field Name="PitchSpeed" Type="float" />
		<Field Name="PitchMin" Type="float" />
		<Field Name="PitchMax" Type="float" />
		<Field Name="MouseLookOffsetPitch" Type="float" />
		<Field Name="CameraFadeDistScalarMin" Type="float" />
		<Field Name="CameraFadeDistScalarMax" Type="float" />
		<Field Name="CameraPitchOffset" Type="float" />
		<Field Name="FacingLimitRight" Type="float" />
		<Field Name="FacingLimitLeft" Type="float" />
		<Field Name="CameraYawOffset" Type="float" />
		<Field Name="SeatId" Type="ushort" ArraySize="8" />
		<Field Name="VehicleUIIndicatorId" Type="ushort" />
		<Field Name="PowerDisplayId" Type="ushort" ArraySize="3" />
		<Field Name="FlagsB" Type="byte" />
		<Field Name="UiLocomotionType" Type="byte" />
		<Field Name="MissileTargetingId" Type="int" />
	</Table>
	<Table Name="VehicleSeat" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="uint" />
		<Field Name="FlagsB" Type="uint" />
		<Field Name="FlagsC" Type="uint" />
		<Field Name="AttachmentOffset" Type="float" ArraySize="3" />
		<Field Name="EnterPreDelay" Type="float" />
		<Field Name="EnterSpeed" Type="float" />
		<Field Name="EnterGravity" Type="float" />
		<Field Name="EnterMinDuration" Type="float" />
		<Field Name="EnterMaxDuration" Type="float" />
		<Field Name="EnterMinArcHeight" Type="float" />
		<Field Name="EnterMaxArcHeight" Type="float" />
		<Field Name="ExitPreDelay" Type="float" />
		<Field Name="ExitSpeed" Type="float" />
		<Field Name="ExitGravity" Type="float" />
		<Field Name="ExitMinDuration" Type="float" />
		<Field Name="ExitMaxDuration" Type="float" />
		<Field Name="ExitMinArcHeight" Type="float" />
		<Field Name="ExitMaxArcHeight" Type="float" />
		<Field Name="PassengerYaw" Type="float" />
		<Field Name="PassengerPitch" Type="float" />
		<Field Name="PassengerRoll" Type="float" />
		<Field Name="VehicleEnterAnimDelay" Type="float" />
		<Field Name="VehicleExitAnimDelay" Type="float" />
		<Field Name="CameraEnteringDelay" Type="float" />
		<Field Name="CameraEnteringDuration" Type="float" />
		<Field Name="CameraExitingDelay" Type="float" />
		<Field Name="CameraExitingDuration" Type="float" />
		<Field Name="CameraOffset" Type="float" ArraySize="3" />
		<Field Name="CameraPosChaseRate" Type="float" />
		<Field Name="CameraFacingChaseRate" Type="float" />
		<Field Name="CameraEnteringZoom" Type="float" />
		<Field Name="CameraSeatZoomMin" Type="float" />
		<Field Name="CameraSeatZoomMax" Type="float" />
		<Field Name="UiSkinFileDataId" Type="int" />
		<Field Name="EnterAnimStart" Type="ushort" />
		<Field Name="EnterAnimLoop" Type="ushort" />
		<Field Name="RideAnimStart" Type="ushort" />
		<Field Name="RideAnimLoop" Type="ushort" />
		<Field Name="RideUpperAnimStart" Type="ushort" />
		<Field Name="RideUpperAnimLoop" Type="ushort" />
		<Field Name="ExitAnimStart" Type="ushort" />
		<Field Name="ExitAnimLoop" Type="ushort" />
		<Field Name="ExitAnimEnd" Type="ushort" />
		<Field Name="VehicleEnterAnim" Type="ushort" />
		<Field Name="VehicleExitAnim" Type="ushort" />
		<Field Name="VehicleRideAnimLoop" Type="ushort" />
		<Field Name="EnterAnimKitId" Type="ushort" />
		<Field Name="RideAnimKitId" Type="ushort" />
		<Field Name="ExitAnimKitId" Type="ushort" />
		<Field Name="VehicleEnterAnimKitId" Type="ushort" />
		<Field Name="VehicleRideAnimKitId" Type="ushort" />
		<Field Name="VehicleExitAnimKitId" Type="ushort" />
		<Field Name="CameraModeId" Type="ushort" />
		<Field Name="AttachmentId" Type="byte" />
		<Field Name="PassengerAttachmentId" Type="byte" />
		<Field Name="VehicleEnterAnimBone" Type="byte" />
		<Field Name="VehicleExitAnimBone" Type="byte" />
		<Field Name="VehicleRideAnimLoopBone" Type="byte" />
		<Field Name="VehicleAbilityDisplay" Type="byte" />
		<Field Name="EnterUISoundId" Type="int" />
		<Field Name="ExitUISoundId" Type="int" />
	</Table>
	<Table Name="VehicleUIIndicator" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BackgroundTextureFileId" Type="int" />
	</Table>
	<Table Name="VehicleUIIndSeat" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="XPos" Type="float" />
		<Field Name="YPos" Type="float" />
		<Field Name="VirtualSeatIndex" Type="byte" />
	</Table>
	<Table Name="Vignette" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="MaxHeight" Type="float" />
		<Field Name="MinHeight" Type="float" />
		<Field Name="QuestFeedbackEffectId" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="VisibleTrackingQuestId" Type="int" />
	</Table>
	<Table Name="VirtualAttachment" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="PositionerId" Type="ushort" />
	</Table>
	<Table Name="VirtualAttachmentCustomization" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="VirtualAttachmentId" Type="ushort" />
		<Field Name="PositionerId" Type="ushort" />
	</Table>
	<Table Name="VocalUISounds" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VocalUIEnum" Type="byte" />
		<Field Name="RaceId" Type="byte" />
		<Field Name="ClassId" Type="byte" />
		<Field Name="NormalSoundId" Type="int" ArraySize="2" />
	</Table>
	<Table Name="WbAccessControlList" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="URL" Type="string" />
		<Field Name="GrantFlags" Type="ushort" />
		<Field Name="RevokeFlags" Type="byte" />
		<Field Name="WowEditInternal" Type="byte" />
		<Field Name="RegionId" Type="byte" />
	</Table>
	<Table Name="WbCertWhitelist" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Domain" Type="string" />
		<Field Name="GrantAccess" Type="byte" />
		<Field Name="RevokeAccess" Type="byte" />
		<Field Name="WowEditInternal" Type="byte" />
	</Table>
	<Table Name="WeaponImpactSounds" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WeaponSubClassId" Type="byte" />
		<Field Name="ParrySoundType" Type="byte" />
		<Field Name="ImpactSource" Type="byte" />
		<Field Name="ImpactSoundId" Type="int" ArraySize="11" />
		<Field Name="CritImpactSoundId" Type="int" ArraySize="11" />
		<Field Name="PierceImpactSoundId" Type="int" ArraySize="11" />
		<Field Name="PierceCritImpactSoundId" Type="int" ArraySize="11" />
	</Table>
	<Table Name="WeaponSwingSounds2" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SwingType" Type="byte" />
		<Field Name="Crit" Type="byte" />
		<Field Name="SoundId" Type="int" />
	</Table>
	<Table Name="WeaponTrail" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="Yaw" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Roll" Type="float" />
		<Field Name="TextureFileDataId" Type="int" ArraySize="3" />
		<Field Name="TextureScrollRateU" Type="float" ArraySize="3" />
		<Field Name="TextureScrollRateV" Type="float" ArraySize="3" />
		<Field Name="TextureScaleU" Type="float" ArraySize="3" />
		<Field Name="TextureScaleV" Type="float" ArraySize="3" />
	</Table>
	<Table Name="WeaponTrailModelDef" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LowDefFileDataId" Type="int" />
		<Field Name="WeaponTrailId" Type="ushort" />
	</Table>
	<Table Name="WeaponTrailParam" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Duration" Type="float" />
		<Field Name="FadeOutTime" Type="float" />
		<Field Name="EdgeLifeSpan" Type="float" />
		<Field Name="InitialDelay" Type="float" />
		<Field Name="SmoothSampleAngle" Type="float" />
		<Field Name="Hand" Type="byte" />
		<Field Name="OverrideAttachTop" Type="byte" />
		<Field Name="OverrideAttachBot" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Weather" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Intensity" Type="float" ArraySize="2" />
		<Field Name="TransitionSkyBox" Type="float" />
		<Field Name="EffectColor" Type="float" ArraySize="3" />
		<Field Name="Scale" Type="float" />
		<Field Name="Volatility" Type="float" />
		<Field Name="TwinkleIntensity" Type="float" />
		<Field Name="FallModifier" Type="float" />
		<Field Name="RotationalSpeed" Type="float" />
		<Field Name="ParticulateFileDataId" Type="int" />
		<Field Name="SoundAmbienceId" Type="ushort" />
		<Field Name="Type" Type="byte" />
		<Field Name="EffectType" Type="byte" />
		<Field Name="WindSettingsId" Type="byte" />
		<Field Name="AmbienceId" Type="int" />
		<Field Name="EffectTextureFileDataId" Type="int" />
	</Table>
	<Table Name="WeatherXParticulate" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataId" Type="int" />
	</Table>
	<Table Name="WindSettings" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseMag" Type="float" />
		<Field Name="BaseDir" Type="float" ArraySize="3" />
		<Field Name="VarianceMagOver" Type="float" />
		<Field Name="VarianceMagUnder" Type="float" />
		<Field Name="VarianceDir" Type="float" ArraySize="3" />
		<Field Name="MaxStepMag" Type="float" />
		<Field Name="MaxStepDir" Type="float" ArraySize="3" />
		<Field Name="Frequency" Type="float" />
		<Field Name="Duration" Type="float" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="WMOAreaTable" Build="26231">
		<Field Name="AreaName" Type="string" />
		<Field Name="WmoGroupId" Type="uint" />
		<Field Name="WmoId" Type="ushort" />
		<Field Name="AmbienceId" Type="ushort" />
		<Field Name="ZoneMusic" Type="ushort" />
		<Field Name="IntroSound" Type="ushort" />
		<Field Name="AreaTableId" Type="ushort" />
		<Field Name="UwIntroSound" Type="ushort" />
		<Field Name="UwAmbience" Type="ushort" />
		<Field Name="NameSetId" Type="byte" />
		<Field Name="SoundProviderPref" Type="byte" />
		<Field Name="SoundProviderPrefUnderwater" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UwZoneMusic" Type="int" />
	</Table>
	<Table Name="WmoMinimapTexture" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataId" Type="int" />
		<Field Name="GroupNum" Type="ushort" />
		<Field Name="BlockX" Type="byte" />
		<Field Name="BlockY" Type="byte" />
	</Table>
	<Table Name="WorldBossLockout" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="TrackingQuestId" Type="ushort" />
	</Table>
	<Table Name="WorldChunkSounds" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="ChunkX" Type="byte" />
		<Field Name="ChunkY" Type="byte" />
		<Field Name="SubChunkX" Type="byte" />
		<Field Name="SubChunkY" Type="byte" />
		<Field Name="SoundOverrideId" Type="byte" />
	</Table>
	<Table Name="WorldEffect" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TargetAsset" Type="uint" />
		<Field Name="CombatConditionId" Type="ushort" />
		<Field Name="TargetType" Type="byte" />
		<Field Name="WhenToDisplay" Type="byte" />
		<Field Name="QuestFeedbackEffectId" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
	</Table>
	<Table Name="WorldElapsedTimer" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Type" Type="byte" />
	</Table>
	<Table Name="WorldMapArea" Build="26231">
		<Field Name="AreaName" Type="string" />
		<Field Name="LocLeft" Type="float" />
		<Field Name="LocRight" Type="float" />
		<Field Name="LocTop" Type="float" />
		<Field Name="LocBottom" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="AreaId" Type="ushort" />
		<Field Name="DisplayMapId" Type="ushort" />
		<Field Name="DefaultDungeonFloor" Type="ushort" />
		<Field Name="ParentWorldMapId" Type="ushort" />
		<Field Name="LevelRangeMin" Type="byte" />
		<Field Name="LevelRangeMax" Type="byte" />
		<Field Name="BountySetId" Type="byte" />
		<Field Name="BountyDisplayLocation" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VisibilityPlayerConditionId" Type="int" />
	</Table>
	<Table Name="WorldMapContinent" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ContinentOffset" Type="float" ArraySize="2" />
		<Field Name="Scale" Type="float" />
		<Field Name="TaxiMin" Type="float" ArraySize="2" />
		<Field Name="TaxiMax" Type="float" ArraySize="2" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="WorldMapId" Type="ushort" />
		<Field Name="LeftBoundary" Type="byte" />
		<Field Name="RightBoundary" Type="byte" />
		<Field Name="TopBoundary" Type="byte" />
		<Field Name="BottomBoundary" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="WorldMapOverlay" Build="26231">
		<Field Name="TextureName" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TextureWidth" Type="ushort" />
		<Field Name="TextureHeight" Type="ushort" />
		<Field Name="MapAreaId" Type="int" />
		<Field Name="OffsetX" Type="int" />
		<Field Name="OffsetY" Type="int" />
		<Field Name="HitRectTop" Type="int" />
		<Field Name="HitRectLeft" Type="int" />
		<Field Name="HitRectBottom" Type="int" />
		<Field Name="HitRectRight" Type="int" />
		<Field Name="PlayerConditionId" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="AreaId" Type="int" ArraySize="4" />
	</Table>
	<Table Name="WorldMapTransforms" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Region" Type="float" ArraySize="6" />
		<Field Name="RegionOffset" Type="float" ArraySize="2" />
		<Field Name="RegionScale" Type="float" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="AreaId" Type="ushort" />
		<Field Name="NewMapId" Type="ushort" />
		<Field Name="NewDungeonMapId" Type="ushort" />
		<Field Name="NewAreaId" Type="ushort" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="WorldSafeLocs" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="X" Type="float" />
		<Field Name="Y" Type="float" />
		<Field Name="Z" Type="float" />
		<Field Name="Rotation" Type="float" />
		<Field Name="MapId" Type="ushort" />
	</Table>
	<Table Name="WorldState" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="WorldStateExpression" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Expression" Type="string" />
	</Table>
	<Table Name="WorldStateUI" Build="26231">
		<Field Name="Icon" Type="string" />
		<Field Name="ExtendedUI" Type="string" />
		<Field Name="DynamicTooltip" Type="string" />
		<Field Name="String" Type="string" />
		<Field Name="Tooltip" Type="string" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="AreaId" Type="ushort" />
		<Field Name="PhaseId" Type="ushort" />
		<Field Name="PhaseGroupId" Type="ushort" />
		<Field Name="StateVariable" Type="ushort" />
		<Field Name="ExtendedUIStateVariable" Type="ushort" ArraySize="3" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DynamicIconFileId" Type="int" />
		<Field Name="DynamicFlashIconFileId" Type="int" />
	</Table>
	<Table Name="WorldStateZoneSounds" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WmoAreaId" Type="int" />
		<Field Name="WorldStateId" Type="ushort" />
		<Field Name="WorldStateValue" Type="ushort" />
		<Field Name="AreaId" Type="ushort" />
		<Field Name="ZoneIntroMusicId" Type="ushort" />
		<Field Name="ZoneMusicId" Type="ushort" />
		<Field Name="SoundAmbienceId" Type="ushort" />
		<Field Name="SoundProviderPreferencesId" Type="byte" />
	</Table>
	<Table Name="World_PVP_Area" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AreaId" Type="ushort" />
		<Field Name="NextTimeWorldstate" Type="ushort" />
		<Field Name="GameTimeWorldstate" Type="ushort" />
		<Field Name="BattlePopulateTime" Type="ushort" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
	</Table>
	<Table Name="ZoneIntroMusicTable" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="MinDelayMinutes" Type="ushort" />
		<Field Name="Priority" Type="byte" />
		<Field Name="SoundId" Type="int" />
	</Table>
	<Table Name="ZoneLight" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="MapId" Type="ushort" />
		<Field Name="LightId" Type="ushort" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ZoneLightPoint" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="2" />
		<Field Name="PointOrder" Type="byte" />
	</Table>
	<Table Name="ZoneMusic" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SetName" Type="string" />
		<Field Name="SilenceIntervalMin" Type="int" ArraySize="2" />
		<Field Name="SilenceIntervalMax" Type="int" ArraySize="2" />
		<Field Name="Sounds" Type="int" ArraySize="2" />
	</Table>
	<Table Name="ZoneStory" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayAchievementId" Type="int" />
		<Field Name="DisplayWorldMapAreaId" Type="int" />
		<Field Name="PlayerFactionGroupId" Type="byte" />
	</Table>
	<Table Name="Unknown" Build="26231">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Status" Type="string" />
	</Table>
</Definition>