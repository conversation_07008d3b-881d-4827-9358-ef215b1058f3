<?xml version="1.0"?>
<Definition xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <Table Name="Achievement" Build="24492">
    <Field Name="Title" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="Flags" Type="uint" />
    <Field Name="Reward" Type="string" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="Supercedes" Type="ushort" />
    <Field Name="Category" Type="ushort" />
    <Field Name="UIOrder" Type="ushort" />
    <Field Name="SharesCriteria" Type="ushort" />
    <Field Name="Faction" Type="byte" />
    <Field Name="Points" Type="byte" />
    <Field Name="MinimumCriteria" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="IconFileDataID" Type="int" />
    <Field Name="CriteriaTree" Type="ushort" />
  </Table>
  <Table Name="Achievement_Category" Build="24492">
    <Field Name="Field0" Type="string" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AdventureJournal" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="string" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" ArraySize="2" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" />
    <Field Name="Field34" Type="byte" ArraySize="2" />
    <Field Name="Field36" Type="byte" />
    <Field Name="Field37" Type="byte" />
  </Table>
  <Table Name="AdventureMapPOI" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="ushort" />
    <Field Name="Field1B" Type="ushort" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="ushort" />
    <Field Name="Field23" Type="ushort" />
    <Field Name="Field25" Type="ushort" />
  </Table>
  <Table Name="AnimationData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="AnimKit" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="OneShotDuration" Type="uint" />
    <Field Name="OneShotStopAnimKitID" Type="ushort" />
    <Field Name="LowDefAnimKitID" Type="ushort" />
  </Table>
  <Table Name="AnimKitBoneSet" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="AnimKitBoneSetAlias" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
  </Table>
  <Table Name="AnimKitConfig" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
  </Table>
  <Table Name="AnimKitConfigBoneSet" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="AnimKitPriority" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
  </Table>
  <Table Name="AnimKitReplacement" Build="24492">
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AnimKitSegment" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="byte" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="ushort" />
  </Table>
  <Table Name="AnimReplacement" Build="24492">
    <Field Name="Field0" Type="ushort" />
    <Field Name="Field2" Type="ushort" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AnimReplacementSet" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
  </Table>
  <Table Name="AreaAssignment" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="AreaFarClipOverride" Build="24492">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="int" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AreaGroupMember" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AreaGroupID" Type="ushort" />
    <Field Name="AreaID" Type="ushort" />
  </Table>
  <Table Name="AreaPOI" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="int" ArraySize="3" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="uint" />
    <Field Name="Field20" Type="uint" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field30" Type="ushort" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="int" />
  </Table>
  <Table Name="AreaPOIState" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="AreaTable" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" ArraySize="2" />
    <Field Name="ZoneName" Type="string" />
    <Field Name="AmbientMultiplier" Type="float" />
    <Field Name="AreaName" Type="string" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="ParentAreaID" Type="ushort" />
    <Field Name="AreaBit" Type="ushort" />
    <Field Name="AmbienceID" Type="ushort" />
    <Field Name="ZoneMusic" Type="ushort" />
    <Field Name="IntroSound" Type="ushort" />
    <Field Name="LiquidTypeID" Type="ushort" ArraySize="4" />
    <Field Name="UWZoneMusic" Type="ushort" />
    <Field Name="UWAmbience" Type="ushort" />
    <Field Name="PvPCombatWorldStateID" Type="ushort" />
    <Field Name="SoundProviderPref" Type="byte" />
    <Field Name="SoundProviderPrefUnderwater" Type="byte" />
    <Field Name="ExplorationLevel" Type="byte" />
    <Field Name="FactionGroupMask" Type="byte" />
    <Field Name="MountFlags" Type="byte" />
    <Field Name="WildBattlePetLevelMin" Type="byte" />
    <Field Name="WildBattlePetLevelMax" Type="byte" />
    <Field Name="WindSettingsID" Type="byte" />
    <Field Name="UWIntroSound" Type="uint" />
  </Table>
  <Table Name="AreaTrigger" Build="24492">
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="Radius" Type="float" />
    <Field Name="BoxLength" Type="float" />
    <Field Name="BoxWidth" Type="float" />
    <Field Name="BoxHeight" Type="float" />
    <Field Name="BoxYaw" Type="float" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="PhaseID" Type="ushort" />
    <Field Name="PhaseGroupID" Type="ushort" />
    <Field Name="ShapeID" Type="ushort" />
    <Field Name="AreaTriggerActionSetID" Type="ushort" />
    <Field Name="PhaseUseFlags" Type="byte" />
    <Field Name="ShapeType" Type="byte" />
    <Field Name="Flag" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AreaTriggerActionSet" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
  </Table>
  <Table Name="AreaTriggerBox" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="float" ArraySize="2" />
  </Table>
  <Table Name="AreaTriggerCylinder" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
  </Table>
  <Table Name="AreaTriggerSphere" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="float" />
  </Table>
  <Table Name="ArmorLocation" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Modifier" Type="float" ArraySize="5" />
  </Table>
  <Table Name="Artifact" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="BarConnectedColor" Type="uint" />
    <Field Name="BarDisconnectedColor" Type="uint" />
    <Field Name="TitleColor" Type="uint" />
    <Field Name="ClassUiTextureKitID" Type="ushort" />
    <Field Name="SpecID" Type="ushort" />
    <Field Name="ArtifactCategoryID" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="UiModelSceneID" Type="uint" />
    <Field Name="SpellVisualKitID" Type="uint" />
  </Table>
  <Table Name="ArtifactAppearance" Build="24492">
    <Field Name="Name" Type="string" />
    <Field Name="SwatchColor" Type="uint" />
    <Field Name="ModelDesaturation" Type="float" />
    <Field Name="ModelAlpha" Type="float" />
    <Field Name="ShapeshiftDisplayID" Type="uint" />
    <Field Name="ArtifactAppearanceSetID" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="DisplayIndex" Type="byte" />
    <Field Name="AppearanceModID" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ModifiesShapeshiftFormDisplay" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PlayerConditionID" Type="ushort" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" ArraySize="3" />
  </Table>
  <Table Name="ArtifactAppearanceSet" Build="24492">
    <Field Name="Name" Type="string" />
    <Field Name="Name2" Type="string" />
    <Field Name="UiCameraID" Type="ushort" />
    <Field Name="AltHandUICameraID" Type="ushort" />
    <Field Name="ArtifactID" Type="byte" />
    <Field Name="DisplayIndex" Type="byte" />
    <Field Name="AttachmentPoint" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ArtifactCategory" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ArtifactKnowledgeCurrencyID" Type="ushort" />
    <Field Name="ArtifactKnowledgeMultiplierCurveID" Type="ushort" />
  </Table>
  <Table Name="ArtifactPower" Build="24492">
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="ArtifactID" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="MaxRank" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field0E" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="ArtifactPowerLink" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FromArtifactPowerID" Type="ushort" />
    <Field Name="ToArtifactPowerID" Type="ushort" />
  </Table>
  <Table Name="ArtifactPowerPicker" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
  </Table>
  <Table Name="ArtifactPowerRank" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="Value" Type="float" />
    <Field Name="ArtifactPowerID" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Rank" Type="byte" />
  </Table>
  <Table Name="ArtifactQuestXP" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Exp" Type="uint" ArraySize="10" />
  </Table>
  <Table Name="ArtifactTier" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="ArtifactUnlock" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="ushort" />
  </Table>
  <Table Name="AuctionHouse" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="FactionID" Type="ushort" />
    <Field Name="DepositRate" Type="byte" />
    <Field Name="ConsignmentRate" Type="byte" />
  </Table>
  <Table Name="BankBagSlotPrices" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Cost" Type="uint" />
  </Table>
  <Table Name="BannedAddOns" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Version" Type="string" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="BarberShopStyle" Build="24492">
    <Field Name="DisplayName" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="CostModifier" Type="float" />
    <Field Name="Type" Type="byte" />
    <Field Name="Race" Type="byte" />
    <Field Name="Sex" Type="byte" />
    <Field Name="Data" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="BattlemasterList" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="GameType" Type="string" />
    <Field Name="ShortDescription" Type="string" />
    <Field Name="LongDescription" Type="string" />
    <Field Name="MapID" Type="ushort" ArraySize="16" />
    <Field Name="HolidayWorldState" Type="ushort" />
    <Field Name="PlayerConditionID" Type="ushort" />
    <Field Name="InstanceType" Type="byte" />
    <Field Name="GroupsAllowed" Type="byte" />
    <Field Name="MaxGroupSize" Type="byte" />
    <Field Name="MinLevel" Type="byte" />
    <Field Name="MaxLevel" Type="byte" />
    <Field Name="RatedPlayers" Type="byte" />
    <Field Name="MinPlayers" Type="byte" />
    <Field Name="MaxPlayers" Type="byte" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="BattlePetAbility" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
  </Table>
  <Table Name="BattlePetAbilityEffect" Build="24492">
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" ArraySize="6" />
    <Field Name="Field14" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="BattlePetAbilityState" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="BattlePetAbilityTurn" Build="24492">
    <Field Name="Field0" Type="ushort" />
    <Field Name="Field2" Type="ushort" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="BattlePetBreedQuality" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Modifier" Type="float" />
    <Field Name="Quality" Type="byte" />
  </Table>
  <Table Name="BattlePetBreedState" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Value" Type="ushort" />
    <Field Name="BreedID" Type="byte" />
    <Field Name="State" Type="byte" />
  </Table>
  <Table Name="BattlePetEffectProperties" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" ArraySize="6" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="BattlePetNPCTeamMember" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
  </Table>
  <Table Name="BattlePetSpecies" Build="24492">
    <Field Name="CreatureID" Type="uint" />
    <Field Name="IconFileID" Type="uint" />
    <Field Name="SummonSpellID" Type="uint" />
    <Field Name="SourceText" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="PetType" Type="byte" />
    <Field Name="Source" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="BattlePetSpeciesState" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Value" Type="int" />
    <Field Name="SpeciesID" Type="ushort" />
    <Field Name="State" Type="byte" />
  </Table>
  <Table Name="BattlePetSpeciesXAbility" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="BattlePetState" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="BattlePetVisual" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
  </Table>
  <Table Name="BeamEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
  </Table>
  <Table Name="BoneWindModifierModel" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="uint" />
  </Table>
  <Table Name="BoneWindModifiers" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" />
  </Table>
  <Table Name="Bounty" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="BountySet" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="BroadcastText" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MaleText" Type="string" />
    <Field Name="FemaleText" Type="string" />
    <Field Name="EmoteID" Type="ushort" ArraySize="3" />
    <Field Name="EmoteDelay" Type="ushort" ArraySize="3" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Language" Type="byte" />
    <Field Name="Type" Type="byte" />
    <Field Name="SoundID" Type="uint" ArraySize="2" />
    <Field Name="PlayerConditionID" Type="uint" />
  </Table>
  <Table Name="CameraEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
  </Table>
  <Table Name="CameraEffectEntry" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
  </Table>
  <Table Name="CameraMode" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="int" ArraySize="3" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="byte" />
  </Table>
  <Table Name="CameraShakes" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="ushort" />
  </Table>
  <Table Name="CastableRaidBuffs" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="CelestialBody" Build="24492">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" ArraySize="2" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" ArraySize="2" />
    <Field Name="Field20" Type="float" ArraySize="2" />
    <Field Name="Field28" Type="float" ArraySize="2" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" ArraySize="2" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="float" ArraySize="3" />
    <Field Name="Field4C" Type="float" />
    <Field Name="Field50" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Cfg_Categories" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="Cfg_Configs" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="Cfg_Regions" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="CharacterFaceBoneSet" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="CharacterFacialHairStyles" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" ArraySize="5" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
  </Table>
  <Table Name="CharacterLoadout" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="CharacterLoadoutItem" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="CharBaseInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
  </Table>
  <Table Name="CharBaseSection" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="CharComponentTextureLayouts" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="CharComponentTextureSections" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="CharHairGeosets" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="CharSections" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TextureFileDataID" Type="uint" ArraySize="3" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="Race" Type="byte" />
    <Field Name="Gender" Type="byte" />
    <Field Name="GenType" Type="byte" />
    <Field Name="Type" Type="byte" />
    <Field Name="Color" Type="byte" />
  </Table>
  <Table Name="CharShipment" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
  </Table>
  <Table Name="CharShipmentContainer" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="byte" />
  </Table>
  <Table Name="CharStartOutfit" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="int" ArraySize="24" />
    <Field Name="PetDisplayID" Type="uint" />
    <Field Name="RaceID" Type="byte" />
    <Field Name="ClassID" Type="byte" />
    <Field Name="GenderID" Type="byte" />
    <Field Name="OutfitID" Type="byte" />
    <Field Name="PetFamilyID" Type="byte" />
  </Table>
  <Table Name="CharTitles" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="NameMale" Type="string" />
    <Field Name="NameFemale" Type="string" />
    <Field Name="MaskID" Type="ushort" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="ChatChannels" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" />
    <Field Name="Name" Type="string" />
    <Field Name="Shortcut" Type="string" />
    <Field Name="FactionGroup" Type="byte" />
  </Table>
  <Table Name="ChatProfanity" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="ChrClasses" Build="24492">
    <Field Name="PetNameToken" Type="string" />
    <Field Name="Name" Type="string" />
    <Field Name="NameFemale" Type="string" />
    <Field Name="NameMale" Type="string" />
    <Field Name="Filename" Type="string" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="uint" />
    <Field Name="Field1C" Type="uint" />
    <Field Name="Field20" Type="uint" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="CinematicSequenceID" Type="ushort" />
    <Field Name="DefaultSpec" Type="ushort" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ChrClassesXPowerTypes" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ClassID" Type="byte" />
    <Field Name="PowerType" Type="byte" />
  </Table>
  <Table Name="ChrClassRaceSex" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="ChrClassTitle" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="ChrClassUIDisplay" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" />
    <Field Name="Field7" Type="ushort" />
  </Table>
  <Table Name="ChrClassVillain" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="ChrRaces" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="uint" />
    <Field Name="Field008" Type="uint" />
    <Field Name="Field00C" Type="uint" />
    <Field Name="Field010" Type="string" />
    <Field Name="Field014" Type="string" />
    <Field Name="Field018" Type="string" />
    <Field Name="Field01C" Type="string" />
    <Field Name="Field020" Type="string" />
    <Field Name="Field024" Type="string" ArraySize="2" />
    <Field Name="Field02C" Type="string" />
    <Field Name="Field030" Type="uint" />
    <Field Name="Field034" Type="uint" />
    <Field Name="Field038" Type="float" ArraySize="3" />
    <Field Name="Field044" Type="float" ArraySize="3" />
    <Field Name="Field050" Type="uint" />
    <Field Name="Field054" Type="ushort" />
    <Field Name="Field056" Type="ushort" />
    <Field Name="Field058" Type="ushort" />
    <Field Name="Field05A" Type="ushort" />
    <Field Name="Field05C" Type="byte" />
    <Field Name="Field05D" Type="byte" />
    <Field Name="Field05E" Type="byte" />
    <Field Name="Field05F" Type="byte" />
    <Field Name="Field060" Type="byte" />
    <Field Name="Field061" Type="byte" />
    <Field Name="Field062" Type="byte" />
    <Field Name="Field063" Type="byte" />
    <Field Name="Field064" Type="byte" />
    <Field Name="Field065" Type="byte" />
    <Field Name="Field066" Type="ushort" />
    <Field Name="Field068" Type="ushort" />
    <Field Name="Field06A" Type="ushort" ArraySize="3" />
    <Field Name="Field070" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="ChrSpecialization" Build="24492">
    <Field Name="Field00" Type="uint" ArraySize="2" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field1B" Type="int" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="ChrUpgradeBucket" Build="24492">
    <Field Name="Field0" Type="ushort" />
    <Field Name="Field2" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ChrUpgradeBucketSpell" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="ChrUpgradeTier" Build="24492">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="CinematicCamera" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundID" Type="uint" />
    <Field Name="Origin" Type="float" ArraySize="3" />
    <Field Name="OriginFacing" Type="float" />
    <Field Name="ModelFileDataID" Type="uint" />
  </Table>
  <Table Name="CinematicSequences" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundID" Type="uint" />
    <Field Name="Camera" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="CloakDampening" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="5" />
    <Field Name="Field18" Type="float" ArraySize="5" />
    <Field Name="Field2C" Type="int" ArraySize="2" />
    <Field Name="Field34" Type="float" ArraySize="2" />
    <Field Name="Field3C" Type="int" />
    <Field Name="Field40" Type="float" />
    <Field Name="Field44" Type="float" />
  </Table>
  <Table Name="CombatCondition" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" ArraySize="2" />
    <Field Name="Field0E" Type="ushort" ArraySize="2" />
    <Field Name="Field12" Type="byte" ArraySize="2" />
    <Field Name="Field14" Type="byte" ArraySize="2" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" ArraySize="2" />
    <Field Name="Field19" Type="byte" ArraySize="2" />
    <Field Name="Field1B" Type="byte" />
  </Table>
  <Table Name="CommentatorStartLocation" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="ushort" />
  </Table>
  <Table Name="CommentatorTrackedCooldown" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="int" />
  </Table>
  <Table Name="ComponentModelFileData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
  </Table>
  <Table Name="ComponentTextureFileData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="Contribution" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="int" ArraySize="4" />
    <Field Name="Field20" Type="int" />
  </Table>
  <Table Name="ConversationLine" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="Creature" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" ArraySize="3" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" ArraySize="4" />
    <Field Name="Field24" Type="float" ArraySize="4" />
    <Field Name="Field34" Type="string" />
    <Field Name="Field38" Type="string" />
    <Field Name="Field3C" Type="string" />
    <Field Name="Field40" Type="string" />
    <Field Name="Field44" Type="byte" />
    <Field Name="Field45" Type="byte" />
    <Field Name="Field46" Type="byte" />
    <Field Name="Field47" Type="byte" />
  </Table>
  <Table Name="CreatureDifficulty" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="int" ArraySize="7" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
  </Table>
  <Table Name="CreatureDisplayInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="int" />
    <Field Name="Field12" Type="int" ArraySize="3" />
    <Field Name="Field1B" Type="int" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="ushort" />
    <Field Name="Field21" Type="float" />
    <Field Name="Field25" Type="int" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="int" />
    <Field Name="Field30" Type="ushort" />
    <Field Name="Field32" Type="ushort" />
    <Field Name="Field34" Type="byte" />
    <Field Name="Field35" Type="int" />
    <Field Name="Field39" Type="float" />
    <Field Name="Field3D" Type="int" />
  </Table>
  <Table Name="CreatureDisplayInfoCond" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="int" ArraySize="2" />
    <Field Name="Field10" Type="uint" ArraySize="2" />
    <Field Name="Field18" Type="uint" ArraySize="2" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="ushort" />
    <Field Name="Field2D" Type="int" ArraySize="2" />
  </Table>
  <Table Name="CreatureDisplayInfoEvt" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="CreatureDisplayInfoExtra" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FileDataID" Type="uint" />
    <Field Name="HDFileDataID" Type="uint" />
    <Field Name="DisplayRaceID" Type="byte" />
    <Field Name="DisplaySexID" Type="byte" />
    <Field Name="DisplayClassID" Type="byte" />
    <Field Name="SkinID" Type="byte" />
    <Field Name="FaceID" Type="byte" />
    <Field Name="HairStyleID" Type="byte" />
    <Field Name="HairColorID" Type="byte" />
    <Field Name="FacialHairID" Type="byte" />
    <Field Name="CustomDisplayOption" Type="byte" ArraySize="3" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="CreatureDisplayInfoTrn" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="float" />
  </Table>
  <Table Name="CreatureDispXUiCamera" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="CreatureFamily" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MinScale" Type="float" />
    <Field Name="MaxScale" Type="float" />
    <Field Name="Name" Type="string" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="SkillLine" Type="ushort" ArraySize="2" />
    <Field Name="PetFoodMask" Type="ushort" />
    <Field Name="MinScaleLevel" Type="byte" />
    <Field Name="MaxScaleLevel" Type="byte" />
    <Field Name="PetTalentType" Type="byte" />
  </Table>
  <Table Name="CreatureImmunities" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" ArraySize="2" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" ArraySize="8" />
    <Field Name="Field1A" Type="byte" ArraySize="14" />
  </Table>
  <Table Name="CreatureModelData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ModelScale" Type="float" />
    <Field Name="FootprintTextureLength" Type="float" />
    <Field Name="FootprintTextureWidth" Type="float" />
    <Field Name="FootprintParticleScale" Type="float" />
    <Field Name="CollisionWidth" Type="float" />
    <Field Name="CollisionHeight" Type="float" />
    <Field Name="MountHeight" Type="float" />
    <Field Name="GeoBox" Type="float" ArraySize="6" />
    <Field Name="WorldEffectScale" Type="float" />
    <Field Name="AttachedEffectScale" Type="float" />
    <Field Name="MissileCollisionRadius" Type="float" />
    <Field Name="MissileCollisionPush" Type="float" />
    <Field Name="MissileCollisionRaise" Type="float" />
    <Field Name="OverrideLootEffectScale" Type="float" />
    <Field Name="OverrideNameScale" Type="float" />
    <Field Name="OverrideSelectionRadius" Type="float" />
    <Field Name="TamedPetBaseScale" Type="float" />
    <Field Name="HoverHeight" Type="float" />
    <Field Name="Flags" Type="uint" />
    <Field Name="FileDataID" Type="uint" />
    <Field Name="SizeClass" Type="uint" />
    <Field Name="BloodID" Type="uint" />
    <Field Name="FootprintTextureID" Type="uint" />
    <Field Name="FoleyMaterialID" Type="uint" />
    <Field Name="FootstepEffectID" Type="uint" />
    <Field Name="DeathThudEffectID" Type="uint" />
    <Field Name="SoundID" Type="uint" />
    <Field Name="CreatureGeosetDataID" Type="uint" />
  </Table>
  <Table Name="CreatureMovementInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="float" />
  </Table>
  <Table Name="CreatureSoundData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="float" />
    <Field Name="Field008" Type="float" />
    <Field Name="Field00C" Type="byte" />
    <Field Name="Field00D" Type="int" />
    <Field Name="Field010" Type="int" />
    <Field Name="Field013" Type="int" />
    <Field Name="Field016" Type="int" />
    <Field Name="Field019" Type="byte" />
    <Field Name="Field01A" Type="int" />
    <Field Name="Field01D" Type="int" />
    <Field Name="Field020" Type="int" />
    <Field Name="Field023" Type="ushort" />
    <Field Name="Field025" Type="int" />
    <Field Name="Field028" Type="int" />
    <Field Name="Field02B" Type="int" />
    <Field Name="Field02E" Type="int" />
    <Field Name="Field031" Type="int" ArraySize="5" />
    <Field Name="Field040" Type="int" ArraySize="4" />
    <Field Name="Field04C" Type="byte" />
    <Field Name="Field04D" Type="int" />
    <Field Name="Field050" Type="int" />
    <Field Name="Field053" Type="int" />
    <Field Name="Field056" Type="ushort" />
    <Field Name="Field058" Type="ushort" />
    <Field Name="Field05A" Type="ushort" />
    <Field Name="Field05C" Type="int" />
    <Field Name="Field05F" Type="ushort" />
    <Field Name="Field061" Type="int" />
    <Field Name="Field064" Type="int" />
    <Field Name="Field067" Type="ushort" />
    <Field Name="Field069" Type="int" />
    <Field Name="Field06C" Type="int" />
    <Field Name="Field06F" Type="int" />
    <Field Name="Field072" Type="byte" />
    <Field Name="Field073" Type="int" />
    <Field Name="Field076" Type="int" />
    <Field Name="Field079" Type="int" />
  </Table>
  <Table Name="CreatureType" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="CreatureXContribution" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="Criteria" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Asset" Type="uint" />
    <Field Name="StartAsset" Type="uint" />
    <Field Name="FailAsset" Type="uint" />
    <Field Name="StartTimer" Type="ushort" />
    <Field Name="ModifierTreeId" Type="ushort" />
    <Field Name="EligibilityWorldStateID" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="StartEvent" Type="byte" />
    <Field Name="FailEvent" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="EligibilityWorldStateValue" Type="byte" />
  </Table>
  <Table Name="CriteriaTree" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Amount" Type="uint" />
    <Field Name="Description" Type="string" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="Operator" Type="byte" />
    <Field Name="CriteriaID" Type="ushort" />
    <Field Name="Parent" Type="ushort" />
    <Field Name="OrderIndex" Type="ushort" />
  </Table>
  <Table Name="CriteriaTreeXEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="CurrencyCategory" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="CurrencyTypes" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="MaxQty" Type="uint" />
    <Field Name="MaxEarnablePerWeek" Type="uint" />
    <Field Name="Flags" Type="uint" />
    <Field Name="Description" Type="string" />
    <Field Name="CategoryID" Type="byte" />
    <Field Name="SpellCategory" Type="byte" />
    <Field Name="Quality" Type="byte" />
    <Field Name="Field1B" Type="int" />
    <Field Name="SpellWeight" Type="byte" />
  </Table>
  <Table Name="Curve" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="byte" />
    <Field Name="Unused" Type="byte" />
  </Table>
  <Table Name="CurvePoint" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Position" Type="float" ArraySize="2" />
    <Field Name="CurveID" Type="ushort" />
    <Field Name="Index" Type="byte" />
  </Table>
  <Table Name="DBCache" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="DeathThudLookups" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="DecalProperties" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="byte" />
  </Table>
  <Table Name="DeclinedWord" Build="24492">
    <Field Name="Field0" Type="uint" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="DeclinedWordCases" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="DestructibleModelData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="StateDamagedDisplayID" Type="ushort" />
    <Field Name="StateDestroyedDisplayID" Type="ushort" />
    <Field Name="StateRebuildingDisplayID" Type="ushort" />
    <Field Name="StateSmokeDisplayID" Type="ushort" />
    <Field Name="HealEffectSpeed" Type="ushort" />
    <Field Name="StateDamagedImpactEffectDoodadSet" Type="byte" />
    <Field Name="StateDamagedAmbientDoodadSet" Type="byte" />
    <Field Name="StateDamagedNameSet" Type="byte" />
    <Field Name="StateDestroyedDestructionDoodadSet" Type="byte" />
    <Field Name="StateDestroyedImpactEffectDoodadSet" Type="byte" />
    <Field Name="StateDestroyedAmbientDoodadSet" Type="byte" />
    <Field Name="StateDestroyedNameSet" Type="byte" />
    <Field Name="StateRebuildingDestructionDoodadSet" Type="byte" />
    <Field Name="StateRebuildingImpactEffectDoodadSet" Type="byte" />
    <Field Name="StateRebuildingAmbientDoodadSet" Type="byte" />
    <Field Name="StateRebuildingNameSet" Type="byte" />
    <Field Name="StateSmokeInitDoodadSet" Type="byte" />
    <Field Name="StateSmokeAmbientDoodadSet" Type="byte" />
    <Field Name="StateSmokeNameSet" Type="byte" />
    <Field Name="EjectDirection" Type="byte" />
    <Field Name="DoNotHighlight" Type="byte" />
    <Field Name="HealEffect" Type="byte" />
  </Table>
  <Table Name="DeviceBlacklist" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="DeviceDefaultSettings" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="Difficulty" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="GroupSizeHealthCurveID" Type="byte" />
    <Field Name="GroupSizeDmgCurveID" Type="byte" />
    <Field Name="GroupSizeSpellPointsCurveID" Type="byte" />
    <Field Name="FallbackDifficultyID" Type="byte" />
    <Field Name="InstanceType" Type="byte" />
    <Field Name="MinPlayers" Type="byte" />
    <Field Name="MaxPlayers" Type="byte" />
    <Field Name="OldEnumValue" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ToggleDifficultyID" Type="byte" />
    <Field Name="ItemBonusTreeModID" Type="byte" />
    <Field Name="OrderIndex" Type="byte" />
  </Table>
  <Table Name="DissolveEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="byte" />
  </Table>
  <Table Name="DriverBlacklist" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
  </Table>
  <Table Name="DungeonEncounter" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field0F" Type="ushort" />
    <Field Name="Field11" Type="int" />
  </Table>
  <Table Name="DungeonMap" Build="24492">
    <Field Name="Field00" Type="int" ArraySize="2" />
    <Field Name="Field08" Type="int" ArraySize="2" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="DungeonMapChunk" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
  </Table>
  <Table Name="DurabilityCosts" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WeaponSubClassCost" Type="ushort" ArraySize="21" />
    <Field Name="ArmorSubClassCost" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="DurabilityQuality" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="QualityMod" Type="float" />
  </Table>
  <Table Name="EdgeGlowEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="byte" />
  </Table>
  <Table Name="Emotes" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EmoteSlashCommand" Type="string" />
    <Field Name="SpellVisualKitID" Type="uint" />
    <Field Name="EmoteFlags" Type="uint" />
    <Field Name="AnimID" Type="ushort" />
    <Field Name="EmoteSpecProc" Type="byte" />
    <Field Name="EmoteSpecProcParam" Type="uint" />
    <Field Name="EmoteSoundID" Type="uint" />
    <Field Name="ClassMask" Type="int" />
    <Field Name="RaceMask" Type="int" />
  </Table>
  <Table Name="EmotesText" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="EmoteID" Type="ushort" />
  </Table>
  <Table Name="EmotesTextData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="EmotesTextSound" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EmotesTextId" Type="ushort" />
    <Field Name="RaceId" Type="byte" />
    <Field Name="SexId" Type="byte" />
    <Field Name="ClassId" Type="byte" />
    <Field Name="SoundId" Type="uint" />
  </Table>
  <Table Name="EnvironmentalDamage" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="Exhaustion" Build="24492">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Faction" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ReputationRaceMask" Type="uint" ArraySize="4" />
    <Field Name="ReputationBase" Type="int" ArraySize="4" />
    <Field Name="ParentFactionMod" Type="float" ArraySize="2" />
    <Field Name="Name" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="ReputationMax" Type="uint" ArraySize="4" />
    <Field Name="ParagonFactionID" Type="ushort" />
    <Field Name="ReputationClassMask" Type="ushort" ArraySize="4" />
    <Field Name="ReputationFlags" Type="ushort" ArraySize="4" />
    <Field Name="ParentFactionID" Type="ushort" />
    <Field Name="ParentFactionCap" Type="byte" ArraySize="2" />
    <Field Name="Expansion" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="FriendshipRepID" Type="byte" />
  </Table>
  <Table Name="FactionGroup" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="FactionTemplate" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Faction" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="Enemies" Type="ushort" ArraySize="4" />
    <Field Name="Friends" Type="ushort" ArraySize="4" />
    <Field Name="Mask" Type="byte" />
    <Field Name="FriendMask" Type="byte" />
    <Field Name="EnemyMask" Type="byte" />
  </Table>
  <Table Name="FootprintTextures" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="int" />
  </Table>
  <Table Name="FootstepTerrainLookup" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field0A" Type="int" />
  </Table>
  <Table Name="FriendshipRepReaction" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="FriendshipReputation" Build="24492">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="FullScreenEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="float" />
    <Field Name="Field008" Type="float" />
    <Field Name="Field00C" Type="float" />
    <Field Name="Field010" Type="float" />
    <Field Name="Field014" Type="uint" />
    <Field Name="Field018" Type="float" />
    <Field Name="Field01C" Type="float" />
    <Field Name="Field020" Type="uint" />
    <Field Name="Field024" Type="float" />
    <Field Name="Field028" Type="float" />
    <Field Name="Field02C" Type="uint" />
    <Field Name="Field030" Type="float" />
    <Field Name="Field034" Type="float" />
    <Field Name="Field038" Type="float" />
    <Field Name="Field03C" Type="float" />
    <Field Name="Field040" Type="float" />
    <Field Name="Field044" Type="uint" />
    <Field Name="Field048" Type="float" />
    <Field Name="Field04C" Type="float" />
    <Field Name="Field050" Type="uint" />
    <Field Name="Field054" Type="uint" />
    <Field Name="Field058" Type="float" />
    <Field Name="Field05C" Type="float" />
    <Field Name="Field060" Type="byte" />
    <Field Name="Field061" Type="ushort" />
    <Field Name="Field063" Type="ushort" />
    <Field Name="Field065" Type="ushort" />
  </Table>
  <Table Name="GameObjectArtKit" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" ArraySize="3" />
    <Field Name="Field7" Type="int" />
  </Table>
  <Table Name="GameObjectDiffAnimMap" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="GameObjectDisplayInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FileDataID" Type="uint" />
    <Field Name="GeoBox" Type="float" ArraySize="6" />
    <Field Name="OverrideLootEffectScale" Type="float" />
    <Field Name="OverrideNameScale" Type="float" />
    <Field Name="ObjectEffectPackageID" Type="ushort" />
  </Table>
  <Table Name="GameObjectDisplayInfoXSoundKit" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="int" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="GameObjects" Build="24492">
    <Field Name="Position" Type="float" ArraySize="3" />
    <Field Name="Rotation" Type="float" ArraySize="4" />
    <Field Name="Size" Type="float" />
    <Field Name="Data" Type="int" ArraySize="8" />
    <Field Name="Name" Type="string" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="DisplayID" Type="ushort" />
    <Field Name="PhaseID" Type="ushort" />
    <Field Name="PhaseGroupID" Type="ushort" />
    <Field Name="PhaseUseFlags" Type="byte" />
    <Field Name="Type" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GameTips" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="GarrAbility" Build="24492">
    <Field Name="Name" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="OtherFactionGarrAbilityID" Type="ushort" />
    <Field Name="GarrAbilityCategoryID" Type="byte" />
    <Field Name="FollowerTypeID" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrAbilityCategory" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="GarrAbilityEffect" Build="24492">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrBuilding" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="HordeGameObjectID" Type="uint" />
    <Field Name="AllianceGameObjectID" Type="uint" />
    <Field Name="NameAlliance" Type="string" />
    <Field Name="NameHorde" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="Tooltip" Type="string" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="CostCurrencyID" Type="ushort" />
    <Field Name="HordeTexPrefixKitID" Type="ushort" />
    <Field Name="AllianceTexPrefixKitID" Type="ushort" />
    <Field Name="AllianceActivationScenePackageID" Type="ushort" />
    <Field Name="HordeActivationScenePackageID" Type="ushort" />
    <Field Name="FollowerRequiredGarrAbilityID" Type="ushort" />
    <Field Name="FollowerGarrAbilityEffectID" Type="ushort" />
    <Field Name="CostMoney" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Type" Type="byte" />
    <Field Name="Level" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="MaxShipments" Type="byte" />
    <Field Name="GarrTypeID" Type="byte" />
    <Field Name="BuildDuration" Type="int" />
    <Field Name="CostCurrencyAmount" Type="int" />
    <Field Name="BonusAmount" Type="int" />
  </Table>
  <Table Name="GarrBuildingDoodadSet" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="GarrBuildingPlotInst" Build="24492">
    <Field Name="LandmarkOffset" Type="float" ArraySize="2" />
    <Field Name="UiTextureAtlasMemberID" Type="ushort" />
    <Field Name="GarrSiteLevelPlotInstID" Type="ushort" />
    <Field Name="GarrBuildingID" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrClassSpec" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrClassSpecPlayerCond" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="GarrEncounter" Build="24492">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="uint" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field16" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="GarrEncounterSetXEncounter" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" />
  </Table>
  <Table Name="GarrEncounterXMechanic" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
  </Table>
  <Table Name="GarrFollItemSetMember" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="GarrFollower" Build="24492">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="uint" />
    <Field Name="Field1C" Type="uint" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" />
    <Field Name="Field34" Type="byte" />
    <Field Name="Field35" Type="byte" />
    <Field Name="Field36" Type="byte" />
    <Field Name="Field37" Type="byte" />
    <Field Name="Field38" Type="byte" />
    <Field Name="Field39" Type="byte" />
    <Field Name="Field3A" Type="byte" />
    <Field Name="Field3B" Type="byte" />
    <Field Name="Field3C" Type="byte" />
    <Field Name="Field3D" Type="byte" />
    <Field Name="Field3E" Type="byte" />
    <Field Name="Field3F" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrFollowerLevelXP" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="GarrFollowerQuality" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="GarrFollowerSetXFollower" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
  </Table>
  <Table Name="GarrFollowerType" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="GarrFollowerUICreature" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="GarrFollowerXAbility" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GarrFollowerID" Type="ushort" />
    <Field Name="GarrAbilityID" Type="ushort" />
    <Field Name="FactionIndex" Type="byte" />
  </Table>
  <Table Name="GarrFollSupportSpell" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="ushort" />
  </Table>
  <Table Name="GarrItemLevelUpgradeData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="GarrMechanic" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="ushort" />
  </Table>
  <Table Name="GarrMechanicSetXMechanic" Build="24492">
    <Field Name="Field0" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field2" Type="byte" />
  </Table>
  <Table Name="GarrMechanicType" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrMission" Build="24492">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="float" ArraySize="2" />
    <Field Name="Field1C" Type="int" ArraySize="2" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="Field32" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field35" Type="byte" />
    <Field Name="Field36" Type="ushort" />
    <Field Name="Field38" Type="byte" />
    <Field Name="Field39" Type="ushort" />
    <Field Name="Field3B" Type="ushort" />
    <Field Name="Field3D" Type="ushort" />
    <Field Name="Field3F" Type="ushort" />
    <Field Name="Field41" Type="ushort" />
    <Field Name="Field43" Type="byte" />
  </Table>
  <Table Name="GarrMissionTexture" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="Field0C" Type="ushort" />
  </Table>
  <Table Name="GarrMissionType" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="GarrMissionXEncounter" Build="24492">
    <Field Name="Field0" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field3" Type="ushort" />
    <Field Name="Field5" Type="ushort" />
    <Field Name="Field7" Type="byte" />
  </Table>
  <Table Name="GarrMissionXFollower" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="GarrMssnBonusAbility" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
  </Table>
  <Table Name="GarrPlot" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="AllianceConstructionGameObjectID" Type="uint" />
    <Field Name="HordeConstructionGameObjectID" Type="uint" />
    <Field Name="GarrPlotUICategoryID" Type="byte" />
    <Field Name="PlotType" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="MinCount" Type="uint" ArraySize="2" />
  </Table>
  <Table Name="GarrPlotBuilding" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GarrPlotID" Type="byte" />
    <Field Name="GarrBuildingID" Type="byte" />
  </Table>
  <Table Name="GarrPlotInstance" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="GarrPlotID" Type="byte" />
  </Table>
  <Table Name="GarrPlotUICategory" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="GarrSiteLevel" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TownHall" Type="float" ArraySize="2" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="SiteID" Type="ushort" />
    <Field Name="MovieID" Type="ushort" />
    <Field Name="UpgradeResourceCost" Type="ushort" />
    <Field Name="UpgradeMoneyCost" Type="byte" />
    <Field Name="Level" Type="byte" />
    <Field Name="UITextureKitID" Type="byte" />
    <Field Name="Level2" Type="byte" />
  </Table>
  <Table Name="GarrSiteLevelPlotInst" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Landmark" Type="float" ArraySize="2" />
    <Field Name="GarrSiteLevelID" Type="ushort" />
    <Field Name="GarrPlotInstanceID" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="GarrSpecialization" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" ArraySize="2" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
  </Table>
  <Table Name="GarrString" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="GarrTalent" Build="24492">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="int" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2B" Type="byte" />
  </Table>
  <Table Name="GarrTalentTree" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
  </Table>
  <Table Name="GarrType" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field07" Type="ushort" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="GarrUiAnimClassInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="GarrUiAnimRaceInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="byte" />
  </Table>
  <Table Name="GemProperties" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="uint" />
    <Field Name="EnchantID" Type="ushort" />
    <Field Name="MinItemLevel" Type="ushort" />
  </Table>
  <Table Name="GlobalStrings" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="GlyphBindableSpell" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="GlyphPropertiesID" Type="ushort" />
  </Table>
  <Table Name="GlyphExclusiveCategory" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="GlyphProperties" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="SpellIconID" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="GlyphExclusiveCategoryID" Type="byte" />
  </Table>
  <Table Name="GlyphRequiredSpec" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GlyphPropertiesID" Type="ushort" />
    <Field Name="ChrSpecializationID" Type="ushort" />
  </Table>
  <Table Name="GMSurveyAnswers" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="GMSurveyCurrentSurvey" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
  </Table>
  <Table Name="GMSurveyQuestions" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="GMSurveySurveys" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" ArraySize="11" />
  </Table>
  <Table Name="GroundEffectDoodad" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="int" />
  </Table>
  <Table Name="GroundEffectTexture" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" ArraySize="4" />
    <Field Name="Field0C" Type="byte" ArraySize="4" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
  </Table>
  <Table Name="GroupFinderActivity" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
  </Table>
  <Table Name="GroupFinderActivityGrp" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="GroupFinderCategory" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="GuildColorBackground" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="GuildColorBorder" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="GuildColorEmblem" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="GuildPerkSpells" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
  </Table>
  <Table Name="Heirloom" Build="24492">
    <Field Name="ItemID" Type="int" />
    <Field Name="SourceText" Type="string" />
    <Field Name="OldItem_1" Type="int" />
    <Field Name="OldItem_2" Type="int" />
    <Field Name="NextDifficultyItemID" Type="int" />
    <Field Name="UpgradeItemID" Type="int" ArraySize="3" />
    <Field Name="ItemBonusListID" Type="ushort" ArraySize="3" />
    <Field Name="Flags" Type="byte" />
    <Field Name="Source" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="HelmetAnimScaling" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="HelmetGeosetVisData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="int" ArraySize="8" />
  </Table>
  <Table Name="HighlightColor" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
  </Table>
  <Table Name="HolidayDescriptions" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="HolidayNames" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="Holidays" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Date" Type="uint" ArraySize="16" />
    <Field Name="Duration" Type="ushort" ArraySize="10" />
    <Field Name="Region" Type="ushort" />
    <Field Name="Looping" Type="byte" />
    <Field Name="CalendarFlags" Type="byte" ArraySize="10" />
    <Field Name="Priority" Type="byte" />
    <Field Name="CalendarFilterType" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="HolidayNameID" Type="ushort" />
    <Field Name="Field06A" Type="byte" />
    <Field Name="Field06B" Type="int" ArraySize="3" />
  </Table>
  <Table Name="ImportPriceArmor" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ClothFactor" Type="float" />
    <Field Name="LeatherFactor" Type="float" />
    <Field Name="MailFactor" Type="float" />
    <Field Name="PlateFactor" Type="float" />
  </Table>
  <Table Name="ImportPriceQuality" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Factor" Type="float" />
  </Table>
  <Table Name="ImportPriceShield" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Factor" Type="float" />
  </Table>
  <Table Name="ImportPriceWeapon" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Factor" Type="float" />
  </Table>
  <Table Name="InvasionClientData" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field0D" Type="ushort" />
    <Field Name="Field0F" Type="ushort" />
    <Field Name="Field11" Type="ushort" />
    <Field Name="Field13" Type="ushort" />
    <Field Name="Field15" Type="ushort" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="Item" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FileDataID" Type="uint" />
    <Field Name="Class" Type="byte" />
    <Field Name="SubClass" Type="byte" />
    <Field Name="SoundOverrideSubclass" Type="byte" />
    <Field Name="Material" Type="byte" />
    <Field Name="InventoryType" Type="byte" />
    <Field Name="Sheath" Type="byte" />
    <Field Name="GroupSoundsID" Type="byte" />
  </Table>
  <Table Name="ItemAppearance" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DisplayID" Type="uint" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="UIOrder" Type="uint" />
    <Field Name="ObjectComponentSlot" Type="byte" />
  </Table>
  <Table Name="ItemAppearanceXUiCamera" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="ItemArmorQuality" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="QualityMod" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemArmorShield" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Quality" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemArmorTotal" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Value" Type="float" ArraySize="4" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemBagFamily" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="ItemBonus" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Value" Type="int" ArraySize="2" />
    <Field Name="BonusListID" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="Index" Type="byte" />
  </Table>
  <Table Name="ItemBonusListLevelDelta" Build="24492">
    <Field Name="Delta" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ItemBonusTreeNode" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="ItemChildEquipment" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="AltItemID" Type="uint" />
    <Field Name="AltEquipmentSlot" Type="byte" />
  </Table>
  <Table Name="ItemClass" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PriceMod" Type="float" />
    <Field Name="Name" Type="string" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="ItemContextPickerEntry" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="ItemCurrencyCost" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemId" Type="uint" />
  </Table>
  <Table Name="ItemDamageAmmo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DPS" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemDamageOneHand" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DPS" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemDamageOneHandCaster" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DPS" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemDamageTwoHand" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DPS" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemDamageTwoHandCaster" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DPS" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemDisenchantLoot" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MinItemLevel" Type="ushort" />
    <Field Name="MaxItemLevel" Type="ushort" />
    <Field Name="RequiredDisenchantSkill" Type="ushort" />
    <Field Name="ItemClass" Type="byte" />
    <Field Name="ItemSubClass" Type="byte" />
    <Field Name="ItemQuality" Type="byte" />
  </Table>
  <Table Name="ItemDisplayInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" ArraySize="2" />
    <Field Name="Field08" Type="int" ArraySize="2" />
    <Field Name="Field0E" Type="byte" ArraySize="3" />
    <Field Name="Field11" Type="byte" ArraySize="3" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="ushort" ArraySize="2" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field27" Type="int" />
    <Field Name="Field2A" Type="int" />
  </Table>
  <Table Name="ItemDisplayInfoMaterialRes" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="ItemDisplayXUiCamera" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="ItemEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="Cooldown" Type="int" />
    <Field Name="CategoryCooldown" Type="int" />
    <Field Name="Charges" Type="ushort" />
    <Field Name="Category" Type="ushort" />
    <Field Name="ChrSpecializationID" Type="ushort" />
    <Field Name="OrderIndex" Type="byte" />
    <Field Name="Trigger" Type="byte" />
  </Table>
  <Table Name="ItemExtendedCost" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RequiredItem" Type="uint" ArraySize="5" />
    <Field Name="RequiredCurrencyCount" Type="uint" ArraySize="5" />
    <Field Name="RequiredItemCount" Type="ushort" ArraySize="5" />
    <Field Name="RequiredPersonalArenaRating" Type="ushort" />
    <Field Name="RequiredCurrency" Type="ushort" ArraySize="5" />
    <Field Name="RequiredArenaSlot" Type="byte" />
    <Field Name="RequiredFactionId" Type="byte" />
    <Field Name="RequiredFactionStanding" Type="byte" />
    <Field Name="RequirementFlags" Type="byte" />
    <Field Name="RequiredAchievement" Type="byte" />
  </Table>
  <Table Name="ItemGroupSounds" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="ItemLevelSelector" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
  </Table>
  <Table Name="ItemLimitCategory" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Quantity" Type="byte" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="ItemLimitCategoryCondition" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" />
    <Field Name="Field7" Type="ushort" />
  </Table>
  <Table Name="ItemModifiedAppearance" Build="24492">
    <Field Name="ItemID" Type="uint" />
    <Field Name="AppearanceID" Type="ushort" />
    <Field Name="AppearanceModID" Type="byte" />
    <Field Name="Index" Type="byte" />
    <Field Name="SourceType" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ItemModifiedAppearanceExtra" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="ItemNameDescription" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="int" />
  </Table>
  <Table Name="ItemPetFood" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ItemPriceBase" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ArmorFactor" Type="float" />
    <Field Name="WeaponFactor" Type="float" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemRandomProperties" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Enchantment" Type="ushort" ArraySize="5" />
  </Table>
  <Table Name="ItemRandomSuffix" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Enchantment" Type="ushort" ArraySize="5" />
    <Field Name="AllocationPct" Type="ushort" ArraySize="5" />
  </Table>
  <Table Name="ItemRangedDisplayInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="ushort" />
    <Field Name="Field9" Type="ushort" />
  </Table>
  <Table Name="ItemSearchName" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Flags" Type="uint" ArraySize="3" />
    <Field Name="AllowableRace" Type="uint" />
    <Field Name="RequiredSpell" Type="ushort" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="ushort" />
    <Field Name="Field1F" Type="ushort" />
    <Field Name="Field23" Type="byte" />
    <Field Name="ItemLevel" Type="ushort" />
    <Field Name="Quality" Type="ushort" />
    <Field Name="Field2F" Type="int" />
  </Table>
  <Table Name="ItemSet" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="ItemID" Type="uint" ArraySize="17" />
    <Field Name="RequiredSkillRank" Type="ushort" />
    <Field Name="RequiredSkill" Type="uint" />
    <Field Name="Flags" Type="uint" />
  </Table>
  <Table Name="ItemSetSpell" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="ItemSetID" Type="ushort" />
    <Field Name="ChrSpecID" Type="ushort" />
    <Field Name="Threshold" Type="byte" />
  </Table>
  <Table Name="ItemSparse" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="int" ArraySize="4" />
    <Field Name="Field014" Type="float" />
    <Field Name="Field018" Type="float" />
    <Field Name="Field01C" Type="uint" />
    <Field Name="Field020" Type="uint" />
    <Field Name="Field024" Type="uint" />
    <Field Name="Field028" Type="int" />
    <Field Name="Field02C" Type="uint" />
    <Field Name="Field030" Type="uint" />
    <Field Name="Field034" Type="uint" />
    <Field Name="Field038" Type="int" ArraySize="10" />
    <Field Name="Field060" Type="float" ArraySize="10" />
    <Field Name="Field088" Type="float" />
    <Field Name="Field08C" Type="string" />
    <Field Name="Field090" Type="string" />
    <Field Name="Field094" Type="string" />
    <Field Name="Field098" Type="string" />
    <Field Name="Field09C" Type="string" />
    <Field Name="Field0A0" Type="int" />
    <Field Name="Field0A4" Type="float" />
    <Field Name="Field0A8" Type="int" />
    <Field Name="Field0AC" Type="float" />
    <Field Name="Field0B0" Type="ushort" />
    <Field Name="Field0B2" Type="ushort" />
    <Field Name="Field0B4" Type="ushort" />
    <Field Name="Field0B6" Type="ushort" />
    <Field Name="Field0B8" Type="ushort" />
    <Field Name="Field0BA" Type="ushort" ArraySize="10" />
    <Field Name="Field0CE" Type="ushort" />
    <Field Name="Field0D0" Type="ushort" />
    <Field Name="Field0D2" Type="ushort" />
    <Field Name="Field0D4" Type="ushort" />
    <Field Name="Field0D6" Type="ushort" />
    <Field Name="Field0D8" Type="ushort" />
    <Field Name="Field0DA" Type="ushort" />
    <Field Name="Field0DC" Type="ushort" />
    <Field Name="Field0DE" Type="ushort" />
    <Field Name="Field0E0" Type="ushort" />
    <Field Name="Field0E2" Type="ushort" />
    <Field Name="Field0E4" Type="ushort" />
    <Field Name="Field0E6" Type="ushort" />
    <Field Name="Field0E8" Type="ushort" />
    <Field Name="Field0EA" Type="ushort" />
    <Field Name="Field0EC" Type="ushort" />
    <Field Name="Field0EE" Type="ushort" />
    <Field Name="Field0F0" Type="byte" />
    <Field Name="Field0F1" Type="byte" />
    <Field Name="Field0F2" Type="byte" />
    <Field Name="Field0F3" Type="byte" />
    <Field Name="Field0F4" Type="byte" />
    <Field Name="Field0F5" Type="byte" />
    <Field Name="Field0F6" Type="byte" />
    <Field Name="Field0F7" Type="byte" ArraySize="10" />
    <Field Name="Field101" Type="byte" />
    <Field Name="Field102" Type="byte" />
    <Field Name="Field103" Type="byte" />
    <Field Name="Field104" Type="byte" />
    <Field Name="Field105" Type="byte" />
    <Field Name="Field106" Type="byte" />
    <Field Name="Field107" Type="byte" ArraySize="3" />
    <Field Name="Field10A" Type="byte" />
    <Field Name="Field10B" Type="byte" />
    <Field Name="Field10C" Type="byte" />
    <Field Name="Field10D" Type="byte" />
  </Table>
  <Table Name="ItemSpec" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpecID" Type="ushort" />
    <Field Name="MinLevel" Type="byte" />
    <Field Name="MaxLevel" Type="byte" />
    <Field Name="ItemType" Type="byte" />
    <Field Name="PrimaryStat" Type="byte" />
    <Field Name="SecondaryStat" Type="byte" />
  </Table>
  <Table Name="ItemSpecOverride" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="SpecID" Type="ushort" />
  </Table>
  <Table Name="ItemSubClass" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
  </Table>
  <Table Name="ItemSubClassMask" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="ItemUpgrade" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CurrencyCost" Type="uint" />
    <Field Name="PrevItemUpgradeID" Type="ushort" />
    <Field Name="CurrencyID" Type="ushort" />
    <Field Name="ItemUpgradePathID" Type="byte" />
    <Field Name="ItemLevelBonus" Type="byte" />
  </Table>
  <Table Name="ItemVisualEffects" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ItemVisuals" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" ArraySize="3" />
  </Table>
  <Table Name="ItemXBonusTree" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="BonusTreeID" Type="ushort" />
  </Table>
  <Table Name="JournalEncounter" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" />
  </Table>
  <Table Name="JournalEncounterCreature" Build="24492">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="JournalEncounterItem" Build="24492">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="JournalEncounterSection" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="byte" />
  </Table>
  <Table Name="JournalEncounterXDifficulty" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="JournalInstance" Build="24492">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="JournalItemXDifficulty" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="JournalSectionXDifficulty" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="JournalTier" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="JournalTierXInstance" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="KeyChain" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Key" Type="byte" ArraySize="32" />
  </Table>
  <Table Name="KeystoneAffix" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="uint" />
  </Table>
  <Table Name="Languages" Build="24492">
    <Field Name="Field0" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="LanguageWords" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="LfgDungeonExpansion" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="LfgDungeonGroup" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="LfgDungeons" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" />
    <Field Name="Field34" Type="byte" />
    <Field Name="Field35" Type="byte" />
    <Field Name="Field36" Type="byte" />
    <Field Name="Field37" Type="int" />
    <Field Name="Field3A" Type="int" />
    <Field Name="Field3D" Type="int" />
  </Table>
  <Table Name="LfgDungeonsGroupingMap" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="LfgRoleRequirement" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="ushort" />
  </Table>
  <Table Name="Light" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="FalloffStart" Type="float" />
    <Field Name="FalloffEnd" Type="float" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="LightParamsID" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="LightData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="int" />
    <Field Name="Field008" Type="int" />
    <Field Name="Field00C" Type="int" />
    <Field Name="Field010" Type="int" />
    <Field Name="Field014" Type="int" />
    <Field Name="Field018" Type="int" />
    <Field Name="Field01C" Type="int" />
    <Field Name="Field020" Type="int" />
    <Field Name="Field024" Type="int" />
    <Field Name="Field028" Type="int" />
    <Field Name="Field02C" Type="int" />
    <Field Name="Field030" Type="int" />
    <Field Name="Field034" Type="int" />
    <Field Name="Field038" Type="int" />
    <Field Name="Field03C" Type="int" />
    <Field Name="Field040" Type="int" />
    <Field Name="Field044" Type="int" />
    <Field Name="Field048" Type="int" />
    <Field Name="Field04C" Type="uint" />
    <Field Name="Field050" Type="int" />
    <Field Name="Field054" Type="float" />
    <Field Name="Field058" Type="float" />
    <Field Name="Field05C" Type="int" />
    <Field Name="Field060" Type="float" />
    <Field Name="Field064" Type="float" />
    <Field Name="Field068" Type="float" />
    <Field Name="Field06C" Type="float" />
    <Field Name="Field070" Type="uint" />
    <Field Name="Field074" Type="uint" />
    <Field Name="Field078" Type="uint" />
    <Field Name="Field07C" Type="uint" />
    <Field Name="Field080" Type="uint" />
    <Field Name="Field084" Type="uint" />
    <Field Name="Field088" Type="ushort" />
    <Field Name="Field08A" Type="ushort" />
  </Table>
  <Table Name="LightParams" Build="24492">
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="int" ArraySize="3" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="LightSkybox" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="LiquidMaterial" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
  </Table>
  <Table Name="LiquidObject" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
  </Table>
  <Table Name="LiquidType" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="MaxDarkenDepth" Type="float" />
    <Field Name="FogDarkenIntensity" Type="float" />
    <Field Name="AmbDarkenIntensity" Type="float" />
    <Field Name="DirDarkenIntensity" Type="float" />
    <Field Name="ParticleScale" Type="float" />
    <Field Name="Texture" Type="string" ArraySize="6" />
    <Field Name="Color" Type="uint" ArraySize="2" />
    <Field Name="Float" Type="float" ArraySize="18" />
    <Field Name="Int" Type="uint" ArraySize="4" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="LightID" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="ParticleMovement" Type="byte" />
    <Field Name="ParticleTexSlots" Type="byte" />
    <Field Name="MaterialID" Type="byte" />
    <Field Name="DepthTexCount" Type="byte" ArraySize="6" />
    <Field Name="SoundID" Type="uint" />
  </Table>
  <Table Name="LoadingScreens" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
  </Table>
  <Table Name="LoadingScreenTaxiSplines" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="10" />
    <Field Name="Field2C" Type="float" ArraySize="10" />
    <Field Name="Field54" Type="ushort" />
    <Field Name="Field56" Type="ushort" />
    <Field Name="Field58" Type="byte" />
  </Table>
  <Table Name="Locale" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="Location" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="int" ArraySize="2" />
  </Table>
  <Table Name="Lock" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Index" Type="uint" ArraySize="8" />
    <Field Name="Skill" Type="ushort" ArraySize="8" />
    <Field Name="Type" Type="byte" ArraySize="8" />
    <Field Name="Action" Type="byte" ArraySize="8" />
  </Table>
  <Table Name="LockType" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="LookAtController" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="byte" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
  </Table>
  <Table Name="MailTemplate" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Body" Type="string" />
  </Table>
  <Table Name="ManagedWorldState" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
  </Table>
  <Table Name="ManagedWorldStateBuff" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="ManagedWorldStateInput" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="ManifestInterfaceActionIcon" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ManifestInterfaceData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="string" />
  </Table>
  <Table Name="ManifestInterfaceItemIcon" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ManifestInterfaceTOCData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ManifestMP3" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Map" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" ArraySize="2" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="int" ArraySize="2" />
    <Field Name="Field1C" Type="string" />
    <Field Name="Field20" Type="string" />
    <Field Name="Field24" Type="string" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field30" Type="ushort" />
    <Field Name="Field32" Type="ushort" />
    <Field Name="Field34" Type="ushort" />
    <Field Name="Field36" Type="byte" />
    <Field Name="Field37" Type="byte" />
    <Field Name="Field38" Type="byte" />
    <Field Name="Field39" Type="byte" />
    <Field Name="Field3A" Type="byte" />
  </Table>
  <Table Name="MapCelestialBody" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="MapChallengeMode" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" ArraySize="3" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="MapDifficulty" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Message_Lang" Type="string" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="RaidDurationType" Type="byte" />
    <Field Name="MaxPlayers" Type="byte" />
    <Field Name="LockID" Type="byte" />
    <Field Name="ItemBonusTreeModID" Type="byte" />
    <Field Name="Context" Type="int" />
  </Table>
  <Table Name="MapDifficultyXCondition" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="MarketingPromotionsXLocale" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
  </Table>
  <Table Name="Material" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" />
    <Field Name="Field7" Type="ushort" />
    <Field Name="Field9" Type="ushort" />
  </Table>
  <Table Name="MinorTalent" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="ModelAnimCloakDampening" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="int" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="ModelFileData" Build="24492">
    <Field Name="Field0" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
  </Table>
  <Table Name="ModelRibbonQuality" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="ModifierTree" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Asset" Type="uint" ArraySize="2" />
    <Field Name="Parent" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Operator" Type="byte" />
    <Field Name="Amount" Type="byte" />
  </Table>
  <Table Name="Mount" Build="24492">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field1B" Type="ushort" />
    <Field Name="Field1D" Type="byte" ArraySize="3" />
  </Table>
  <Table Name="MountCapability" Build="24492">
    <Field Name="RequiredSpell" Type="uint" />
    <Field Name="SpeedModSpell" Type="uint" />
    <Field Name="RequiredRidingSkill" Type="ushort" />
    <Field Name="RequiredArea" Type="ushort" />
    <Field Name="RequiredMap" Type="ushort" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RequiredAura" Type="uint" />
  </Table>
  <Table Name="MountTypeXCapability" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MountTypeID" Type="ushort" />
    <Field Name="MountCapabilityID" Type="ushort" />
    <Field Name="OrderIndex" Type="byte" />
  </Table>
  <Table Name="MountXDisplay" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MountID" Type="uint" />
    <Field Name="DisplayID" Type="uint" />
    <Field Name="PlayerConditionID" Type="uint" />
  </Table>
  <Table Name="Movie" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AudioFileDataID" Type="uint" />
    <Field Name="SubtitleFileDataID" Type="uint" />
    <Field Name="Volume" Type="byte" />
    <Field Name="KeyID" Type="byte" />
  </Table>
  <Table Name="MovieFileData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
  </Table>
  <Table Name="MovieVariation" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="NameGen" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Race" Type="byte" />
    <Field Name="Sex" Type="byte" />
  </Table>
  <Table Name="NamesProfanity" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Language" Type="byte" />
  </Table>
  <Table Name="NamesReserved" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="NamesReservedLocale" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="LocaleMask" Type="byte" />
  </Table>
  <Table Name="NpcModelItemSlotDisplayInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="NPCSounds" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="int" ArraySize="2" />
  </Table>
  <Table Name="ObjectEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="int" />
    <Field Name="Field19" Type="ushort" />
  </Table>
  <Table Name="ObjectEffectGroup" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ObjectEffectModifier" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="4" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="ObjectEffectPackage" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ObjectEffectPackageElem" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="OutlineEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="OverrideSpellData" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" ArraySize="10" />
    <Field Name="PlayerActionbarFileDataID" Type="uint" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="PageTextMaterial" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="PaperDollItemFrame" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field0C" Type="int" />
  </Table>
  <Table Name="ParagonReputation" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="ParticleColor" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="int" ArraySize="3" />
    <Field Name="Field1C" Type="int" ArraySize="2" />
  </Table>
  <Table Name="Path" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="PathNode" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="PathNodeProperty" Build="24492">
    <Field Name="Field0" Type="ushort" />
    <Field Name="Field2" Type="ushort" />
    <Field Name="Field4" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field8" Type="int" />
  </Table>
  <Table Name="PathProperty" Build="24492">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Phase" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="ushort" />
  </Table>
  <Table Name="PhaseShiftZoneSounds" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
  </Table>
  <Table Name="PhaseXPhaseGroup" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PhaseID" Type="ushort" />
    <Field Name="PhaseGroupID" Type="ushort" />
  </Table>
  <Table Name="PlayerCondition" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="int" />
    <Field Name="Field008" Type="uint" ArraySize="2" />
    <Field Name="Field010" Type="uint" ArraySize="4" />
    <Field Name="Field020" Type="string" />
    <Field Name="Field024" Type="ushort" ArraySize="4" />
    <Field Name="Field02C" Type="ushort" ArraySize="4" />
    <Field Name="Field034" Type="ushort" ArraySize="4" />
    <Field Name="Field03C" Type="ushort" ArraySize="4" />
    <Field Name="Field044" Type="ushort" ArraySize="4" />
    <Field Name="Field04C" Type="ushort" ArraySize="4" />
    <Field Name="Field054" Type="ushort" ArraySize="2" />
    <Field Name="Field058" Type="ushort" ArraySize="4" />
    <Field Name="Field060" Type="ushort" ArraySize="4" />
    <Field Name="Field068" Type="byte" />
    <Field Name="Field069" Type="byte" ArraySize="3" />
    <Field Name="Field06C" Type="byte" ArraySize="4" />
    <Field Name="Field070" Type="byte" ArraySize="4" />
    <Field Name="Field074" Type="byte" ArraySize="4" />
    <Field Name="Field078" Type="byte" ArraySize="4" />
    <Field Name="Field07C" Type="ushort" />
    <Field Name="Field07E" Type="ushort" ArraySize="3" />
    <Field Name="Field084" Type="int" ArraySize="4" />
    <Field Name="Field090" Type="int" ArraySize="4" />
    <Field Name="Field09C" Type="byte" ArraySize="4" />
    <Field Name="Field0A0" Type="byte" ArraySize="4" />
    <Field Name="Field0A4" Type="ushort" ArraySize="4" />
    <Field Name="Field0AC" Type="int" ArraySize="6" />
    <Field Name="Field0BE" Type="byte" />
    <Field Name="Field0BF" Type="ushort" />
    <Field Name="Field0C1" Type="ushort" />
    <Field Name="Field0C3" Type="byte" />
    <Field Name="Field0C4" Type="byte" />
    <Field Name="Field0C5" Type="int" />
    <Field Name="Field0C9" Type="byte" />
    <Field Name="Field0CA" Type="byte" />
    <Field Name="Field0CB" Type="int" />
    <Field Name="Field0CF" Type="ushort" />
    <Field Name="Field0D1" Type="byte" />
    <Field Name="Field0D2" Type="int" />
    <Field Name="Field0D6" Type="byte" />
    <Field Name="Field0D7" Type="byte" />
    <Field Name="Field0D8" Type="byte" />
    <Field Name="Field0D9" Type="byte" />
    <Field Name="Field0DA" Type="int" />
    <Field Name="Field0DE" Type="int" />
    <Field Name="Field0E2" Type="int" />
    <Field Name="Field0E6" Type="int" />
    <Field Name="Field0EA" Type="int" />
    <Field Name="Field0EE" Type="byte" />
    <Field Name="Field0EF" Type="int" />
    <Field Name="Field0F3" Type="ushort" />
    <Field Name="Field0F5" Type="byte" />
    <Field Name="Field0F6" Type="byte" />
    <Field Name="Field0F7" Type="byte" />
    <Field Name="Field0F8" Type="int" />
    <Field Name="Field0FC" Type="int" />
    <Field Name="Field100" Type="int" />
    <Field Name="Field104" Type="int" />
    <Field Name="Field108" Type="ushort" />
    <Field Name="Field10A" Type="int" />
    <Field Name="Field10E" Type="byte" />
    <Field Name="Field10F" Type="byte" />
    <Field Name="Field110" Type="byte" />
    <Field Name="Field111" Type="byte" />
    <Field Name="Field112" Type="byte" />
    <Field Name="Field113" Type="byte" />
    <Field Name="Field114" Type="byte" />
    <Field Name="Field115" Type="ushort" />
    <Field Name="Field117" Type="int" />
    <Field Name="Field11B" Type="int" />
    <Field Name="Field11F" Type="int" />
    <Field Name="Field123" Type="ushort" />
    <Field Name="Field125" Type="ushort" />
    <Field Name="Field127" Type="byte" />
    <Field Name="Field128" Type="byte" />
    <Field Name="Field129" Type="byte" />
    <Field Name="Field12A" Type="byte" />
    <Field Name="Field12B" Type="byte" />
    <Field Name="Field12C" Type="int" />
    <Field Name="Field130" Type="int" />
  </Table>
  <Table Name="Positioner" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="PositionerState" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="ushort" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
  </Table>
  <Table Name="PositionerStateEntry" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="ushort" />
  </Table>
  <Table Name="PowerDisplay" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GlobalStringBaseTag" Type="string" />
    <Field Name="PowerType" Type="byte" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="PowerType" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PowerTypeToken" Type="string" />
    <Field Name="PowerCostToken" Type="string" />
    <Field Name="RegenerationPeace" Type="float" />
    <Field Name="RegenerationCombat" Type="float" />
    <Field Name="MaxPower" Type="ushort" />
    <Field Name="RegenerationDelay" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="PowerTypeEnum" Type="byte" />
    <Field Name="RegenerationMin" Type="byte" />
    <Field Name="RegenerationCenter" Type="byte" />
    <Field Name="RegenerationMax" Type="byte" />
    <Field Name="UIModifier" Type="byte" />
  </Table>
  <Table Name="PrestigeLevelInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="PvpBracketTypes" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="PvpDifficulty" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="BracketID" Type="byte" />
    <Field Name="MinLevel" Type="byte" />
    <Field Name="MaxLevel" Type="byte" />
  </Table>
  <Table Name="PvpItem" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="PvpReward" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="PvpScalingEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="PvpScalingEffectType" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="PvpTalent" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="PvpTalentUnlock" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="QuestFactionReward" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="QuestRewFactionValue" Type="ushort" ArraySize="10" />
  </Table>
  <Table Name="QuestFeedbackEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="QuestInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="QuestLine" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="QuestLineXQuest" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="QuestMoneyReward" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Money" Type="uint" ArraySize="10" />
  </Table>
  <Table Name="QuestObjective" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
  </Table>
  <Table Name="QuestPackageItem" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="QuestPackageID" Type="ushort" />
    <Field Name="FilterType" Type="byte" />
    <Field Name="ItemCount" Type="uint" />
  </Table>
  <Table Name="QuestPOIBlob" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="int" />
  </Table>
  <Table Name="QuestPOIPoint" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="QuestPOIPointCliTask" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="int" />
  </Table>
  <Table Name="QuestSort" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SortName" Type="string" />
    <Field Name="SortOrder" Type="byte" />
  </Table>
  <Table Name="QuestV2" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="UniqueBitFlag" Type="ushort" />
  </Table>
  <Table Name="QuestV2CliTask" Build="24492">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" ArraySize="3" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" ArraySize="3" />
  </Table>
  <Table Name="QuestXGroupActivity" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="QuestXP" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Exp" Type="ushort" ArraySize="10" />
  </Table>
  <Table Name="RacialMounts" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="RandPropPoints" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EpicPropertiesPoints" Type="uint" ArraySize="5" />
    <Field Name="RarePropertiesPoints" Type="uint" ArraySize="5" />
    <Field Name="UncommonPropertiesPoints" Type="uint" ArraySize="5" />
  </Table>
  <Table Name="ResearchBranch" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="int" />
    <Field Name="Field12" Type="int" />
  </Table>
  <Table Name="ResearchField" Build="24492">
    <Field Name="Field0" Type="int" />
    <Field Name="Field4" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ResearchProject" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field15" Type="byte" ArraySize="3" />
  </Table>
  <Table Name="ResearchSite" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="Resistances" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="ushort" />
  </Table>
  <Table Name="RewardPack" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
  </Table>
  <Table Name="RewardPackXCurrencyType" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" />
    <Field Name="Field7" Type="byte" />
  </Table>
  <Table Name="RewardPackXItem" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="RibbonQuality" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="RulesetItemUpgrade" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="ItemUpgradeID" Type="ushort" />
  </Table>
  <Table Name="ScalingStatDistribution" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemLevelCurveID" Type="ushort" />
    <Field Name="MinLevel" Type="uint" />
    <Field Name="MaxLevel" Type="uint" />
  </Table>
  <Table Name="Scenario" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Data" Type="ushort" />
    <Field Name="Flags" Type="byte" />
    <Field Name="Type" Type="byte" />
  </Table>
  <Table Name="ScenarioEventEntry" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="ScenarioStep" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Description" Type="string" />
    <Field Name="Name" Type="string" />
    <Field Name="CriteriaTreeID" Type="ushort" />
    <Field Name="ScenarioID" Type="ushort" />
    <Field Name="PreviousStepID" Type="ushort" />
    <Field Name="QuestRewardID" Type="ushort" />
    <Field Name="Step" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="BonusRequiredStepID" Type="uint" />
  </Table>
  <Table Name="SceneScript" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Script" Type="string" />
    <Field Name="PrevScriptId" Type="ushort" />
    <Field Name="NextScriptId" Type="ushort" />
  </Table>
  <Table Name="SceneScriptPackage" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="SceneScriptPackageMember" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="ScheduledInterval" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="uint" />
    <Field Name="Field0A" Type="int" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="ScheduledWorldState" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="ScheduledWorldStateGroup" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="ScheduledWorldStateXUniqCat" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field2" Type="ushort" />
    <Field Name="Field4" Type="ushort" />
  </Table>
  <Table Name="ScreenEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" ArraySize="4" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
  </Table>
  <Table Name="ScreenLocation" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SeamlessSite" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
  </Table>
  <Table Name="ServerMessages" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ShadowyEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
  </Table>
  <Table Name="SkillLine" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DisplayName" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="AlternateVerb" Type="string" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="CategoryID" Type="byte" />
    <Field Name="CanLink" Type="byte" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="ParentSkillLineID" Type="uint" />
  </Table>
  <Table Name="SkillLineAbility" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="RaceMask" Type="uint" />
    <Field Name="SupercedesSpell" Type="uint" />
    <Field Name="SkillLine" Type="ushort" />
    <Field Name="MinSkillLineRank" Type="ushort" />
    <Field Name="TrivialSkillLineRankHigh" Type="ushort" />
    <Field Name="TrivialSkillLineRankLow" Type="ushort" />
    <Field Name="UniqueBit" Type="ushort" />
    <Field Name="TradeSkillCategoryID" Type="ushort" />
    <Field Name="AcquireMethod" Type="byte" />
    <Field Name="NumSkillUps" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="ClassMask" Type="int" />
  </Table>
  <Table Name="SkillRaceClassInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceMask" Type="int" />
    <Field Name="SkillID" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="SkillTierID" Type="ushort" />
    <Field Name="Availability" Type="byte" />
    <Field Name="MinLevel" Type="byte" />
    <Field Name="ClassMask" Type="int" />
  </Table>
  <Table Name="SoundAmbience" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" ArraySize="2" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="SoundAmbienceFlavor" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="int" />
    <Field Name="Field9" Type="int" />
  </Table>
  <Table Name="SoundBus" Build="24492">
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SoundBusName" Build="24492">
    <Field Name="Field0" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SoundBusOverride" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="int" />
    <Field Name="Field0F" Type="int" />
  </Table>
  <Table Name="SoundEmitterPillPoints" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="ushort" />
  </Table>
  <Table Name="SoundEmitters" Build="24492">
    <Field Name="Field00" Type="int" ArraySize="3" />
    <Field Name="Field0C" Type="int" ArraySize="3" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field27" Type="int" />
    <Field Name="Field2A" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="SoundFilter" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SoundFilterElem" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="9" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
  </Table>
  <Table Name="SoundKit" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="VolumeFloat" Type="float" />
    <Field Name="MinDistance" Type="float" />
    <Field Name="DistanceCutoff" Type="float" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field1A" Type="float" />
    <Field Name="Field1E" Type="float" />
    <Field Name="Field22" Type="float" />
    <Field Name="Field26" Type="float" />
    <Field Name="Field2A" Type="float" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field32" Type="byte" />
  </Table>
  <Table Name="SoundKitAdvanced" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="float" />
    <Field Name="Field008" Type="float" />
    <Field Name="Field00C" Type="float" />
    <Field Name="Field010" Type="float" />
    <Field Name="Field014" Type="float" />
    <Field Name="Field018" Type="uint" />
    <Field Name="Field01C" Type="uint" />
    <Field Name="Field020" Type="float" />
    <Field Name="Field024" Type="byte" />
    <Field Name="Field025" Type="int" />
    <Field Name="Field028" Type="int" />
    <Field Name="Field02C" Type="int" />
    <Field Name="Field030" Type="int" />
    <Field Name="Field034" Type="int" />
    <Field Name="Field038" Type="int" />
    <Field Name="Field03C" Type="int" />
    <Field Name="Field040" Type="int" />
    <Field Name="Field044" Type="int" />
    <Field Name="Field048" Type="int" />
    <Field Name="Field04C" Type="byte" />
    <Field Name="Field04D" Type="float" />
    <Field Name="Field051" Type="float" />
    <Field Name="Field055" Type="float" />
    <Field Name="Field059" Type="float" />
    <Field Name="Field05D" Type="byte" />
    <Field Name="Field05E" Type="ushort" />
    <Field Name="Field060" Type="float" />
    <Field Name="Field064" Type="float" />
    <Field Name="Field068" Type="int" />
    <Field Name="Field06C" Type="int" />
    <Field Name="Field070" Type="int" />
    <Field Name="Field074" Type="byte" />
    <Field Name="Field075" Type="byte" />
    <Field Name="Field076" Type="int" />
    <Field Name="Field07A" Type="int" />
    <Field Name="Field07E" Type="int" />
    <Field Name="Field082" Type="int" />
    <Field Name="Field086" Type="int" />
    <Field Name="Field08A" Type="int" />
  </Table>
  <Table Name="SoundKitChild" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="int" />
    <Field Name="Field7" Type="int" />
  </Table>
  <Table Name="SoundKitEntry" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="int" />
  </Table>
  <Table Name="SoundKitFallback" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="SoundKitName" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SoundOverride" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="SoundProviderPreferences" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="float" />
    <Field Name="Field44" Type="ushort" />
    <Field Name="Field46" Type="ushort" />
    <Field Name="Field48" Type="ushort" />
    <Field Name="Field4A" Type="ushort" />
    <Field Name="Field4C" Type="ushort" />
    <Field Name="Field4E" Type="byte" />
    <Field Name="Field4F" Type="byte" />
  </Table>
  <Table Name="SourceInfo" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="SpamMessages" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SpecializationSpells" Build="24492">
    <Field Name="SpellID" Type="uint" />
    <Field Name="OverridesSpellID" Type="uint" />
    <Field Name="Description" Type="string" />
    <Field Name="SpecID" Type="ushort" />
    <Field Name="OrderIndex" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Spell" Build="24492">
    <Field Name="Name" Type="string" />
    <Field Name="NameSubtext" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="AuraDescription" Type="string" />
    <Field Name="MiscID" Type="uint" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DescriptionVariablesID" Type="uint" />
  </Table>
  <Table Name="SpellActionBarPref" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="SpellActivationOverlay" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="uint" ArraySize="4" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="ushort" />
  </Table>
  <Table Name="SpellAuraOptions" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="ProcCharges" Type="uint" />
    <Field Name="ProcTypeMask" Type="uint" />
    <Field Name="ProcCategoryRecovery" Type="uint" />
    <Field Name="CumulativeAura" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="ProcChance" Type="byte" />
    <Field Name="SpellProcsPerMinuteID" Type="byte" />
  </Table>
  <Table Name="SpellAuraRestrictions" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="CasterAuraSpell" Type="uint" />
    <Field Name="TargetAuraSpell" Type="uint" />
    <Field Name="ExcludeCasterAuraSpell" Type="uint" />
    <Field Name="ExcludeTargetAuraSpell" Type="uint" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="CasterAuraState" Type="byte" />
    <Field Name="TargetAuraState" Type="byte" />
    <Field Name="ExcludeCasterAuraState" Type="byte" />
    <Field Name="ExcludeTargetAuraState" Type="byte" />
  </Table>
  <Table Name="SpellAuraVisibility" Build="24492">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellAuraVisXChrSpec" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="SpellCastingRequirements" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="MinFactionID" Type="ushort" />
    <Field Name="RequiredAreasID" Type="ushort" />
    <Field Name="RequiresSpellFocus" Type="ushort" />
    <Field Name="FacingCasterFlags" Type="byte" />
    <Field Name="MinReputation" Type="byte" />
    <Field Name="RequiredAuraVision" Type="byte" />
  </Table>
  <Table Name="SpellCastTimes" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CastTime" Type="int" />
    <Field Name="MinCastTime" Type="int" />
    <Field Name="CastTimePerLevel" Type="ushort" />
  </Table>
  <Table Name="SpellCategories" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="Category" Type="ushort" />
    <Field Name="StartRecoveryCategory" Type="ushort" />
    <Field Name="ChargeCategory" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="DefenseType" Type="byte" />
    <Field Name="DispelType" Type="byte" />
    <Field Name="Mechanic" Type="byte" />
    <Field Name="PreventionType" Type="byte" />
  </Table>
  <Table Name="SpellCategory" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="ChargeRecoveryTime" Type="int" />
    <Field Name="Flags" Type="byte" />
    <Field Name="UsesPerWeek" Type="byte" />
    <Field Name="MaxCharges" Type="byte" />
    <Field Name="ChargeCategoryType" Type="uint" />
  </Table>
  <Table Name="SpellChainEffects" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="float" />
    <Field Name="Field008" Type="float" />
    <Field Name="Field00C" Type="int" />
    <Field Name="Field010" Type="uint" />
    <Field Name="Field014" Type="uint" />
    <Field Name="Field018" Type="float" />
    <Field Name="Field01C" Type="float" />
    <Field Name="Field020" Type="float" />
    <Field Name="Field024" Type="int" />
    <Field Name="Field028" Type="float" />
    <Field Name="Field02C" Type="float" />
    <Field Name="Field030" Type="float" />
    <Field Name="Field034" Type="int" />
    <Field Name="Field038" Type="int" />
    <Field Name="Field03C" Type="int" />
    <Field Name="Field040" Type="int" />
    <Field Name="Field044" Type="int" />
    <Field Name="Field048" Type="int" />
    <Field Name="Field04C" Type="int" />
    <Field Name="Field050" Type="int" />
    <Field Name="Field054" Type="int" />
    <Field Name="Field058" Type="int" />
    <Field Name="Field05C" Type="int" />
    <Field Name="Field060" Type="int" />
    <Field Name="Field064" Type="uint" />
    <Field Name="Field068" Type="float" />
    <Field Name="Field06C" Type="float" />
    <Field Name="Field070" Type="float" />
    <Field Name="Field074" Type="float" />
    <Field Name="Field078" Type="int" />
    <Field Name="Field07C" Type="float" />
    <Field Name="Field080" Type="float" />
    <Field Name="Field084" Type="int" />
    <Field Name="Field088" Type="float" />
    <Field Name="Field08C" Type="float" />
    <Field Name="Field090" Type="int" ArraySize="3" />
    <Field Name="Field09C" Type="int" ArraySize="3" />
    <Field Name="Field0A8" Type="int" ArraySize="3" />
    <Field Name="Field0B4" Type="float" ArraySize="3" />
    <Field Name="Field0C0" Type="uint" />
    <Field Name="Field0C4" Type="float" />
    <Field Name="Field0C8" Type="float" />
    <Field Name="Field0CC" Type="float" />
    <Field Name="Field0D0" Type="float" />
    <Field Name="Field0D4" Type="ushort" />
    <Field Name="Field0D6" Type="ushort" />
    <Field Name="Field0D8" Type="ushort" ArraySize="11" />
    <Field Name="Field0EE" Type="ushort" />
    <Field Name="Field0F0" Type="byte" />
    <Field Name="Field0F1" Type="byte" />
    <Field Name="Field0F2" Type="byte" />
    <Field Name="Field0F3" Type="byte" />
    <Field Name="Field0F4" Type="byte" />
    <Field Name="Field0F5" Type="byte" />
    <Field Name="Field0F6" Type="byte" />
    <Field Name="Field0F7" Type="byte" />
    <Field Name="Field0F8" Type="byte" />
    <Field Name="Field0F9" Type="byte" />
    <Field Name="Field0FA" Type="int" />
    <Field Name="Field0FD" Type="int" ArraySize="2" />
  </Table>
  <Table Name="SpellClassOptions" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="SpellClassSet" Type="byte" />
    <Field Name="ModalNextSpell" Type="uint" />
  </Table>
  <Table Name="SpellCooldowns" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="CategoryRecoveryTime" Type="uint" />
    <Field Name="RecoveryTime" Type="uint" />
    <Field Name="StartRecoveryTime" Type="uint" />
    <Field Name="DifficultyID" Type="byte" />
  </Table>
  <Table Name="SpellDescriptionVariables" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SpellDispelType" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="SpellDuration" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Duration" Type="int" />
    <Field Name="MaxDuration" Type="int" />
    <Field Name="DurationPerLevel" Type="int" />
  </Table>
  <Table Name="SpellEffect" Build="24492">
    <Field Name="Field000" Type="int" ArraySize="4" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field013" Type="int" />
    <Field Name="Field016" Type="byte" />
    <Field Name="Field017" Type="ushort" />
    <Field Name="Field019" Type="int" />
    <Field Name="Field01D" Type="byte" />
    <Field Name="Field01E" Type="int" ArraySize="2" />
    <Field Name="Field026" Type="byte" ArraySize="2" />
    <Field Name="Field028" Type="byte" />
    <Field Name="Field029" Type="int" />
    <Field Name="Field02D" Type="float" />
    <Field Name="Field031" Type="int" />
    <Field Name="Field035" Type="float" />
    <Field Name="Field039" Type="float" />
    <Field Name="Field03D" Type="int" />
    <Field Name="Field041" Type="int" />
    <Field Name="Field045" Type="int" />
    <Field Name="Field049" Type="int" />
    <Field Name="Field04D" Type="float" />
    <Field Name="Field051" Type="float" />
    <Field Name="Field055" Type="int" />
    <Field Name="Field059" Type="float" />
    <Field Name="Field05D" Type="int" />
    <Field Name="Field061" Type="float" />
    <Field Name="Field065" Type="float" />
  </Table>
  <Table Name="SpellEffectCameraShakes" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" ArraySize="3" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="SpellEffectEmission" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="SpellEffectGroupSize" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="float" />
  </Table>
  <Table Name="SpellEffectScaling" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Coefficient" Type="float" />
    <Field Name="Variance" Type="float" />
    <Field Name="ResourceCoefficient" Type="float" />
    <Field Name="SpellEffectID" Type="uint" />
  </Table>
  <Table Name="SpellEquippedItems" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="EquippedItemInventoryTypeMask" Type="int" />
    <Field Name="EquippedItemSubClassMask" Type="int" />
    <Field Name="EquippedItemClass" Type="byte" />
  </Table>
  <Table Name="SpellFlyout" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="ushort" />
    <Field Name="Field13" Type="int" />
  </Table>
  <Table Name="SpellFlyoutItem" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="SpellFocusObject" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="SpellIcon" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SpellInterrupts" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="AuraInterruptFlags" Type="uint" ArraySize="2" />
    <Field Name="ChannelInterruptFlags" Type="uint" ArraySize="2" />
    <Field Name="InterruptFlags" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
  </Table>
  <Table Name="SpellItemEnchantment" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EffectSpellID" Type="uint" ArraySize="3" />
    <Field Name="Name" Type="string" />
    <Field Name="EffectScalingPoints" Type="float" ArraySize="3" />
    <Field Name="TransmogCost" Type="uint" />
    <Field Name="TextureFileDataID" Type="uint" />
    <Field Name="EffectPointsMin" Type="ushort" ArraySize="3" />
    <Field Name="ItemVisual" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="RequiredSkillID" Type="ushort" />
    <Field Name="RequiredSkillRank" Type="ushort" />
    <Field Name="ItemLevel" Type="ushort" />
    <Field Name="Charges" Type="byte" />
    <Field Name="Effect" Type="byte" ArraySize="3" />
    <Field Name="ConditionID" Type="byte" />
    <Field Name="MinLevel" Type="byte" />
    <Field Name="MaxLevel" Type="byte" />
    <Field Name="ScalingClass" Type="byte" />
    <Field Name="ScalingClassRestricted" Type="byte" />
    <Field Name="PlayerConditionID" Type="uint" />
  </Table>
  <Table Name="SpellItemEnchantmentCondition" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" ArraySize="5" />
    <Field Name="Field09" Type="byte" ArraySize="5" />
    <Field Name="Field0E" Type="byte" ArraySize="5" />
    <Field Name="Field13" Type="byte" ArraySize="5" />
    <Field Name="Field18" Type="byte" ArraySize="5" />
    <Field Name="Field1D" Type="byte" />
  </Table>
  <Table Name="SpellKeyboundOverride" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="SpellLabel" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="SpellLearnSpell" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LearnSpellID" Type="uint" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="OverridesSpellID" Type="uint" />
  </Table>
  <Table Name="SpellLevels" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="BaseLevel" Type="ushort" />
    <Field Name="MaxLevel" Type="ushort" />
    <Field Name="SpellLevel" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="MaxUsableLevel" Type="byte" />
  </Table>
  <Table Name="SpellMechanic" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SpellMisc" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Attributes" Type="int" ArraySize="14" />
    <Field Name="Speed" Type="float" />
    <Field Name="MultistrikeSpeedMod" Type="float" />
    <Field Name="CastingTimeIndex" Type="ushort" />
    <Field Name="DurationIndex" Type="ushort" />
    <Field Name="RangeIndex" Type="ushort" />
    <Field Name="SchoolMask" Type="byte" />
    <Field Name="IconFileDataID" Type="int" />
    <Field Name="ActiveIconFileDataID" Type="int" />
  </Table>
  <Table Name="SpellMiscDifficulty" Build="24492">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellMissile" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="int" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="byte" />
  </Table>
  <Table Name="SpellMissileMotion" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="SpellPower" Build="24492">
    <Field Name="SpellID" Type="uint" />
    <Field Name="ManaCost" Type="uint" />
    <Field Name="ManaCostPercentage" Type="float" />
    <Field Name="ManaCostPercentagePerSecond" Type="float" />
    <Field Name="RequiredAura" Type="uint" />
    <Field Name="HealthCostPercentage" Type="float" />
    <Field Name="PowerIndex" Type="byte" />
    <Field Name="PowerType" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ManaCostPerLevel" Type="int" />
    <Field Name="ManaCostPerSecond" Type="int" />
    <Field Name="ManaCostAdditional" Type="uint" />
    <Field Name="PowerDisplayID" Type="uint" />
    <Field Name="UnitPowerBarID" Type="uint" />
  </Table>
  <Table Name="SpellPowerDifficulty" Build="24492">
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="PowerIndex" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellProceduralEffect" Build="24492">
    <Field Name="Field00" Type="int" ArraySize="4" />
    <Field Name="Field10" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellProcsPerMinute" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="BaseProcRate" Type="float" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="SpellProcsPerMinuteMod" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Coeff" Type="float" />
    <Field Name="Param" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="SpellProcsPerMinuteID" Type="byte" />
  </Table>
  <Table Name="SpellRadius" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Radius" Type="float" />
    <Field Name="RadiusPerLevel" Type="float" />
    <Field Name="RadiusMin" Type="float" />
    <Field Name="RadiusMax" Type="float" />
  </Table>
  <Table Name="SpellRange" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MinRange" Type="float" ArraySize="2" />
    <Field Name="MaxRange" Type="float" ArraySize="2" />
    <Field Name="DisplayName" Type="string" />
    <Field Name="DisplayNameShort" Type="string" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="SpellReagents" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="Reagent" Type="int" ArraySize="8" />
    <Field Name="ReagentCount" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="SpellReagentsCurrency" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="SpellScaling" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="ScalesFromItemLevel" Type="ushort" />
    <Field Name="ScalingClass" Type="int" />
    <Field Name="MinScalingLevel" Type="uint" />
    <Field Name="MaxScalingLevel" Type="uint" />
  </Table>
  <Table Name="SpellShapeshift" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="ShapeshiftExclude" Type="uint" ArraySize="2" />
    <Field Name="ShapeshiftMask" Type="uint" ArraySize="2" />
    <Field Name="StanceBarOrder" Type="byte" />
  </Table>
  <Table Name="SpellShapeshiftForm" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="WeaponDamageVariance" Type="float" />
    <Field Name="Flags" Type="uint" />
    <Field Name="CombatRoundTime" Type="ushort" />
    <Field Name="MountTypeID" Type="ushort" />
    <Field Name="CreatureType" Type="byte" />
    <Field Name="BonusActionBar" Type="byte" />
    <Field Name="AttackIconFileDataID" Type="uint" />
    <Field Name="CreatureDisplayID" Type="uint" ArraySize="4" />
    <Field Name="PresetSpellID" Type="uint" ArraySize="8" />
  </Table>
  <Table Name="SpellSpecialUnitEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="SpellTargetRestrictions" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="ConeAngle" Type="float" />
    <Field Name="Width" Type="float" />
    <Field Name="Targets" Type="uint" />
    <Field Name="TargetCreatureType" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="MaxAffectedTargets" Type="byte" />
    <Field Name="MaxTargetLevel" Type="uint" />
  </Table>
  <Table Name="SpellTotems" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="Totem" Type="uint" ArraySize="2" />
    <Field Name="RequiredTotemCategoryID" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="SpellVisual" Build="24492">
    <Field Name="Field00" Type="int" ArraySize="3" />
    <Field Name="Field0C" Type="int" ArraySize="3" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field1D" Type="int" />
    <Field Name="Field21" Type="int" />
    <Field Name="Field25" Type="ushort" />
    <Field Name="Field27" Type="int" />
    <Field Name="Field2B" Type="int" />
    <Field Name="Field2F" Type="int" />
    <Field Name="Field33" Type="ushort" />
    <Field Name="Field35" Type="int" />
    <Field Name="Field39" Type="int" />
    <Field Name="Field3D" Type="int" />
  </Table>
  <Table Name="SpellVisualAnim" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="SpellVisualColorEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="ushort" />
  </Table>
  <Table Name="SpellVisualEffectName" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="uint" />
    <Field Name="Field20" Type="uint" />
    <Field Name="Field24" Type="uint" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="int" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="ushort" />
  </Table>
  <Table Name="SpellVisualKit" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
  </Table>
  <Table Name="SpellVisualKitAreaModel" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="SpellVisualKitEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
  </Table>
  <Table Name="SpellVisualKitModelAttach" Build="24492">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" ArraySize="3" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field26" Type="float" />
    <Field Name="Field2A" Type="float" />
    <Field Name="Field2E" Type="float" />
    <Field Name="Field32" Type="float" />
    <Field Name="Field36" Type="float" />
    <Field Name="Field3A" Type="float" />
    <Field Name="Field3E" Type="float" />
    <Field Name="Field42" Type="float" />
    <Field Name="Field44" Type="ushort" DefaultValue="65535" />
    <Field Name="Field46" Type="ushort" DefaultValue="65535" />
    <Field Name="Field48" Type="ushort" DefaultValue="65535" />
    <Field Name="Field4A" Type="ushort" />
    <Field Name="Field4E" Type="int" />
    <Field Name="Field52" Type="float" />
  </Table>
  <Table Name="SpellVisualMissile" Build="24492">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="int" ArraySize="3" />
    <Field Name="Field18" Type="int" ArraySize="3" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field34" Type="int" />
    <Field Name="Field37" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="SpellXSpellVisual" Build="24492">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="uint" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field0B" Type="float" />
    <Field Name="Field0F" Type="ushort" />
    <Field Name="Field11" Type="ushort" />
    <Field Name="Field13" Type="ushort" />
    <Field Name="Field15" Type="ushort" />
    <Field Name="Field17" Type="int" />
    <Field Name="Field1B" Type="int" />
    <Field Name="Field1F" Type="byte" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
  </Table>
  <Table Name="Startup_Strings" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="string" />
  </Table>
  <Table Name="StartupFiles" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="Stationery" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" />
    <Field Name="Field7" Type="int" />
  </Table>
  <Table Name="StringLookups" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SummonProperties" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" />
    <Field Name="Category" Type="uint" />
    <Field Name="Faction" Type="uint" />
    <Field Name="Type" Type="int" />
    <Field Name="Slot" Type="int" />
  </Table>
  <Table Name="TactKey" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Key" Type="byte" ArraySize="16" />
  </Table>
  <Table Name="TactKeyLookup" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" ArraySize="4" />
  </Table>
  <Table Name="Talent" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="OverridesSpellID" Type="uint" />
    <Field Name="Description" Type="string" />
    <Field Name="SpecID" Type="ushort" />
    <Field Name="TierID" Type="byte" />
    <Field Name="ColumnIndex" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="CategoryMask" Type="byte" ArraySize="2" />
    <Field Name="ClassID" Type="byte" />
  </Table>
  <Table Name="TaxiNodes" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="uint" ArraySize="2" />
    <Field Name="Field1C" Type="int" ArraySize="2" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="string" ArraySize="2" />
    <Field Name="Field30" Type="ushort" />
    <Field Name="Field32" Type="ushort" />
    <Field Name="Field34" Type="ushort" />
    <Field Name="Field36" Type="byte" />
    <Field Name="Field37" Type="ushort" />
    <Field Name="Field39" Type="ushort" />
  </Table>
  <Table Name="TaxiPath" Build="24492">
    <Field Name="From" Type="ushort" />
    <Field Name="To" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Cost" Type="uint" />
  </Table>
  <Table Name="TaxiPathNode" Build="24492">
    <Field Name="Loc" Type="float" ArraySize="3" />
    <Field Name="PathID" Type="ushort" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="NodeIndex" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="int" />
    <Field Name="ArrivalEventId" Type="ushort" />
    <Field Name="Field1B" Type="ushort" />
  </Table>
  <Table Name="TerrainMaterial" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="int" />
    <Field Name="Field8" Type="int" />
  </Table>
  <Table Name="TerrainType" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="TerrainTypeSounds" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="TextureBlendSet" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" ArraySize="3" />
    <Field Name="Field10" Type="int" ArraySize="3" />
    <Field Name="Field1C" Type="int" ArraySize="3" />
    <Field Name="Field28" Type="float" ArraySize="3" />
    <Field Name="Field34" Type="float" ArraySize="3" />
    <Field Name="Field40" Type="float" ArraySize="4" />
    <Field Name="Field50" Type="byte" />
    <Field Name="Field51" Type="byte" />
    <Field Name="Field52" Type="byte" />
    <Field Name="Field53" Type="byte" />
  </Table>
  <Table Name="TextureFileData" Build="24492">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TotemCategory" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="CategoryMask" Type="uint" />
    <Field Name="CategoryType" Type="byte" />
  </Table>
  <Table Name="Toy" Build="24492">
    <Field Name="ItemID" Type="uint" />
    <Field Name="Description" Type="string" />
    <Field Name="Flags" Type="byte" />
    <Field Name="CategoryFilter" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TradeSkillCategory" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="TradeSkillItem" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="TransformMatrix" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
  </Table>
  <Table Name="TransmogHoliday" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="TransmogSet" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="TransmogSetGroup" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TransmogSetItem" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field3" Type="ushort" />
    <Field Name="Field5" Type="int" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="TransportAnimation" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TransportID" Type="uint" />
    <Field Name="TimeIndex" Type="uint" />
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="SequenceID" Type="byte" />
  </Table>
  <Table Name="TransportPhysics" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
  </Table>
  <Table Name="TransportRotation" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TransportID" Type="uint" />
    <Field Name="TimeIndex" Type="uint" />
    <Field Name="Position" Type="float" ArraySize="4" />
  </Table>
  <Table Name="Trophy" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="ushort" />
  </Table>
  <Table Name="UiCamera" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" ArraySize="3" />
    <Field Name="Field14" Type="int" ArraySize="3" />
    <Field Name="Field20" Type="float" ArraySize="3" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
  </Table>
  <Table Name="UiCameraType" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="UiCamFbackTransmogChrRace" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="UiCamFbackTransmogWeapon" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="UiMapPOI" Build="24492">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="UiModelScene" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
  </Table>
  <Table Name="UiModelSceneActor" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field25" Type="int" />
    <Field Name="Field29" Type="int" />
  </Table>
  <Table Name="UiModelSceneActorDisplay" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
  </Table>
  <Table Name="UiModelSceneCamera" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" ArraySize="3" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="byte" />
    <Field Name="Field41" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field46" Type="int" />
  </Table>
  <Table Name="UiTextureAtlas" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="UiTextureAtlasMember" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
  </Table>
  <Table Name="UiTextureKit" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="UnitBlood" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="int" />
    <Field Name="Field0D" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="UnitBloodLevels" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
  </Table>
  <Table Name="UnitCondition" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" ArraySize="8" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" ArraySize="8" />
    <Field Name="Field2D" Type="byte" ArraySize="7" />
  </Table>
  <Table Name="UnitPowerBar" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RegenerationPeace" Type="float" />
    <Field Name="RegenerationCombat" Type="float" />
    <Field Name="FileDataID" Type="uint" ArraySize="6" />
    <Field Name="Color" Type="uint" ArraySize="6" />
    <Field Name="Name" Type="string" />
    <Field Name="Cost" Type="string" />
    <Field Name="OutOfError" Type="string" />
    <Field Name="ToolTip" Type="string" />
    <Field Name="StartInset" Type="float" />
    <Field Name="EndInset" Type="float" />
    <Field Name="StartPower" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="CenterPower" Type="byte" />
    <Field Name="BarType" Type="byte" />
    <Field Name="MinPower" Type="uint" />
    <Field Name="MaxPower" Type="uint" />
  </Table>
  <Table Name="Vehicle" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" />
    <Field Name="TurnSpeed" Type="float" />
    <Field Name="PitchSpeed" Type="float" />
    <Field Name="PitchMin" Type="float" />
    <Field Name="PitchMax" Type="float" />
    <Field Name="MouseLookOffsetPitch" Type="float" />
    <Field Name="CameraFadeDistScalarMin" Type="float" />
    <Field Name="CameraFadeDistScalarMax" Type="float" />
    <Field Name="CameraPitchOffset" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="CameraYawOffset" Type="float" />
    <Field Name="MsslTrgtImpactTexRadius" Type="float" />
    <Field Name="SeatID" Type="ushort" ArraySize="8" />
    <Field Name="VehicleUIIndicatorID" Type="ushort" />
    <Field Name="PowerDisplayID" Type="ushort" ArraySize="3" />
    <Field Name="FlagsB" Type="byte" />
    <Field Name="UILocomotionType" Type="byte" />
    <Field Name="Field4E" Type="ushort" />
  </Table>
  <Table Name="VehicleSeat" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" ArraySize="3" />
    <Field Name="AttachmentOffset" Type="float" ArraySize="3" />
    <Field Name="EnterPreDelay" Type="float" />
    <Field Name="EnterSpeed" Type="float" />
    <Field Name="EnterGravity" Type="float" />
    <Field Name="EnterMinDuration" Type="float" />
    <Field Name="EnterMaxDuration" Type="float" />
    <Field Name="EnterMinArcHeight" Type="float" />
    <Field Name="EnterMaxArcHeight" Type="float" />
    <Field Name="ExitPreDelay" Type="float" />
    <Field Name="ExitSpeed" Type="float" />
    <Field Name="ExitGravity" Type="float" />
    <Field Name="ExitMinDuration" Type="float" />
    <Field Name="ExitMaxDuration" Type="float" />
    <Field Name="ExitMinArcHeight" Type="float" />
    <Field Name="ExitMaxArcHeight" Type="float" />
    <Field Name="PassengerYaw" Type="float" />
    <Field Name="PassengerPitch" Type="float" />
    <Field Name="PassengerRoll" Type="float" />
    <Field Name="VehicleEnterAnimDelay" Type="float" />
    <Field Name="VehicleExitAnimDelay" Type="float" />
    <Field Name="CameraEnteringDelay" Type="float" />
    <Field Name="CameraEnteringDuration" Type="float" />
    <Field Name="CameraExitingDelay" Type="float" />
    <Field Name="CameraExitingDuration" Type="float" />
    <Field Name="CameraOffset" Type="float" ArraySize="3" />
    <Field Name="CameraPosChaseRate" Type="float" />
    <Field Name="CameraFacingChaseRate" Type="float" />
    <Field Name="CameraEnteringZoom" Type="float" />
    <Field Name="CameraSeatZoomMin" Type="float" />
    <Field Name="CameraSeatZoomMax" Type="float" />
    <Field Name="UISkinFileDataID" Type="uint" />
    <Field Name="EnterAnimStart" Type="ushort" />
    <Field Name="EnterAnimLoop" Type="ushort" />
    <Field Name="RideAnimStart" Type="ushort" />
    <Field Name="RideAnimLoop" Type="ushort" />
    <Field Name="RideUpperAnimStart" Type="ushort" />
    <Field Name="RideUpperAnimLoop" Type="ushort" />
    <Field Name="ExitAnimStart" Type="ushort" />
    <Field Name="ExitAnimLoop" Type="ushort" />
    <Field Name="ExitAnimEnd" Type="ushort" />
    <Field Name="VehicleEnterAnim" Type="ushort" />
    <Field Name="VehicleExitAnim" Type="ushort" />
    <Field Name="VehicleRideAnimLoop" Type="ushort" />
    <Field Name="EnterAnimKitID" Type="ushort" />
    <Field Name="RideAnimKitID" Type="ushort" />
    <Field Name="ExitAnimKitID" Type="ushort" />
    <Field Name="VehicleEnterAnimKitID" Type="ushort" />
    <Field Name="VehicleRideAnimKitID" Type="ushort" />
    <Field Name="VehicleExitAnimKitID" Type="ushort" />
    <Field Name="CameraModeID" Type="ushort" />
    <Field Name="AttachmentID" Type="byte" />
    <Field Name="PassengerAttachmentID" Type="byte" />
    <Field Name="VehicleEnterAnimBone" Type="byte" />
    <Field Name="VehicleExitAnimBone" Type="byte" />
    <Field Name="VehicleRideAnimLoopBone" Type="byte" />
    <Field Name="VehicleAbilityDisplay" Type="byte" />
    <Field Name="EnterUISoundID" Type="uint" />
    <Field Name="ExitUISoundID" Type="uint" />
  </Table>
  <Table Name="VehicleUIIndicator" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="int" />
  </Table>
  <Table Name="VehicleUIIndSeat" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="VideoHardware" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="Vignette" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
  </Table>
  <Table Name="VocalUISounds" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="ushort" />
  </Table>
  <Table Name="WbAccessControlList" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="WbCertBlacklist" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" ArraySize="16" />
  </Table>
  <Table Name="WbCertWhitelist" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="WbPermissions" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="WeaponImpactSounds" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="int" ArraySize="11" />
    <Field Name="Field28" Type="int" ArraySize="11" />
    <Field Name="Field49" Type="ushort" ArraySize="11" />
    <Field Name="Field5F" Type="ushort" ArraySize="9" />
  </Table>
  <Table Name="WeaponSwingSounds2" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="WeaponTrail" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" ArraySize="3" />
    <Field Name="Field20" Type="uint" ArraySize="3" />
    <Field Name="Field2C" Type="uint" ArraySize="3" />
    <Field Name="Field38" Type="float" ArraySize="3" />
    <Field Name="Field44" Type="float" ArraySize="2" />
  </Table>
  <Table Name="WeaponTrailModelDef" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="WeaponTrailParam" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
  </Table>
  <Table Name="Weather" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" ArraySize="3" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="ushort" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" />
    <Field Name="Field34" Type="byte" />
    <Field Name="Field35" Type="ushort" />
    <Field Name="Field37" Type="int" />
  </Table>
  <Table Name="WindSettings" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="int" ArraySize="3" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" ArraySize="3" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" ArraySize="3" />
    <Field Name="Field38" Type="uint" />
    <Field Name="Field3C" Type="uint" />
    <Field Name="Field40" Type="byte" />
  </Table>
  <Table Name="WMOAreaTable" Build="24492">
    <Field Name="WMOGroupID" Type="int" />
    <Field Name="AreaName" Type="string" />
    <Field Name="WMOID" Type="ushort" />
    <Field Name="AmbienceID" Type="ushort" />
    <Field Name="ZoneMusic" Type="ushort" />
    <Field Name="IntroSound" Type="ushort" />
    <Field Name="AreaTableID" Type="ushort" />
    <Field Name="UWIntroSound" Type="ushort" />
    <Field Name="UWAmbience" Type="ushort" />
    <Field Name="NameSet" Type="byte" />
    <Field Name="SoundProviderPref" Type="byte" />
    <Field Name="SoundProviderPrefUnderwater" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="UWZoneMusic" Type="uint" />
  </Table>
  <Table Name="WmoMinimapTexture" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="World_PVP_Area" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
  </Table>
  <Table Name="WorldBossLockout" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="WorldChunkSounds" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="WorldEffect" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="ushort" />
  </Table>
  <Table Name="WorldElapsedTimer" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="WorldMapArea" Build="24492">
    <Field Name="AreaName" Type="string" />
    <Field Name="LocLeft" Type="float" />
    <Field Name="LocRight" Type="float" />
    <Field Name="LocTop" Type="float" />
    <Field Name="LocBottom" Type="float" />
    <Field Name="Flags" Type="uint" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="AreaID" Type="ushort" />
    <Field Name="DisplayMapID" Type="ushort" />
    <Field Name="DefaultDungeonFloor" Type="ushort" />
    <Field Name="ParentWorldMapID" Type="ushort" />
    <Field Name="LevelRangeMin" Type="byte" />
    <Field Name="LevelRangeMax" Type="byte" />
    <Field Name="BountySetID" Type="byte" />
    <Field Name="BountyBoardLocation" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PlayerConditionID" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="WorldMapContinent" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="2" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" ArraySize="2" />
    <Field Name="Field18" Type="float" ArraySize="2" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
  </Table>
  <Table Name="WorldMapOverlay" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TextureName" Type="string" />
    <Field Name="TextureWidth" Type="ushort" />
    <Field Name="TextureHeight" Type="ushort" />
    <Field Name="MapAreaID" Type="uint" />
    <Field Name="AreaID" Type="uint" ArraySize="4" />
    <Field Name="OffsetX" Type="int" />
    <Field Name="OffsetY" Type="int" />
    <Field Name="HitRectTop" Type="int" />
    <Field Name="HitRectLeft" Type="int" />
    <Field Name="HitRectBottom" Type="int" />
    <Field Name="HitRectRight" Type="int" />
    <Field Name="PlayerConditionID" Type="uint" />
    <Field Name="Flags" Type="uint" />
  </Table>
  <Table Name="WorldMapTransforms" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Region" Type="float" ArraySize="6" />
    <Field Name="RegionOffset" Type="float" ArraySize="2" />
    <Field Name="RegionScale" Type="float" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="AreaID" Type="ushort" />
    <Field Name="NewMapID" Type="ushort" />
    <Field Name="NewDungeonMapID" Type="ushort" />
    <Field Name="NewAreaID" Type="ushort" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="WorldSafeLocs" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Loc" Type="float" ArraySize="3" />
    <Field Name="Facing" Type="float" />
    <Field Name="AreaName" Type="string" />
    <Field Name="MapID" Type="ushort" />
  </Table>
  <Table Name="WorldState" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="WorldStateExpression" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="WorldStateUI" Build="24492">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" ArraySize="3" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field29" Type="int" />
    <Field Name="Field2C" Type="int" />
  </Table>
  <Table Name="WorldStateZoneSounds" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="byte" />
  </Table>
  <Table Name="ZoneIntroMusicTable" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="int" />
  </Table>
  <Table Name="ZoneLight" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="ZoneLightPoint" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="2" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="ZoneMusic" Build="24492">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="uint" ArraySize="2" />
    <Field Name="Field10" Type="uint" ArraySize="2" />
    <Field Name="Field18" Type="int" />
  </Table>
</Definition>