
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>Tools</title>
<meta name="GENERATOR" content="WinCHM">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style>
html,body { 
	/* Default Font */
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11pt;
}
</style>




<style type="text/css">

#winchm_template_navigation{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-SIZE: 11px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}

#winchm_template_title{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-WEIGHT: bold; 
	FONT-SIZE: 14px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}


</style>
</head>

<body topMargin=0 marginheight="0" marginwidth="10">
<TABLE style="HEIGHT: 50px; WIDTH: 100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
  
  <TR>
    <TD>
      <DIV id=winchm_template_navigation><FONT color=#000000>WDBX 
      Editor&nbsp;Help </FONT> </DIV>
      <DIV id=winchm_template_title><FONT color=#000080>Tools</FONT></DIV></TD>
    <TD align=right width=50><A href="Datagrid.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="CommandLine.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></TD></TR></TABLE>   

<HR>
<BR>
<P><STRONG>Defintion Editor</STRONG> </P>
<P>The definition editor allows the ability to create, edit 
and delete defintions.</P>
<P>These can also&nbsp;be&nbsp;created and 
edited&nbsp;manually. Any *.xml&nbsp;files in the Definition will be imported 
provided they meet the basic rules of being named correctly and having a single 
int32&nbsp;ID column.</P>
<P>Available Types:</P>
<UL>
  <LI>
<DIV> byte</DIV>
  <LI>
<DIV> float</DIV>
  <LI>
<DIV> int</DIV>
  <LI>
<DIV>loc - This is for pre-Cata strings whereby the DBC 
  file contains localisations and a mask</DIV>
  <LI>
<DIV> long</DIV>
  <LI>
<DIV> sbyte</DIV>
  <LI>
<DIV> short</DIV>
  <LI>
<DIV> string</DIV>
  <LI>
<DIV> uint</DIV>
  <LI>
<DIV> ulong</DIV>
  <LI>
<DIV> ushort</DIV></LI></UL>
<P>There are also a number of additional settings that are 
rarely used.</P>
<UL>
  <LI>
<DIV>AutoGenerated - This is used to add a column that 
  does not read data from the file i.e. CharBaseInfo where there is no 
  ID field</DIV>
  <LI>
<DIV>ArraySize - Allows the grouping of simlar fields. The 
  column name will be incremented when displayed i.e. Field, Field_1, Field_2 etc</DIV>
  <LI>
<DIV>Padding - This is used where files are nonuniformly 
  padded such as having a padding in the middle of the file</DIV></LI></UL><BR> <BR><BR>

<HR>

<P></FONT></P>
<P align=right><A href="Datagrid.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="Commandline.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></P>
</body>
</html>
