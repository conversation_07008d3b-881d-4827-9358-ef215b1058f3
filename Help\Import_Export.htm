
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>Import Export</title>
<meta name="GENERATOR" content="WinCHM">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style>
html,body { 
	/* Default Font */
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11pt;
}
</style>




<style type="text/css">

#winchm_template_navigation{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-SIZE: 11px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}

#winchm_template_title{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-WEIGHT: bold; 
	FONT-SIZE: 14px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}


</style>
</head>

<body topMargin=0 marginheight="0" marginwidth="10">
<TABLE style="HEIGHT: 50px; WIDTH: 100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
  
  <TR>
    <TD>
      <DIV id=winchm_template_navigation><FONT color=#000000>WDBX 
      Editor&nbsp;Help </FONT> </DIV>
      <DIV id=winchm_template_title><FONT color=#000080>Import Export</FONT></DIV></TD>
    <TD align=right width=50><A href="FindReplace.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="Datagrid.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></TD></TR></TABLE>   

<HR>
<BR>
<P><STRONG>Export</STRONG></P>
<P>To SQL - When exporting directly to a database, the system will create a 
table named `db_&lt;FileName&gt;` which is then populated using the MySQL Bulk 
Copy which is faster than importing a SQL file.</P>
<P>To SQL File - This will generate a MySQL formatted file</P>
<P>To CSV - This will produce a comma seperated file, with escaped strings</P>
<P>To MPQ - This will export the selected file to an MPQ archive, if an existing 
archive is selected then the file will be appended to it otherwise a new archive 
will be created</P>
<P><STRONG>Import</STRONG></P>
<P>From SQL - This will import data directly from a MySQL database</P>
<P>From CSV</P>
<I><P>Import Notes:</P>
<UL>
  <LI>Columns currently need to be identical to the definition file i.e. same column names, types and count</LI>
  <LI>Importing data has the choice of:</LI>
  <OL>
    <LI>Importing new rows only</LI>
    <LI>Updating different rows and importing new rows</LI>
    <LI>Overwriting all existing data</LI></OL></UL></I><BR> <BR><BR>

<HR>

<P></FONT></P>
<P align=right><A href="FindReplace.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="Datagrid.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></P>
</body>
</html>
