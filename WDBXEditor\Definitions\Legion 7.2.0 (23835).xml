<?xml version="1.0"?>
<Definition xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <Table Name="Achievement" Build="23835">
    <Field Name="Title" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="Flags" Type="uint" />
    <Field Name="Reward" Type="string" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="Supercedes" Type="ushort" />
    <Field Name="Category" Type="ushort" />
    <Field Name="UIOrder" Type="ushort" />
    <Field Name="SharesCriteria" Type="ushort" />
    <Field Name="CriteriaTree" Type="ushort" />
    <Field Name="Faction" Type="byte" />
    <Field Name="Points" Type="byte" />
    <Field Name="MinimumCriteria" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="IconFileDataID" Type="uint" />
  </Table>
  <Table Name="Achievement_Category" Build="23835">
    <Field Name="Field0" Type="string" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AdventureJournal" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="string" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" ArraySize="2" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" ArraySize="2" />
    <Field Name="Field35" Type="byte" />
    <Field Name="Field36" Type="ushort" />
    <Field Name="Field38" Type="byte" />
  </Table>
  <Table Name="AdventureMapPOI" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="ushort" />
    <Field Name="Field1B" Type="ushort" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="ushort" />
    <Field Name="Field23" Type="ushort" />
    <Field Name="Field25" Type="ushort" />
  </Table>
  <Table Name="AnimationData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="byte" ArraySize="4" />
  </Table>
  <Table Name="AnimKit" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="OneShotDuration" Type="uint" />
    <Field Name="OneShotStopAnimKitID" Type="ushort" />
    <Field Name="LowDefAnimKitID" Type="ushort" />
  </Table>
  <Table Name="AnimKitBoneSet" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="AnimKitBoneSetAlias" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
  </Table>
  <Table Name="AnimKitConfig" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
  </Table>
  <Table Name="AnimKitConfigBoneSet" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="AnimKitPriority" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
  </Table>
  <Table Name="AnimKitReplacement" Build="23835">
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AnimKitSegment" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="byte" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="ushort" />
  </Table>
  <Table Name="AnimReplacement" Build="23835">
    <Field Name="Field0" Type="ushort" />
    <Field Name="Field2" Type="ushort" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AnimReplacementSet" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
  </Table>
  <Table Name="AreaAssignment" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="AreaFarClipOverride" Build="23835">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="int" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AreaGroupMember" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AreaGroupID" Type="ushort" />
    <Field Name="AreaID" Type="ushort" />
  </Table>
  <Table Name="AreaPOI" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" ArraySize="2" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="uint" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
  </Table>
  <Table Name="AreaPOIState" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="AreaTable" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" ArraySize="2" />
    <Field Name="ZoneName" Type="string" />
    <Field Name="AmbientMultiplier" Type="float" />
    <Field Name="AreaName" Type="string" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="ParentAreaID" Type="ushort" />
    <Field Name="AreaBit" Type="ushort" />
    <Field Name="AmbienceID" Type="ushort" />
    <Field Name="ZoneMusic" Type="ushort" />
    <Field Name="IntroSound" Type="ushort" />
    <Field Name="LiquidTypeID" Type="ushort" ArraySize="4" />
    <Field Name="UWZoneMusic" Type="ushort" />
    <Field Name="UWAmbience" Type="ushort" />
    <Field Name="PvPCombatWorldStateID" Type="ushort" />
    <Field Name="SoundProviderPref" Type="byte" />
    <Field Name="SoundProviderPrefUnderwater" Type="byte" />
    <Field Name="ExplorationLevel" Type="byte" />
    <Field Name="FactionGroupMask" Type="byte" />
    <Field Name="MountFlags" Type="byte" />
    <Field Name="WildBattlePetLevelMin" Type="byte" />
    <Field Name="WildBattlePetLevelMax" Type="byte" />
    <Field Name="WindSettingsID" Type="byte" />
    <Field Name="UWIntroSound" Type="uint" />
  </Table>
  <Table Name="AreaTrigger" Build="23835">
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="Radius" Type="float" />
    <Field Name="BoxLength" Type="float" />
    <Field Name="BoxWidth" Type="float" />
    <Field Name="BoxHeight" Type="float" />
    <Field Name="BoxYaw" Type="float" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="PhaseID" Type="ushort" />
    <Field Name="PhaseGroupID" Type="ushort" />
    <Field Name="ShapeID" Type="ushort" />
    <Field Name="AreaTriggerActionSetID" Type="ushort" />
    <Field Name="PhaseUseFlags" Type="byte" />
    <Field Name="ShapeType" Type="byte" />
    <Field Name="Flag" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AreaTriggerActionSet" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
  </Table>
  <Table Name="AreaTriggerBox" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="float" ArraySize="2" />
  </Table>
  <Table Name="AreaTriggerCylinder" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
  </Table>
  <Table Name="AreaTriggerSphere" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="float" />
  </Table>
  <Table Name="ArmorLocation" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Modifier" Type="float" ArraySize="5" />
  </Table>
  <Table Name="Artifact" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="BarConnectedColor" Type="uint" />
    <Field Name="BarDisconnectedColor" Type="uint" />
    <Field Name="TitleColor" Type="uint" />
    <Field Name="ClassUiTextureKitID" Type="ushort" />
    <Field Name="SpecID" Type="ushort" />
    <Field Name="ArtifactCategoryID" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="UiModelSceneID" Type="uint" />
    <Field Name="SpellVisualKitID" Type="uint" />
  </Table>
  <Table Name="ArtifactAppearance" Build="23835">
    <Field Name="Name" Type="string" />
    <Field Name="SwatchColor" Type="uint" />
    <Field Name="ModelDesaturation" Type="float" />
    <Field Name="ModelAlpha" Type="float" />
    <Field Name="ShapeshiftDisplayID" Type="uint" />
    <Field Name="ArtifactAppearanceSetID" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="DisplayIndex" Type="byte" />
    <Field Name="AppearanceModID" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ModifiesShapeshiftFormDisplay" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PlayerConditionID" Type="uint" />
    <Field Name="ItemAppearanceID" Type="uint" />
    <Field Name="AltItemAppearanceID" Type="uint" />
  </Table>
  <Table Name="ArtifactAppearanceSet" Build="23835">
    <Field Name="Name" Type="string" />
    <Field Name="Name2" Type="string" />
    <Field Name="UiCameraID" Type="ushort" />
    <Field Name="AltHandUICameraID" Type="ushort" />
    <Field Name="ArtifactID" Type="byte" />
    <Field Name="DisplayIndex" Type="byte" />
    <Field Name="AttachmentPoint" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ArtifactCategory" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ArtifactKnowledgeCurrencyID" Type="ushort" />
    <Field Name="ArtifactKnowledgeMultiplierCurveID" Type="ushort" />
  </Table>
  <Table Name="ArtifactPower" Build="23835">
    <Field Name="Pos" Type="float" ArraySize="2" />
    <Field Name="ArtifactID" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="MaxRank" Type="byte" />
    <Field Name="ArtifactTier" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RelicType" Type="int" />
  </Table>
  <Table Name="ArtifactPowerLink" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FromArtifactPowerID" Type="ushort" />
    <Field Name="ToArtifactPowerID" Type="ushort" />
  </Table>
  <Table Name="ArtifactPowerPicker" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
  </Table>
  <Table Name="ArtifactPowerRank" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="Value" Type="float" />
    <Field Name="ArtifactPowerID" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Rank" Type="byte" />
  </Table>
  <Table Name="ArtifactQuestXP" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Exp" Type="uint" ArraySize="10" />
  </Table>
  <Table Name="ArtifactTier" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="ArtifactUnlock" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="ushort" />
  </Table>
  <Table Name="AuctionHouse" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="FactionID" Type="ushort" />
    <Field Name="DepositRate" Type="byte" />
    <Field Name="ConsignmentRate" Type="byte" />
  </Table>
  <Table Name="BankBagSlotPrices" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Cost" Type="uint" />
  </Table>
  <Table Name="BannedAddOns" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Version" Type="string" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="BarberShopStyle" Build="23835">
    <Field Name="DisplayName" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="CostModifier" Type="float" />
    <Field Name="Type" Type="byte" />
    <Field Name="Race" Type="byte" />
    <Field Name="Sex" Type="byte" />
    <Field Name="Data" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="BattlemasterList" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="GameType" Type="string" />
    <Field Name="ShortDescription" Type="string" />
    <Field Name="LongDescription" Type="string" />
    <Field Name="MapID" Type="ushort" ArraySize="16" />
    <Field Name="HolidayWorldState" Type="ushort" />
    <Field Name="PlayerConditionID" Type="ushort" />
    <Field Name="InstanceType" Type="byte" />
    <Field Name="GroupsAllowed" Type="byte" />
    <Field Name="MaxGroupSize" Type="byte" />
    <Field Name="MinLevel" Type="byte" />
    <Field Name="MaxLevel" Type="byte" />
    <Field Name="RatedPlayers" Type="byte" />
    <Field Name="MinPlayers" Type="byte" />
    <Field Name="MaxPlayers" Type="byte" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="BattlePetAbility" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
  </Table>
  <Table Name="BattlePetAbilityEffect" Build="23835">
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" ArraySize="6" />
    <Field Name="Field14" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="BattlePetAbilityState" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="BattlePetAbilityTurn" Build="23835">
    <Field Name="Field0" Type="ushort" />
    <Field Name="Field2" Type="ushort" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="BattlePetBreedQuality" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Modifier" Type="float" />
    <Field Name="Quality" Type="byte" />
  </Table>
  <Table Name="BattlePetBreedState" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Value" Type="ushort" />
    <Field Name="BreedID" Type="byte" />
    <Field Name="State" Type="byte" />
  </Table>
  <Table Name="BattlePetEffectProperties" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" ArraySize="6" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="BattlePetNPCTeamMember" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
  </Table>
  <Table Name="BattlePetSpecies" Build="23835">
    <Field Name="CreatureID" Type="uint" />
    <Field Name="IconFileID" Type="uint" />
    <Field Name="SummonSpellID" Type="uint" />
    <Field Name="SourceText" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="PetType" Type="byte" />
    <Field Name="Source" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CardModelSceneID" Type="uint" />
    <Field Name="LoadoutModelSceneID" Type="uint" />
  </Table>
  <Table Name="BattlePetSpeciesState" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Value" Type="int" />
    <Field Name="SpeciesID" Type="ushort" />
    <Field Name="State" Type="byte" />
  </Table>
  <Table Name="BattlePetSpeciesXAbility" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="BattlePetState" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="BattlePetVisual" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
  </Table>
  <Table Name="BeamEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
  </Table>
  <Table Name="BoneWindModifierModel" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="uint" />
  </Table>
  <Table Name="BoneWindModifiers" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" />
  </Table>
  <Table Name="Bounty" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="BountySet" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="BroadcastText" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MaleText" Type="string" />
    <Field Name="FemaleText" Type="string" />
    <Field Name="EmoteID" Type="ushort" ArraySize="3" />
    <Field Name="EmoteDelay" Type="ushort" ArraySize="3" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Language" Type="byte" />
    <Field Name="Type" Type="byte" />
    <Field Name="SoundID" Type="uint" ArraySize="2" />
    <Field Name="PlayerConditionID" Type="uint" />
  </Table>
  <Table Name="CameraEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
  </Table>
  <Table Name="CameraEffectEntry" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
  </Table>
  <Table Name="CameraMode" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="int" ArraySize="3" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="byte" />
  </Table>
  <Table Name="CastableRaidBuffs" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="CelestialBody" Build="23835">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" ArraySize="2" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" ArraySize="2" />
    <Field Name="Field20" Type="float" ArraySize="2" />
    <Field Name="Field28" Type="float" ArraySize="2" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" ArraySize="2" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="float" ArraySize="3" />
    <Field Name="Field4C" Type="float" />
    <Field Name="Field50" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Cfg_Categories" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="Cfg_Configs" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="Cfg_Regions" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
  </Table>
  <Table Name="CharacterFaceBoneSet" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="CharacterFacialHairStyles" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" ArraySize="5" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
  </Table>
  <Table Name="CharacterLoadout" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="CharacterLoadoutItem" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="CharBaseInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
  </Table>
  <Table Name="CharBaseSection" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="CharComponentTextureLayouts" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="CharComponentTextureSections" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="CharHairGeosets" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="CharSections" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TextureFileDataID" Type="uint" ArraySize="3" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="Race" Type="byte" />
    <Field Name="Gender" Type="byte" />
    <Field Name="GenType" Type="byte" />
    <Field Name="Type" Type="byte" />
    <Field Name="Color" Type="byte" />
  </Table>
  <Table Name="CharShipment" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
  </Table>
  <Table Name="CharShipmentContainer" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="byte" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
  </Table>
  <Table Name="CharStartOutfit" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="int" ArraySize="24" />
    <Field Name="PetDisplayID" Type="uint" />
    <Field Name="RaceID" Type="byte" />
    <Field Name="ClassID" Type="byte" />
    <Field Name="GenderID" Type="byte" />
    <Field Name="OutfitID" Type="byte" />
    <Field Name="PetFamilyID" Type="byte" />
  </Table>
  <Table Name="CharTitles" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="NameMale" Type="string" />
    <Field Name="NameFemale" Type="string" />
    <Field Name="MaskID" Type="ushort" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="ChatChannels" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" />
    <Field Name="Name" Type="string" />
    <Field Name="Shortcut" Type="string" />
    <Field Name="FactionGroup" Type="byte" />
  </Table>
  <Table Name="ChatProfanity" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="ChrClasses" Build="23835">
    <Field Name="PetNameToken" Type="string" />
    <Field Name="Name" Type="string" />
    <Field Name="NameFemale" Type="string" />
    <Field Name="NameMale" Type="string" />
    <Field Name="Filename" Type="string" />
    <Field Name="CreateScreenFileDataID" Type="uint" />
    <Field Name="SelectScreenFileDataID" Type="uint" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="LowResScreenFileDataID" Type="uint" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="CinematicSequenceID" Type="ushort" />
    <Field Name="DefaultSpec" Type="ushort" />
    <Field Name="PowerType" Type="byte" />
    <Field Name="SpellClassSet" Type="byte" />
    <Field Name="AttackPowerPerStrength" Type="byte" />
    <Field Name="AttackPowerPerAgility" Type="byte" />
    <Field Name="RangedAttackPowerPerAgility" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ChrClassesXPowerTypes" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ClassID" Type="byte" />
    <Field Name="PowerType" Type="byte" />
  </Table>
  <Table Name="ChrClassRaceSex" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="ChrClassTitle" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="ChrClassUIDisplay" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" />
    <Field Name="Field7" Type="ushort" />
  </Table>
  <Table Name="ChrClassVillain" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="ChrRaces" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" />
    <Field Name="ClientPrefix" Type="string" />
    <Field Name="ClientFileString" Type="string" />
    <Field Name="Name" Type="string" />
    <Field Name="NameFemale" Type="string" />
    <Field Name="NameMale" Type="string" />
    <Field Name="FacialHairCustomization" Type="string" ArraySize="2" />
    <Field Name="HairCustomization" Type="string" />
    <Field Name="CreateScreenFileDataID" Type="uint" />
    <Field Name="SelectScreenFileDataID" Type="uint" />
    <Field Name="MaleCustomizeOffset" Type="float" ArraySize="3" />
    <Field Name="FemaleCustomizeOffset" Type="float" ArraySize="3" />
    <Field Name="LowResScreenFileDataID" Type="uint" />
    <Field Name="FactionID" Type="ushort" />
    <Field Name="MaleDisplayID" Type="ushort" />
    <Field Name="FemaleDisplayID" Type="ushort" />
    <Field Name="ResSicknessSpellID" Type="ushort" />
    <Field Name="SplashSoundID" Type="ushort" />
    <Field Name="CinematicSequenceID" Type="ushort" />
    <Field Name="BaseLanguage" Type="byte" />
    <Field Name="CreatureType" Type="byte" />
    <Field Name="TeamID" Type="byte" />
    <Field Name="RaceRelated" Type="byte" />
    <Field Name="UnalteredVisualRaceID" Type="byte" />
    <Field Name="CharComponentTextureLayoutID" Type="byte" />
    <Field Name="DefaultClassID" Type="byte" />
    <Field Name="NeutralRaceID" Type="byte" />
    <Field Name="ItemAppearanceFrameRaceID" Type="byte" />
    <Field Name="CharComponentTexLayoutHiResID" Type="byte" />
    <Field Name="HighResMaleDisplayID" Type="uint" />
    <Field Name="HighResFemaleDisplayID" Type="uint" />
    <Field Name="Field32" Type="uint" ArraySize="3" />
  </Table>
  <Table Name="ChrSpecialization" Build="23835">
    <Field Name="MasterySpellID" Type="uint" ArraySize="2" />
    <Field Name="Name" Type="string" />
    <Field Name="Name2" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="ClassID" Type="byte" />
    <Field Name="OrderIndex" Type="byte" />
    <Field Name="PetTalentType" Type="byte" />
    <Field Name="Role" Type="byte" />
    <Field Name="PrimaryStatOrder" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="Flags" Type="uint" />
    <Field Name="AnimReplacementSetID" Type="uint" />
  </Table>
  <Table Name="ChrUpgradeBucket" Build="23835">
    <Field Name="Field0" Type="ushort" />
    <Field Name="Field2" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ChrUpgradeBucketSpell" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="ChrUpgradeTier" Build="23835">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="CinematicCamera" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundID" Type="uint" />
    <Field Name="Origin" Type="float" ArraySize="3" />
    <Field Name="OriginFacing" Type="float" />
    <Field Name="ModelFileDataID" Type="uint" />
  </Table>
  <Table Name="CinematicSequences" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundID" Type="uint" />
    <Field Name="Camera" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="CloakDampening" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="5" />
    <Field Name="Field18" Type="float" ArraySize="5" />
    <Field Name="Field2C" Type="int" ArraySize="2" />
    <Field Name="Field34" Type="float" ArraySize="2" />
    <Field Name="Field3C" Type="int" />
    <Field Name="Field40" Type="float" />
    <Field Name="Field44" Type="float" />
  </Table>
  <Table Name="CombatCondition" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" ArraySize="2" />
    <Field Name="Field0E" Type="ushort" ArraySize="2" />
    <Field Name="Field12" Type="byte" ArraySize="2" />
    <Field Name="Field14" Type="byte" ArraySize="2" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" ArraySize="2" />
    <Field Name="Field19" Type="byte" ArraySize="2" />
    <Field Name="Field1B" Type="byte" />
  </Table>
  <Table Name="ComponentModelFileData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
  </Table>
  <Table Name="ComponentTextureFileData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="Contribution" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="int" ArraySize="4" />
    <Field Name="Field20" Type="int" />
  </Table>
  <Table Name="ConversationLine" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="Creature" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" ArraySize="3" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" ArraySize="4" />
    <Field Name="Field24" Type="float" ArraySize="4" />
    <Field Name="Field34" Type="string" />
    <Field Name="Field38" Type="string" />
    <Field Name="Field3C" Type="string" />
    <Field Name="Field40" Type="string" />
    <Field Name="Field44" Type="byte" />
    <Field Name="Field45" Type="byte" />
    <Field Name="Field46" Type="byte" />
    <Field Name="Field47" Type="byte" />
  </Table>
  <Table Name="CreatureDifficulty" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="int" ArraySize="7" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
  </Table>
  <Table Name="CreatureDisplayInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CreatureModelScale" Type="float" />
    <Field Name="ModelID" Type="ushort" />
    <Field Name="NPCSoundID" Type="ushort" />
    <Field Name="SizeClass" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="Gender" Type="byte" />
    <Field Name="ExtendedDisplayInfoID" Type="uint" />
    <Field Name="TextureVariation" Type="uint" ArraySize="3" />
    <Field Name="PortraitTextureFileDataID" Type="uint" />
    <Field Name="CreatureModelAlpha" Type="byte" DefaultValue="255" />
    <Field Name="SoundID" Type="ushort" />
    <Field Name="PlayerModelScale" Type="float" />
    <Field Name="PortraitCreatureDisplayInfoID" Type="uint" />
    <Field Name="BloodID" Type="byte" />
    <Field Name="ParticleColorID" Type="ushort" />
    <Field Name="CreatureGeosetData" Type="uint" />
    <Field Name="ObjectEffectPackageID" Type="ushort" />
    <Field Name="AnimReplacementSetID" Type="ushort" />
    <Field Name="UnarmedWeaponSubclass" Type="byte" DefaultValue="255" />
    <Field Name="StateSpellVisualKitID" Type="uint" />
    <Field Name="InstanceOtherPlayerPetScale" Type="float" />
    <Field Name="MountSpellVisualKitID" Type="uint" />
  </Table>
  <Table Name="CreatureDisplayInfoCond" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="int" ArraySize="2" />
    <Field Name="Field10" Type="uint" ArraySize="2" />
    <Field Name="Field18" Type="uint" ArraySize="2" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="ushort" />
    <Field Name="Field2D" Type="int" ArraySize="2" />
  </Table>
  <Table Name="CreatureDisplayInfoEvt" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="CreatureDisplayInfoExtra" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FileDataID" Type="uint" />
    <Field Name="HDFileDataID" Type="uint" />
    <Field Name="DisplayRaceID" Type="byte" />
    <Field Name="DisplaySexID" Type="byte" />
    <Field Name="DisplayClassID" Type="byte" />
    <Field Name="SkinID" Type="byte" />
    <Field Name="FaceID" Type="byte" />
    <Field Name="HairStyleID" Type="byte" />
    <Field Name="HairColorID" Type="byte" />
    <Field Name="FacialHairID" Type="byte" />
    <Field Name="CustomDisplayOption" Type="byte" ArraySize="3" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="CreatureDisplayInfoTrn" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="float" />
  </Table>
  <Table Name="CreatureDispXUiCamera" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="CreatureFamily" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MinScale" Type="float" />
    <Field Name="MaxScale" Type="float" />
    <Field Name="Name" Type="string" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="SkillLine" Type="ushort" ArraySize="2" />
    <Field Name="PetFoodMask" Type="ushort" />
    <Field Name="MinScaleLevel" Type="byte" />
    <Field Name="MaxScaleLevel" Type="byte" />
    <Field Name="PetTalentType" Type="byte" />
  </Table>
  <Table Name="CreatureImmunities" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" ArraySize="2" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" ArraySize="8" />
    <Field Name="Field1A" Type="ushort" ArraySize="15" />
  </Table>
  <Table Name="CreatureModelData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ModelScale" Type="float" />
    <Field Name="FootprintTextureLength" Type="float" />
    <Field Name="FootprintTextureWidth" Type="float" />
    <Field Name="FootprintParticleScale" Type="float" />
    <Field Name="CollisionWidth" Type="float" />
    <Field Name="CollisionHeight" Type="float" />
    <Field Name="MountHeight" Type="float" />
    <Field Name="GeoBox" Type="float" ArraySize="6" />
    <Field Name="WorldEffectScale" Type="float" />
    <Field Name="AttachedEffectScale" Type="float" />
    <Field Name="MissileCollisionRadius" Type="float" />
    <Field Name="MissileCollisionPush" Type="float" />
    <Field Name="MissileCollisionRaise" Type="float" />
    <Field Name="OverrideLootEffectScale" Type="float" />
    <Field Name="OverrideNameScale" Type="float" />
    <Field Name="OverrideSelectionRadius" Type="float" />
    <Field Name="TamedPetBaseScale" Type="float" />
    <Field Name="HoverHeight" Type="float" />
    <Field Name="Flags" Type="uint" />
    <Field Name="FileDataID" Type="uint" />
    <Field Name="SizeClass" Type="uint" />
    <Field Name="BloodID" Type="uint" />
    <Field Name="FootprintTextureID" Type="uint" />
    <Field Name="FoleyMaterialID" Type="uint" />
    <Field Name="FootstepEffectID" Type="uint" />
    <Field Name="DeathThudEffectID" Type="uint" />
    <Field Name="SoundID" Type="uint" />
    <Field Name="CreatureGeosetDataID" Type="uint" />
  </Table>
  <Table Name="CreatureMovementInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="float" />
  </Table>
  <Table Name="CreatureSoundData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="float" />
    <Field Name="Field008" Type="float" />
    <Field Name="Field00C" Type="byte" />
    <Field Name="Field00D" Type="int" />
    <Field Name="Field010" Type="int" />
    <Field Name="Field013" Type="int" />
    <Field Name="Field016" Type="int" />
    <Field Name="Field019" Type="byte" />
    <Field Name="Field01A" Type="int" />
    <Field Name="Field01D" Type="int" />
    <Field Name="Field020" Type="int" />
    <Field Name="Field023" Type="ushort" />
    <Field Name="Field025" Type="int" />
    <Field Name="Field028" Type="int" />
    <Field Name="Field02B" Type="int" />
    <Field Name="Field02E" Type="int" />
    <Field Name="Field031" Type="int" ArraySize="5" />
    <Field Name="Field040" Type="int" ArraySize="4" />
    <Field Name="Field04C" Type="byte" />
    <Field Name="Field04D" Type="int" />
    <Field Name="Field050" Type="int" />
    <Field Name="Field053" Type="int" />
    <Field Name="Field056" Type="ushort" />
    <Field Name="Field058" Type="ushort" />
    <Field Name="Field05A" Type="ushort" />
    <Field Name="Field05C" Type="int" />
    <Field Name="Field05F" Type="ushort" />
    <Field Name="Field061" Type="int" />
    <Field Name="Field064" Type="int" />
    <Field Name="Field067" Type="ushort" />
    <Field Name="Field069" Type="int" />
    <Field Name="Field06C" Type="int" />
    <Field Name="Field06F" Type="int" />
    <Field Name="Field072" Type="byte" />
    <Field Name="Field073" Type="int" />
    <Field Name="Field076" Type="int" />
    <Field Name="Field079" Type="int" />
  </Table>
  <Table Name="CreatureType" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="CreatureXContribution" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="Criteria" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Asset" Type="uint" />
    <Field Name="StartAsset" Type="uint" />
    <Field Name="FailAsset" Type="uint" />
    <Field Name="StartTimer" Type="ushort" />
    <Field Name="ModifierTreeId" Type="ushort" />
    <Field Name="EligibilityWorldStateID" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="StartEvent" Type="byte" />
    <Field Name="FailEvent" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="EligibilityWorldStateValue" Type="byte" />
  </Table>
  <Table Name="CriteriaTree" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Amount" Type="uint" />
    <Field Name="Description" Type="string" />
    <Field Name="Parent" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="Operator" Type="byte" />
    <Field Name="CriteriaID" Type="uint" />
    <Field Name="OrderIndex" Type="int" />
  </Table>
  <Table Name="CriteriaTreeXEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="CurrencyCategory" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="CurrencyTypes" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="InventoryIcon" Type="string" ArraySize="2" />
    <Field Name="MaxQty" Type="uint" />
    <Field Name="MaxEarnablePerWeek" Type="uint" />
    <Field Name="Flags" Type="uint" />
    <Field Name="Description" Type="string" />
    <Field Name="CategoryID" Type="byte" />
    <Field Name="SpellCategory" Type="byte" />
    <Field Name="Quality" Type="byte" />
    <Field Name="SpellWeight" Type="uint" />
  </Table>
  <Table Name="Curve" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="byte" />
    <Field Name="Unused" Type="byte" />
  </Table>
  <Table Name="CurvePoint" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Position" Type="float" ArraySize="2" />
    <Field Name="CurveID" Type="ushort" />
    <Field Name="Index" Type="byte" />
  </Table>
  <Table Name="DBCache" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="DeathThudLookups" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="DecalProperties" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" ArraySize="4" />
  </Table>
  <Table Name="DeclinedWord" Build="23835">
    <Field Name="Field0" Type="uint" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="DeclinedWordCases" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="DestructibleModelData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="StateDamagedDisplayID" Type="ushort" />
    <Field Name="StateDestroyedDisplayID" Type="ushort" />
    <Field Name="StateRebuildingDisplayID" Type="ushort" />
    <Field Name="StateSmokeDisplayID" Type="ushort" />
    <Field Name="HealEffectSpeed" Type="ushort" />
    <Field Name="StateDamagedImpactEffectDoodadSet" Type="byte" />
    <Field Name="StateDamagedAmbientDoodadSet" Type="byte" />
    <Field Name="StateDamagedNameSet" Type="byte" />
    <Field Name="StateDestroyedDestructionDoodadSet" Type="byte" />
    <Field Name="StateDestroyedImpactEffectDoodadSet" Type="byte" />
    <Field Name="StateDestroyedAmbientDoodadSet" Type="byte" />
    <Field Name="StateDestroyedNameSet" Type="byte" />
    <Field Name="StateRebuildingDestructionDoodadSet" Type="byte" />
    <Field Name="StateRebuildingImpactEffectDoodadSet" Type="byte" />
    <Field Name="StateRebuildingAmbientDoodadSet" Type="byte" />
    <Field Name="StateRebuildingNameSet" Type="byte" />
    <Field Name="StateSmokeInitDoodadSet" Type="byte" />
    <Field Name="StateSmokeAmbientDoodadSet" Type="byte" />
    <Field Name="StateSmokeNameSet" Type="byte" />
    <Field Name="EjectDirection" Type="byte" />
    <Field Name="DoNotHighlight" Type="byte" />
    <Field Name="HealEffect" Type="byte" />
  </Table>
  <Table Name="DeviceBlacklist" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="DeviceDefaultSettings" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="Difficulty" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="GroupSizeHealthCurveID" Type="ushort" />
    <Field Name="GroupSizeDmgCurveID" Type="ushort" />
    <Field Name="GroupSizeSpellPointsCurveID" Type="ushort" />
    <Field Name="FallbackDifficultyID" Type="byte" />
    <Field Name="InstanceType" Type="byte" />
    <Field Name="MinPlayers" Type="byte" />
    <Field Name="MaxPlayers" Type="byte" />
    <Field Name="OldEnumValue" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ToggleDifficultyID" Type="byte" />
    <Field Name="ItemBonusTreeModID" Type="byte" />
    <Field Name="OrderIndex" Type="byte" />
  </Table>
  <Table Name="DissolveEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="byte" />
  </Table>
  <Table Name="DriverBlacklist" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
  </Table>
  <Table Name="DungeonEncounter" Build="23835">
    <Field Name="Name" Type="string" />
    <Field Name="CreatureDisplayID" Type="uint" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="Bit" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="OrderIndex" Type="int" />
    <Field Name="TextureFileDataID" Type="uint" />
  </Table>
  <Table Name="DungeonMap" Build="23835">
    <Field Name="Field00" Type="int" ArraySize="2" />
    <Field Name="Field08" Type="int" ArraySize="2" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="DungeonMapChunk" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
  </Table>
  <Table Name="DurabilityCosts" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WeaponSubClassCost" Type="ushort" ArraySize="21" />
    <Field Name="ArmorSubClassCost" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="DurabilityQuality" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="QualityMod" Type="float" />
  </Table>
  <Table Name="EdgeGlowEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="byte" />
  </Table>
  <Table Name="Emotes" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EmoteSlashCommand" Type="string" />
    <Field Name="SpellVisualKitID" Type="uint" />
    <Field Name="EmoteFlags" Type="uint" />
    <Field Name="AnimID" Type="ushort" />
    <Field Name="EmoteSpecProc" Type="byte" />
    <Field Name="EmoteSpecProcParam" Type="uint" />
    <Field Name="EmoteSoundID" Type="uint" />
    <Field Name="ClassMask" Type="int" />
    <Field Name="RaceMask" Type="int" />
  </Table>
  <Table Name="EmotesText" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="EmoteID" Type="ushort" />
  </Table>
  <Table Name="EmotesTextData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="EmotesTextSound" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EmotesTextId" Type="ushort" />
    <Field Name="RaceId" Type="byte" />
    <Field Name="SexId" Type="byte" />
    <Field Name="ClassId" Type="byte" />
    <Field Name="SoundId" Type="uint" />
  </Table>
  <Table Name="EnvironmentalDamage" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="Exhaustion" Build="23835">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Faction" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ReputationRaceMask" Type="uint" ArraySize="4" />
    <Field Name="ReputationBase" Type="int" ArraySize="4" />
    <Field Name="ParentFactionMod" Type="float" ArraySize="2" />
    <Field Name="Name" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="ReputationMax" Type="uint" ArraySize="4" />
    <Field Name="ReputationIndex" Type="int" />
    <Field Name="ParagonFactionID" Type="ushort" />
    <Field Name="ReputationClassMask" Type="ushort" ArraySize="4" />
    <Field Name="ReputationFlags" Type="ushort" ArraySize="4" />
    <Field Name="ParentFactionID" Type="ushort" />
    <Field Name="ParentFactionCap" Type="byte" ArraySize="2" />
    <Field Name="Expansion" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="FriendshipRepID" Type="byte" />
  </Table>
  <Table Name="FactionGroup" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="FactionTemplate" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Faction" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="Enemies" Type="ushort" ArraySize="4" />
    <Field Name="Friends" Type="ushort" ArraySize="4" />
    <Field Name="Mask" Type="byte" />
    <Field Name="FriendMask" Type="byte" />
    <Field Name="EnemyMask" Type="byte" />
  </Table>
  <Table Name="FootprintTextures" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="int" />
  </Table>
  <Table Name="FootstepTerrainLookup" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field0A" Type="int" />
  </Table>
  <Table Name="FriendshipRepReaction" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="FriendshipReputation" Build="23835">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="FullScreenEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="float" />
    <Field Name="Field008" Type="float" />
    <Field Name="Field00C" Type="float" />
    <Field Name="Field010" Type="float" />
    <Field Name="Field014" Type="uint" />
    <Field Name="Field018" Type="float" />
    <Field Name="Field01C" Type="float" />
    <Field Name="Field020" Type="uint" />
    <Field Name="Field024" Type="float" />
    <Field Name="Field028" Type="float" />
    <Field Name="Field02C" Type="uint" />
    <Field Name="Field030" Type="float" />
    <Field Name="Field034" Type="float" />
    <Field Name="Field038" Type="float" />
    <Field Name="Field03C" Type="float" />
    <Field Name="Field040" Type="float" />
    <Field Name="Field044" Type="uint" />
    <Field Name="Field048" Type="float" />
    <Field Name="Field04C" Type="float" />
    <Field Name="Field050" Type="uint" />
    <Field Name="Field054" Type="uint" />
    <Field Name="Field058" Type="int" />
    <Field Name="Field05C" Type="float" />
    <Field Name="Field060" Type="float" />
    <Field Name="Field064" Type="float" />
    <Field Name="Field068" Type="int" />
    <Field Name="Field06C" Type="float" />
    <Field Name="Field070" Type="float" />
    <Field Name="Field074" Type="float" />
    <Field Name="Field078" Type="float" />
    <Field Name="Field07C" Type="float" />
    <Field Name="Field080" Type="uint" />
    <Field Name="Field084" Type="uint" />
    <Field Name="Field088" Type="float" />
    <Field Name="Field08C" Type="float" />
    <Field Name="Field090" Type="byte" />
    <Field Name="Field091" Type="byte" />
    <Field Name="Field092" Type="ushort" />
    <Field Name="Field094" Type="ushort" />
  </Table>
  <Table Name="GameObjectArtKit" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" ArraySize="3" />
    <Field Name="Field10" Type="string" ArraySize="3" />
  </Table>
  <Table Name="GameObjectDiffAnimMap" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="GameObjectDisplayInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FileDataID" Type="uint" />
    <Field Name="GeoBox" Type="float" ArraySize="6" />
    <Field Name="OverrideLootEffectScale" Type="float" />
    <Field Name="OverrideNameScale" Type="float" />
    <Field Name="ObjectEffectPackageID" Type="ushort" />
  </Table>
  <Table Name="GameObjectDisplayInfoXSoundKit" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="int" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="GameObjects" Build="23835">
    <Field Name="Position" Type="float" ArraySize="3" />
    <Field Name="Rotation" Type="float" ArraySize="4" />
    <Field Name="Size" Type="float" />
    <Field Name="Data" Type="int" ArraySize="8" />
    <Field Name="Name" Type="string" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="DisplayID" Type="ushort" />
    <Field Name="PhaseID" Type="ushort" />
    <Field Name="PhaseGroupID" Type="ushort" />
    <Field Name="PhaseUseFlags" Type="byte" />
    <Field Name="Type" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GameTips" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="GarrAbility" Build="23835">
    <Field Name="Name" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="OtherFactionGarrAbilityID" Type="ushort" />
    <Field Name="GarrAbilityCategoryID" Type="byte" />
    <Field Name="FollowerTypeID" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrAbilityCategory" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="GarrAbilityEffect" Build="23835">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrBuilding" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="HordeGameObjectID" Type="uint" />
    <Field Name="AllianceGameObjectID" Type="uint" />
    <Field Name="NameAlliance" Type="string" />
    <Field Name="NameHorde" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="Tooltip" Type="string" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="CostCurrencyID" Type="ushort" />
    <Field Name="HordeTexPrefixKitID" Type="ushort" />
    <Field Name="AllianceTexPrefixKitID" Type="ushort" />
    <Field Name="AllianceActivationScenePackageID" Type="ushort" />
    <Field Name="HordeActivationScenePackageID" Type="ushort" />
    <Field Name="FollowerRequiredGarrAbilityID" Type="ushort" />
    <Field Name="FollowerGarrAbilityEffectID" Type="ushort" />
    <Field Name="CostMoney" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Type" Type="byte" />
    <Field Name="Level" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="MaxShipments" Type="byte" />
    <Field Name="GarrTypeID" Type="byte" />
    <Field Name="BuildDuration" Type="int" />
    <Field Name="CostCurrencyAmount" Type="int" />
    <Field Name="BonusAmount" Type="int" />
  </Table>
  <Table Name="GarrBuildingDoodadSet" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="GarrBuildingPlotInst" Build="23835">
    <Field Name="LandmarkOffset" Type="float" ArraySize="2" />
    <Field Name="UiTextureAtlasMemberID" Type="ushort" />
    <Field Name="GarrSiteLevelPlotInstID" Type="ushort" />
    <Field Name="GarrBuildingID" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrClassSpec" Build="23835">
    <Field Name="NameMale" Type="string" />
    <Field Name="NameFemale" Type="string" />
    <Field Name="NameGenderless" Type="string" />
    <Field Name="ClassAtlasID" Type="ushort" />
    <Field Name="GarrFollItemSetID" Type="ushort" />
    <Field Name="Limit" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrClassSpecPlayerCond" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="GarrEncounter" Build="23835">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="uint" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field16" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="GarrEncounterSetXEncounter" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" />
  </Table>
  <Table Name="GarrEncounterXMechanic" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
  </Table>
  <Table Name="GarrFollItemSetMember" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="GarrFollower" Build="23835">
    <Field Name="HordeCreatureID" Type="uint" />
    <Field Name="AllianceCreatureID" Type="uint" />
    <Field Name="HordeSourceText" Type="string" />
    <Field Name="AllianceSourceText" Type="string" />
    <Field Name="HordePortraitIconID" Type="uint" />
    <Field Name="AlliancePortraitIconID" Type="uint" />
    <Field Name="HordeAddedBroadcastTextID" Type="uint" />
    <Field Name="AllianceAddedBroadcastTextID" Type="uint" />
    <Field Name="Name" Type="string" />
    <Field Name="HordeGarrFollItemSetID" Type="ushort" />
    <Field Name="AllianceGarrFollItemSetID" Type="ushort" />
    <Field Name="ItemLevelWeapon" Type="ushort" />
    <Field Name="ItemLevelArmor" Type="ushort" />
    <Field Name="HordeListPortraitTextureKitID" Type="ushort" />
    <Field Name="AllianceListPortraitTextureKitID" Type="ushort" />
    <Field Name="FollowerTypeID" Type="byte" />
    <Field Name="HordeUiAnimRaceInfoID" Type="byte" />
    <Field Name="AllianceUiAnimRaceInfoID" Type="byte" />
    <Field Name="Quality" Type="byte" />
    <Field Name="HordeGarrClassSpecID" Type="byte" />
    <Field Name="AllianceGarrClassSpecID" Type="byte" />
    <Field Name="Level" Type="byte" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="GarrTypeID" Type="byte" />
    <Field Name="MaxDurability" Type="byte" />
    <Field Name="Class" Type="byte" />
    <Field Name="HordeFlavorTextGarrStringID" Type="byte" />
    <Field Name="AllianceFlavorTextGarrStringID" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrFollowerLevelXP" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="GarrFollowerQuality" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="GarrFollowerSetXFollower" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
  </Table>
  <Table Name="GarrFollowerType" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="GarrFollowerUICreature" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="GarrFollowerXAbility" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GarrFollowerID" Type="ushort" />
    <Field Name="GarrAbilityID" Type="ushort" />
    <Field Name="FactionIndex" Type="byte" />
  </Table>
  <Table Name="GarrFollSupportSpell" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="ushort" />
  </Table>
  <Table Name="GarrItemLevelUpgradeData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="GarrMechanic" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="ushort" />
  </Table>
  <Table Name="GarrMechanicSetXMechanic" Build="23835">
    <Field Name="Field0" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field2" Type="byte" />
  </Table>
  <Table Name="GarrMechanicType" Build="23835">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrMission" Build="23835">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="float" ArraySize="2" />
    <Field Name="Field1C" Type="int" ArraySize="2" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="Field32" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field35" Type="byte" />
    <Field Name="Field36" Type="ushort" />
    <Field Name="Field38" Type="byte" />
    <Field Name="Field39" Type="ushort" />
    <Field Name="Field3B" Type="ushort" />
    <Field Name="Field3D" Type="ushort" />
    <Field Name="Field3F" Type="ushort" />
    <Field Name="Field41" Type="ushort" />
    <Field Name="Field43" Type="byte" />
  </Table>
  <Table Name="GarrMissionTexture" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="Field0C" Type="ushort" />
  </Table>
  <Table Name="GarrMissionType" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="GarrMissionXEncounter" Build="23835">
    <Field Name="Field0" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field3" Type="ushort" />
    <Field Name="Field5" Type="ushort" />
    <Field Name="Field7" Type="byte" />
  </Table>
  <Table Name="GarrMissionXFollower" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="GarrMssnBonusAbility" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
  </Table>
  <Table Name="GarrPlot" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="AllianceConstructionGameObjectID" Type="uint" />
    <Field Name="HordeConstructionGameObjectID" Type="uint" />
    <Field Name="GarrPlotUICategoryID" Type="byte" />
    <Field Name="PlotType" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="MinCount" Type="uint" ArraySize="2" />
  </Table>
  <Table Name="GarrPlotBuilding" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GarrPlotID" Type="byte" />
    <Field Name="GarrBuildingID" Type="byte" />
  </Table>
  <Table Name="GarrPlotInstance" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="GarrPlotID" Type="byte" />
  </Table>
  <Table Name="GarrPlotUICategory" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="GarrSiteLevel" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TownHall" Type="float" ArraySize="2" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="SiteID" Type="ushort" />
    <Field Name="MovieID" Type="ushort" />
    <Field Name="UpgradeResourceCost" Type="ushort" />
    <Field Name="UpgradeMoneyCost" Type="ushort" />
    <Field Name="Level" Type="byte" />
    <Field Name="UITextureKitID" Type="byte" />
    <Field Name="Level2" Type="byte" />
  </Table>
  <Table Name="GarrSiteLevelPlotInst" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Landmark" Type="float" ArraySize="2" />
    <Field Name="GarrSiteLevelID" Type="ushort" />
    <Field Name="GarrPlotInstanceID" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="GarrSpecialization" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" ArraySize="2" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
  </Table>
  <Table Name="GarrString" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="GarrTalent" Build="23835">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="int" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2B" Type="byte" />
  </Table>
  <Table Name="GarrTalentTree" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
  </Table>
  <Table Name="GarrType" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="ushort" />
    <Field Name="Field07" Type="ushort" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="GarrUiAnimClassInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="GarrUiAnimRaceInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="byte" />
  </Table>
  <Table Name="GemProperties" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="uint" />
    <Field Name="EnchantID" Type="ushort" />
    <Field Name="MinItemLevel" Type="ushort" />
  </Table>
  <Table Name="GlobalStrings" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="GlyphBindableSpell" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="GlyphPropertiesID" Type="ushort" />
  </Table>
  <Table Name="GlyphExclusiveCategory" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="GlyphProperties" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="SpellIconID" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="GlyphExclusiveCategoryID" Type="byte" />
  </Table>
  <Table Name="GlyphRequiredSpec" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GlyphPropertiesID" Type="ushort" />
    <Field Name="ChrSpecializationID" Type="ushort" />
  </Table>
  <Table Name="GMSurveyAnswers" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="GMSurveyCurrentSurvey" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
  </Table>
  <Table Name="GMSurveyQuestions" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="GMSurveySurveys" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" ArraySize="11" />
  </Table>
  <Table Name="GroundEffectDoodad" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="GroundEffectTexture" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" ArraySize="4" />
    <Field Name="Field0C" Type="byte" ArraySize="4" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
  </Table>
  <Table Name="GroupFinderActivity" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
  </Table>
  <Table Name="GroupFinderActivityGrp" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="GroupFinderCategory" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="GuildColorBackground" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="GuildColorBorder" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="GuildColorEmblem" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="GuildPerkSpells" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
  </Table>
  <Table Name="Heirloom" Build="23835">
    <Field Name="ItemID" Type="int" />
    <Field Name="SourceText" Type="string" />
    <Field Name="OldItem_1" Type="int" />
    <Field Name="OldItem_2" Type="int" />
    <Field Name="NextDifficultyItemID" Type="int" />
    <Field Name="UpgradeItemID" Type="int" ArraySize="2" />
    <Field Name="ItemBonusListID" Type="ushort" ArraySize="2" />
    <Field Name="Flags" Type="byte" />
    <Field Name="Source" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="HelmetAnimScaling" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="HelmetGeosetVisData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="int" ArraySize="8" />
  </Table>
  <Table Name="HighlightColor" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
  </Table>
  <Table Name="HolidayDescriptions" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="HolidayNames" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="Holidays" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Date" Type="uint" ArraySize="16" />
    <Field Name="TextureFilename" Type="string" />
    <Field Name="Duration" Type="ushort" ArraySize="10" />
    <Field Name="Region" Type="ushort" />
    <Field Name="Looping" Type="byte" />
    <Field Name="CalendarFlags" Type="byte" ArraySize="10" />
    <Field Name="Priority" Type="byte" />
    <Field Name="CalendarFilterType" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="HolidayNameID" Type="uint" />
    <Field Name="HolidayDescriptionID" Type="uint" />
  </Table>
  <Table Name="ImportPriceArmor" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ClothFactor" Type="float" />
    <Field Name="LeatherFactor" Type="float" />
    <Field Name="MailFactor" Type="float" />
    <Field Name="PlateFactor" Type="float" />
  </Table>
  <Table Name="ImportPriceQuality" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Factor" Type="float" />
  </Table>
  <Table Name="ImportPriceShield" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Factor" Type="float" />
  </Table>
  <Table Name="ImportPriceWeapon" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Factor" Type="float" />
  </Table>
  <Table Name="InvasionClientData" Build="23835">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field0D" Type="ushort" />
    <Field Name="Field0F" Type="ushort" />
    <Field Name="Field11" Type="ushort" />
    <Field Name="Field13" Type="ushort" />
    <Field Name="Field15" Type="ushort" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="Item" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FileDataID" Type="uint" />
    <Field Name="Class" Type="byte" />
    <Field Name="SubClass" Type="byte" />
    <Field Name="SoundOverrideSubclass" Type="byte" />
    <Field Name="Material" Type="byte" />
    <Field Name="InventoryType" Type="byte" />
    <Field Name="Sheath" Type="byte" />
    <Field Name="GroupSoundsID" Type="byte" />
  </Table>
  <Table Name="ItemAppearance" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DisplayID" Type="uint" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="UIOrder" Type="uint" />
    <Field Name="ObjectComponentSlot" Type="byte" />
  </Table>
  <Table Name="ItemAppearanceXUiCamera" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="ItemArmorQuality" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="QualityMod" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemArmorShield" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Quality" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemArmorTotal" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Value" Type="float" ArraySize="4" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemBagFamily" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="ItemBonus" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Value" Type="int" ArraySize="2" />
    <Field Name="BonusListID" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="Index" Type="byte" />
  </Table>
  <Table Name="ItemBonusListLevelDelta" Build="23835">
    <Field Name="Delta" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ItemBonusTreeNode" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="BonusTreeID" Type="ushort" />
    <Field Name="SubTreeID" Type="ushort" />
    <Field Name="BonusListID" Type="ushort" />
    <Field Name="ItemLevelSelectorID" Type="ushort" />
    <Field Name="BonusTreeModID" Type="byte" />
  </Table>
  <Table Name="ItemChildEquipment" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="AltItemID" Type="uint" />
    <Field Name="AltEquipmentSlot" Type="byte" />
  </Table>
  <Table Name="ItemClass" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PriceMod" Type="float" />
    <Field Name="Name" Type="string" />
    <Field Name="OldEnumValue" Type="byte" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="ItemContextPickerEntry" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="ItemCurrencyCost" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemId" Type="uint" />
  </Table>
  <Table Name="ItemDamageAmmo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DPS" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemDamageOneHand" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DPS" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemDamageOneHandCaster" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DPS" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemDamageTwoHand" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DPS" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemDamageTwoHandCaster" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DPS" Type="float" ArraySize="7" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemDisenchantLoot" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MinItemLevel" Type="ushort" />
    <Field Name="MaxItemLevel" Type="ushort" />
    <Field Name="RequiredDisenchantSkill" Type="ushort" />
    <Field Name="ItemClass" Type="byte" />
    <Field Name="ItemSubClass" Type="byte" />
    <Field Name="ItemQuality" Type="byte" />
  </Table>
  <Table Name="ItemDisplayInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" ArraySize="2" />
    <Field Name="Field08" Type="int" ArraySize="2" />
    <Field Name="Field0E" Type="byte" ArraySize="3" />
    <Field Name="Field11" Type="byte" ArraySize="3" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="ushort" ArraySize="2" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field27" Type="int" />
    <Field Name="Field2A" Type="int" />
  </Table>
  <Table Name="ItemDisplayInfoMaterialRes" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="ItemDisplayXUiCamera" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="ItemEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="Cooldown" Type="int" />
    <Field Name="CategoryCooldown" Type="int" />
    <Field Name="Charges" Type="ushort" />
    <Field Name="Category" Type="ushort" />
    <Field Name="ChrSpecializationID" Type="ushort" />
    <Field Name="OrderIndex" Type="byte" />
    <Field Name="Trigger" Type="byte" />
  </Table>
  <Table Name="ItemExtendedCost" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RequiredItem" Type="uint" ArraySize="5" />
    <Field Name="RequiredCurrencyCount" Type="uint" ArraySize="5" />
    <Field Name="RequiredItemCount" Type="ushort" ArraySize="5" />
    <Field Name="RequiredPersonalArenaRating" Type="ushort" />
    <Field Name="RequiredCurrency" Type="ushort" ArraySize="5" />
    <Field Name="RequiredArenaSlot" Type="byte" />
    <Field Name="RequiredFactionId" Type="byte" />
    <Field Name="RequiredFactionStanding" Type="byte" />
    <Field Name="RequirementFlags" Type="byte" />
    <Field Name="RequiredAchievement" Type="byte" />
  </Table>
  <Table Name="ItemGroupSounds" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="ItemLevelSelector" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
  </Table>
  <Table Name="ItemLimitCategory" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Quantity" Type="byte" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="ItemLimitCategoryCondition" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" />
    <Field Name="Field7" Type="ushort" />
  </Table>
  <Table Name="ItemModifiedAppearance" Build="23835">
    <Field Name="ItemID" Type="uint" />
    <Field Name="AppearanceID" Type="ushort" />
    <Field Name="AppearanceModID" Type="byte" />
    <Field Name="Index" Type="byte" />
    <Field Name="SourceType" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ItemModifiedAppearanceExtra" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="ItemNameDescription" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="int" />
  </Table>
  <Table Name="ItemPetFood" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ItemPriceBase" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ArmorFactor" Type="float" />
    <Field Name="WeaponFactor" Type="float" />
    <Field Name="ItemLevel" Type="ushort" />
  </Table>
  <Table Name="ItemRandomProperties" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Enchantment" Type="ushort" ArraySize="5" />
  </Table>
  <Table Name="ItemRandomSuffix" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Enchantment" Type="ushort" ArraySize="5" />
    <Field Name="AllocationPct" Type="ushort" ArraySize="5" />
  </Table>
  <Table Name="ItemRangedDisplayInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="ushort" />
    <Field Name="Field9" Type="ushort" />
  </Table>
  <Table Name="ItemSearchName" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Flags" Type="uint" ArraySize="3" />
    <Field Name="AllowableRace" Type="uint" />
    <Field Name="RequiredSpell" Type="uint" />
    <Field Name="RequiredReputationFaction" Type="ushort" />
    <Field Name="RequiredSkill" Type="ushort" />
    <Field Name="RequiredSkillRank" Type="ushort" />
    <Field Name="ItemLevel" Type="ushort" />
    <Field Name="Quality" Type="byte" />
    <Field Name="RequiredExpansion" Type="byte" />
    <Field Name="RequiredReputationRank" Type="byte" />
    <Field Name="RequiredLevel" Type="byte" />
    <Field Name="AllowableClass" Type="int" />
  </Table>
  <Table Name="ItemSet" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="ItemID" Type="uint" ArraySize="17" />
    <Field Name="RequiredSkillRank" Type="ushort" />
    <Field Name="RequiredSkill" Type="uint" />
    <Field Name="Flags" Type="uint" />
  </Table>
  <Table Name="ItemSetSpell" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="ItemSetID" Type="ushort" />
    <Field Name="ChrSpecID" Type="ushort" />
    <Field Name="Threshold" Type="byte" />
  </Table>
  <Table Name="ItemSparse" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" ArraySize="3" />
    <Field Name="Field02" Type="float" />
    <Field Name="Field03" Type="float" />
    <Field Name="BuyCount" Type="uint" />
    <Field Name="BuyPrice" Type="uint" />
    <Field Name="SellPrice" Type="uint" />
    <Field Name="AllowableRace" Type="int" />
    <Field Name="RequiredSpell" Type="uint" />
    <Field Name="MaxCount" Type="uint" />
    <Field Name="Stackable" Type="uint" />
    <Field Name="ItemStatAllocation" Type="int" ArraySize="10" />
    <Field Name="ItemStatSocketCostMultiplier" Type="float" ArraySize="10" />
    <Field Name="RangedModRange" Type="float" />
    <Field Name="Name" Type="string" />
    <Field Name="Name2" Type="string" />
    <Field Name="Name3" Type="string" />
    <Field Name="Name4" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="BagFamily" Type="uint" />
    <Field Name="ArmorDamageModifier" Type="float" />
    <Field Name="Duration" Type="uint" />
    <Field Name="StatScalingFactor" Type="float" />
    <Field Name="AllowableClass" Type="ushort" />
    <Field Name="ItemLevel" Type="ushort" />
    <Field Name="RequiredSkill" Type="ushort" />
    <Field Name="RequiredSkillRank" Type="ushort" />
    <Field Name="RequiredReputationFaction" Type="ushort" />
    <Field Name="ItemStatValue" Type="ushort" ArraySize="10" />
    <Field Name="ScalingStatDistribution" Type="ushort" />
    <Field Name="Delay" Type="ushort" />
    <Field Name="PageText" Type="ushort" />
    <Field Name="StartQuest" Type="ushort" />
    <Field Name="LockID" Type="ushort" />
    <Field Name="RandomProperty" Type="ushort" />
    <Field Name="RandomSuffix" Type="ushort" />
    <Field Name="ItemSet" Type="ushort" />
    <Field Name="Area" Type="ushort" />
    <Field Name="Map" Type="ushort" />
    <Field Name="TotemCategory" Type="ushort" />
    <Field Name="SocketBonus" Type="ushort" />
    <Field Name="GemProperties" Type="ushort" />
    <Field Name="ItemLimitCategory" Type="ushort" />
    <Field Name="HolidayID" Type="ushort" />
    <Field Name="RequiredTransmogHolidayID" Type="ushort" />
    <Field Name="ItemNameDescriptionID" Type="ushort" />
    <Field Name="Quality" Type="byte" />
    <Field Name="InventoryType" Type="byte" />
    <Field Name="RequiredLevel" Type="byte" />
    <Field Name="RequiredHonorRank" Type="byte" />
    <Field Name="RequiredCityRank" Type="byte" />
    <Field Name="RequiredReputationRank" Type="byte" />
    <Field Name="ContainerSlots" Type="byte" />
    <Field Name="ItemStatType" Type="byte" ArraySize="10" />
    <Field Name="DamageType" Type="byte" />
    <Field Name="Bonding" Type="byte" />
    <Field Name="LanguageID" Type="byte" />
    <Field Name="PageMaterial" Type="byte" />
    <Field Name="Material" Type="byte" />
    <Field Name="Sheath" Type="byte" />
    <Field Name="SocketColor" Type="byte" ArraySize="3" />
    <Field Name="CurrencySubstitutionID" Type="byte" />
    <Field Name="CurrencySubstitutionCount" Type="byte" />
    <Field Name="ArtifactID" Type="byte" />
    <Field Name="RequiredExpansion" Type="byte" />
  </Table>
  <Table Name="ItemSpec" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpecID" Type="ushort" />
    <Field Name="MinLevel" Type="byte" />
    <Field Name="MaxLevel" Type="byte" />
    <Field Name="ItemType" Type="byte" />
    <Field Name="PrimaryStat" Type="byte" />
    <Field Name="SecondaryStat" Type="byte" />
  </Table>
  <Table Name="ItemSpecOverride" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="SpecID" Type="ushort" />
  </Table>
  <Table Name="ItemSubClass" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
  </Table>
  <Table Name="ItemSubClassMask" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="ItemUpgrade" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CurrencyCost" Type="uint" />
    <Field Name="PrevItemUpgradeID" Type="ushort" />
    <Field Name="CurrencyID" Type="ushort" />
    <Field Name="ItemUpgradePathID" Type="byte" />
    <Field Name="ItemLevelBonus" Type="byte" />
  </Table>
  <Table Name="ItemVisualEffects" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ItemVisuals" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" ArraySize="3" />
  </Table>
  <Table Name="ItemXBonusTree" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="BonusTreeID" Type="ushort" />
  </Table>
  <Table Name="JournalEncounter" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="byte" />
  </Table>
  <Table Name="JournalEncounterCreature" Build="23835">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="JournalEncounterItem" Build="23835">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="JournalEncounterSection" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="byte" />
  </Table>
  <Table Name="JournalEncounterXDifficulty" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="JournalInstance" Build="23835">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="JournalItemXDifficulty" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="JournalSectionXDifficulty" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="JournalTier" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="JournalTierXInstance" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="KeyChain" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Key" Type="byte" ArraySize="32" />
  </Table>
  <Table Name="KeystoneAffix" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="uint" />
  </Table>
  <Table Name="Languages" Build="23835">
    <Field Name="Field0" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="LanguageWords" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="LfgDungeonExpansion" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="LfgDungeonGroup" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="LfgDungeons" Build="23835">
    <Field Name="Name" Type="string" />
    <Field Name="Flags" Type="int" />
    <Field Name="TextureFilename" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="MinItemLevel" Type="float" />
    <Field Name="MaxLevel" Type="ushort" />
    <Field Name="TargetLevelMax" Type="ushort" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="RandomID" Type="ushort" />
    <Field Name="ScenarioID" Type="ushort" />
    <Field Name="LastBossJournalEncounterID" Type="ushort" />
    <Field Name="BonusReputationAmount" Type="ushort" />
    <Field Name="MentorItemLevel" Type="ushort" />
    <Field Name="PlayerConditionID" Type="ushort" />
    <Field Name="MinLevel" Type="byte" />
    <Field Name="TargetLevel" Type="byte" />
    <Field Name="TargetLevelMin" Type="byte" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="Type" Type="byte" />
    <Field Name="Faction" Type="byte" />
    <Field Name="Expansion" Type="byte" />
    <Field Name="OrderIndex" Type="byte" />
    <Field Name="GroupID" Type="byte" />
    <Field Name="CountTank" Type="byte" />
    <Field Name="CountHealer" Type="byte" />
    <Field Name="CountDamage" Type="byte" />
    <Field Name="MinCountTank" Type="byte" />
    <Field Name="MinCountHealer" Type="byte" />
    <Field Name="MinCountDamage" Type="byte" />
    <Field Name="SubType" Type="byte" />
    <Field Name="MentorCharLevel" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="LfgDungeonsGroupingMap" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="LfgRoleRequirement" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="ushort" />
  </Table>
  <Table Name="Light" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="FalloffStart" Type="float" />
    <Field Name="FalloffEnd" Type="float" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="LightParamsID" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="LightData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="int" />
    <Field Name="Field008" Type="int" />
    <Field Name="Field00C" Type="int" />
    <Field Name="Field010" Type="int" />
    <Field Name="Field014" Type="int" />
    <Field Name="Field018" Type="int" />
    <Field Name="Field01C" Type="int" />
    <Field Name="Field020" Type="int" />
    <Field Name="Field024" Type="int" />
    <Field Name="Field028" Type="int" />
    <Field Name="Field02C" Type="int" />
    <Field Name="Field030" Type="int" />
    <Field Name="Field034" Type="int" />
    <Field Name="Field038" Type="int" />
    <Field Name="Field03C" Type="int" />
    <Field Name="Field040" Type="int" />
    <Field Name="Field044" Type="int" />
    <Field Name="Field048" Type="int" />
    <Field Name="Field04C" Type="uint" />
    <Field Name="Field050" Type="int" />
    <Field Name="Field054" Type="float" />
    <Field Name="Field058" Type="float" />
    <Field Name="Field05C" Type="int" />
    <Field Name="Field060" Type="float" />
    <Field Name="Field064" Type="float" />
    <Field Name="Field068" Type="float" />
    <Field Name="Field06C" Type="float" />
    <Field Name="Field070" Type="uint" />
    <Field Name="Field074" Type="uint" />
    <Field Name="Field078" Type="uint" />
    <Field Name="Field07C" Type="uint" />
    <Field Name="Field080" Type="uint" />
    <Field Name="Field084" Type="uint" />
    <Field Name="Field088" Type="ushort" />
    <Field Name="Field08A" Type="ushort" />
  </Table>
  <Table Name="LightParams" Build="23835">
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="int" ArraySize="3" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="LightSkybox" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="LiquidMaterial" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
  </Table>
  <Table Name="LiquidObject" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
  </Table>
  <Table Name="LiquidType" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="MaxDarkenDepth" Type="float" />
    <Field Name="FogDarkenIntensity" Type="float" />
    <Field Name="AmbDarkenIntensity" Type="float" />
    <Field Name="DirDarkenIntensity" Type="float" />
    <Field Name="ParticleScale" Type="float" />
    <Field Name="Texture" Type="string" ArraySize="6" />
    <Field Name="Color" Type="uint" ArraySize="2" />
    <Field Name="Float" Type="float" ArraySize="18" />
    <Field Name="Int" Type="uint" ArraySize="4" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="LightID" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="ParticleMovement" Type="byte" />
    <Field Name="ParticleTexSlots" Type="byte" />
    <Field Name="MaterialID" Type="byte" />
    <Field Name="DepthTexCount" Type="byte" ArraySize="6" />
    <Field Name="SoundID" Type="uint" />
  </Table>
  <Table Name="LoadingScreens" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
  </Table>
  <Table Name="LoadingScreenTaxiSplines" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="10" />
    <Field Name="Field2C" Type="float" ArraySize="10" />
    <Field Name="Field54" Type="ushort" />
    <Field Name="Field56" Type="ushort" />
    <Field Name="Field58" Type="byte" />
  </Table>
  <Table Name="Locale" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="Location" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="int" ArraySize="2" />
  </Table>
  <Table Name="Lock" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Index" Type="uint" ArraySize="8" />
    <Field Name="Skill" Type="ushort" ArraySize="8" />
    <Field Name="Type" Type="byte" ArraySize="8" />
    <Field Name="Action" Type="byte" ArraySize="8" />
  </Table>
  <Table Name="LockType" Build="23835">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="LookAtController" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="byte" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
  </Table>
  <Table Name="MailTemplate" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Body" Type="string" />
  </Table>
  <Table Name="ManagedWorldState" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
  </Table>
  <Table Name="ManagedWorldStateBuff" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="ManagedWorldStateInput" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="ManifestInterfaceActionIcon" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ManifestInterfaceData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="string" />
  </Table>
  <Table Name="ManifestInterfaceItemIcon" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ManifestInterfaceTOCData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ManifestMP3" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Map" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Directory" Type="string" />
    <Field Name="Flags" Type="uint" ArraySize="2" />
    <Field Name="MinimapIconScale" Type="float" />
    <Field Name="CorpsePos" Type="float" ArraySize="2" />
    <Field Name="MapName" Type="string" />
    <Field Name="MapDescription0" Type="string" />
    <Field Name="MapDescription1" Type="string" />
    <Field Name="ShortDescription" Type="string" />
    <Field Name="LongDescription" Type="string" />
    <Field Name="AreaTableID" Type="ushort" />
    <Field Name="LoadingScreenID" Type="ushort" />
    <Field Name="CorpseMapID" Type="ushort" />
    <Field Name="TimeOfDayOverride" Type="ushort" />
    <Field Name="ParentMapID" Type="ushort" />
    <Field Name="CosmeticParentMapID" Type="ushort" />
    <Field Name="WindSettingsID" Type="ushort" />
    <Field Name="InstanceType" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="ExpansionID" Type="byte" />
    <Field Name="MaxPlayers" Type="byte" />
    <Field Name="TimeOffset" Type="byte" />
  </Table>
  <Table Name="MapCelestialBody" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="MapChallengeMode" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" ArraySize="3" />
    <Field Name="Field10" Type="byte" ArraySize="4" />
  </Table>
  <Table Name="MapDifficulty" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Message_Lang" Type="string" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="RaidDurationType" Type="byte" />
    <Field Name="MaxPlayers" Type="byte" />
    <Field Name="LockID" Type="byte" />
    <Field Name="ItemBonusTreeModID" Type="byte" />
    <Field Name="Context" Type="int" />
  </Table>
  <Table Name="MapDifficultyXCondition" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="MarketingPromotionsXLocale" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
  </Table>
  <Table Name="Material" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" />
    <Field Name="Field7" Type="ushort" />
    <Field Name="Field9" Type="ushort" />
  </Table>
  <Table Name="MinorTalent" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="ModelAnimCloakDampening" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="int" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="ModelFileData" Build="23835">
    <Field Name="Field0" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
  </Table>
  <Table Name="ModelRibbonQuality" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="ModifierTree" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Asset" Type="uint" ArraySize="2" />
    <Field Name="Parent" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Operator" Type="byte" />
    <Field Name="Amount" Type="byte" />
  </Table>
  <Table Name="Mount" Build="23835">
    <Field Name="SpellId" Type="uint" />
    <Field Name="Name" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="SourceDescription" Type="string" />
    <Field Name="CameraPivotMultiplier" Type="float" />
    <Field Name="MountTypeId" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="Source" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PlayerConditionId" Type="uint" />
    <Field Name="UiModelSceneID" Type="int" />
  </Table>
  <Table Name="MountCapability" Build="23835">
    <Field Name="RequiredSpell" Type="uint" />
    <Field Name="SpeedModSpell" Type="uint" />
    <Field Name="RequiredRidingSkill" Type="ushort" />
    <Field Name="RequiredArea" Type="ushort" />
    <Field Name="RequiredMap" Type="ushort" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RequiredAura" Type="uint" />
  </Table>
  <Table Name="MountTypeXCapability" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MountTypeID" Type="ushort" />
    <Field Name="MountCapabilityID" Type="ushort" />
    <Field Name="OrderIndex" Type="byte" />
  </Table>
  <Table Name="MountXDisplay" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MountID" Type="uint" />
    <Field Name="DisplayID" Type="uint" />
    <Field Name="PlayerConditionID" Type="uint" />
  </Table>
  <Table Name="Movie" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AudioFileDataID" Type="uint" />
    <Field Name="SubtitleFileDataID" Type="uint" />
    <Field Name="Volume" Type="byte" />
    <Field Name="KeyID" Type="byte" />
  </Table>
  <Table Name="MovieFileData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
  </Table>
  <Table Name="MovieVariation" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="NameGen" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Race" Type="byte" />
    <Field Name="Sex" Type="byte" />
  </Table>
  <Table Name="NamesProfanity" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Language" Type="byte" />
  </Table>
  <Table Name="NamesReserved" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="NamesReservedLocale" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="LocaleMask" Type="byte" />
  </Table>
  <Table Name="NpcModelItemSlotDisplayInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="NPCSounds" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="int" ArraySize="2" />
  </Table>
  <Table Name="ObjectEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" ArraySize="3" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="int" />
    <Field Name="Field1D" Type="ushort" />
  </Table>
  <Table Name="ObjectEffectGroup" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ObjectEffectModifier" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="4" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="ObjectEffectPackage" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ObjectEffectPackageElem" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="OutlineEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="OverrideSpellData" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" ArraySize="10" />
    <Field Name="PlayerActionbarFileDataID" Type="uint" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="PageTextMaterial" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="PaperDollItemFrame" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="ParagonReputation" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="ParticleColor" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="int" ArraySize="3" />
    <Field Name="Field1C" Type="int" ArraySize="2" />
  </Table>
  <Table Name="Path" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="PathNode" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="PathNodeProperty" Build="23835">
    <Field Name="Field0" Type="ushort" />
    <Field Name="Field2" Type="ushort" />
    <Field Name="Field4" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field8" Type="int" />
  </Table>
  <Table Name="PathProperty" Build="23835">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Phase" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="ushort" />
  </Table>
  <Table Name="PhaseShiftZoneSounds" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
  </Table>
  <Table Name="PhaseXPhaseGroup" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PhaseID" Type="ushort" />
    <Field Name="PhaseGroupID" Type="ushort" />
  </Table>
  <Table Name="PlayerCondition" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceMask" Type="uint" />
    <Field Name="SkillLogic" Type="uint" />
    <Field Name="ReputationLogic" Type="uint" />
    <Field Name="PrevQuestLogic" Type="uint" />
    <Field Name="CurrQuestLogic" Type="uint" />
    <Field Name="CurrentCompletedQuestLogic" Type="uint" />
    <Field Name="SpellLogic" Type="uint" />
    <Field Name="ItemLogic" Type="uint" />
    <Field Name="Time" Type="uint" ArraySize="2" />
    <Field Name="AuraSpellLogic" Type="uint" />
    <Field Name="AuraSpellID" Type="uint" ArraySize="4" />
    <Field Name="AchievementLogic" Type="uint" />
    <Field Name="AreaLogic" Type="uint" />
    <Field Name="QuestKillLogic" Type="uint" />
    <Field Name="FailureDescription" Type="string" />
    <Field Name="MinLevel" Type="ushort" />
    <Field Name="MaxLevel" Type="ushort" />
    <Field Name="SkillID" Type="ushort" ArraySize="4" />
    <Field Name="MinSkill" Type="ushort" ArraySize="4" />
    <Field Name="MaxSkill" Type="ushort" ArraySize="4" />
    <Field Name="MaxFactionID" Type="ushort" />
    <Field Name="PrevQuestID" Type="ushort" ArraySize="4" />
    <Field Name="CurrQuestID" Type="ushort" ArraySize="4" />
    <Field Name="CurrentCompletedQuestID" Type="ushort" ArraySize="4" />
    <Field Name="Explored" Type="ushort" ArraySize="2" />
    <Field Name="WorldStateExpressionID" Type="ushort" />
    <Field Name="Achievement" Type="ushort" ArraySize="4" />
    <Field Name="AreaID" Type="ushort" ArraySize="4" />
    <Field Name="QuestKillID" Type="ushort" />
    <Field Name="PhaseID" Type="ushort" />
    <Field Name="MinAvgEquippedItemLevel" Type="ushort" />
    <Field Name="MaxAvgEquippedItemLevel" Type="ushort" />
    <Field Name="ModifierTreeID" Type="ushort" />
    <Field Name="Flags" Type="byte" />
    <Field Name="Gender" Type="byte" />
    <Field Name="NativeGender" Type="byte" />
    <Field Name="MinLanguage" Type="byte" />
    <Field Name="MaxLanguage" Type="byte" />
    <Field Name="MinReputation" Type="byte" ArraySize="3" />
    <Field Name="MaxReputation" Type="byte" />
    <Field Name="Field41" Type="byte" />
    <Field Name="MinPVPRank" Type="byte" />
    <Field Name="MaxPVPRank" Type="byte" />
    <Field Name="PvpMedal" Type="byte" />
    <Field Name="ItemFlags" Type="byte" />
    <Field Name="AuraCount" Type="byte" ArraySize="4" />
    <Field Name="WeatherID" Type="byte" />
    <Field Name="PartyStatus" Type="byte" />
    <Field Name="LifetimeMaxPVPRank" Type="byte" />
    <Field Name="LfgStatus" Type="byte" ArraySize="4" />
    <Field Name="LfgCompare" Type="byte" ArraySize="4" />
    <Field Name="CurrencyCount" Type="byte" ArraySize="4" />
    <Field Name="MinExpansionLevel" Type="byte" />
    <Field Name="MaxExpansionLevel" Type="byte" />
    <Field Name="MinExpansionTier" Type="byte" />
    <Field Name="MaxExpansionTier" Type="byte" />
    <Field Name="MinGuildLevel" Type="byte" />
    <Field Name="MaxGuildLevel" Type="byte" />
    <Field Name="PhaseUseFlags" Type="byte" />
    <Field Name="ChrSpecializationIndex" Type="byte" />
    <Field Name="ChrSpecializationRole" Type="byte" />
    <Field Name="PowerType" Type="byte" />
    <Field Name="PowerTypeComp" Type="byte" />
    <Field Name="PowerTypeValue" Type="byte" />
    <Field Name="ClassMask" Type="int" />
    <Field Name="LanguageID" Type="uint" />
    <Field Name="MinFactionID" Type="uint" ArraySize="3" />
    <Field Name="SpellID" Type="uint" ArraySize="4" />
    <Field Name="ItemID" Type="uint" ArraySize="4" />
    <Field Name="ItemCount" Type="uint" ArraySize="4" />
    <Field Name="LfgLogic" Type="uint" />
    <Field Name="LfgValue" Type="uint" ArraySize="4" />
    <Field Name="CurrencyLogic" Type="uint" />
    <Field Name="CurrencyID" Type="uint" ArraySize="4" />
    <Field Name="QuestKillMonster" Type="uint" ArraySize="6" />
    <Field Name="PhaseGroupID" Type="uint" />
    <Field Name="MinAvgItemLevel" Type="uint" />
    <Field Name="MaxAvgItemLevel" Type="uint" />
    <Field Name="MovementFlags" Type="int" ArraySize="2" />
    <Field Name="MainHandItemSubclassMask" Type="uint" />
  </Table>
  <Table Name="Positioner" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="PositionerState" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="ushort" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
  </Table>
  <Table Name="PositionerStateEntry" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="ushort" />
  </Table>
  <Table Name="PowerDisplay" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GlobalStringBaseTag" Type="string" />
    <Field Name="PowerType" Type="byte" />
    <Field Name="Red" Type="byte" />
    <Field Name="Green" Type="byte" />
    <Field Name="Blue" Type="byte" />
  </Table>
  <Table Name="PowerType" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PowerTypeToken" Type="string" />
    <Field Name="PowerCostToken" Type="string" />
    <Field Name="RegenerationPeace" Type="float" />
    <Field Name="RegenerationCombat" Type="float" />
    <Field Name="MaxPower" Type="ushort" />
    <Field Name="RegenerationDelay" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="PowerTypeEnum" Type="byte" />
    <Field Name="RegenerationMin" Type="byte" />
    <Field Name="RegenerationCenter" Type="byte" />
    <Field Name="RegenerationMax" Type="byte" />
    <Field Name="UIModifier" Type="byte" />
  </Table>
  <Table Name="PrestigeLevelInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="PvpBracketTypes" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="PvpDifficulty" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="BracketID" Type="byte" />
    <Field Name="MinLevel" Type="byte" />
    <Field Name="MaxLevel" Type="byte" />
  </Table>
  <Table Name="PvpItem" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="PvpReward" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="PvpScalingEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="PvpScalingEffectType" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="PvpTalent" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="PvpTalentUnlock" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="QuestFactionReward" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="QuestRewFactionValue" Type="ushort" ArraySize="10" />
  </Table>
  <Table Name="QuestFeedbackEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="QuestInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="QuestLine" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="QuestLineXQuest" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="QuestMoneyReward" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Money" Type="uint" ArraySize="10" />
  </Table>
  <Table Name="QuestObjective" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
  </Table>
  <Table Name="QuestPackageItem" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="QuestPackageID" Type="ushort" />
    <Field Name="FilterType" Type="byte" />
    <Field Name="ItemCount" Type="uint" />
  </Table>
  <Table Name="QuestPOIBlob" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="QuestPOIPoint" Build="23835">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="QuestSort" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SortName" Type="string" />
    <Field Name="SortOrder" Type="byte" />
  </Table>
  <Table Name="QuestV2" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="UniqueBitFlag" Type="ushort" />
  </Table>
  <Table Name="QuestV2CliTask" Build="23835">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" ArraySize="3" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" ArraySize="3" />
  </Table>
  <Table Name="QuestXGroupActivity" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="QuestXP" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Exp" Type="ushort" ArraySize="10" />
  </Table>
  <Table Name="RacialMounts" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="RandPropPoints" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EpicPropertiesPoints" Type="uint" ArraySize="5" />
    <Field Name="RarePropertiesPoints" Type="uint" ArraySize="5" />
    <Field Name="UncommonPropertiesPoints" Type="uint" ArraySize="5" />
  </Table>
  <Table Name="ResearchBranch" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
  </Table>
  <Table Name="ResearchField" Build="23835">
    <Field Name="Field0" Type="int" />
    <Field Name="Field4" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ResearchProject" Build="23835">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field16" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="ResearchSite" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="Resistances" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="ushort" />
  </Table>
  <Table Name="RewardPack" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
  </Table>
  <Table Name="RewardPackXCurrencyType" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="ushort" />
    <Field Name="Field7" Type="ushort" />
  </Table>
  <Table Name="RewardPackXItem" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="RibbonQuality" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
  </Table>
  <Table Name="RulesetItemUpgrade" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="uint" />
    <Field Name="ItemUpgradeID" Type="ushort" />
  </Table>
  <Table Name="ScalingStatDistribution" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemLevelCurveID" Type="ushort" />
    <Field Name="MinLevel" Type="uint" />
    <Field Name="MaxLevel" Type="uint" />
  </Table>
  <Table Name="Scenario" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Data" Type="ushort" />
    <Field Name="Flags" Type="byte" />
    <Field Name="Type" Type="byte" />
  </Table>
  <Table Name="ScenarioEventEntry" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="ScenarioStep" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Description" Type="string" />
    <Field Name="Name" Type="string" />
    <Field Name="CriteriaTreeID" Type="ushort" />
    <Field Name="ScenarioID" Type="ushort" />
    <Field Name="PreviousStepID" Type="ushort" />
    <Field Name="QuestRewardID" Type="ushort" />
    <Field Name="Step" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="BonusRequiredStepID" Type="uint" />
  </Table>
  <Table Name="SceneScript" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Script" Type="string" />
    <Field Name="PrevScriptId" Type="ushort" />
    <Field Name="NextScriptId" Type="ushort" />
  </Table>
  <Table Name="SceneScriptPackage" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="SceneScriptPackageMember" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="ScheduledInterval" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="uint" />
    <Field Name="Field0A" Type="int" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="ScheduledWorldState" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="ScheduledWorldStateGroup" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="ScheduledWorldStateXUniqCat" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field2" Type="ushort" />
    <Field Name="Field4" Type="ushort" />
  </Table>
  <Table Name="ScreenEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" ArraySize="4" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
  </Table>
  <Table Name="ScreenLocation" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SeamlessSite" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
  </Table>
  <Table Name="ServerMessages" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="ShadowyEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="uint" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
  </Table>
  <Table Name="SkillLine" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DisplayName" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="AlternateVerb" Type="string" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="CategoryID" Type="byte" />
    <Field Name="CanLink" Type="byte" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="ParentSkillLineID" Type="uint" />
  </Table>
  <Table Name="SkillLineAbility" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="RaceMask" Type="uint" />
    <Field Name="SupercedesSpell" Type="uint" />
    <Field Name="SkillLine" Type="ushort" />
    <Field Name="MinSkillLineRank" Type="ushort" />
    <Field Name="TrivialSkillLineRankHigh" Type="ushort" />
    <Field Name="TrivialSkillLineRankLow" Type="ushort" />
    <Field Name="UniqueBit" Type="ushort" />
    <Field Name="TradeSkillCategoryID" Type="ushort" />
    <Field Name="AcquireMethod" Type="byte" />
    <Field Name="NumSkillUps" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="ClassMask" Type="int" />
  </Table>
  <Table Name="SkillRaceClassInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceMask" Type="int" />
    <Field Name="SkillID" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="SkillTierID" Type="ushort" />
    <Field Name="Availability" Type="byte" />
    <Field Name="MinLevel" Type="byte" />
    <Field Name="ClassMask" Type="int" />
  </Table>
  <Table Name="SoundAmbience" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" ArraySize="2" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="SoundAmbienceFlavor" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="int" />
    <Field Name="Field9" Type="int" />
  </Table>
  <Table Name="SoundBus" Build="23835">
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SoundBusName" Build="23835">
    <Field Name="Field0" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SoundBusOverride" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="int" />
    <Field Name="Field0F" Type="int" />
  </Table>
  <Table Name="SoundEmitterPillPoints" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="ushort" />
  </Table>
  <Table Name="SoundEmitters" Build="23835">
    <Field Name="Field00" Type="int" ArraySize="3" />
    <Field Name="Field0C" Type="int" ArraySize="3" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field27" Type="int" />
    <Field Name="Field2A" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="SoundFilter" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SoundFilterElem" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="9" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
  </Table>
  <Table Name="SoundKit" Build="23835">
    <Field Name="Name" Type="string" />
    <Field Name="VolumeFloat" Type="float" />
    <Field Name="MinDistance" Type="float" />
    <Field Name="DistanceCutoff" Type="float" />
    <Field Name="VolumeVariationPlus" Type="float" />
    <Field Name="VolumeVariationMinus" Type="float" />
    <Field Name="PitchVariationPlus" Type="float" />
    <Field Name="PitchVariationMinus" Type="float" />
    <Field Name="PitchAdjust" Type="float" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="SoundEntriesAdvancedID" Type="ushort" />
    <Field Name="BusOverwriteID" Type="ushort" />
    <Field Name="SoundType" Type="byte" />
    <Field Name="EAXDef" Type="byte" />
    <Field Name="DialogType" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SoundKitAdvanced" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="float" />
    <Field Name="Field008" Type="uint" />
    <Field Name="Field00C" Type="uint" />
    <Field Name="Field010" Type="uint" />
    <Field Name="Field014" Type="uint" />
    <Field Name="Field018" Type="uint" />
    <Field Name="Field01C" Type="uint" />
    <Field Name="Field020" Type="float" />
    <Field Name="Field024" Type="float" />
    <Field Name="Field028" Type="float" />
    <Field Name="Field02C" Type="float" />
    <Field Name="Field030" Type="float" />
    <Field Name="Field034" Type="uint" />
    <Field Name="Field038" Type="uint" />
    <Field Name="Field03C" Type="int" />
    <Field Name="Field040" Type="int" />
    <Field Name="Field044" Type="float" />
    <Field Name="Field048" Type="float" />
    <Field Name="Field04C" Type="float" />
    <Field Name="Field050" Type="float" />
    <Field Name="Field054" Type="int" />
    <Field Name="Field058" Type="ushort" />
    <Field Name="Field05A" Type="byte" />
    <Field Name="Field05B" Type="byte" />
    <Field Name="Field05C" Type="byte" />
    <Field Name="Field05D" Type="byte" />
    <Field Name="Field05E" Type="int" />
    <Field Name="Field061" Type="ushort" />
    <Field Name="Field063" Type="int" />
    <Field Name="Field066" Type="int" />
    <Field Name="Field069" Type="int" />
    <Field Name="Field06C" Type="int" />
  </Table>
  <Table Name="SoundKitChild" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="int" />
    <Field Name="Field7" Type="int" />
  </Table>
  <Table Name="SoundKitEntry" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="int" />
  </Table>
  <Table Name="SoundKitFallback" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="SoundOverride" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="SoundProviderPreferences" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="float" />
    <Field Name="Field44" Type="ushort" />
    <Field Name="Field46" Type="ushort" />
    <Field Name="Field48" Type="ushort" />
    <Field Name="Field4A" Type="ushort" />
    <Field Name="Field4C" Type="ushort" />
    <Field Name="Field4E" Type="byte" />
    <Field Name="Field4F" Type="byte" />
  </Table>
  <Table Name="SourceInfo" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="SpamMessages" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SpecializationSpells" Build="23835">
    <Field Name="SpellID" Type="uint" />
    <Field Name="OverridesSpellID" Type="uint" />
    <Field Name="Description" Type="string" />
    <Field Name="SpecID" Type="ushort" />
    <Field Name="OrderIndex" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Spell" Build="23835">
    <Field Name="Name" Type="string" />
    <Field Name="NameSubtext" Type="string" />
    <Field Name="Description" Type="string" />
    <Field Name="AuraDescription" Type="string" />
    <Field Name="MiscID" Type="uint" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DescriptionVariablesID" Type="uint" />
  </Table>
  <Table Name="SpellActionBarPref" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="SpellActivationOverlay" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="uint" ArraySize="4" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="ushort" />
  </Table>
  <Table Name="SpellAuraOptions" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="ProcCharges" Type="uint" />
    <Field Name="ProcTypeMask" Type="uint" />
    <Field Name="ProcCategoryRecovery" Type="uint" />
    <Field Name="CumulativeAura" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="ProcChance" Type="byte" />
    <Field Name="SpellProcsPerMinuteID" Type="byte" />
  </Table>
  <Table Name="SpellAuraRestrictions" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="CasterAuraSpell" Type="uint" />
    <Field Name="TargetAuraSpell" Type="uint" />
    <Field Name="ExcludeCasterAuraSpell" Type="uint" />
    <Field Name="ExcludeTargetAuraSpell" Type="uint" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="CasterAuraState" Type="byte" />
    <Field Name="TargetAuraState" Type="byte" />
    <Field Name="ExcludeCasterAuraState" Type="byte" />
    <Field Name="ExcludeTargetAuraState" Type="byte" />
  </Table>
  <Table Name="SpellAuraVisibility" Build="23835">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellAuraVisXChrSpec" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="SpellCastingRequirements" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="MinFactionID" Type="ushort" />
    <Field Name="RequiredAreasID" Type="ushort" />
    <Field Name="RequiresSpellFocus" Type="ushort" />
    <Field Name="FacingCasterFlags" Type="byte" />
    <Field Name="MinReputation" Type="byte" />
    <Field Name="RequiredAuraVision" Type="byte" />
  </Table>
  <Table Name="SpellCastTimes" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CastTime" Type="int" />
    <Field Name="MinCastTime" Type="int" />
    <Field Name="CastTimePerLevel" Type="ushort" />
  </Table>
  <Table Name="SpellCategories" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="Category" Type="ushort" />
    <Field Name="StartRecoveryCategory" Type="ushort" />
    <Field Name="ChargeCategory" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="DefenseType" Type="byte" />
    <Field Name="DispelType" Type="byte" />
    <Field Name="Mechanic" Type="byte" />
    <Field Name="PreventionType" Type="byte" />
  </Table>
  <Table Name="SpellCategory" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="ChargeRecoveryTime" Type="int" />
    <Field Name="Flags" Type="byte" />
    <Field Name="UsesPerWeek" Type="byte" />
    <Field Name="MaxCharges" Type="byte" />
    <Field Name="ChargeCategoryType" Type="uint" />
  </Table>
  <Table Name="SpellChainEffects" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field004" Type="float" />
    <Field Name="Field008" Type="float" />
    <Field Name="Field00C" Type="int" />
    <Field Name="Field010" Type="uint" />
    <Field Name="Field014" Type="uint" />
    <Field Name="Field018" Type="float" />
    <Field Name="Field01C" Type="float" />
    <Field Name="Field020" Type="float" />
    <Field Name="Field024" Type="int" />
    <Field Name="Field028" Type="float" />
    <Field Name="Field02C" Type="float" />
    <Field Name="Field030" Type="float" />
    <Field Name="Field034" Type="int" />
    <Field Name="Field038" Type="int" />
    <Field Name="Field03C" Type="int" />
    <Field Name="Field040" Type="int" />
    <Field Name="Field044" Type="int" />
    <Field Name="Field048" Type="int" />
    <Field Name="Field04C" Type="int" />
    <Field Name="Field050" Type="int" />
    <Field Name="Field054" Type="int" />
    <Field Name="Field058" Type="int" />
    <Field Name="Field05C" Type="int" />
    <Field Name="Field060" Type="int" />
    <Field Name="Field064" Type="uint" />
    <Field Name="Field068" Type="float" />
    <Field Name="Field06C" Type="float" />
    <Field Name="Field070" Type="float" />
    <Field Name="Field074" Type="float" />
    <Field Name="Field078" Type="int" />
    <Field Name="Field07C" Type="float" />
    <Field Name="Field080" Type="float" />
    <Field Name="Field084" Type="int" />
    <Field Name="Field088" Type="float" />
    <Field Name="Field08C" Type="float" />
    <Field Name="Field090" Type="int" ArraySize="3" />
    <Field Name="Field09C" Type="int" ArraySize="3" />
    <Field Name="Field0A8" Type="int" ArraySize="3" />
    <Field Name="Field0B4" Type="float" ArraySize="3" />
    <Field Name="Field0C0" Type="uint" />
    <Field Name="Field0C4" Type="float" />
    <Field Name="Field0C8" Type="float" />
    <Field Name="Field0CC" Type="string" ArraySize="3" />
    <Field Name="Field0D8" Type="string" />
    <Field Name="Field0DC" Type="ushort" />
    <Field Name="Field0DE" Type="ushort" />
    <Field Name="Field0E0" Type="ushort" ArraySize="11" />
    <Field Name="Field0F6" Type="ushort" />
    <Field Name="Field0F8" Type="byte" />
    <Field Name="Field0F9" Type="byte" />
    <Field Name="Field0FA" Type="byte" />
    <Field Name="Field0FB" Type="byte" />
    <Field Name="Field0FC" Type="byte" />
    <Field Name="Field0FD" Type="byte" />
    <Field Name="Field0FE" Type="byte" />
    <Field Name="Field0FF" Type="byte" />
    <Field Name="Field100" Type="byte" />
    <Field Name="Field101" Type="byte" />
    <Field Name="Field102" Type="int" />
  </Table>
  <Table Name="SpellClassOptions" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="SpellClassSet" Type="byte" />
    <Field Name="ModalNextSpell" Type="uint" />
  </Table>
  <Table Name="SpellCooldowns" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="CategoryRecoveryTime" Type="uint" />
    <Field Name="RecoveryTime" Type="uint" />
    <Field Name="StartRecoveryTime" Type="uint" />
    <Field Name="DifficultyID" Type="byte" />
  </Table>
  <Table Name="SpellDescriptionVariables" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SpellDispelType" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="SpellDuration" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Duration" Type="int" />
    <Field Name="MaxDuration" Type="int" />
    <Field Name="DurationPerLevel" Type="int" />
  </Table>
  <Table Name="SpellEffect" Build="23835">
    <Field Name="EffectSpellClassMask" Type="int" ArraySize="4" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="Effect" Type="uint" />
    <Field Name="EffectAura" Type="uint" />
    <Field Name="EffectBasePoints" Type="int" />
    <Field Name="EffectIndex" Type="uint" />
    <Field Name="EffectMiscValue" Type="int" ArraySize="2" />
    <Field Name="EffectRadiusIndex" Type="uint" ArraySize="2" />
    <Field Name="ImplicitTarget" Type="uint" ArraySize="2" />
    <Field Name="DifficultyID" Type="int" />
    <Field Name="EffectAmplitude" Type="float" />
    <Field Name="EffectAuraPeriod" Type="int" />
    <Field Name="EffectBonusCoefficient" Type="float" />
    <Field Name="EffectChainAmplitude" Type="float" DefaultValue="1" />
    <Field Name="EffectChainTargets" Type="uint" />
    <Field Name="EffectDieSides" Type="int" />
    <Field Name="EffectItemType" Type="uint" />
    <Field Name="EffectMechanic" Type="uint" />
    <Field Name="EffectPointsPerResource" Type="float" />
    <Field Name="EffectRealPointsPerLevel" Type="float" />
    <Field Name="EffectTriggerSpell" Type="uint" />
    <Field Name="EffectPosFacing" Type="float" />
    <Field Name="EffectAttributes" Type="uint" />
    <Field Name="BonusCoefficientFromAP" Type="float" />
    <Field Name="PvPMultiplier" Type="float" DefaultValue="1" />
  </Table>
  <Table Name="SpellEffectEmission" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="SpellEffectGroupSize" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="float" />
  </Table>
  <Table Name="SpellEffectScaling" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Coefficient" Type="float" />
    <Field Name="Variance" Type="float" />
    <Field Name="ResourceCoefficient" Type="float" />
    <Field Name="SpellEffectID" Type="uint" />
  </Table>
  <Table Name="SpellEquippedItems" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="EquippedItemInventoryTypeMask" Type="int" />
    <Field Name="EquippedItemSubClassMask" Type="int" />
    <Field Name="EquippedItemClass" Type="byte" />
  </Table>
  <Table Name="SpellFlyout" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="ushort" />
    <Field Name="Field13" Type="int" />
  </Table>
  <Table Name="SpellFlyoutItem" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="SpellFocusObject" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="SpellInterrupts" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="AuraInterruptFlags" Type="uint" ArraySize="2" />
    <Field Name="ChannelInterruptFlags" Type="uint" ArraySize="2" />
    <Field Name="InterruptFlags" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
  </Table>
  <Table Name="SpellItemEnchantment" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EffectSpellID" Type="uint" ArraySize="3" />
    <Field Name="Name" Type="string" />
    <Field Name="EffectScalingPoints" Type="float" ArraySize="3" />
    <Field Name="TransmogCost" Type="uint" />
    <Field Name="TextureFileDataID" Type="uint" />
    <Field Name="EffectPointsMin" Type="ushort" ArraySize="3" />
    <Field Name="ItemVisual" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="RequiredSkillID" Type="ushort" />
    <Field Name="RequiredSkillRank" Type="ushort" />
    <Field Name="ItemLevel" Type="ushort" />
    <Field Name="Charges" Type="byte" />
    <Field Name="Effect" Type="byte" ArraySize="3" />
    <Field Name="ConditionID" Type="byte" />
    <Field Name="MinLevel" Type="byte" />
    <Field Name="MaxLevel" Type="byte" />
    <Field Name="ScalingClass" Type="byte" />
    <Field Name="ScalingClassRestricted" Type="byte" />
    <Field Name="PlayerConditionID" Type="uint" />
  </Table>
  <Table Name="SpellItemEnchantmentCondition" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LTOperandType" Type="byte" ArraySize="5" />
    <Field Name="Operator" Type="byte" ArraySize="5" />
    <Field Name="RTOperandType" Type="byte" ArraySize="5" />
    <Field Name="RTOperand" Type="byte" ArraySize="5" />
    <Field Name="Logic" Type="byte" ArraySize="5" />
    <Field Name="LTOperand" Type="uint" ArraySize="5" />
  </Table>
  <Table Name="SpellKeyboundOverride" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="SpellLabel" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="uint" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="SpellLearnSpell" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LearnSpellID" Type="uint" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="OverridesSpellID" Type="uint" />
  </Table>
  <Table Name="SpellLevels" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="BaseLevel" Type="ushort" />
    <Field Name="MaxLevel" Type="ushort" />
    <Field Name="SpellLevel" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="MaxUsableLevel" Type="byte" />
  </Table>
  <Table Name="SpellMechanic" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SpellMisc" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Attributes" Type="int" ArraySize="14" />
    <Field Name="Speed" Type="float" />
    <Field Name="MultistrikeSpeedMod" Type="float" />
    <Field Name="CastingTimeIndex" Type="ushort" />
    <Field Name="DurationIndex" Type="ushort" />
    <Field Name="RangeIndex" Type="ushort" />
    <Field Name="SchoolMask" Type="byte" />
    <Field Name="IconFileDataID" Type="int" />
    <Field Name="ActiveIconFileDataID" Type="int" />
  </Table>
  <Table Name="SpellMiscDifficulty" Build="23835">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellMissile" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="int" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="byte" />
  </Table>
  <Table Name="SpellMissileMotion" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="SpellPower" Build="23835">
    <Field Name="SpellID" Type="uint" />
    <Field Name="ManaCost" Type="uint" />
    <Field Name="ManaCostPercentage" Type="float" />
    <Field Name="ManaCostPercentagePerSecond" Type="float" />
    <Field Name="RequiredAura" Type="uint" />
    <Field Name="HealthCostPercentage" Type="float" />
    <Field Name="PowerIndex" Type="byte" />
    <Field Name="PowerType" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ManaCostPerLevel" Type="int" />
    <Field Name="ManaCostPerSecond" Type="int" />
    <Field Name="ManaCostAdditional" Type="uint" />
    <Field Name="PowerDisplayID" Type="uint" />
    <Field Name="UnitPowerBarID" Type="uint" />
  </Table>
  <Table Name="SpellPowerDifficulty" Build="23835">
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="PowerIndex" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellProceduralEffect" Build="23835">
    <Field Name="Field00" Type="int" ArraySize="4" />
    <Field Name="Field10" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellProcsPerMinute" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="BaseProcRate" Type="float" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="SpellProcsPerMinuteMod" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Coeff" Type="float" />
    <Field Name="Param" Type="ushort" />
    <Field Name="Type" Type="byte" />
    <Field Name="SpellProcsPerMinuteID" Type="byte" />
  </Table>
  <Table Name="SpellRadius" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Radius" Type="float" />
    <Field Name="RadiusPerLevel" Type="float" />
    <Field Name="RadiusMin" Type="float" />
    <Field Name="RadiusMax" Type="float" />
  </Table>
  <Table Name="SpellRange" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MinRange" Type="float" ArraySize="2" />
    <Field Name="MaxRange" Type="float" ArraySize="2" />
    <Field Name="DisplayName" Type="string" />
    <Field Name="DisplayNameShort" Type="string" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="SpellReagents" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="Reagent" Type="int" ArraySize="8" />
    <Field Name="ReagentCount" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="SpellReagentsCurrency" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="SpellScaling" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="ScalesFromItemLevel" Type="ushort" />
    <Field Name="ScalingClass" Type="int" />
    <Field Name="MinScalingLevel" Type="uint" />
    <Field Name="MaxScalingLevel" Type="uint" />
  </Table>
  <Table Name="SpellShapeshift" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="ShapeshiftExclude" Type="uint" ArraySize="2" />
    <Field Name="ShapeshiftMask" Type="uint" ArraySize="2" />
    <Field Name="StanceBarOrder" Type="byte" />
  </Table>
  <Table Name="SpellShapeshiftForm" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="WeaponDamageVariance" Type="float" />
    <Field Name="Flags" Type="uint" />
    <Field Name="CombatRoundTime" Type="ushort" />
    <Field Name="MountTypeID" Type="ushort" />
    <Field Name="CreatureType" Type="byte" />
    <Field Name="BonusActionBar" Type="byte" />
    <Field Name="AttackIconFileDataID" Type="uint" />
    <Field Name="CreatureDisplayID" Type="uint" ArraySize="4" />
    <Field Name="PresetSpellID" Type="uint" ArraySize="8" />
  </Table>
  <Table Name="SpellSpecialUnitEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="SpellTargetRestrictions" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="ConeAngle" Type="float" />
    <Field Name="Width" Type="float" />
    <Field Name="Targets" Type="uint" />
    <Field Name="TargetCreatureType" Type="ushort" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="MaxAffectedTargets" Type="byte" />
    <Field Name="MaxTargetLevel" Type="uint" />
  </Table>
  <Table Name="SpellTotems" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="Totem" Type="uint" ArraySize="2" />
    <Field Name="RequiredTotemCategoryID" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="SpellVisual" Build="23835">
    <Field Name="Field000" Type="uint" />
    <Field Name="Field004" Type="uint" />
    <Field Name="Field008" Type="uint" />
    <Field Name="Field00C" Type="uint" />
    <Field Name="Field010" Type="uint" />
    <Field Name="Field014" Type="uint" />
    <Field Name="Field018" Type="uint" />
    <Field Name="Field01C" Type="uint" />
    <Field Name="Field020" Type="uint" />
    <Field Name="Field024" Type="uint" />
    <Field Name="Field028" Type="uint" />
    <Field Name="Field02C" Type="uint" />
    <Field Name="Field030" Type="uint" />
    <Field Name="Field034" Type="uint" />
    <Field Name="Field038" Type="uint" />
    <Field Name="Field03C" Type="int" ArraySize="3" />
    <Field Name="Field048" Type="int" ArraySize="3" />
    <Field Name="Field054" Type="uint" />
    <Field Name="Field058" Type="uint" />
    <Field Name="Field05C" Type="uint" />
    <Field Name="Field060" Type="uint" />
    <Field Name="Field064" Type="uint" />
    <Field Name="Field068" Type="ushort" />
    <Field Name="Field06A" Type="ushort" />
    <Field Name="Field06C" Type="byte" />
    <Field Name="Field06D" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field071" Type="int" />
    <Field Name="Field074" Type="byte" />
    <Field Name="Field075" Type="byte" ArraySize="3" />
  </Table>
  <Table Name="SpellVisualAnim" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="ushort" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="SpellVisualColorEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="ushort" />
  </Table>
  <Table Name="SpellVisualEffectName" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="uint" />
    <Field Name="Field24" Type="uint" />
    <Field Name="Field28" Type="uint" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="ushort" />
  </Table>
  <Table Name="SpellVisualKit" Build="23835">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field0F" Type="byte" />
  </Table>
  <Table Name="SpellVisualKitAreaModel" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="SpellVisualKitEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="uint" />
  </Table>
  <Table Name="SpellVisualKitModelAttach" Build="23835">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" ArraySize="3" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field26" Type="float" />
    <Field Name="Field2A" Type="float" />
    <Field Name="Field2E" Type="float" />
    <Field Name="Field32" Type="float" />
    <Field Name="Field36" Type="float" />
    <Field Name="Field3A" Type="float" />
    <Field Name="Field3E" Type="float" />
    <Field Name="Field42" Type="float" />
    <Field Name="Field44" Type="ushort" DefaultValue="65535" />
    <Field Name="Field46" Type="ushort" DefaultValue="65535" />
    <Field Name="Field48" Type="ushort" DefaultValue="65535" />
    <Field Name="Field4A" Type="ushort" />
    <Field Name="Field4E" Type="int" />
    <Field Name="Field52" Type="float" />
  </Table>
  <Table Name="SpellVisualMissile" Build="23835">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field0C" Type="int" ArraySize="3" />
    <Field Name="Field18" Type="int" ArraySize="3" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field34" Type="int" />
    <Field Name="Field37" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="SpellXSpellVisual" Build="23835">
    <Field Name="SpellID" Type="uint" />
    <Field Name="SpellVisualID" Type="uint" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Chance" Type="float" DefaultValue="1" />
    <Field Name="CasterPlayerConditionID" Type="ushort" />
    <Field Name="CasterUnitConditionID" Type="ushort" />
    <Field Name="PlayerConditionID" Type="ushort" />
    <Field Name="UnitConditionID" Type="ushort" />
    <Field Name="IconFileDataID" Type="uint" />
    <Field Name="ActiveIconFileDataID" Type="uint" />
    <Field Name="Flags" Type="byte" />
    <Field Name="DifficultyID" Type="byte" />
    <Field Name="Priority" Type="byte" />
  </Table>
  <Table Name="Startup_Strings" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="string" />
  </Table>
  <Table Name="StartupFiles" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="Stationery" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="ushort" />
  </Table>
  <Table Name="StringLookups" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="SummonProperties" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" />
    <Field Name="Category" Type="uint" />
    <Field Name="Faction" Type="uint" />
    <Field Name="Type" Type="int" />
    <Field Name="Slot" Type="int" />
  </Table>
  <Table Name="TactKey" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Key" Type="byte" ArraySize="16" />
  </Table>
  <Table Name="TactKeyLookup" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" ArraySize="4" />
  </Table>
  <Table Name="Talent" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SpellID" Type="uint" />
    <Field Name="OverridesSpellID" Type="uint" />
    <Field Name="Description" Type="string" />
    <Field Name="SpecID" Type="ushort" />
    <Field Name="TierID" Type="byte" />
    <Field Name="ColumnIndex" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="CategoryMask" Type="byte" ArraySize="2" />
    <Field Name="ClassID" Type="byte" />
  </Table>
  <Table Name="TaxiNodes" Build="23835">
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="Name" Type="string" />
    <Field Name="MountCreatureID" Type="uint" ArraySize="2" />
    <Field Name="MapOffset" Type="float" ArraySize="2" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="ConditionID" Type="ushort" />
    <Field Name="LearnableIndex" Type="ushort" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TaxiPath" Build="23835">
    <Field Name="From" Type="ushort" />
    <Field Name="To" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Cost" Type="uint" />
  </Table>
  <Table Name="TaxiPathNode" Build="23835">
    <Field Name="Loc" Type="float" ArraySize="3" />
    <Field Name="Delay" Type="uint" />
    <Field Name="PathID" Type="ushort" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="ArrivalEventID" Type="ushort" />
    <Field Name="DepartureEventID" Type="ushort" />
    <Field Name="NodeIndex" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TerrainMaterial" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="TerrainType" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="TerrainTypeSounds" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="TextureBlendSet" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" ArraySize="3" />
    <Field Name="Field10" Type="int" ArraySize="3" />
    <Field Name="Field1C" Type="int" ArraySize="3" />
    <Field Name="Field28" Type="float" ArraySize="3" />
    <Field Name="Field34" Type="float" ArraySize="3" />
    <Field Name="Field40" Type="float" ArraySize="4" />
    <Field Name="Field50" Type="byte" />
    <Field Name="Field51" Type="byte" />
    <Field Name="Field52" Type="byte" />
    <Field Name="Field53" Type="byte" />
  </Table>
  <Table Name="TextureFileData" Build="23835">
    <Field Name="Field0" Type="uint" />
    <Field Name="Field4" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TotemCategory" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="CategoryMask" Type="uint" />
    <Field Name="CategoryType" Type="byte" />
  </Table>
  <Table Name="Toy" Build="23835">
    <Field Name="ItemID" Type="uint" />
    <Field Name="Description" Type="string" />
    <Field Name="Flags" Type="byte" />
    <Field Name="CategoryFilter" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TradeSkillCategory" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="TradeSkillItem" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
  </Table>
  <Table Name="TransformMatrix" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
  </Table>
  <Table Name="TransmogHoliday" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="TransmogSet" Build="23835">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="TransmogSetGroup" Build="23835">
    <Field Name="Field00" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TransmogSetItem" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field3" Type="ushort" />
    <Field Name="Field5" Type="int" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="TransportAnimation" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TransportID" Type="uint" />
    <Field Name="TimeIndex" Type="uint" />
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="SequenceID" Type="byte" />
  </Table>
  <Table Name="TransportPhysics" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
  </Table>
  <Table Name="TransportRotation" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TransportID" Type="uint" />
    <Field Name="TimeIndex" Type="uint" />
    <Field Name="Position" Type="float" ArraySize="4" />
  </Table>
  <Table Name="Trophy" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="ushort" />
  </Table>
  <Table Name="UiCamera" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" ArraySize="3" />
    <Field Name="Field14" Type="int" ArraySize="3" />
    <Field Name="Field20" Type="float" ArraySize="3" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
  </Table>
  <Table Name="UiCameraType" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="UiCamFbackTransmogChrRace" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="UiCamFbackTransmogWeapon" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="ushort" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="byte" />
    <Field Name="Field8" Type="byte" />
  </Table>
  <Table Name="UiMapPOI" Build="23835">
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="UiModelScene" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
  </Table>
  <Table Name="UiModelSceneActor" Build="23835">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field25" Type="int" />
    <Field Name="Field29" Type="int" />
  </Table>
  <Table Name="UiModelSceneActorDisplay" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
  </Table>
  <Table Name="UiModelSceneCamera" Build="23835">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" ArraySize="3" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="byte" />
    <Field Name="Field41" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field46" Type="int" />
  </Table>
  <Table Name="UiTextureAtlas" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="UiTextureAtlasMember" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
  </Table>
  <Table Name="UiTextureKit" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="UnitBlood" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" ArraySize="5" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="int" />
    <Field Name="Field21" Type="int" />
    <Field Name="Field24" Type="int" />
  </Table>
  <Table Name="UnitBloodLevels" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
  </Table>
  <Table Name="UnitCondition" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" ArraySize="8" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" ArraySize="8" />
    <Field Name="Field2D" Type="byte" ArraySize="7" />
  </Table>
  <Table Name="UnitPowerBar" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RegenerationPeace" Type="float" />
    <Field Name="RegenerationCombat" Type="float" />
    <Field Name="FileDataID" Type="uint" ArraySize="6" />
    <Field Name="Color" Type="uint" ArraySize="6" />
    <Field Name="Name" Type="string" />
    <Field Name="Cost" Type="string" />
    <Field Name="OutOfError" Type="string" />
    <Field Name="ToolTip" Type="string" />
    <Field Name="StartInset" Type="float" />
    <Field Name="EndInset" Type="float" />
    <Field Name="StartPower" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="CenterPower" Type="byte" />
    <Field Name="BarType" Type="byte" />
    <Field Name="MinPower" Type="uint" />
    <Field Name="MaxPower" Type="uint" />
  </Table>
  <Table Name="Vehicle" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" />
    <Field Name="TurnSpeed" Type="float" />
    <Field Name="PitchSpeed" Type="float" />
    <Field Name="PitchMin" Type="float" />
    <Field Name="PitchMax" Type="float" />
    <Field Name="MouseLookOffsetPitch" Type="float" />
    <Field Name="CameraFadeDistScalarMin" Type="float" />
    <Field Name="CameraFadeDistScalarMax" Type="float" />
    <Field Name="CameraPitchOffset" Type="float" />
    <Field Name="FacingLimitRight" Type="float" />
    <Field Name="FacingLimitLeft" Type="float" />
    <Field Name="MsslTrgtTurnLingering" Type="float" />
    <Field Name="MsslTrgtPitchLingering" Type="float" />
    <Field Name="MsslTrgtMouseLingering" Type="float" />
    <Field Name="MsslTrgtEndOpacity" Type="float" />
    <Field Name="MsslTrgtArcSpeed" Type="float" />
    <Field Name="MsslTrgtArcRepeat" Type="float" />
    <Field Name="MsslTrgtArcWidth" Type="float" />
    <Field Name="MsslTrgtImpactRadius" Type="float" ArraySize="2" />
    <Field Name="MsslTrgtArcTexture" Type="string" />
    <Field Name="MsslTrgtImpactTexture" Type="string" />
    <Field Name="MsslTrgtImpactModel" Type="string" ArraySize="2" />
    <Field Name="CameraYawOffset" Type="float" />
    <Field Name="MsslTrgtImpactTexRadius" Type="float" />
    <Field Name="SeatID" Type="ushort" ArraySize="8" />
    <Field Name="VehicleUIIndicatorID" Type="ushort" />
    <Field Name="PowerDisplayID" Type="ushort" ArraySize="3" />
    <Field Name="FlagsB" Type="byte" />
    <Field Name="UILocomotionType" Type="byte" />
  </Table>
  <Table Name="VehicleSeat" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="uint" ArraySize="3" />
    <Field Name="AttachmentOffset" Type="float" ArraySize="3" />
    <Field Name="EnterPreDelay" Type="float" />
    <Field Name="EnterSpeed" Type="float" />
    <Field Name="EnterGravity" Type="float" />
    <Field Name="EnterMinDuration" Type="float" />
    <Field Name="EnterMaxDuration" Type="float" />
    <Field Name="EnterMinArcHeight" Type="float" />
    <Field Name="EnterMaxArcHeight" Type="float" />
    <Field Name="ExitPreDelay" Type="float" />
    <Field Name="ExitSpeed" Type="float" />
    <Field Name="ExitGravity" Type="float" />
    <Field Name="ExitMinDuration" Type="float" />
    <Field Name="ExitMaxDuration" Type="float" />
    <Field Name="ExitMinArcHeight" Type="float" />
    <Field Name="ExitMaxArcHeight" Type="float" />
    <Field Name="PassengerYaw" Type="float" />
    <Field Name="PassengerPitch" Type="float" />
    <Field Name="PassengerRoll" Type="float" />
    <Field Name="VehicleEnterAnimDelay" Type="float" />
    <Field Name="VehicleExitAnimDelay" Type="float" />
    <Field Name="CameraEnteringDelay" Type="float" />
    <Field Name="CameraEnteringDuration" Type="float" />
    <Field Name="CameraExitingDelay" Type="float" />
    <Field Name="CameraExitingDuration" Type="float" />
    <Field Name="CameraOffset" Type="float" ArraySize="3" />
    <Field Name="CameraPosChaseRate" Type="float" />
    <Field Name="CameraFacingChaseRate" Type="float" />
    <Field Name="CameraEnteringZoom" Type="float" />
    <Field Name="CameraSeatZoomMin" Type="float" />
    <Field Name="CameraSeatZoomMax" Type="float" />
    <Field Name="UISkinFileDataID" Type="uint" />
    <Field Name="EnterAnimStart" Type="ushort" />
    <Field Name="EnterAnimLoop" Type="ushort" />
    <Field Name="RideAnimStart" Type="ushort" />
    <Field Name="RideAnimLoop" Type="ushort" />
    <Field Name="RideUpperAnimStart" Type="ushort" />
    <Field Name="RideUpperAnimLoop" Type="ushort" />
    <Field Name="ExitAnimStart" Type="ushort" />
    <Field Name="ExitAnimLoop" Type="ushort" />
    <Field Name="ExitAnimEnd" Type="ushort" />
    <Field Name="VehicleEnterAnim" Type="ushort" />
    <Field Name="VehicleExitAnim" Type="ushort" />
    <Field Name="VehicleRideAnimLoop" Type="ushort" />
    <Field Name="EnterAnimKitID" Type="ushort" />
    <Field Name="RideAnimKitID" Type="ushort" />
    <Field Name="ExitAnimKitID" Type="ushort" />
    <Field Name="VehicleEnterAnimKitID" Type="ushort" />
    <Field Name="VehicleRideAnimKitID" Type="ushort" />
    <Field Name="VehicleExitAnimKitID" Type="ushort" />
    <Field Name="CameraModeID" Type="ushort" />
    <Field Name="AttachmentID" Type="byte" />
    <Field Name="PassengerAttachmentID" Type="byte" />
    <Field Name="VehicleEnterAnimBone" Type="byte" />
    <Field Name="VehicleExitAnimBone" Type="byte" />
    <Field Name="VehicleRideAnimLoopBone" Type="byte" />
    <Field Name="VehicleAbilityDisplay" Type="byte" />
    <Field Name="EnterUISoundID" Type="uint" />
    <Field Name="ExitUISoundID" Type="uint" />
  </Table>
  <Table Name="VehicleUIIndicator" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="VehicleUIIndSeat" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="VideoHardware" Build="23835">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" ArraySize="2" />
  </Table>
  <Table Name="Vignette" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
  </Table>
  <Table Name="VocalUISounds" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="byte" />
    <Field Name="Field7" Type="ushort" />
  </Table>
  <Table Name="WbAccessControlList" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="WbCertWhitelist" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="WeaponImpactSounds" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="int" ArraySize="11" />
    <Field Name="Field28" Type="int" ArraySize="11" />
    <Field Name="Field49" Type="ushort" ArraySize="11" />
    <Field Name="Field5F" Type="ushort" ArraySize="9" />
  </Table>
  <Table Name="WeaponSwingSounds2" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="byte" />
    <Field Name="Field5" Type="byte" />
    <Field Name="Field6" Type="ushort" />
  </Table>
  <Table Name="WeaponTrail" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="uint" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field14" Type="uint" ArraySize="3" />
    <Field Name="Field20" Type="uint" ArraySize="3" />
    <Field Name="Field2C" Type="uint" ArraySize="3" />
    <Field Name="Field38" Type="float" ArraySize="3" />
    <Field Name="Field44" Type="float" ArraySize="2" />
  </Table>
  <Table Name="WeaponTrailModelDef" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="WeaponTrailParam" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
  </Table>
  <Table Name="Weather" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" ArraySize="3" />
    <Field Name="Field1C" Type="string" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="ushort" />
    <Field Name="Field36" Type="byte" />
    <Field Name="Field37" Type="byte" />
    <Field Name="Field38" Type="byte" />
    <Field Name="Field39" Type="ushort" />
  </Table>
  <Table Name="WindSettings" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="int" ArraySize="3" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" ArraySize="3" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" ArraySize="3" />
    <Field Name="Field38" Type="uint" />
    <Field Name="Field3C" Type="uint" />
    <Field Name="Field40" Type="byte" />
  </Table>
  <Table Name="WMOAreaTable" Build="23835">
    <Field Name="WMOGroupID" Type="int" />
    <Field Name="AreaName" Type="string" />
    <Field Name="WMOID" Type="ushort" />
    <Field Name="AmbienceID" Type="ushort" />
    <Field Name="ZoneMusic" Type="ushort" />
    <Field Name="IntroSound" Type="ushort" />
    <Field Name="AreaTableID" Type="ushort" />
    <Field Name="UWIntroSound" Type="ushort" />
    <Field Name="UWAmbience" Type="ushort" />
    <Field Name="NameSet" Type="byte" />
    <Field Name="SoundProviderPref" Type="byte" />
    <Field Name="SoundProviderPrefUnderwater" Type="byte" />
    <Field Name="Flags" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="UWZoneMusic" Type="uint" />
  </Table>
  <Table Name="WmoMinimapTexture" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="World_PVP_Area" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
  </Table>
  <Table Name="WorldBossLockout" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="ushort" />
  </Table>
  <Table Name="WorldChunkSounds" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="WorldEffect" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="ushort" />
  </Table>
  <Table Name="WorldElapsedTimer" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
    <Field Name="Field8" Type="byte" />
    <Field Name="Field9" Type="byte" />
  </Table>
  <Table Name="WorldMapArea" Build="23835">
    <Field Name="AreaName" Type="string" />
    <Field Name="LocLeft" Type="float" />
    <Field Name="LocRight" Type="float" />
    <Field Name="LocTop" Type="float" />
    <Field Name="LocBottom" Type="float" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="AreaID" Type="ushort" />
    <Field Name="DisplayMapID" Type="ushort" />
    <Field Name="DefaultDungeonFloor" Type="ushort" />
    <Field Name="ParentWorldMapID" Type="ushort" />
    <Field Name="Flags" Type="ushort" />
    <Field Name="LevelRangeMin" Type="byte" />
    <Field Name="LevelRangeMax" Type="byte" />
    <Field Name="BountySetID" Type="byte" />
    <Field Name="BountyBoardLocation" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PlayerConditionID" Type="uint" />
  </Table>
  <Table Name="WorldMapContinent" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="2" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" ArraySize="2" />
    <Field Name="Field18" Type="float" ArraySize="2" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
  </Table>
  <Table Name="WorldMapOverlay" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TextureName" Type="string" />
    <Field Name="TextureWidth" Type="ushort" />
    <Field Name="TextureHeight" Type="ushort" />
    <Field Name="MapAreaID" Type="uint" />
    <Field Name="AreaID" Type="uint" ArraySize="4" />
    <Field Name="OffsetX" Type="int" />
    <Field Name="OffsetY" Type="int" />
    <Field Name="HitRectTop" Type="int" />
    <Field Name="HitRectLeft" Type="int" />
    <Field Name="HitRectBottom" Type="int" />
    <Field Name="HitRectRight" Type="int" />
    <Field Name="PlayerConditionID" Type="uint" />
    <Field Name="Flags" Type="uint" />
  </Table>
  <Table Name="WorldMapTransforms" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Region" Type="float" ArraySize="6" />
    <Field Name="RegionOffset" Type="float" ArraySize="2" />
    <Field Name="RegionScale" Type="float" />
    <Field Name="MapID" Type="ushort" />
    <Field Name="AreaID" Type="ushort" />
    <Field Name="NewMapID" Type="ushort" />
    <Field Name="NewDungeonMapID" Type="ushort" />
    <Field Name="NewAreaID" Type="ushort" />
    <Field Name="Flags" Type="byte" />
  </Table>
  <Table Name="WorldSafeLocs" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Loc" Type="float" ArraySize="3" />
    <Field Name="Facing" Type="float" />
    <Field Name="AreaName" Type="string" />
    <Field Name="MapID" Type="ushort" />
  </Table>
  <Table Name="WorldState" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="WorldStateExpression" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field4" Type="string" />
  </Table>
  <Table Name="WorldStateUI" Build="23835">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" ArraySize="3" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="WorldStateZoneSounds" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="byte" />
  </Table>
  <Table Name="ZoneIntroMusicTable" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="int" />
  </Table>
  <Table Name="ZoneLight" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="ZoneLightPoint" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" ArraySize="2" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="ZoneMusic" Build="23835">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="uint" ArraySize="2" />
    <Field Name="Field10" Type="uint" ArraySize="2" />
    <Field Name="Field18" Type="int" />
  </Table>
</Definition>