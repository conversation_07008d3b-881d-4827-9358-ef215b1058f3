<?xml version="1.0"?>
<Definition xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <Table Name="Achievement" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="byte" />
    <Field Name="Field20" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Achievement_Category" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AdventureJournal" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" ArraySize="2" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" ArraySize="2" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" />
  </Table>
  <Table Name="AdventureMapPOI" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="int" />
    <Field Name="Field19" Type="int" />
    <Field Name="Field1D" Type="int" />
    <Field Name="Field21" Type="int" />
    <Field Name="Field25" Type="int" />
    <Field Name="Field29" Type="int" />
    <Field Name="Field2D" Type="int" />
    <Field Name="Field31" Type="int" />
  </Table>
  <Table Name="AnimationData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="AnimKit" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
  </Table>
  <Table Name="AnimKitBoneSet" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="AnimKitBoneSetAlias" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
  </Table>
  <Table Name="AnimKitConfig" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
  </Table>
  <Table Name="AnimKitConfigBoneSet" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="AnimKitPriority" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
  </Table>
  <Table Name="AnimKitSegment" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="byte" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="int" />
  </Table>
  <Table Name="AnimReplacement" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
  </Table>
  <Table Name="AnimReplacementSet" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
  </Table>
  <Table Name="AreaAssignment" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="AreaGroupMember" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="AreaPOI" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
  </Table>
  <Table Name="AreaPOIState" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="AreaTable" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="2" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" ArraySize="4" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" />
    <Field Name="Field34" Type="byte" />
    <Field Name="Field35" Type="byte" />
    <Field Name="Field36" Type="int" />
  </Table>
  <Table Name="AreaTrigger" Build="22578">
    <Field Name="Field00" Type="float" ArraySize="3" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="AreaTriggerActionSet" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
  </Table>
  <Table Name="AreaTriggerBox" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="3" />
  </Table>
  <Table Name="AreaTriggerCylinder" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
  </Table>
  <Table Name="AreaTriggerSphere" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
  </Table>
  <Table Name="ArmorLocation" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
  </Table>
  <Table Name="Artifact" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
  </Table>
  <Table Name="ArtifactAppearance" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field22" Type="int" />
    <Field Name="Field26" Type="int" />
  </Table>
  <Table Name="ArtifactAppearanceSet" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ArtifactCategory" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="ArtifactPower" Build="22578">
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field0F" Type="int" />
  </Table>
  <Table Name="ArtifactPowerLink" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="ArtifactPowerRank" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="ArtifactQuestXP" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="10" />
  </Table>
  <Table Name="ArtifactUnlock" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="AuctionHouse" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="BankBagSlotPrices" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
  </Table>
  <Table Name="BannedAddOns" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="BarberShopStyle" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="BattlemasterList" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" ArraySize="16" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" />
    <Field Name="Field34" Type="byte" />
    <Field Name="Field35" Type="byte" />
    <Field Name="Field36" Type="byte" />
    <Field Name="Field37" Type="byte" />
    <Field Name="Field38" Type="byte" />
  </Table>
  <Table Name="BattlePetAbility" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="BattlePetAbilityEffect" Build="22578">
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" ArraySize="6" />
    <Field Name="Field14" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="BattlePetAbilityState" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="BattlePetAbilityTurn" Build="22578">
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="BattlePetBreedQuality" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="BattlePetBreedState" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="BattlePetEffectProperties" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" ArraySize="6" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="byte" ArraySize="6" />
  </Table>
  <Table Name="BattlePetNPCTeamMember" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="BattlePetSpecies" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="BattlePetSpeciesState" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="BattlePetSpeciesXAbility" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="BattlePetState" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="BattlePetVisual" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="BoneWindModifierModel" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="BoneWindModifiers" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="3" />
    <Field Name="Field0C" Type="float" />
  </Table>
  <Table Name="Bounty" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="BountySet" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="BroadcastText" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" ArraySize="3" />
    <Field Name="Field0E" Type="ushort" ArraySize="3" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="int" ArraySize="2" />
    <Field Name="Field20" Type="int" />
  </Table>
  <Table Name="CameraEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
  </Table>
  <Table Name="CameraEffectEntry" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
  </Table>
  <Table Name="CameraMode" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="3" />
    <Field Name="Field0C" Type="float" ArraySize="3" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="byte" />
  </Table>
  <Table Name="CameraShakes" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="int" />
  </Table>
  <Table Name="CastableRaidBuffs" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="Cfg_Categories" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="Cfg_Configs" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="Cfg_Regions" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="CharacterFaceBoneSet" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="CharacterFacialHairStyles" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="5" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="CharacterLoadout" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="CharacterLoadoutItem" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="CharBaseInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
  </Table>
  <Table Name="CharBaseSection" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="CharComponentTextureLayouts" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="CharComponentTextureSections" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="CharHairGeosets" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="int" />
  </Table>
  <Table Name="CharSections" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="3" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
  </Table>
  <Table Name="CharShipment" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
  </Table>
  <Table Name="CharShipmentContainer" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="int" />
  </Table>
  <Table Name="CharStartOutfit" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="24" />
    <Field Name="Field60" Type="int" />
    <Field Name="Field64" Type="byte" />
    <Field Name="Field65" Type="byte" />
    <Field Name="Field66" Type="byte" />
    <Field Name="Field67" Type="byte" />
    <Field Name="Field68" Type="byte" />
  </Table>
  <Table Name="CharTitles" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="ChatChannels" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="ChatProfanity" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="ChrClasses" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ChrClassesXPowerTypes" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
  </Table>
  <Table Name="ChrClassRaceSex" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field0B" Type="int" />
  </Table>
  <Table Name="ChrClassTitle" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="ChrClassUIDisplay" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="ChrClassVillain" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="ChrRaces" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="string" ArraySize="2" />
    <Field Name="Field20" Type="string" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2C" Type="float" ArraySize="3" />
    <Field Name="Field38" Type="float" ArraySize="3" />
    <Field Name="Field44" Type="int" />
    <Field Name="Field48" Type="ushort" />
    <Field Name="Field4A" Type="ushort" />
    <Field Name="Field4C" Type="ushort" />
    <Field Name="Field4E" Type="ushort" />
    <Field Name="Field50" Type="ushort" />
    <Field Name="Field52" Type="ushort" />
    <Field Name="Field54" Type="ushort" />
    <Field Name="Field56" Type="ushort" />
    <Field Name="Field58" Type="ushort" />
    <Field Name="Field5A" Type="byte" />
    <Field Name="Field5B" Type="byte" />
    <Field Name="Field5C" Type="byte" />
    <Field Name="Field5D" Type="byte" />
    <Field Name="Field5E" Type="byte" />
    <Field Name="Field5F" Type="byte" />
    <Field Name="Field60" Type="byte" />
    <Field Name="Field61" Type="byte" />
    <Field Name="Field62" Type="byte" />
    <Field Name="Field63" Type="byte" />
    <Field Name="Field64" Type="int" />
    <Field Name="Field68" Type="int" />
    <Field Name="Field6C" Type="int" ArraySize="3" />
  </Table>
  <Table Name="ChrSpecialization" Build="22578">
    <Field Name="Field00" Type="int" ArraySize="2" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field23" Type="int" />
    <Field Name="Field27" Type="int" />
  </Table>
  <Table Name="ChrUpgradeBucket" Build="22578">
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ChrUpgradeBucketSpell" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="ChrUpgradeTier" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="CinematicCamera" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="ushort" />
  </Table>
  <Table Name="CinematicSequences" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="CloakDampening" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="5" />
    <Field Name="Field14" Type="float" ArraySize="5" />
    <Field Name="Field28" Type="float" ArraySize="2" />
    <Field Name="Field30" Type="float" ArraySize="2" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="float" />
  </Table>
  <Table Name="CombatCondition" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" ArraySize="2" />
    <Field Name="Field0A" Type="ushort" ArraySize="2" />
    <Field Name="Field0E" Type="byte" ArraySize="2" />
    <Field Name="Field10" Type="byte" ArraySize="2" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" ArraySize="2" />
    <Field Name="Field15" Type="byte" ArraySize="2" />
    <Field Name="Field17" Type="byte" />
  </Table>
  <Table Name="ComponentModelFileData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="ComponentTextureFileData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="ConversationLine" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
  </Table>
  <Table Name="Creature" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="3" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" ArraySize="4" />
    <Field Name="Field20" Type="float" ArraySize="4" />
    <Field Name="Field30" Type="string" />
    <Field Name="Field34" Type="string" />
    <Field Name="Field38" Type="string" />
    <Field Name="Field3C" Type="string" />
    <Field Name="Field40" Type="byte" />
    <Field Name="Field41" Type="byte" />
    <Field Name="Field42" Type="byte" />
    <Field Name="Field43" Type="byte" />
  </Table>
  <Table Name="CreatureDifficulty" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" ArraySize="7" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
  </Table>
  <Table Name="CreatureDisplayInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="int" ArraySize="3" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field30" Type="ushort" />
    <Field Name="Field32" Type="ushort" />
    <Field Name="Field34" Type="ushort" />
    <Field Name="Field36" Type="ushort" />
    <Field Name="Field38" Type="byte" />
    <Field Name="Field39" Type="byte" />
    <Field Name="Field3A" Type="byte" />
    <Field Name="Field3B" Type="byte" />
    <Field Name="Field3C" Type="byte" />
    <Field Name="Field3D" Type="byte" />
  </Table>
  <Table Name="CreatureDisplayInfoCond" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" ArraySize="2" />
    <Field Name="Field0C" Type="int" ArraySize="2" />
    <Field Name="Field14" Type="int" ArraySize="2" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="int" />
    <Field Name="Field22" Type="int" />
    <Field Name="Field26" Type="int" />
    <Field Name="Field2A" Type="int" />
    <Field Name="Field2E" Type="int" />
    <Field Name="Field32" Type="int" />
    <Field Name="Field36" Type="int" />
    <Field Name="Field3A" Type="int" />
    <Field Name="Field3E" Type="int" ArraySize="3" />
  </Table>
  <Table Name="CreatureDisplayInfoExtra" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" ArraySize="3" />
    <Field Name="Field13" Type="byte" />
  </Table>
  <Table Name="CreatureDisplayInfoTrn" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="ushort" />
  </Table>
  <Table Name="CreatureDispXUiCamera" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="CreatureFamily" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" ArraySize="2" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
  </Table>
  <Table Name="CreatureImmunities" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="2" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="int" />
    <Field Name="Field11" Type="int" ArraySize="8" />
    <Field Name="Field31" Type="int" ArraySize="16" />
  </Table>
  <Table Name="CreatureModelData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" ArraySize="6" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="float" />
    <Field Name="Field44" Type="float" />
    <Field Name="Field48" Type="float" />
    <Field Name="Field4C" Type="float" />
    <Field Name="Field50" Type="float" />
    <Field Name="Field54" Type="float" />
    <Field Name="Field58" Type="float" />
    <Field Name="Field5C" Type="int" />
    <Field Name="Field60" Type="int" />
    <Field Name="Field64" Type="int" />
    <Field Name="Field68" Type="int" />
    <Field Name="Field6C" Type="int" />
    <Field Name="Field70" Type="int" />
    <Field Name="Field74" Type="int" />
    <Field Name="Field78" Type="int" />
    <Field Name="Field7C" Type="int" />
    <Field Name="Field80" Type="int" />
    <Field Name="Field84" Type="int" />
    <Field Name="Field88" Type="int" />
  </Table>
  <Table Name="CreatureMovementInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
  </Table>
  <Table Name="CreatureSoundData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="int" />
    <Field Name="Field0E" Type="int" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field16" Type="int" />
    <Field Name="Field1A" Type="int" />
    <Field Name="Field1E" Type="int" />
    <Field Name="Field22" Type="int" />
    <Field Name="Field26" Type="int" />
    <Field Name="Field2A" Type="int" />
    <Field Name="Field2E" Type="int" />
    <Field Name="Field32" Type="int" />
    <Field Name="Field36" Type="int" />
    <Field Name="Field3A" Type="int" />
    <Field Name="Field3E" Type="int" ArraySize="5" />
    <Field Name="Field52" Type="int" ArraySize="4" />
    <Field Name="Field62" Type="int" />
    <Field Name="Field66" Type="int" />
    <Field Name="Field6A" Type="int" />
    <Field Name="Field6E" Type="int" />
    <Field Name="Field72" Type="int" />
    <Field Name="Field76" Type="int" />
    <Field Name="Field7A" Type="int" />
    <Field Name="Field7E" Type="int" />
    <Field Name="Field82" Type="int" />
    <Field Name="Field86" Type="int" />
    <Field Name="Field8A" Type="int" />
    <Field Name="Field8E" Type="int" />
    <Field Name="Field92" Type="int" />
    <Field Name="Field96" Type="int" />
    <Field Name="Field9A" Type="int" />
    <Field Name="Field9E" Type="int" />
    <Field Name="FieldA2" Type="int" />
    <Field Name="FieldA6" Type="int" />
  </Table>
  <Table Name="CreatureType" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="Criteria" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="CriteriaTree" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="int" />
  </Table>
  <Table Name="CriteriaTreeXEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="CurrencyCategory" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="CurrencyTypes" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" ArraySize="2" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="int" />
  </Table>
  <Table Name="Curve" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
  </Table>
  <Table Name="CurvePoint" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="DeathThudLookups" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="DecalProperties" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="int" />
    <Field Name="Field2A" Type="int" />
    <Field Name="Field2E" Type="int" />
  </Table>
  <Table Name="DeclinedWord" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="DeclinedWordCases" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="DestructibleModelData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
  </Table>
  <Table Name="DeviceBlacklist" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="DeviceDefaultSettings" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="Difficulty" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
  </Table>
  <Table Name="DissolveEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="int" />
    <Field Name="Field22" Type="int" />
  </Table>
  <Table Name="DriverBlacklist" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="DungeonEncounter" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="int" />
  </Table>
  <Table Name="DungeonMap" Build="22578">
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="float" ArraySize="2" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="DungeonMapChunk" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
  </Table>
  <Table Name="DurabilityCosts" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" ArraySize="21" />
    <Field Name="Field2A" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="DurabilityQuality" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
  </Table>
  <Table Name="EdgeGlowEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="byte" />
  </Table>
  <Table Name="Emotes" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="int" />
    <Field Name="Field13" Type="int" />
    <Field Name="Field17" Type="int" />
    <Field Name="Field1B" Type="int" />
  </Table>
  <Table Name="EmotesText" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="EmotesTextData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="EmotesTextSound" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="EnvironmentalDamage" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="Exhaustion" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Faction" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="4" />
    <Field Name="Field10" Type="int" ArraySize="4" />
    <Field Name="Field20" Type="float" ArraySize="2" />
    <Field Name="Field28" Type="string" />
    <Field Name="Field2C" Type="string" />
    <Field Name="Field30" Type="int" ArraySize="4" />
    <Field Name="Field40" Type="ushort" />
    <Field Name="Field42" Type="ushort" ArraySize="4" />
    <Field Name="Field4A" Type="ushort" ArraySize="4" />
    <Field Name="Field52" Type="ushort" />
    <Field Name="Field54" Type="byte" ArraySize="2" />
    <Field Name="Field56" Type="byte" />
    <Field Name="Field57" Type="byte" />
    <Field Name="Field58" Type="byte" />
  </Table>
  <Table Name="FactionGroup" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="FactionTemplate" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" ArraySize="4" />
    <Field Name="Field0C" Type="ushort" ArraySize="4" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="FootprintTextures" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="FootstepTerrainLookup" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field07" Type="int" />
  </Table>
  <Table Name="FriendshipRepReaction" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="FriendshipReputation" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="FullScreenEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="float" />
    <Field Name="Field44" Type="float" />
    <Field Name="Field48" Type="float" />
    <Field Name="Field4C" Type="int" />
    <Field Name="Field50" Type="int" />
    <Field Name="Field54" Type="float" />
    <Field Name="Field58" Type="float" />
    <Field Name="Field5C" Type="float" />
    <Field Name="Field60" Type="float" />
    <Field Name="Field64" Type="float" />
    <Field Name="Field68" Type="float" />
    <Field Name="Field6C" Type="float" />
    <Field Name="Field70" Type="float" />
    <Field Name="Field74" Type="float" />
    <Field Name="Field78" Type="float" />
    <Field Name="Field7C" Type="float" />
    <Field Name="Field80" Type="float" />
    <Field Name="Field84" Type="float" />
    <Field Name="Field88" Type="float" />
    <Field Name="Field8C" Type="byte" />
    <Field Name="Field8D" Type="int" />
    <Field Name="Field91" Type="int" />
    <Field Name="Field95" Type="int" />
  </Table>
  <Table Name="GameObjectArtKit" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" ArraySize="3" />
    <Field Name="Field0C" Type="string" ArraySize="4" />
  </Table>
  <Table Name="GameObjectDiffAnimMap" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="GameObjectDisplayInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" ArraySize="6" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="ushort" />
  </Table>
  <Table Name="GameObjectDisplayInfoXSoundKit" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="GameObjects" Build="22578">
    <Field Name="Field00" Type="float" ArraySize="3" />
    <Field Name="Field0C" Type="float" ArraySize="4" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="int" ArraySize="8" />
    <Field Name="Field40" Type="string" />
    <Field Name="Field44" Type="ushort" />
    <Field Name="Field46" Type="ushort" />
    <Field Name="Field48" Type="ushort" />
    <Field Name="Field4A" Type="ushort" />
    <Field Name="Field4C" Type="byte" />
    <Field Name="Field4D" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GameTips" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="GarrAbility" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrAbilityCategory" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="GarrAbilityEffect" Build="22578">
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrBuilding" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="Field32" Type="int" />
    <Field Name="Field36" Type="int" />
    <Field Name="Field3A" Type="int" />
  </Table>
  <Table Name="GarrBuildingDoodadSet" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="GarrBuildingPlotInst" Build="22578">
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrClassSpec" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrClassSpecPlayerCond" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field0D" Type="int" />
    <Field Name="Field11" Type="int" />
  </Table>
  <Table Name="GarrEncounter" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrEncounterSetXEncounter" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="GarrEncounterXMechanic" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
  </Table>
  <Table Name="GarrFollItemSetMember" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="GarrFollower" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" />
    <Field Name="Field34" Type="byte" />
    <Field Name="Field35" Type="byte" />
    <Field Name="Field36" Type="byte" />
    <Field Name="Field37" Type="byte" />
    <Field Name="Field38" Type="byte" />
    <Field Name="Field39" Type="byte" />
    <Field Name="Field3A" Type="byte" />
    <Field Name="Field3B" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrFollowerLevelXP" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="GarrFollowerQuality" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="int" />
  </Table>
  <Table Name="GarrFollowerSetXFollower" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="GarrFollowerType" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="GarrFollowerUICreature" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="GarrFollowerXAbility" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="GarrFollSupportSpell" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="int" />
  </Table>
  <Table Name="GarrMechanic" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="GarrMechanicSetXMechanic" Build="22578">
    <Field Name="Field00" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="GarrMechanicType" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="GarrMission" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="float" ArraySize="2" />
    <Field Name="Field24" Type="float" ArraySize="2" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field30" Type="ushort" />
    <Field Name="Field32" Type="ushort" />
    <Field Name="Field34" Type="ushort" />
    <Field Name="Field36" Type="ushort" />
    <Field Name="Field38" Type="byte" />
    <Field Name="Field39" Type="byte" />
    <Field Name="Field3A" Type="byte" />
    <Field Name="Field3B" Type="byte" />
    <Field Name="Field3C" Type="byte" />
    <Field Name="Field3D" Type="byte" />
    <Field Name="Field3E" Type="byte" />
    <Field Name="Field3F" Type="byte" />
    <Field Name="Field40" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field45" Type="int" />
    <Field Name="Field49" Type="int" />
    <Field Name="Field4D" Type="int" />
    <Field Name="Field51" Type="int" />
  </Table>
  <Table Name="GarrMissionTexture" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="ushort" />
  </Table>
  <Table Name="GarrMissionType" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
  </Table>
  <Table Name="GarrMissionXEncounter" Build="22578">
    <Field Name="Field00" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field0D" Type="int" />
  </Table>
  <Table Name="GarrMissionXFollower" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="GarrMssnBonusAbility" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="GarrPlot" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="int" ArraySize="2" />
  </Table>
  <Table Name="GarrPlotBuilding" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
  </Table>
  <Table Name="GarrPlotInstance" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="GarrPlotUICategory" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="GarrSiteLevel" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
  </Table>
  <Table Name="GarrSiteLevelPlotInst" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="GarrSpecialization" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="GarrString" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="GarrTalent" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field17" Type="int" />
    <Field Name="Field1B" Type="int" />
    <Field Name="Field1F" Type="int" />
    <Field Name="Field23" Type="int" />
    <Field Name="Field27" Type="int" />
    <Field Name="Field2B" Type="int" />
    <Field Name="Field2F" Type="int" />
    <Field Name="Field33" Type="int" />
    <Field Name="Field37" Type="int" />
    <Field Name="Field3B" Type="int" />
    <Field Name="Field3F" Type="int" />
    <Field Name="Field43" Type="int" />
  </Table>
  <Table Name="GarrTalentTree" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="GarrType" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="GarrUiAnimClassInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="GarrUiAnimRaceInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="byte" />
  </Table>
  <Table Name="GemProperties" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
  </Table>
  <Table Name="GlobalStrings" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="GlyphBindableSpell" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="GlyphExclusiveCategory" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="GlyphProperties" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="GlyphRequiredSpec" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="GMSurveyAnswers" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="GMSurveyCurrentSurvey" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
  </Table>
  <Table Name="GMSurveyQuestions" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="GMSurveySurveys" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" ArraySize="15" />
  </Table>
  <Table Name="GroundEffectDoodad" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="GroundEffectTexture" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" ArraySize="4" />
    <Field Name="Field08" Type="byte" ArraySize="4" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="int" />
  </Table>
  <Table Name="GroupFinderActivity" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="GroupFinderActivityGrp" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="GroupFinderCategory" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="GuildColorBackground" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="GuildColorBorder" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="GuildColorEmblem" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="GuildPerkSpells" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
  </Table>
  <Table Name="Heirloom" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" ArraySize="2" />
    <Field Name="Field1C" Type="ushort" ArraySize="2" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="HelmetAnimScaling" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="HelmetGeosetVisData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="9" />
  </Table>
  <Table Name="HighlightColor" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="HolidayDescriptions" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="HolidayNames" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="Holidays" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="16" />
    <Field Name="Field40" Type="string" />
    <Field Name="Field44" Type="ushort" ArraySize="10" />
    <Field Name="Field58" Type="ushort" />
    <Field Name="Field5A" Type="byte" />
    <Field Name="Field5B" Type="byte" ArraySize="10" />
    <Field Name="Field65" Type="byte" />
    <Field Name="Field66" Type="byte" />
    <Field Name="Field67" Type="byte" />
    <Field Name="Field68" Type="byte" />
    <Field Name="Field69" Type="byte" />
  </Table>
  <Table Name="ImportPriceArmor" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
  </Table>
  <Table Name="ImportPriceQuality" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
  </Table>
  <Table Name="ImportPriceShield" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
  </Table>
  <Table Name="ImportPriceWeapon" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
  </Table>
  <Table Name="InvasionClientData" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" ArraySize="2" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="int" />
  </Table>
  <Table Name="Item" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="ItemAppearance" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="ItemAppearanceXUiCamera" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="ItemArmorQuality" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="7" />
    <Field Name="Field1C" Type="ushort" />
  </Table>
  <Table Name="ItemArmorShield" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="7" />
    <Field Name="Field1C" Type="ushort" />
  </Table>
  <Table Name="ItemArmorTotal" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="ushort" />
  </Table>
  <Table Name="ItemBagFamily" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="ItemBonus" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="2" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="ItemBonusListLevelDelta" Build="22578">
    <Field Name="Field00" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ItemBonusTreeNode" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="ItemChildEquipment" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="ItemClass" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="ItemContextPickerEntry" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field0A" Type="int" />
  </Table>
  <Table Name="ItemCurrencyCost" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
  </Table>
  <Table Name="ItemDamageAmmo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="7" />
    <Field Name="Field1C" Type="ushort" />
  </Table>
  <Table Name="ItemDamageOneHand" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="7" />
    <Field Name="Field1C" Type="ushort" />
  </Table>
  <Table Name="ItemDamageOneHandCaster" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="7" />
    <Field Name="Field1C" Type="ushort" />
  </Table>
  <Table Name="ItemDamageTwoHand" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="7" />
    <Field Name="Field1C" Type="ushort" />
  </Table>
  <Table Name="ItemDamageTwoHandCaster" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="7" />
    <Field Name="Field1C" Type="ushort" />
  </Table>
  <Table Name="ItemDisenchantLoot" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="ItemDisplayInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="2" />
    <Field Name="Field08" Type="int" ArraySize="2" />
    <Field Name="Field10" Type="int" ArraySize="3" />
    <Field Name="Field1C" Type="int" ArraySize="3" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2C" Type="int" />
    <Field Name="Field30" Type="int" ArraySize="2" />
    <Field Name="Field38" Type="int" />
    <Field Name="Field3C" Type="int" />
    <Field Name="Field40" Type="int" />
    <Field Name="Field44" Type="int" />
    <Field Name="Field48" Type="int" />
    <Field Name="Field4C" Type="int" />
    <Field Name="Field50" Type="int" />
    <Field Name="Field54" Type="int" />
  </Table>
  <Table Name="ItemDisplayInfoMaterialRes" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="ItemDisplayXUiCamera" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="ItemEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
  </Table>
  <Table Name="ItemExtendedCost" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="5" />
    <Field Name="Field14" Type="int" ArraySize="5" />
    <Field Name="Field28" Type="ushort" ArraySize="5" />
    <Field Name="Field32" Type="ushort" />
    <Field Name="Field34" Type="ushort" ArraySize="5" />
    <Field Name="Field3E" Type="byte" />
    <Field Name="Field3F" Type="byte" />
    <Field Name="Field40" Type="byte" />
    <Field Name="Field41" Type="byte" />
    <Field Name="Field42" Type="byte" />
  </Table>
  <Table Name="ItemGroupSounds" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="4" />
  </Table>
  <Table Name="ItemLimitCategory" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="ItemLimitCategoryCondition" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="ItemModifiedAppearance" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ItemModifiedAppearanceExtra" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="ItemNameDescription" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="ItemPetFood" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="ItemPriceBase" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
  </Table>
  <Table Name="ItemRandomProperties" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" ArraySize="5" />
  </Table>
  <Table Name="ItemRandomSuffix" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" ArraySize="5" />
    <Field Name="Field0E" Type="ushort" ArraySize="5" />
  </Table>
  <Table Name="ItemRangedDisplayInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
  </Table>
  <Table Name="ItemSearchName" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" ArraySize="3" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="int" />
  </Table>
  <Table Name="ItemSet" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" ArraySize="17" />
    <Field Name="Field48" Type="ushort" />
    <Field Name="Field4A" Type="int" />
    <Field Name="Field4E" Type="int" />
  </Table>
  <Table Name="ItemSetSpell" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="Item-sparse" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="3" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2C" Type="int" />
    <Field Name="Field30" Type="int" ArraySize="10" />
    <Field Name="Field58" Type="float" ArraySize="10" />
    <Field Name="Field80" Type="float" />
    <Field Name="Field84" Type="string" />
    <Field Name="Field88" Type="string" />
    <Field Name="Field8C" Type="string" />
    <Field Name="Field90" Type="string" />
    <Field Name="Field94" Type="string" />
    <Field Name="Field98" Type="int" />
    <Field Name="Field9C" Type="float" />
    <Field Name="FieldA0" Type="int" />
    <Field Name="FieldA4" Type="float" />
    <Field Name="FieldA8" Type="ushort" />
    <Field Name="FieldAA" Type="ushort" />
    <Field Name="FieldAC" Type="ushort" />
    <Field Name="FieldAE" Type="ushort" />
    <Field Name="FieldB0" Type="ushort" ArraySize="10" />
    <Field Name="FieldC4" Type="ushort" />
    <Field Name="FieldC6" Type="ushort" />
    <Field Name="FieldC8" Type="ushort" />
    <Field Name="FieldCA" Type="ushort" />
    <Field Name="FieldCC" Type="ushort" />
    <Field Name="FieldCE" Type="ushort" />
    <Field Name="FieldD0" Type="ushort" />
    <Field Name="FieldD2" Type="ushort" />
    <Field Name="FieldD4" Type="ushort" />
    <Field Name="FieldD6" Type="ushort" />
    <Field Name="FieldD8" Type="ushort" />
    <Field Name="FieldDA" Type="ushort" />
    <Field Name="FieldDC" Type="ushort" />
    <Field Name="FieldDE" Type="ushort" />
    <Field Name="FieldE0" Type="ushort" />
    <Field Name="FieldE2" Type="byte" />
    <Field Name="FieldE3" Type="byte" />
    <Field Name="FieldE4" Type="byte" />
    <Field Name="FieldE5" Type="byte" />
    <Field Name="FieldE6" Type="byte" />
    <Field Name="FieldE7" Type="byte" />
    <Field Name="FieldE8" Type="byte" />
    <Field Name="FieldE9" Type="byte" />
    <Field Name="FieldEA" Type="byte" ArraySize="10" />
    <Field Name="FieldF4" Type="byte" />
    <Field Name="FieldF5" Type="byte" />
    <Field Name="FieldF6" Type="byte" />
    <Field Name="FieldF7" Type="byte" />
    <Field Name="FieldF8" Type="byte" />
    <Field Name="FieldF9" Type="byte" />
    <Field Name="FieldFA" Type="byte" />
    <Field Name="FieldFB" Type="byte" ArraySize="3" />
    <Field Name="FieldFE" Type="byte" />
    <Field Name="FieldFF" Type="byte" />
    <Field Name="Field100" Type="byte" />
    <Field Name="Field101" Type="byte" />
  </Table>
  <Table Name="ItemSpec" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="ItemSpecOverride" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="ItemSubClass" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="ItemSubClassMask" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="ItemUpgrade" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
  </Table>
  <Table Name="ItemVisualEffects" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="ItemVisuals" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" ArraySize="5" />
  </Table>
  <Table Name="ItemXBonusTree" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="JournalEncounter" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="int" />
  </Table>
  <Table Name="JournalEncounterCreature" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="JournalEncounterItem" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="JournalEncounterSection" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
  </Table>
  <Table Name="JournalEncounterXDifficulty" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="JournalInstance" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="JournalItemXDifficulty" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="JournalSectionXDifficulty" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="JournalTier" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="JournalTierXInstance" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="KeyChain" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" ArraySize="32" />
  </Table>
  <Table Name="KeystoneAffix" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="Languages" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="LanguageWords" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="LfgDungeonExpansion" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field0B" Type="int" />
  </Table>
  <Table Name="LfgDungeonGroup" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="LfgDungeons" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="Field2E" Type="byte" />
    <Field Name="Field2F" Type="byte" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" />
    <Field Name="Field34" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="LfgDungeonsGroupingMap" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="LfgRoleRequirement" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="Light" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="3" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="LightData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2C" Type="int" />
    <Field Name="Field30" Type="int" />
    <Field Name="Field34" Type="int" />
    <Field Name="Field38" Type="int" />
    <Field Name="Field3C" Type="int" />
    <Field Name="Field40" Type="int" />
    <Field Name="Field44" Type="int" />
    <Field Name="Field48" Type="float" />
    <Field Name="Field4C" Type="float" />
    <Field Name="Field50" Type="float" />
    <Field Name="Field54" Type="float" />
    <Field Name="Field58" Type="float" />
    <Field Name="Field5C" Type="float" />
    <Field Name="Field60" Type="float" />
    <Field Name="Field64" Type="float" />
    <Field Name="Field68" Type="float" />
    <Field Name="Field6C" Type="int" />
    <Field Name="Field70" Type="int" />
    <Field Name="Field74" Type="int" />
    <Field Name="Field78" Type="int" />
    <Field Name="Field7C" Type="int" />
    <Field Name="Field80" Type="int" />
    <Field Name="Field84" Type="ushort" />
    <Field Name="Field86" Type="ushort" />
  </Table>
  <Table Name="LightParams" Build="22578">
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" ArraySize="3" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="LightSkybox" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="LiquidMaterial" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
  </Table>
  <Table Name="LiquidObject" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="LiquidType" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="string" ArraySize="6" />
    <Field Name="Field34" Type="int" ArraySize="2" />
    <Field Name="Field3C" Type="float" ArraySize="18" />
    <Field Name="Field84" Type="int" ArraySize="4" />
    <Field Name="Field94" Type="ushort" />
    <Field Name="Field96" Type="ushort" />
    <Field Name="Field98" Type="byte" />
    <Field Name="Field99" Type="byte" />
    <Field Name="Field9A" Type="byte" />
    <Field Name="Field9B" Type="byte" />
    <Field Name="Field9C" Type="byte" ArraySize="6" />
    <Field Name="FieldA2" Type="int" />
  </Table>
  <Table Name="LoadingScreens" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="LoadingScreenTaxiSplines" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="10" />
    <Field Name="Field28" Type="float" ArraySize="10" />
    <Field Name="Field50" Type="ushort" />
    <Field Name="Field52" Type="ushort" />
    <Field Name="Field54" Type="byte" />
  </Table>
  <Table Name="Locale" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="Location" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="3" />
    <Field Name="Field0C" Type="float" ArraySize="3" />
  </Table>
  <Table Name="Lock" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="8" />
    <Field Name="Field20" Type="ushort" ArraySize="8" />
    <Field Name="Field30" Type="byte" ArraySize="8" />
    <Field Name="Field38" Type="byte" ArraySize="8" />
  </Table>
  <Table Name="LockType" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="LookAtController" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
    <Field Name="Field1B" Type="byte" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="int" />
    <Field Name="Field21" Type="int" />
    <Field Name="Field25" Type="int" />
    <Field Name="Field29" Type="int" />
    <Field Name="Field2D" Type="int" />
  </Table>
  <Table Name="MailTemplate" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="ManifestInterfaceActionIcon" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ManifestInterfaceData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
  </Table>
  <Table Name="ManifestInterfaceItemIcon" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ManifestInterfaceTOCData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="ManifestMP3" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Map" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" ArraySize="2" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" ArraySize="2" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="string" />
    <Field Name="Field20" Type="string" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field30" Type="ushort" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" />
    <Field Name="Field34" Type="byte" />
    <Field Name="Field35" Type="byte" />
    <Field Name="Field36" Type="byte" />
  </Table>
  <Table Name="MapChallengeMode" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" ArraySize="3" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="MapDifficulty" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="int" />
  </Table>
  <Table Name="MapDifficultyXCondition" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
  </Table>
  <Table Name="MarketingPromotionsXLocale" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
  </Table>
  <Table Name="Material" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field09" Type="int" />
  </Table>
  <Table Name="MinorTalent" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="ModelAnimCloakDampening" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="ModelFileData" Build="22578">
    <Field Name="Field00" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="ModelRibbonQuality" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="uint" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="ModifierTree" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
  </Table>
  <Table Name="Mount" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="MountCapability" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field13" Type="int" />
  </Table>
  <Table Name="MountTypeXCapability" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="Movie" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
  </Table>
  <Table Name="MovieFileData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
  </Table>
  <Table Name="MovieVariation" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="NameGen" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="NamesProfanity" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="NamesReserved" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="NamesReservedLocale" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="NpcModelItemSlotDisplayInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="NPCSounds" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="4" />
  </Table>
  <Table Name="ObjectEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="int" />
  </Table>
  <Table Name="ObjectEffectGroup" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="ObjectEffectModifier" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="4" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
  </Table>
  <Table Name="ObjectEffectPackage" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="ObjectEffectPackageElem" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="OutlineEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
  </Table>
  <Table Name="OverrideSpellData" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="10" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2C" Type="byte" />
  </Table>
  <Table Name="PageTextMaterial" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="PaperDollItemFrame" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="ParticleColor" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="3" />
    <Field Name="Field0C" Type="int" ArraySize="3" />
    <Field Name="Field18" Type="int" ArraySize="3" />
  </Table>
  <Table Name="Path" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="PathNode" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="PathNodeProperty" Build="22578">
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field09" Type="int" />
  </Table>
  <Table Name="PathProperty" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Phase" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
  </Table>
  <Table Name="PhaseShiftZoneSounds" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="int" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field16" Type="int" />
    <Field Name="Field1A" Type="int" />
  </Table>
  <Table Name="PhaseXPhaseGroup" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="PlayerCondition" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" ArraySize="2" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2C" Type="int" ArraySize="4" />
    <Field Name="Field3C" Type="int" />
    <Field Name="Field40" Type="int" />
    <Field Name="Field44" Type="int" />
    <Field Name="Field48" Type="string" />
    <Field Name="Field4C" Type="ushort" />
    <Field Name="Field4E" Type="ushort" />
    <Field Name="Field50" Type="ushort" ArraySize="4" />
    <Field Name="Field58" Type="ushort" ArraySize="4" />
    <Field Name="Field60" Type="ushort" ArraySize="4" />
    <Field Name="Field68" Type="ushort" />
    <Field Name="Field6A" Type="ushort" ArraySize="4" />
    <Field Name="Field72" Type="ushort" ArraySize="4" />
    <Field Name="Field7A" Type="ushort" ArraySize="4" />
    <Field Name="Field82" Type="ushort" ArraySize="2" />
    <Field Name="Field86" Type="ushort" />
    <Field Name="Field88" Type="ushort" ArraySize="4" />
    <Field Name="Field90" Type="ushort" ArraySize="4" />
    <Field Name="Field98" Type="ushort" />
    <Field Name="Field9A" Type="ushort" />
    <Field Name="Field9C" Type="ushort" />
    <Field Name="Field9E" Type="ushort" />
    <Field Name="FieldA0" Type="ushort" />
    <Field Name="FieldA2" Type="byte" />
    <Field Name="FieldA3" Type="byte" />
    <Field Name="FieldA4" Type="byte" />
    <Field Name="FieldA5" Type="byte" />
    <Field Name="FieldA6" Type="byte" />
    <Field Name="FieldA7" Type="byte" ArraySize="3" />
    <Field Name="FieldAA" Type="byte" />
    <Field Name="FieldAB" Type="byte" />
    <Field Name="FieldAC" Type="byte" />
    <Field Name="FieldAD" Type="byte" />
    <Field Name="FieldAE" Type="byte" />
    <Field Name="FieldAF" Type="byte" />
    <Field Name="FieldB0" Type="byte" ArraySize="4" />
    <Field Name="FieldB4" Type="byte" />
    <Field Name="FieldB5" Type="byte" />
    <Field Name="FieldB6" Type="byte" />
    <Field Name="FieldB7" Type="byte" ArraySize="4" />
    <Field Name="FieldBB" Type="byte" ArraySize="4" />
    <Field Name="FieldBF" Type="byte" ArraySize="4" />
    <Field Name="FieldC3" Type="byte" />
    <Field Name="FieldC4" Type="byte" />
    <Field Name="FieldC5" Type="byte" />
    <Field Name="FieldC6" Type="byte" />
    <Field Name="FieldC7" Type="byte" />
    <Field Name="FieldC8" Type="byte" />
    <Field Name="FieldC9" Type="byte" />
    <Field Name="FieldCA" Type="byte" />
    <Field Name="FieldCB" Type="byte" />
    <Field Name="FieldCC" Type="byte" />
    <Field Name="FieldCD" Type="byte" />
    <Field Name="FieldCE" Type="byte" />
    <Field Name="FieldCF" Type="int" />
    <Field Name="FieldD3" Type="int" />
    <Field Name="FieldD7" Type="int" ArraySize="3" />
    <Field Name="FieldE3" Type="int" ArraySize="4" />
    <Field Name="FieldF3" Type="int" ArraySize="4" />
    <Field Name="Field103" Type="int" ArraySize="4" />
    <Field Name="Field113" Type="int" />
    <Field Name="Field117" Type="int" ArraySize="4" />
    <Field Name="Field127" Type="int" />
    <Field Name="Field12B" Type="int" ArraySize="4" />
    <Field Name="Field13B" Type="int" ArraySize="6" />
    <Field Name="Field153" Type="int" />
    <Field Name="Field157" Type="int" />
    <Field Name="Field15B" Type="int" />
    <Field Name="Field15F" Type="int" ArraySize="2" />
  </Table>
  <Table Name="Positioner" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="PositionerState" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field0D" Type="int" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field15" Type="int" />
    <Field Name="Field19" Type="int" />
  </Table>
  <Table Name="PositionerStateEntry" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="PowerDisplay" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="PowerType" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="Field1A" Type="byte" />
  </Table>
  <Table Name="PrestigeLevelInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
  </Table>
  <Table Name="PvpBracketTypes" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="int" ArraySize="4" />
  </Table>
  <Table Name="PvpDifficulty" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="PvpItem" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="PvpReward" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="PvpTalent" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
  </Table>
  <Table Name="PvpTalentUnlock" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="QuestFactionReward" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" ArraySize="10" />
  </Table>
  <Table Name="QuestFeedbackEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
  </Table>
  <Table Name="QuestInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="QuestLine" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="QuestLineXQuest" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="QuestMoneyReward" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="10" />
  </Table>
  <Table Name="QuestObjective" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
  </Table>
  <Table Name="QuestPackageItem" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="QuestPOIBlob" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="QuestPOIPoint" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="QuestPOIPointCliTask" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="int" />
  </Table>
  <Table Name="QuestSort" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="QuestV2" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
  </Table>
  <Table Name="QuestV2CliTask" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" ArraySize="3" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field2F" Type="int" />
    <Field Name="Field33" Type="int" />
  </Table>
  <Table Name="QuestXP" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" ArraySize="10" />
  </Table>
  <Table Name="RacialMounts" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="RandPropPoints" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="5" />
    <Field Name="Field14" Type="int" ArraySize="5" />
    <Field Name="Field28" Type="int" ArraySize="5" />
  </Table>
  <Table Name="ResearchBranch" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="ResearchField" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="ResearchProject" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field18" Type="int" />
  </Table>
  <Table Name="ResearchSite" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="int" />
  </Table>
  <Table Name="Resistances" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="RewardPack" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="int" />
    <Field Name="Field0E" Type="int" />
  </Table>
  <Table Name="RewardPackXCurrencyType" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="RewardPackXItem" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="RibbonQuality" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="RulesetItemUpgrade" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="ScalingStatDistribution" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="Scenario" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="ScenarioEventEntry" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="ScenarioStep" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="int" />
  </Table>
  <Table Name="SceneScript" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
  </Table>
  <Table Name="SceneScriptPackage" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="SceneScriptPackageMember" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="ScheduledInterval" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="ScheduledWorldState" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
  </Table>
  <Table Name="ScheduledWorldStateGroup" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="ScheduledWorldStateXUniqCat" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="ScreenEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" ArraySize="4" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="byte" />
    <Field Name="Field1F" Type="int" />
    <Field Name="Field23" Type="int" />
    <Field Name="Field27" Type="int" />
  </Table>
  <Table Name="ScreenLocation" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="SeamlessSite" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
  </Table>
  <Table Name="ServerMessages" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="ShadowyEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
  </Table>
  <Table Name="SkillLine" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="int" />
  </Table>
  <Table Name="SkillLineAbility" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="byte" />
    <Field Name="Field1D" Type="byte" />
    <Field Name="Field1E" Type="int" />
  </Table>
  <Table Name="SkillRaceClassInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="int" />
  </Table>
  <Table Name="SoundAmbience" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="int" ArraySize="2" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field0D" Type="int" />
  </Table>
  <Table Name="SoundAmbienceFlavor" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field06" Type="int" />
  </Table>
  <Table Name="SoundBus" Build="22578">
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SoundBusName" Build="22578">
    <Field Name="Name" Type="string" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SoundEmitterPillPoints" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="3" />
    <Field Name="Field0C" Type="ushort" />
  </Table>
  <Table Name="SoundEmitters" Build="22578">
    <Field Name="Field00" Type="float" ArraySize="3" />
    <Field Name="Field0C" Type="float" ArraySize="3" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field2A" Type="int" />
  </Table>
  <Table Name="SoundFilter" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="SoundFilterElem" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="9" />
    <Field Name="Field24" Type="byte" />
    <Field Name="Field25" Type="byte" />
  </Table>
  <Table Name="SoundKit" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SoundKitAdvanced" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="int" />
    <Field Name="Field34" Type="int" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="float" />
    <Field Name="Field44" Type="float" />
    <Field Name="Field48" Type="float" />
    <Field Name="Field4C" Type="float" />
    <Field Name="Field50" Type="int" />
    <Field Name="Field54" Type="ushort" />
    <Field Name="Field56" Type="byte" />
    <Field Name="Field57" Type="byte" />
    <Field Name="Field58" Type="byte" />
    <Field Name="Field59" Type="byte" />
    <Field Name="Field5A" Type="int" />
    <Field Name="Field5E" Type="int" />
    <Field Name="Field62" Type="int" />
    <Field Name="Field66" Type="int" />
    <Field Name="Field6A" Type="int" />
    <Field Name="Field6E" Type="int" />
  </Table>
  <Table Name="SoundKitChild" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="SoundKitEntry" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="int" />
  </Table>
  <Table Name="SoundKitFallback" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="SoundOverride" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="SoundProviderPreferences" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="ushort" />
    <Field Name="Field42" Type="ushort" />
    <Field Name="Field44" Type="ushort" />
    <Field Name="Field46" Type="ushort" />
    <Field Name="Field48" Type="ushort" />
    <Field Name="Field4A" Type="byte" />
    <Field Name="Field4B" Type="byte" />
  </Table>
  <Table Name="SourceInfo" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
  </Table>
  <Table Name="SpamMessages" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="SpecializationSpells" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Spell" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="int" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field18" Type="int" />
  </Table>
  <Table Name="SpellActionBarPref" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="SpellActivationOverlay" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" ArraySize="4" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="int" />
  </Table>
  <Table Name="SpellAuraOptions" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
  </Table>
  <Table Name="SpellAuraRestrictions" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
  </Table>
  <Table Name="SpellAuraVisibility" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellAuraVisXChrSpec" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
  </Table>
  <Table Name="SpellCastingRequirements" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="SpellCastTimes" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
  </Table>
  <Table Name="SpellCategories" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="SpellCategory" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="int" />
  </Table>
  <Table Name="SpellChainEffects" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="float" />
    <Field Name="Field44" Type="float" />
    <Field Name="Field48" Type="float" />
    <Field Name="Field4C" Type="float" />
    <Field Name="Field50" Type="float" />
    <Field Name="Field54" Type="float" />
    <Field Name="Field58" Type="float" />
    <Field Name="Field5C" Type="float" />
    <Field Name="Field60" Type="float" />
    <Field Name="Field64" Type="float" />
    <Field Name="Field68" Type="float" />
    <Field Name="Field6C" Type="float" />
    <Field Name="Field70" Type="float" />
    <Field Name="Field74" Type="float" />
    <Field Name="Field78" Type="float" />
    <Field Name="Field7C" Type="float" />
    <Field Name="Field80" Type="float" />
    <Field Name="Field84" Type="float" />
    <Field Name="Field88" Type="float" />
    <Field Name="Field8C" Type="float" />
    <Field Name="Field90" Type="float" />
    <Field Name="Field94" Type="float" ArraySize="3" />
    <Field Name="FieldA0" Type="float" ArraySize="3" />
    <Field Name="FieldAC" Type="float" ArraySize="3" />
    <Field Name="FieldB8" Type="float" ArraySize="3" />
    <Field Name="FieldC4" Type="int" />
    <Field Name="FieldC8" Type="float" />
    <Field Name="FieldCC" Type="float" />
    <Field Name="FieldD0" Type="string" ArraySize="3" />
    <Field Name="FieldDC" Type="string" />
    <Field Name="FieldE0" Type="ushort" />
    <Field Name="FieldE2" Type="ushort" />
    <Field Name="FieldE4" Type="ushort" ArraySize="11" />
    <Field Name="FieldFA" Type="ushort" />
    <Field Name="FieldFC" Type="byte" />
    <Field Name="FieldFD" Type="byte" />
    <Field Name="FieldFE" Type="byte" />
    <Field Name="FieldFF" Type="byte" />
    <Field Name="Field100" Type="byte" />
    <Field Name="Field101" Type="byte" />
    <Field Name="Field102" Type="byte" />
    <Field Name="Field103" Type="byte" />
    <Field Name="Field104" Type="byte" />
    <Field Name="Field105" Type="byte" />
    <Field Name="Field106" Type="byte" />
    <Field Name="Field107" Type="int" />
  </Table>
  <Table Name="SpellClassOptions" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" ArraySize="4" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="int" />
  </Table>
  <Table Name="SpellCooldowns" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="SpellDescriptionVariables" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="SpellDispelType" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
  </Table>
  <Table Name="SpellDuration" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="ushort" />
  </Table>
  <Table Name="SpellEffect" Build="22578">
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="int" ArraySize="4" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field30" Type="int" />
    <Field Name="Field34" Type="int" />
    <Field Name="Field38" Type="int" />
    <Field Name="Field3C" Type="int" />
    <Field Name="Field40" Type="int" />
    <Field Name="Field44" Type="int" />
    <Field Name="Field48" Type="int" />
    <Field Name="Field4C" Type="int" />
    <Field Name="Field50" Type="int" />
    <Field Name="Field54" Type="int" ArraySize="2" />
    <Field Name="Field5C" Type="int" ArraySize="2" />
    <Field Name="Field64" Type="int" />
    <Field Name="Field68" Type="int" ArraySize="2" />
    <Field Name="Field70" Type="int" />
    <Field Name="Field74" Type="int" />
    <Field Name="Field78" Type="int" />
  </Table>
  <Table Name="SpellEffectCameraShakes" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" ArraySize="3" />
    <Field Name="Field06" Type="ushort" />
  </Table>
  <Table Name="SpellEffectEmission" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="SpellEffectGroupSize" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
  </Table>
  <Table Name="SpellEffectScaling" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="int" />
  </Table>
  <Table Name="SpellEquippedItems" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="byte" />
  </Table>
  <Table Name="SpellFlyout" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="int" />
  </Table>
  <Table Name="SpellFlyoutItem" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="SpellFocusObject" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="SpellIcon" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="SpellInterrupts" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" ArraySize="2" />
    <Field Name="Field0C" Type="int" ArraySize="2" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
  </Table>
  <Table Name="SpellItemEnchantment" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="3" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="float" ArraySize="3" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field24" Type="ushort" ArraySize="3" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field30" Type="ushort" />
    <Field Name="Field32" Type="ushort" />
    <Field Name="Field34" Type="byte" />
    <Field Name="Field35" Type="byte" ArraySize="3" />
    <Field Name="Field38" Type="byte" />
    <Field Name="Field39" Type="byte" />
    <Field Name="Field3A" Type="byte" />
    <Field Name="Field3B" Type="byte" />
    <Field Name="Field3C" Type="byte" />
    <Field Name="Field3D" Type="int" />
  </Table>
  <Table Name="SpellItemEnchantmentCondition" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" ArraySize="5" />
    <Field Name="Field05" Type="byte" ArraySize="5" />
    <Field Name="Field0A" Type="byte" ArraySize="5" />
    <Field Name="Field0F" Type="byte" ArraySize="5" />
    <Field Name="Field14" Type="byte" ArraySize="5" />
    <Field Name="Field19" Type="int" ArraySize="5" />
  </Table>
  <Table Name="SpellKeyboundOverride" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="SpellLabel" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="SpellLearnSpell" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="SpellLevels" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="SpellMechanic" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="SpellMisc" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="14" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="ushort" />
    <Field Name="Field42" Type="ushort" />
    <Field Name="Field44" Type="ushort" />
    <Field Name="Field46" Type="ushort" />
    <Field Name="Field48" Type="ushort" />
    <Field Name="Field4A" Type="byte" />
  </Table>
  <Table Name="SpellMiscDifficulty" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellMissile" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="byte" />
  </Table>
  <Table Name="SpellMissileMotion" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
  </Table>
  <Table Name="SpellPower" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field1E" Type="int" />
    <Field Name="Field22" Type="int" />
    <Field Name="Field26" Type="int" />
    <Field Name="Field2A" Type="int" />
    <Field Name="Field2E" Type="int" />
  </Table>
  <Table Name="SpellPowerDifficulty" Build="22578">
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellProceduralEffect" Build="22578">
    <Field Name="Field00" Type="float" ArraySize="4" />
    <Field Name="Field10" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellProcsPerMinute" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="SpellProcsPerMinuteMod" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
  </Table>
  <Table Name="SpellRadius" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
  </Table>
  <Table Name="SpellRange" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="float" ArraySize="2" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="byte" />
  </Table>
  <Table Name="SpellReagents" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" ArraySize="8" />
    <Field Name="Field24" Type="ushort" ArraySize="8" />
  </Table>
  <Table Name="SpellReagentsCurrency" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
  </Table>
  <Table Name="SpellScaling" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field0A" Type="int" />
    <Field Name="Field0E" Type="int" />
  </Table>
  <Table Name="SpellShapeshift" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" ArraySize="2" />
    <Field Name="Field0C" Type="int" ArraySize="2" />
    <Field Name="Field14" Type="byte" />
  </Table>
  <Table Name="SpellShapeshiftForm" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" ArraySize="4" />
    <Field Name="Field18" Type="ushort" ArraySize="8" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
  </Table>
  <Table Name="SpellSpecialUnitEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="SpellTargetRestrictions" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="int" />
  </Table>
  <Table Name="SpellTotems" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" ArraySize="2" />
    <Field Name="Field0C" Type="ushort" ArraySize="2" />
  </Table>
  <Table Name="SpellVisual" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2C" Type="int" />
    <Field Name="Field30" Type="int" />
    <Field Name="Field34" Type="int" />
    <Field Name="Field38" Type="int" />
    <Field Name="Field3C" Type="float" ArraySize="3" />
    <Field Name="Field48" Type="float" ArraySize="3" />
    <Field Name="Field54" Type="int" />
    <Field Name="Field58" Type="int" />
    <Field Name="Field5C" Type="ushort" />
    <Field Name="Field5E" Type="ushort" />
    <Field Name="Field60" Type="ushort" />
    <Field Name="Field62" Type="byte" />
    <Field Name="Field63" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field68" Type="int" />
    <Field Name="Field6C" Type="int" />
    <Field Name="Field70" Type="int" />
  </Table>
  <Table Name="SpellVisualAnim" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="SpellVisualColorEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="int" />
  </Table>
  <Table Name="SpellVisualEffectName" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="int" />
    <Field Name="Field2D" Type="int" />
  </Table>
  <Table Name="SpellVisualKit" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="SpellVisualKitAreaModel" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="byte" />
  </Table>
  <Table Name="SpellVisualKitEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="SpellVisualKitModelAttach" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" ArraySize="3" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="int" />
    <Field Name="Field40" Type="float" />
    <Field Name="Field44" Type="ushort" />
    <Field Name="Field46" Type="ushort" />
    <Field Name="Field48" Type="ushort" />
    <Field Name="Field4A" Type="ushort" />
    <Field Name="Field4C" Type="ushort" />
    <Field Name="Field4E" Type="ushort" />
    <Field Name="Field50" Type="byte" />
    <Field Name="Field51" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="SpellVisualMissile" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="float" ArraySize="3" />
    <Field Name="Field18" Type="float" ArraySize="3" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="ushort" />
    <Field Name="Field30" Type="byte" />
    <Field Name="Field31" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field36" Type="int" />
    <Field Name="Field3A" Type="int" />
  </Table>
  <Table Name="SpellXSpellVisual" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="Startup_Strings" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
  </Table>
  <Table Name="Stationery" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="StringLookups" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="SummonProperties" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="int" />
  </Table>
  <Table Name="TactKey" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" ArraySize="16" />
  </Table>
  <Table Name="TactKeyLookup" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" ArraySize="8" />
  </Table>
  <Table Name="Talent" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" ArraySize="2" />
    <Field Name="Field13" Type="byte" />
  </Table>
  <Table Name="TaxiNodes" Build="22578">
    <Field Name="Field00" Type="float" ArraySize="3" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="int" ArraySize="2" />
    <Field Name="Field18" Type="float" ArraySize="2" />
    <Field Name="Field20" Type="ushort" />
    <Field Name="Field22" Type="ushort" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TaxiPath" Build="22578">
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="TaxiPathNode" Build="22578">
    <Field Name="Field00" Type="float" ArraySize="3" />
    <Field Name="Field0C" Type="int" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TerrainMaterial" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="TerrainType" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
  </Table>
  <Table Name="TerrainTypeSounds" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="TextureBlendSet" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="3" />
    <Field Name="Field0C" Type="float" ArraySize="3" />
    <Field Name="Field18" Type="float" ArraySize="3" />
    <Field Name="Field24" Type="float" ArraySize="3" />
    <Field Name="Field30" Type="float" ArraySize="3" />
    <Field Name="Field3C" Type="float" ArraySize="4" />
    <Field Name="Field4C" Type="byte" />
    <Field Name="Field4D" Type="byte" />
    <Field Name="Field4E" Type="byte" />
    <Field Name="Field4F" Type="byte" />
  </Table>
  <Table Name="TextureFileData" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TotemCategory" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="Toy" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TradeSkillCategory" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="TradeSkillItem" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
  </Table>
  <Table Name="TransformMatrix" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="3" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
  </Table>
  <Table Name="TransmogSet" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
  </Table>
  <Table Name="TransmogSetItem" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" />
  </Table>
  <Table Name="TransportAnimation" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="float" ArraySize="3" />
    <Field Name="Field14" Type="byte" />
  </Table>
  <Table Name="TransportPhysics" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
  </Table>
  <Table Name="TransportRotation" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="float" ArraySize="4" />
  </Table>
  <Table Name="Trophy" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="UiCamera" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" ArraySize="3" />
    <Field Name="Field1C" Type="float" ArraySize="3" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="byte" />
    <Field Name="Field2B" Type="byte" />
    <Field Name="Field2C" Type="byte" />
    <Field Name="Field2D" Type="int" />
  </Table>
  <Table Name="UiCameraType" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="UiCamFbackTransmogChrRace" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="UiCamFbackTransmogWeapon" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="UiMapPOI" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field18" Type="int" />
    <Field Name="Field1C" Type="int" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="UiTextureAtlas" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
  </Table>
  <Table Name="UiTextureAtlasMember" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
  </Table>
  <Table Name="UiTextureKit" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="UnitBlood" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" ArraySize="5" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="int" />
    <Field Name="Field1E" Type="int" />
    <Field Name="Field22" Type="int" />
  </Table>
  <Table Name="UnitBloodLevels" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" ArraySize="3" />
  </Table>
  <Table Name="UnitCondition" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" ArraySize="8" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" ArraySize="8" />
    <Field Name="Field29" Type="byte" ArraySize="8" />
  </Table>
  <Table Name="UnitPowerBar" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="int" ArraySize="6" />
    <Field Name="Field20" Type="int" ArraySize="6" />
    <Field Name="Field38" Type="string" />
    <Field Name="Field3C" Type="string" />
    <Field Name="Field40" Type="string" />
    <Field Name="Field44" Type="string" />
    <Field Name="Field48" Type="float" />
    <Field Name="Field4C" Type="float" />
    <Field Name="Field50" Type="ushort" />
    <Field Name="Field52" Type="ushort" />
    <Field Name="Field54" Type="byte" />
    <Field Name="Field55" Type="byte" />
    <Field Name="Field56" Type="int" />
    <Field Name="Field5A" Type="int" />
  </Table>
  <Table Name="Vehicle" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="float" />
    <Field Name="Field44" Type="float" />
    <Field Name="Field48" Type="float" ArraySize="2" />
    <Field Name="Field50" Type="string" />
    <Field Name="Field54" Type="string" />
    <Field Name="Field58" Type="string" ArraySize="2" />
    <Field Name="Field60" Type="float" />
    <Field Name="Field64" Type="float" />
    <Field Name="Field68" Type="ushort" ArraySize="8" />
    <Field Name="Field78" Type="ushort" />
    <Field Name="Field7A" Type="ushort" ArraySize="3" />
    <Field Name="Field80" Type="byte" />
    <Field Name="Field81" Type="byte" />
  </Table>
  <Table Name="VehicleSeat" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="float" ArraySize="3" />
    <Field Name="Field18" Type="float" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="float" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="float" />
    <Field Name="Field40" Type="float" />
    <Field Name="Field44" Type="float" />
    <Field Name="Field48" Type="float" />
    <Field Name="Field4C" Type="float" />
    <Field Name="Field50" Type="float" />
    <Field Name="Field54" Type="float" />
    <Field Name="Field58" Type="float" />
    <Field Name="Field5C" Type="float" />
    <Field Name="Field60" Type="float" />
    <Field Name="Field64" Type="float" />
    <Field Name="Field68" Type="float" />
    <Field Name="Field6C" Type="float" />
    <Field Name="Field70" Type="float" />
    <Field Name="Field74" Type="float" ArraySize="3" />
    <Field Name="Field80" Type="float" />
    <Field Name="Field84" Type="float" />
    <Field Name="Field88" Type="float" />
    <Field Name="Field8C" Type="float" />
    <Field Name="Field90" Type="float" />
    <Field Name="Field94" Type="int" />
    <Field Name="Field98" Type="ushort" />
    <Field Name="Field9A" Type="ushort" />
    <Field Name="Field9C" Type="ushort" />
    <Field Name="Field9E" Type="ushort" />
    <Field Name="FieldA0" Type="ushort" />
    <Field Name="FieldA2" Type="ushort" />
    <Field Name="FieldA4" Type="ushort" />
    <Field Name="FieldA6" Type="ushort" />
    <Field Name="FieldA8" Type="ushort" />
    <Field Name="FieldAA" Type="ushort" />
    <Field Name="FieldAC" Type="ushort" />
    <Field Name="FieldAE" Type="ushort" />
    <Field Name="FieldB0" Type="ushort" />
    <Field Name="FieldB2" Type="ushort" />
    <Field Name="FieldB4" Type="ushort" />
    <Field Name="FieldB6" Type="ushort" />
    <Field Name="FieldB8" Type="ushort" />
    <Field Name="FieldBA" Type="ushort" />
    <Field Name="FieldBC" Type="ushort" />
    <Field Name="FieldBE" Type="byte" />
    <Field Name="FieldBF" Type="byte" />
    <Field Name="FieldC0" Type="byte" />
    <Field Name="FieldC1" Type="byte" />
    <Field Name="FieldC2" Type="byte" />
    <Field Name="FieldC3" Type="byte" />
    <Field Name="FieldC4" Type="int" />
    <Field Name="FieldC8" Type="int" />
  </Table>
  <Table Name="VehicleUIIndicator" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="VehicleUIIndSeat" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="VideoHardware" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="byte" />
    <Field Name="Field0D" Type="byte" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="byte" />
    <Field Name="Field10" Type="byte" />
    <Field Name="Field11" Type="byte" />
    <Field Name="Field12" Type="byte" />
    <Field Name="Field13" Type="byte" />
    <Field Name="Field14" Type="byte" />
    <Field Name="Field15" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field1A" Type="int" />
    <Field Name="Field1E" Type="int" />
    <Field Name="Field22" Type="int" />
    <Field Name="Field26" Type="int" />
    <Field Name="Field2A" Type="int" />
    <Field Name="Field2E" Type="int" />
    <Field Name="Field32" Type="int" />
  </Table>
  <Table Name="Vignette" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="byte" />
    <Field Name="Field0F" Type="int" />
    <Field Name="Field13" Type="int" />
  </Table>
  <Table Name="VocalUISounds" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="int" ArraySize="2" />
  </Table>
  <Table Name="WbAccessControlList" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="byte" />
    <Field Name="Field08" Type="byte" />
  </Table>
  <Table Name="WbCertBlacklist" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" ArraySize="20" />
  </Table>
  <Table Name="WbCertWhitelist" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="WbPermissions" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
  </Table>
  <Table Name="WeaponImpactSounds" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="int" ArraySize="11" />
    <Field Name="Field2F" Type="int" ArraySize="11" />
    <Field Name="Field5B" Type="int" ArraySize="11" />
    <Field Name="Field87" Type="int" ArraySize="11" />
  </Table>
  <Table Name="WeaponSwingSounds2" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="byte" />
    <Field Name="Field01" Type="byte" />
    <Field Name="Field02" Type="int" />
  </Table>
  <Table Name="WeaponTrail" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="int" ArraySize="3" />
    <Field Name="Field1C" Type="float" ArraySize="3" />
    <Field Name="Field28" Type="float" ArraySize="3" />
    <Field Name="Field34" Type="float" ArraySize="3" />
    <Field Name="Field40" Type="float" ArraySize="3" />
  </Table>
  <Table Name="WeaponTrailModelDef" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
  </Table>
  <Table Name="WeaponTrailParam" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
  </Table>
  <Table Name="Weather" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" ArraySize="3" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field1C" Type="float" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" />
    <Field Name="Field2C" Type="float" />
    <Field Name="Field30" Type="ushort" />
    <Field Name="Field32" Type="byte" />
    <Field Name="Field33" Type="byte" />
    <Field Name="Field34" Type="byte" />
    <Field Name="Field35" Type="int" />
  </Table>
  <Table Name="WindSettings" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" />
    <Field Name="Field04" Type="float" ArraySize="3" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="float" />
    <Field Name="Field18" Type="float" ArraySize="3" />
    <Field Name="Field24" Type="float" />
    <Field Name="Field28" Type="float" ArraySize="3" />
    <Field Name="Field34" Type="float" />
    <Field Name="Field38" Type="float" />
    <Field Name="Field3C" Type="byte" />
  </Table>
  <Table Name="WMOAreaTable" Build="22578">
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="ushort" />
    <Field Name="Field12" Type="ushort" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="byte" />
    <Field Name="Field17" Type="byte" />
    <Field Name="Field18" Type="byte" />
    <Field Name="Field19" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field1E" Type="int" />
  </Table>
  <Table Name="WmoMinimapTexture" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="byte" />
    <Field Name="Field09" Type="byte" />
  </Table>
  <Table Name="World_PVP_Area" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="ushort" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="WorldBossLockout" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
  </Table>
  <Table Name="WorldChunkSounds" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="ushort" />
    <Field Name="Field02" Type="byte" />
    <Field Name="Field03" Type="byte" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
    <Field Name="Field06" Type="byte" />
  </Table>
  <Table Name="WorldEffect" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
    <Field Name="Field0B" Type="byte" />
  </Table>
  <Table Name="WorldElapsedTimer" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="byte" />
    <Field Name="Field05" Type="byte" />
  </Table>
  <Table Name="WorldMapArea" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="float" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" />
    <Field Name="Field10" Type="float" />
    <Field Name="Field14" Type="ushort" />
    <Field Name="Field16" Type="ushort" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field28" Type="int" />
  </Table>
  <Table Name="WorldMapContinent" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field0C" Type="float" ArraySize="2" />
    <Field Name="Field14" Type="float" ArraySize="2" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="byte" />
    <Field Name="Field21" Type="byte" />
    <Field Name="Field22" Type="byte" />
    <Field Name="Field23" Type="byte" />
    <Field Name="Field24" Type="byte" />
  </Table>
  <Table Name="WorldMapOverlay" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field0C" Type="int" ArraySize="4" />
    <Field Name="Field1C" Type="int" />
    <Field Name="Field20" Type="int" />
    <Field Name="Field24" Type="int" />
    <Field Name="Field28" Type="int" />
    <Field Name="Field2C" Type="int" />
    <Field Name="Field30" Type="int" />
    <Field Name="Field34" Type="int" />
    <Field Name="Field38" Type="int" />
  </Table>
  <Table Name="WorldMapTransforms" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="6" />
    <Field Name="Field18" Type="float" ArraySize="2" />
    <Field Name="Field20" Type="float" />
    <Field Name="Field24" Type="ushort" />
    <Field Name="Field26" Type="ushort" />
    <Field Name="Field28" Type="ushort" />
    <Field Name="Field2A" Type="ushort" />
    <Field Name="Field2C" Type="ushort" />
    <Field Name="Field2E" Type="byte" />
  </Table>
  <Table Name="WorldSafeLocs" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Pos" Type="float" ArraySize="3" />
    <Field Name="Facing" Type="float" />
    <Field Name="Name" Type="string" />
    <Field Name="Map" Type="uint" />
  </Table>
  <Table Name="WorldState" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="WorldStateExpression" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
  </Table>
  <Table Name="WorldStateUI" Build="22578">
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field0C" Type="string" />
    <Field Name="Field10" Type="string" />
    <Field Name="Field14" Type="string" />
    <Field Name="Field18" Type="ushort" />
    <Field Name="Field1A" Type="ushort" />
    <Field Name="Field1C" Type="ushort" />
    <Field Name="Field1E" Type="ushort" />
    <Field Name="Field20" Type="ushort" ArraySize="3" />
    <Field Name="Field26" Type="byte" />
    <Field Name="Field27" Type="byte" />
    <Field Name="Field28" Type="byte" />
    <Field Name="Field29" Type="byte" />
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="WorldStateZoneSounds" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="int" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="ushort" />
    <Field Name="Field0C" Type="ushort" />
    <Field Name="Field0E" Type="ushort" />
    <Field Name="Field10" Type="byte" />
  </Table>
  <Table Name="ZoneIntroMusicTable" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="byte" />
    <Field Name="Field07" Type="int" />
  </Table>
  <Table Name="ZoneLight" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="ushort" />
    <Field Name="Field06" Type="ushort" />
  </Table>
  <Table Name="ZoneLightPoint" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="float" ArraySize="2" />
    <Field Name="Field08" Type="ushort" />
    <Field Name="Field0A" Type="byte" />
  </Table>
  <Table Name="ZoneMusic" Build="22578">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field00" Type="string" />
    <Field Name="Field04" Type="int" ArraySize="2" />
    <Field Name="Field0C" Type="int" ArraySize="2" />
    <Field Name="Field14" Type="int" ArraySize="2" />
  </Table>
</Definition>