<?xml version="1.0"?>
<Definition xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<Table Name="Achievement" Build="27075">
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Title_Lang" Type="string" />
		<Field Name="Reward_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Instance_ID" Type="short" />
		<Field Name="Faction" Type="byte" />
		<Field Name="Supercedes" Type="short" />
		<Field Name="Category" Type="short" />
		<Field Name="Minimum_Criteria" Type="byte" />
		<Field Name="Points" Type="byte" />
		<Field Name="Flags" Type="int" />
		<Field Name="Ui_Order" Type="short" />
		<Field Name="IconFileID" Type="int" />
		<Field Name="Criteria_Tree" Type="int" />
		<Field Name="Shares_Criteria" Type="short" />
	</Table>
	<Table Name="Achievement_Category" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Parent" Type="short" />
		<Field Name="Ui_Order" Type="byte" />
	</Table>
	<Table Name="AdventureJournal" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ButtonText_Lang" Type="string" />
		<Field Name="RewardDescription_Lang" Type="string" />
		<Field Name="ContinueDescription_Lang" Type="string" />
		<Field Name="Type" Type="byte" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ButtonActionType" Type="byte" />
		<Field Name="TextureFileDataID" Type="int" />
		<Field Name="LfgDungeonID" Type="short" />
		<Field Name="QuestID" Type="short" />
		<Field Name="BattleMasterListID" Type="short" />
		<Field Name="PriorityMin" Type="byte" />
		<Field Name="PriorityMax" Type="byte" />
		<Field Name="ItemID" Type="int" />
		<Field Name="ItemQuantity" Type="int" />
		<Field Name="CurrencyType" Type="short" />
		<Field Name="CurrencyQuantity" Type="byte" />
		<Field Name="UIMapID" Type="short" />
		<Field Name="BonusPlayerConditionID" Type="int" ArraySize="2" />
		<Field Name="BonusValue" Type="byte" ArraySize="2" />
	</Table>
	<Table Name="AdventureMapPOI" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Title_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="WorldPosition" Type="float" ArraySize="2" />
		<Field Name="Type" Type="byte" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="QuestID" Type="int" />
		<Field Name="LfgDungeonID" Type="int" />
		<Field Name="RewardItemID" Type="int" />
		<Field Name="UiTextureAtlasMemberID" Type="int" />
		<Field Name="UiTextureKitID" Type="int" />
		<Field Name="MapID" Type="int" />
		<Field Name="AreaTableID" Type="int" />
	</Table>
	<Table Name="AlliedRace" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceID" Type="int" />
		<Field Name="BannerColor" Type="int" />
		<Field Name="CrestTextureID" Type="int" />
		<Field Name="ModelBackgroundTextureID" Type="int" />
		<Field Name="MaleCreatureDisplayID" Type="int" />
		<Field Name="FemaleCreatureDisplayID" Type="int" />
		<Field Name="Ui_UnlockAchievementID" Type="int" />
	</Table>
	<Table Name="AlliedRaceRacialAbility" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="AnimationData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BehaviorID" Type="short" />
		<Field Name="BehaviorTier" Type="byte" />
		<Field Name="Fallback" Type="int" />
		<Field Name="Flags" Type="int" ArraySize="2" />
	</Table>
	<Table Name="AnimKit" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OneShotDuration" Type="int" />
		<Field Name="OneShotStopAnimKitID" Type="short" />
		<Field Name="LowDefAnimKitID" Type="short" />
	</Table>
	<Table Name="AnimKitBoneSet" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="BoneDataID" Type="byte" />
		<Field Name="ParentAnimKitBoneSetID" Type="byte" />
		<Field Name="ExtraBoneCount" Type="byte" />
		<Field Name="AltAnimKitBoneSetID" Type="byte" />
	</Table>
	<Table Name="AnimKitBoneSetAlias" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BoneDataID" Type="byte" />
		<Field Name="AnimKitBoneSetID" Type="byte" />
	</Table>
	<Table Name="AnimKitConfig" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ConfigFlags" Type="int" />
	</Table>
	<Table Name="AnimKitConfigBoneSet" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AnimKitBoneSetID" Type="byte" />
		<Field Name="AnimKitPriorityID" Type="short" />
	</Table>
	<Table Name="AnimKitPriority" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Priority" Type="byte" />
	</Table>
	<Table Name="AnimKitReplacement" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SrcAnimKitID" Type="short" />
		<Field Name="DstAnimKitID" Type="short" />
		<Field Name="Flags" Type="short" />
	</Table>
	<Table Name="AnimKitSegment" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ParentAnimKitID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="AnimID" Type="short" />
		<Field Name="AnimStartTime" Type="int" />
		<Field Name="AnimKitConfigID" Type="short" />
		<Field Name="StartCondition" Type="byte" />
		<Field Name="StartConditionParam" Type="byte" />
		<Field Name="StartConditionDelay" Type="int" />
		<Field Name="EndCondition" Type="byte" />
		<Field Name="EndConditionParam" Type="int" />
		<Field Name="EndConditionDelay" Type="int" />
		<Field Name="Speed" Type="float" />
		<Field Name="SegmentFlags" Type="short" />
		<Field Name="ForcedVariation" Type="byte" />
		<Field Name="OverrideConfigFlags" Type="int" />
		<Field Name="LoopToSegmentIndex" Type="byte" />
		<Field Name="BlendInTimeMs" Type="short" />
		<Field Name="BlendOutTimeMs" Type="short" />
	</Table>
	<Table Name="AnimReplacement" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SrcAnimID" Type="short" />
		<Field Name="DstAnimID" Type="short" />
		<Field Name="Flags" Type="short" />
	</Table>
	<Table Name="AnimReplacementSet" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ExecOrder" Type="byte" />
	</Table>
	<Table Name="AoiBox" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Extents" Type="float" ArraySize="6" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
	</Table>
	<Table Name="AreaConditionalData" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="AreaFarClipOverride" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AreaID" Type="int" />
		<Field Name="MinFarClip" Type="float" />
		<Field Name="MinHorizonStart" Type="float" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="AreaGroupMember" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AreaID" Type="short" />
	</Table>
	<Table Name="AreaPOI" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="PortLocID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="UiTextureAtlasMemberID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="WMOGroupID" Type="int" />
		<Field Name="PoiDataType" Type="int" />
		<Field Name="PoiData" Type="int" />
		<Field Name="ContinentID" Type="short" />
		<Field Name="AreaID" Type="short" />
		<Field Name="WorldStateID" Type="short" />
		<Field Name="UiWidgetParentSetID" Type="short" />
		<Field Name="Importance" Type="byte" />
		<Field Name="Icon" Type="byte" />
	</Table>
	<Table Name="AreaPOIState" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="WorldStateValue" Type="byte" />
		<Field Name="IconEnumValue" Type="byte" />
		<Field Name="UiTextureAtlasMemberID" Type="int" />
	</Table>
	<Table Name="AreaTable" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ZoneName" Type="string" />
		<Field Name="AreaName_Lang" Type="string" />
		<Field Name="ContinentID" Type="short" />
		<Field Name="ParentAreaID" Type="short" />
		<Field Name="AreaBit" Type="short" />
		<Field Name="SoundProviderPref" Type="byte" />
		<Field Name="SoundProviderPrefUnderwater" Type="byte" />
		<Field Name="AmbienceID" Type="short" />
		<Field Name="UwAmbience" Type="short" />
		<Field Name="ZoneMusic" Type="short" />
		<Field Name="UwZoneMusic" Type="short" />
		<Field Name="ExplorationLevel" Type="byte" />
		<Field Name="IntroSound" Type="short" />
		<Field Name="UwIntroSound" Type="int" />
		<Field Name="FactionGroupMask" Type="byte" />
		<Field Name="Ambient_Multiplier" Type="float" />
		<Field Name="MountFlags" Type="byte" />
		<Field Name="PvpCombatWorldStateID" Type="short" />
		<Field Name="WildBattlePetLevelMin" Type="byte" />
		<Field Name="WildBattlePetLevelMax" Type="byte" />
		<Field Name="WindSettingsID" Type="byte" />
		<Field Name="Flags" Type="int" ArraySize="2" />
		<Field Name="LiquidTypeID" Type="short" ArraySize="4" />
	</Table>
	<Table Name="AreaTrigger" Build="27075">
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ContinentID" Type="short" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="PhaseID" Type="short" />
		<Field Name="PhaseGroupID" Type="short" />
		<Field Name="Radius" Type="float" />
		<Field Name="Box_Length" Type="float" />
		<Field Name="Box_Width" Type="float" />
		<Field Name="Box_Height" Type="float" />
		<Field Name="Box_Yaw" Type="float" />
		<Field Name="ShapeType" Type="byte" />
		<Field Name="ShapeID" Type="short" />
		<Field Name="AreaTriggerActionSetID" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="AreaTriggerActionSet" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="short" />
	</Table>
	<Table Name="AreaTriggerBox" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Extents" Type="float" ArraySize="3" />
	</Table>
	<Table Name="AreaTriggerCreateProperties" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ShapeType" Type="byte" />
		<Field Name="StartShapeID" Type="short" />
	</Table>
	<Table Name="AreaTriggerCylinder" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Radius" Type="float" />
		<Field Name="Height" Type="float" />
		<Field Name="ZOffset" Type="float" />
	</Table>
	<Table Name="AreaTriggerSphere" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaxRadius" Type="float" />
	</Table>
	<Table Name="ArmorLocation" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Clothmodifier" Type="float" />
		<Field Name="Leathermodifier" Type="float" />
		<Field Name="Chainmodifier" Type="float" />
		<Field Name="Platemodifier" Type="float" />
		<Field Name="Modifier" Type="float" />
	</Table>
	<Table Name="Artifact" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiTextureKitID" Type="short" />
		<Field Name="UiNameColor" Type="int" />
		<Field Name="UiBarOverlayColor" Type="int" />
		<Field Name="UiBarBackgroundColor" Type="int" />
		<Field Name="ChrSpecializationID" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ArtifactCategoryID" Type="byte" />
		<Field Name="UiModelSceneID" Type="int" />
		<Field Name="SpellVisualKitID" Type="int" />
	</Table>
	<Table Name="ArtifactAppearance" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ArtifactAppearanceSetID" Type="short" />
		<Field Name="DisplayIndex" Type="byte" />
		<Field Name="UnlockPlayerConditionID" Type="int" />
		<Field Name="ItemAppearanceModifierID" Type="byte" />
		<Field Name="UiSwatchColor" Type="int" />
		<Field Name="UiModelSaturation" Type="float" />
		<Field Name="UiModelOpacity" Type="float" />
		<Field Name="OverrideShapeshiftFormID" Type="byte" />
		<Field Name="OverrideShapeshiftDisplayID" Type="int" />
		<Field Name="UiItemAppearanceID" Type="int" />
		<Field Name="UiAltItemAppearanceID" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UiCameraID" Type="short" />
	</Table>
	<Table Name="ArtifactAppearanceSet" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayIndex" Type="byte" />
		<Field Name="UiCameraID" Type="short" />
		<Field Name="AltHandUICameraID" Type="short" />
		<Field Name="ForgeAttachmentOverride" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ArtifactCategory" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="XpMultCurrencyID" Type="short" />
		<Field Name="XpMultCurveID" Type="short" />
	</Table>
	<Table Name="ArtifactItemToTransmog" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="ArtifactPower" Build="27075">
		<Field Name="DisplayPos" Type="float" ArraySize="2" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ArtifactID" Type="byte" />
		<Field Name="MaxPurchasableRank" Type="byte" />
		<Field Name="Label" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Tier" Type="byte" />
	</Table>
	<Table Name="ArtifactPowerLink" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PowerA" Type="short" />
		<Field Name="PowerB" Type="short" />
	</Table>
	<Table Name="ArtifactPowerPicker" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="ArtifactPowerRank" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RankIndex" Type="byte" />
		<Field Name="SpellID" Type="int" />
		<Field Name="ItemBonusListID" Type="short" />
		<Field Name="AuraPointsOverride" Type="float" />
	</Table>
	<Table Name="ArtifactQuestXP" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="int" ArraySize="10" />
	</Table>
	<Table Name="ArtifactTier" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ArtifactTier" Type="int" />
		<Field Name="MaxNumTraits" Type="int" />
		<Field Name="MaxArtifactKnowledge" Type="int" />
		<Field Name="KnowledgePlayerCondition" Type="int" />
		<Field Name="MinimumEmpowerKnowledge" Type="int" />
	</Table>
	<Table Name="ArtifactUnlock" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PowerID" Type="int" />
		<Field Name="PowerRank" Type="byte" />
		<Field Name="ItemBonusListID" Type="short" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="AuctionHouse" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="FactionID" Type="short" />
		<Field Name="DepositRate" Type="byte" />
		<Field Name="ConsignmentRate" Type="byte" />
	</Table>
	<Table Name="AzeriteEmpoweredItem" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
		<Field Name="AzeriteTierUnlockSetID" Type="int" />
		<Field Name="AzeritePowerSetID" Type="int" />
	</Table>
	<Table Name="AzeriteItem" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
	</Table>
	<Table Name="AzeriteItemMilestonePower" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="AzeritePowerID" Type="short" />
	</Table>
	<Table Name="AzeritePower" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="ItemBonusListID" Type="int" />
		<Field Name="SpecSet" Type="int" />
		<Field Name="Field_5" Type="int" />
	</Table>
	<Table Name="AzeritePowerSetMember" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AzeritePowerID" Type="short" />
		<Field Name="Class" Type="byte" />
		<Field Name="Tier" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="AzeriteTierUnlock" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemCreationContext" Type="byte" />
		<Field Name="Tier" Type="byte" />
		<Field Name="AzeriteLevel" Type="byte" />
	</Table>
	<Table Name="BankBagSlotPrices" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Cost" Type="int" />
	</Table>
	<Table Name="BarberShopStyle" Build="27075">
		<Field Name="DisplayName_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="byte" />
		<Field Name="Cost_Modifier" Type="float" />
		<Field Name="Race" Type="byte" />
		<Field Name="Sex" Type="byte" />
		<Field Name="Data" Type="byte" />
	</Table>
	<Table Name="BattlemasterList" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Gametype_Lang" Type="string" />
		<Field Name="ShortDescription_Lang" Type="string" />
		<Field Name="LongDescription_Lang" Type="string" />
		<Field Name="InstanceType" Type="byte" />
		<Field Name="Minlevel" Type="byte" />
		<Field Name="Maxlevel" Type="byte" />
		<Field Name="RatedPlayers" Type="byte" />
		<Field Name="MinPlayers" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="GroupsAllowed" Type="byte" />
		<Field Name="MaxGroupSize" Type="byte" />
		<Field Name="HolidayWorldState" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="Required_Player_Condition_ID" Type="short" />
		<Field Name="MapID" Type="short" ArraySize="16" />
	</Table>
	<Table Name="BattlePetAbility" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="PetTypeEnum" Type="byte" />
		<Field Name="Cooldown" Type="int" />
		<Field Name="BattlePetVisualID" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="BattlePetAbilityEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BattlePetAbilityTurnID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="BattlePetEffectPropertiesID" Type="short" />
		<Field Name="AuraBattlePetAbilityID" Type="short" />
		<Field Name="BattlePetVisualID" Type="short" />
		<Field Name="Param" Type="short" ArraySize="6" />
	</Table>
	<Table Name="BattlePetAbilityState" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BattlePetStateID" Type="byte" />
		<Field Name="Value" Type="int" />
	</Table>
	<Table Name="BattlePetAbilityTurn" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BattlePetAbilityID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="TurnTypeEnum" Type="byte" />
		<Field Name="EventTypeEnum" Type="byte" />
		<Field Name="BattlePetVisualID" Type="short" />
	</Table>
	<Table Name="BattlePetBreedQuality" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StateMultiplier" Type="float" />
		<Field Name="QualityEnum" Type="byte" />
	</Table>
	<Table Name="BattlePetBreedState" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BattlePetStateID" Type="byte" />
		<Field Name="Value" Type="short" />
	</Table>
	<Table Name="BattlePetDisplayOverride" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BattlePetSpeciesID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="CreatureDisplayInfoID" Type="int" />
		<Field Name="PriorityCategory" Type="byte" />
	</Table>
	<Table Name="BattlePetEffectProperties" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ParamLabel" Type="string" ArraySize="6" />
		<Field Name="BattlePetVisualID" Type="short" />
		<Field Name="ParamTypeEnum" Type="byte" ArraySize="6" />
	</Table>
	<Table Name="BattlePetNPCTeamMember" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="BattlePetSpecies" Build="27075">
		<Field Name="Description_Lang" Type="string" />
		<Field Name="SourceText_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureID" Type="int" />
		<Field Name="SummonSpellID" Type="int" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="PetTypeEnum" Type="byte" />
		<Field Name="Flags" Type="short" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="CardUIModelSceneID" Type="int" />
		<Field Name="LoadoutUIModelSceneID" Type="int" />
	</Table>
	<Table Name="BattlePetSpeciesState" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BattlePetStateID" Type="byte" />
		<Field Name="Value" Type="int" />
	</Table>
	<Table Name="BattlePetSpeciesXAbility" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BattlePetAbilityID" Type="short" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="SlotEnum" Type="byte" />
	</Table>
	<Table Name="BattlePetState" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LuaName" Type="string" />
		<Field Name="Flags" Type="short" />
		<Field Name="BattlePetVisualID" Type="short" />
	</Table>
	<Table Name="BattlePetVisual" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SceneScriptFunction" Type="string" />
		<Field Name="SpellVisualID" Type="int" />
		<Field Name="CastMilliSeconds" Type="short" />
		<Field Name="ImpactMilliSeconds" Type="short" />
		<Field Name="RangeTypeEnum" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="SceneScriptPackageID" Type="short" />
	</Table>
	<Table Name="BeamEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BeamID" Type="int" />
		<Field Name="SourceMinDistance" Type="float" />
		<Field Name="FixedLength" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="SourceOffset" Type="int" />
		<Field Name="DestOffset" Type="int" />
		<Field Name="SourceAttachID" Type="int" />
		<Field Name="DestAttachID" Type="int" />
		<Field Name="SourcePositionerID" Type="int" />
		<Field Name="DestPositionerID" Type="int" />
	</Table>
	<Table Name="BoneWindModifierModel" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="BoneWindModifierID" Type="int" />
	</Table>
	<Table Name="BoneWindModifiers" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Multiplier" Type="float" ArraySize="3" />
		<Field Name="PhaseMultiplier" Type="float" />
	</Table>
	<Table Name="BonusRoll" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CurrencyTypesID" Type="int" />
		<Field Name="CurrencyCost" Type="int" />
		<Field Name="JournalEncounterID" Type="int" />
		<Field Name="JournalInstanceID" Type="int" />
	</Table>
	<Table Name="Bounty" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QuestID" Type="short" />
		<Field Name="FactionID" Type="short" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="TurninPlayerConditionID" Type="int" />
	</Table>
	<Table Name="BountySet" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VisiblePlayerConditionID" Type="int" />
		<Field Name="LockedQuestID" Type="short" />
	</Table>
	<Table Name="BroadcastText" Build="27075">
		<Field Name="Text_Lang" Type="string" />
		<Field Name="Text1_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LanguageID" Type="byte" />
		<Field Name="ConditionID" Type="int" />
		<Field Name="EmotesID" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Field_8" Type="int" />
		<Field Name="SoundEntriesID" Type="int" ArraySize="2" />
		<Field Name="EmoteID" Type="short" ArraySize="3" />
		<Field Name="EmoteDelay" Type="short" ArraySize="3" />
	</Table>
	<Table Name="CameraEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CameraEffectEntry" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="AmplitudeCurveID" Type="short" />
		<Field Name="Duration" Type="float" />
		<Field Name="Delay" Type="float" />
		<Field Name="Phase" Type="float" />
		<Field Name="Amplitude" Type="float" />
		<Field Name="AmplitudeB" Type="float" />
		<Field Name="Frequency" Type="float" />
		<Field Name="RadiusMin" Type="float" />
		<Field Name="RadiusMax" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="EffectType" Type="byte" />
		<Field Name="DirectionType" Type="byte" />
		<Field Name="MovementType" Type="byte" />
		<Field Name="AttenuationType" Type="byte" />
	</Table>
	<Table Name="CameraMode" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PositionOffset" Type="float" ArraySize="3" />
		<Field Name="TargetOffset" Type="float" ArraySize="3" />
		<Field Name="Type" Type="byte" />
		<Field Name="Flags" Type="int" />
		<Field Name="PositionSmoothing" Type="float" />
		<Field Name="RotationSmoothing" Type="float" />
		<Field Name="FieldOfView" Type="float" />
		<Field Name="LockedPositionOffsetBase" Type="byte" />
		<Field Name="LockedPositionOffsetDirection" Type="byte" />
		<Field Name="LockedTargetOffsetBase" Type="byte" />
		<Field Name="LockedTargetOffsetDirection" Type="byte" />
	</Table>
	<Table Name="Campaign" Build="27075">
		<Field Name="Title_Lang" Type="string" />
		<Field Name="FactionTitle" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiTextureKitID" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="CampaignXCondition" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="CampaignXQuestLine" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CampaignID" Type="int" />
		<Field Name="QuestLineID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="CastableRaidBuffs" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CastingSpellID" Type="int" />
	</Table>
	<Table Name="CelestialBody" Build="27075">
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="short" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="int" ArraySize="2" />
		<Field Name="Field_12" Type="float" ArraySize="2" />
		<Field Name="Field_13" Type="int" ArraySize="2" />
		<Field Name="Field_14" Type="float" ArraySize="2" />
		<Field Name="Field_15" Type="float" ArraySize="2" />
	</Table>
	<Table Name="Cfg_Categories" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="LocaleMask" Type="short" />
		<Field Name="Create_CharsetMask" Type="byte" />
		<Field Name="Existing_CharsetMask" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Cfg_Configs" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerKillingAllowed" Type="byte" />
		<Field Name="Roleplaying" Type="byte" />
		<Field Name="PlayerAttackSpeedBase" Type="short" />
		<Field Name="MaxDamageReductionPctPhysical" Type="float" />
	</Table>
	<Table Name="Cfg_Regions" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Tag" Type="string" />
		<Field Name="Region_ID" Type="short" />
		<Field Name="Raidorigin" Type="int" />
		<Field Name="Region_Group_Mask" Type="byte" />
		<Field Name="Challenge_Origin" Type="int" />
	</Table>
	<Table Name="CharacterFaceBoneSet" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SexID" Type="byte" />
		<Field Name="ModelFileDataID" Type="int" />
		<Field Name="FaceVariationIndex" Type="byte" />
		<Field Name="BoneSetFileDataID" Type="int" />
	</Table>
	<Table Name="CharacterFacialHairStyles" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Geoset" Type="int" ArraySize="5" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="VariationID" Type="byte" />
	</Table>
	<Table Name="CharacterLoadout" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Racemask" Type="long" />
		<Field Name="ChrClassID" Type="byte" />
		<Field Name="Purpose" Type="byte" />
	</Table>
	<Table Name="CharacterLoadoutItem" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CharacterLoadoutID" Type="short" />
		<Field Name="ItemID" Type="int" />
	</Table>
	<Table Name="CharacterServiceInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FlowTitle_Lang" Type="string" />
		<Field Name="PopupTitle_Lang" Type="string" />
		<Field Name="PopupDescription_Lang" Type="string" />
		<Field Name="BoostType" Type="int" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="Priority" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="ProfessionLevel" Type="int" />
		<Field Name="BoostLevel" Type="int" />
		<Field Name="Expansion" Type="int" />
		<Field Name="PopupUITextureKitID" Type="int" />
	</Table>
	<Table Name="CharBaseInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="ClassID" Type="byte" />
	</Table>
	<Table Name="CharBaseSection" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LayoutResType" Type="byte" />
		<Field Name="VariationEnum" Type="byte" />
		<Field Name="ResolutionVariationEnum" Type="byte" />
	</Table>
	<Table Name="CharComponentTextureLayouts" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Width" Type="short" />
		<Field Name="Height" Type="short" />
	</Table>
	<Table Name="CharComponentTextureSections" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CharComponentTextureLayoutID" Type="byte" />
		<Field Name="SectionType" Type="byte" />
		<Field Name="X" Type="short" />
		<Field Name="Y" Type="short" />
		<Field Name="Width" Type="short" />
		<Field Name="Height" Type="short" />
		<Field Name="OverlapSectionMask" Type="int" />
	</Table>
	<Table Name="CharHairGeosets" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="VariationID" Type="byte" />
		<Field Name="GeosetID" Type="byte" />
		<Field Name="Showscalp" Type="byte" />
		<Field Name="VariationType" Type="byte" />
		<Field Name="GeosetType" Type="byte" />
		<Field Name="ColorIndex" Type="byte" />
		<Field Name="CustomGeoFileDataID" Type="int" />
		<Field Name="HdCustomGeoFileDataID" Type="int" />
	</Table>
	<Table Name="CharSections" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="BaseSection" Type="byte" />
		<Field Name="VariationIndex" Type="byte" />
		<Field Name="ColorIndex" Type="byte" />
		<Field Name="Flags" Type="short" />
		<Field Name="MaterialResourcesID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="CharShipment" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ContainerID" Type="short" />
		<Field Name="DummyItemID" Type="int" />
		<Field Name="TreasureID" Type="int" />
		<Field Name="SpellID" Type="int" />
		<Field Name="OnCompleteSpellID" Type="int" />
		<Field Name="Duration" Type="int" />
		<Field Name="MaxShipments" Type="byte" />
		<Field Name="GarrFollowerID" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CharShipmentContainer" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="PendingText_Lang" Type="string" />
		<Field Name="UiTextureKitID" Type="short" />
		<Field Name="GarrTypeID" Type="byte" />
		<Field Name="GarrBuildingType" Type="byte" />
		<Field Name="BaseCapacity" Type="byte" />
		<Field Name="SmallDisplayInfoID" Type="short" />
		<Field Name="MediumDisplayInfoID" Type="short" />
		<Field Name="LargeDisplayInfoID" Type="short" />
		<Field Name="WorkingDisplayInfoID" Type="short" />
		<Field Name="WorkingSpellVisualID" Type="int" />
		<Field Name="CompleteSpellVisualID" Type="int" />
		<Field Name="MediumThreshold" Type="byte" />
		<Field Name="LargeThreshold" Type="byte" />
		<Field Name="Faction" Type="byte" />
		<Field Name="CrossFactionID" Type="short" />
	</Table>
	<Table Name="CharStartOutfit" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="OutfitID" Type="byte" />
		<Field Name="PetDisplayID" Type="int" />
		<Field Name="PetFamilyID" Type="byte" />
		<Field Name="ItemID" Type="int" ArraySize="24" />
	</Table>
	<Table Name="CharTitles" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Name1_Lang" Type="string" />
		<Field Name="Mask_ID" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ChatChannels" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Shortcut_Lang" Type="string" />
		<Field Name="Flags" Type="int" />
		<Field Name="FactionGroup" Type="byte" />
	</Table>
	<Table Name="ChatProfanity" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text" Type="string" />
		<Field Name="Language" Type="byte" />
	</Table>
	<Table Name="ChrClasses" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Filename" Type="string" />
		<Field Name="Name_Male_Lang" Type="string" />
		<Field Name="Name_Female_Lang" Type="string" />
		<Field Name="PetNameToken" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreateScreenFileDataID" Type="int" />
		<Field Name="SelectScreenFileDataID" Type="int" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="LowResScreenFileDataID" Type="int" />
		<Field Name="StartingLevel" Type="int" />
		<Field Name="Flags" Type="short" />
		<Field Name="CinematicSequenceID" Type="short" />
		<Field Name="DefaultSpec" Type="short" />
		<Field Name="PrimaryStatPriority" Type="byte" />
		<Field Name="DisplayPower" Type="byte" />
		<Field Name="RangedAttackPowerPerAgility" Type="byte" />
		<Field Name="AttackPowerPerAgility" Type="byte" />
		<Field Name="AttackPowerPerStrength" Type="byte" />
		<Field Name="SpellClassSet" Type="byte" />
	</Table>
	<Table Name="ChrClassesXPowerTypes" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PowerType" Type="byte" />
	</Table>
	<Table Name="ChrClassRaceSex" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="Sex" Type="byte" />
		<Field Name="Flags" Type="int" />
		<Field Name="SoundID" Type="int" />
		<Field Name="VoiceSoundFilterID" Type="int" />
	</Table>
	<Table Name="ChrClassTitle" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Male_Lang" Type="string" />
		<Field Name="Name_Female_Lang" Type="string" />
		<Field Name="ChrClassID" Type="byte" />
	</Table>
	<Table Name="ChrClassUIDisplay" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrClassesID" Type="byte" />
		<Field Name="AdvGuidePlayerConditionID" Type="int" />
		<Field Name="SplashPlayerConditionID" Type="int" />
	</Table>
	<Table Name="ChrClassVillain" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ChrClassID" Type="byte" />
		<Field Name="Gender" Type="byte" />
	</Table>
	<Table Name="ChrCustomization" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Sex" Type="int" />
		<Field Name="BaseSection" Type="int" />
		<Field Name="UiCustomizationType" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="ComponentSection" Type="int" ArraySize="3" />
	</Table>
	<Table Name="ChrRaces" Build="27075">
		<Field Name="ClientPrefix" Type="string" />
		<Field Name="ClientFileString" Type="string" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Name_Female_Lang" Type="string" />
		<Field Name="Name_Lowercase_Lang" Type="string" />
		<Field Name="Name_Female_Lowercase_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="MaleDisplayID" Type="int" />
		<Field Name="FemaleDisplayID" Type="int" />
		<Field Name="HighResMaleDisplayID" Type="int" />
		<Field Name="HighResFemaleDisplayID" Type="int" />
		<Field Name="CreateScreenFileDataID" Type="int" />
		<Field Name="SelectScreenFileDataID" Type="int" />
		<Field Name="MaleCustomizeOffset" Type="float" ArraySize="3" />
		<Field Name="FemaleCustomizeOffset" Type="float" ArraySize="3" />
		<Field Name="LowResScreenFileDataID" Type="int" />
		<Field Name="AlteredFormStartVisualKitID" Type="int" ArraySize="3" />
		<Field Name="AlteredFormFinishVisualKitID" Type="int" ArraySize="3" />
		<Field Name="HeritageArmorAchievementID" Type="int" />
		<Field Name="StartingLevel" Type="int" />
		<Field Name="UiDisplayOrder" Type="int" />
		<Field Name="FemaleSkeletonFileDataID" Type="int" />
		<Field Name="MaleSkeletonFileDataID" Type="int" />
		<Field Name="BaseRaceID" Type="int" />
		<Field Name="FactionID" Type="short" />
		<Field Name="CinematicSequenceID" Type="short" />
		<Field Name="ResSicknessSpellID" Type="short" />
		<Field Name="SplashSoundID" Type="short" />
		<Field Name="BaseLanguage" Type="byte" />
		<Field Name="CreatureType" Type="byte" />
		<Field Name="Alliance" Type="byte" />
		<Field Name="Race_Related" Type="byte" />
		<Field Name="UnalteredVisualRaceID" Type="byte" />
		<Field Name="CharComponentTextureLayoutID" Type="byte" />
		<Field Name="CharComponentTexLayoutHiResID" Type="byte" />
		<Field Name="DefaultClassID" Type="byte" />
		<Field Name="NeutralRaceID" Type="byte" />
		<Field Name="MaleModelFallbackRaceID" Type="byte" />
		<Field Name="MaleModelFallbackSex" Type="byte" />
		<Field Name="FemaleModelFallbackRaceID" Type="byte" />
		<Field Name="FemaleModelFallbackSex" Type="byte" />
		<Field Name="MaleTextureFallbackRaceID" Type="byte" />
		<Field Name="MaleTextureFallbackSex" Type="byte" />
		<Field Name="FemaleTextureFallbackRaceID" Type="byte" />
		<Field Name="FemaleTextureFallbackSex" Type="byte" />
	</Table>
	<Table Name="ChrSpecialization" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="FemaleName_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PetTalentType" Type="byte" />
		<Field Name="Role" Type="byte" />
		<Field Name="Flags" Type="int" />
		<Field Name="SpellIconFileID" Type="int" />
		<Field Name="PrimaryStatPriority" Type="byte" />
		<Field Name="AnimReplacements" Type="int" />
		<Field Name="MasterySpellID" Type="int" ArraySize="2" />
	</Table>
	<Table Name="ChrUpgradeBucket" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrSpecializationID" Type="short" />
	</Table>
	<Table Name="ChrUpgradeBucketSpell" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
	</Table>
	<Table Name="ChrUpgradeTier" Build="27075">
		<Field Name="DisplayName_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="NumTalents" Type="byte" />
	</Table>
	<Table Name="CinematicCamera" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Origin" Type="float" ArraySize="3" />
		<Field Name="SoundID" Type="int" />
		<Field Name="OriginFacing" Type="float" />
		<Field Name="FileDataID" Type="int" />
	</Table>
	<Table Name="CinematicSequences" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundID" Type="int" />
		<Field Name="Camera" Type="short" ArraySize="8" />
	</Table>
	<Table Name="ClientSceneEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SceneScriptPackageID" Type="int" />
	</Table>
	<Table Name="CloakDampening" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TabardAngle" Type="float" />
		<Field Name="TabardDampening" Type="float" />
		<Field Name="ExpectedWeaponSize" Type="float" />
		<Field Name="Angle" Type="float" ArraySize="5" />
		<Field Name="Dampening" Type="float" ArraySize="5" />
		<Field Name="TailAngle" Type="float" ArraySize="2" />
		<Field Name="TailDampening" Type="float" ArraySize="2" />
	</Table>
	<Table Name="CloneEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
	</Table>
	<Table Name="CombatCondition" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WorldStateExpressionID" Type="short" />
		<Field Name="SelfConditionID" Type="short" />
		<Field Name="TargetConditionID" Type="short" />
		<Field Name="FriendConditionLogic" Type="byte" />
		<Field Name="EnemyConditionLogic" Type="byte" />
		<Field Name="FriendConditionID" Type="short" ArraySize="2" />
		<Field Name="FriendConditionOp" Type="byte" ArraySize="2" />
		<Field Name="FriendConditionCount" Type="byte" ArraySize="2" />
		<Field Name="EnemyConditionID" Type="short" ArraySize="2" />
		<Field Name="EnemyConditionOp" Type="byte" ArraySize="2" />
		<Field Name="EnemyConditionCount" Type="byte" ArraySize="2" />
	</Table>
	<Table Name="CommentatorStartLocation" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="MapID" Type="int" />
	</Table>
	<Table Name="CommentatorTrackedCooldown" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="Priority" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CommunityIcon" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="ComponentModelFileData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GenderIndex" Type="byte" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="PositionIndex" Type="byte" />
	</Table>
	<Table Name="ComponentTextureFileData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GenderIndex" Type="byte" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="RaceID" Type="byte" />
	</Table>
	<Table Name="ConfigurationWarning" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Warning_Lang" Type="string" />
		<Field Name="Type" Type="int" />
	</Table>
	<Table Name="ContentTuning" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinLevel" Type="int" />
		<Field Name="MaxLevel" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="ExpectedStatModID" Type="int" />
		<Field Name="Field_6" Type="int" />
	</Table>
	<Table Name="Contribution" Build="27075">
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ManagedWorldStateInputID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="UiTextureAtlasMemberID" Type="int" ArraySize="4" />
	</Table>
	<Table Name="ContributionStyle" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
	</Table>
	<Table Name="ContributionStyleContainer" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" ArraySize="5" />
	</Table>
	<Table Name="ConversationLine" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BroadcastTextID" Type="int" />
		<Field Name="SpellVisualKitID" Type="int" />
		<Field Name="AdditionalDuration" Type="int" />
		<Field Name="NextConversationLineID" Type="short" />
		<Field Name="AnimKitID" Type="short" />
		<Field Name="SpeechType" Type="byte" />
		<Field Name="StartAnimation" Type="byte" />
		<Field Name="EndAnimation" Type="byte" />
	</Table>
	<Table Name="Creature" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="NameAlt_Lang" Type="string" />
		<Field Name="Title_Lang" Type="string" />
		<Field Name="TitleAlt_Lang" Type="string" />
		<Field Name="Classification" Type="byte" />
		<Field Name="CreatureType" Type="byte" />
		<Field Name="CreatureFamily" Type="short" />
		<Field Name="StartAnimState" Type="byte" />
		<Field Name="DisplayID" Type="int" ArraySize="4" />
		<Field Name="DisplayProbability" Type="float" ArraySize="4" />
		<Field Name="AlwaysItem" Type="int" ArraySize="3" />
	</Table>
	<Table Name="CreatureDifficulty" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ExpansionID" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
		<Field Name="FactionID" Type="short" />
		<Field Name="ContentTuningID" Type="int" />
		<Field Name="Flags" Type="int" ArraySize="7" />
	</Table>
	<Table Name="CreatureDisplayInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelID" Type="short" />
		<Field Name="Field_3" Type="short" />
		<Field Name="SizeClass" Type="byte" />
		<Field Name="CreatureModelScale" Type="float" />
		<Field Name="CreatureModelAlpha" Type="byte" />
		<Field Name="BloodID" Type="byte" />
		<Field Name="ExtendedDisplayInfoID" Type="int" />
		<Field Name="NPCSoundID" Type="short" />
		<Field Name="ParticleColorID" Type="short" />
		<Field Name="PortraitCreatureDisplayInfoID" Type="int" />
		<Field Name="PortraitTextureFileDataID" Type="int" />
		<Field Name="ObjectEffectPackageID" Type="short" />
		<Field Name="AnimReplacementSetID" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="StateSpellVisualKitID" Type="int" />
		<Field Name="PlayerOverrideScale" Type="float" />
		<Field Name="PetInstanceScale" Type="float" />
		<Field Name="UnarmedWeaponType" Type="byte" />
		<Field Name="MountPoofSpellVisualKitID" Type="int" />
		<Field Name="DissolveEffectID" Type="int" />
		<Field Name="Gender" Type="byte" />
		<Field Name="DissolveOutEffectID" Type="int" />
		<Field Name="CreatureModelMinLod" Type="byte" />
		<Field Name="TextureVariationFileDataID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="CreatureDisplayInfoCond" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="long" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Gender" Type="byte" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="SkinColorMask" Type="int" />
		<Field Name="HairColorMask" Type="int" />
		<Field Name="HairStyleMask" Type="int" />
		<Field Name="FaceStyleMask" Type="int" />
		<Field Name="FacialHairStyleMask" Type="int" />
		<Field Name="CreatureModelDataID" Type="int" />
		<Field Name="CustomOption0_Mask" Type="int" ArraySize="2" />
		<Field Name="CustomOption1_Mask" Type="int" ArraySize="2" />
		<Field Name="CustomOption2_Mask" Type="int" ArraySize="2" />
		<Field Name="TextureVariationFileDataID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="CreatureDisplayInfoEvt" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Fourcc" Type="int" />
		<Field Name="SpellVisualKitID" Type="int" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CreatureDisplayInfoExtra" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayRaceID" Type="byte" />
		<Field Name="DisplaySexID" Type="byte" />
		<Field Name="DisplayClassID" Type="byte" />
		<Field Name="SkinID" Type="byte" />
		<Field Name="FaceID" Type="byte" />
		<Field Name="HairStyleID" Type="byte" />
		<Field Name="HairColorID" Type="byte" />
		<Field Name="FacialHairID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="BakeMaterialResourcesID" Type="int" />
		<Field Name="HDBakeMaterialResourcesID" Type="int" />
		<Field Name="CustomDisplayOption" Type="byte" ArraySize="3" />
	</Table>
	<Table Name="CreatureDisplayInfoGeosetData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GeosetIndex" Type="byte" />
		<Field Name="GeosetValue" Type="byte" />
	</Table>
	<Table Name="CreatureDisplayInfoTrn" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DstCreatureDisplayInfoID" Type="int" />
		<Field Name="DissolveEffectID" Type="int" />
		<Field Name="StartVisualKitID" Type="int" />
		<Field Name="MaxTime" Type="float" />
		<Field Name="FinishVisualKitID" Type="int" />
	</Table>
	<Table Name="CreatureDispXUiCamera" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureDisplayInfoID" Type="int" />
		<Field Name="UiCameraID" Type="short" />
	</Table>
	<Table Name="CreatureFamily" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="MinScale" Type="float" />
		<Field Name="MinScaleLevel" Type="byte" />
		<Field Name="MaxScale" Type="float" />
		<Field Name="MaxScaleLevel" Type="byte" />
		<Field Name="PetFoodMask" Type="short" />
		<Field Name="PetTalentType" Type="byte" />
		<Field Name="IconFileID" Type="int" />
		<Field Name="SkillLine" Type="short" ArraySize="2" />
	</Table>
	<Table Name="CreatureImmunities" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="School" Type="byte" />
		<Field Name="DispelType" Type="int" />
		<Field Name="MechanicsAllowed" Type="byte" />
		<Field Name="EffectsAllowed" Type="byte" />
		<Field Name="StatesAllowed" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Mechanic" Type="int" ArraySize="2" />
		<Field Name="Effect" Type="int" ArraySize="9" />
		<Field Name="State" Type="int" ArraySize="16" />
	</Table>
	<Table Name="CreatureModelData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GeoBox" Type="float" ArraySize="6" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="int" />
		<Field Name="Field_15" Type="float" />
		<Field Name="Field_16" Type="float" />
		<Field Name="Field_17" Type="float" />
		<Field Name="Field_18" Type="int" />
		<Field Name="Field_19" Type="float" />
		<Field Name="Field_20" Type="float" />
		<Field Name="Field_21" Type="float" />
		<Field Name="Field_22" Type="float" />
		<Field Name="Field_23" Type="float" />
		<Field Name="Field_24" Type="float" />
		<Field Name="Field_25" Type="float" />
		<Field Name="Field_26" Type="float" />
		<Field Name="Field_27" Type="float" />
		<Field Name="Field_28" Type="float" />
		<Field Name="Field_29" Type="float" />
	</Table>
	<Table Name="CreatureMovementInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SmoothFacingChaseRate" Type="float" />
	</Table>
	<Table Name="CreatureSoundData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="int" />
		<Field Name="Field_15" Type="int" />
		<Field Name="Field_16" Type="int" />
		<Field Name="Field_17" Type="int" />
		<Field Name="Field_18" Type="int" />
		<Field Name="Field_19" Type="int" />
		<Field Name="Field_20" Type="int" />
		<Field Name="Field_21" Type="int" />
		<Field Name="Field_22" Type="int" />
		<Field Name="Field_23" Type="int" />
		<Field Name="Field_24" Type="int" />
		<Field Name="Field_25" Type="int" />
		<Field Name="Field_26" Type="int" />
		<Field Name="Field_27" Type="int" />
		<Field Name="Field_28" Type="int" />
		<Field Name="Field_29" Type="int" />
		<Field Name="Field_30" Type="int" />
		<Field Name="Field_31" Type="int" />
		<Field Name="Field_32" Type="int" />
		<Field Name="Field_33" Type="float" />
		<Field Name="Field_34" Type="float" />
		<Field Name="Field_35" Type="byte" />
		<Field Name="Field_36" Type="int" />
		<Field Name="SoundFidget" Type="int" ArraySize="5" />
		<Field Name="CustomAttack" Type="int" ArraySize="4" />
	</Table>
	<Table Name="CreatureType" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CreatureXContribution" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ContributionID" Type="int" />
	</Table>
	<Table Name="CreatureXDisplayInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureDisplayInfoID" Type="int" />
		<Field Name="Probability" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="Criteria" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="short" />
		<Field Name="Field_12" Type="byte" />
	</Table>
	<Table Name="CriteriaTree" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="short" />
	</Table>
	<Table Name="CriteriaTreeXEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WorldEffectID" Type="short" />
	</Table>
	<Table Name="CurrencyCategory" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ExpansionID" Type="byte" />
	</Table>
	<Table Name="CurrencyContainer" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ContainerName_Lang" Type="string" />
		<Field Name="ContainerDescription_Lang" Type="string" />
		<Field Name="MinAmount" Type="int" />
		<Field Name="MaxAmount" Type="int" />
		<Field Name="ContainerIconID" Type="int" />
		<Field Name="ContainerQuality" Type="int" />
		<Field Name="Field_8" Type="int" />
	</Table>
	<Table Name="CurrencyTypes" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="int" />
	</Table>
	<Table Name="Curve" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CurvePoint" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="2" />
		<Field Name="CurveID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="DeathThudLookups" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SizeClass" Type="byte" />
		<Field Name="TerrainTypeSoundID" Type="byte" />
		<Field Name="SoundEntryID" Type="int" />
		<Field Name="SoundEntryIDWater" Type="int" />
	</Table>
	<Table Name="DecalProperties" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="float" />
		<Field Name="Field_13" Type="float" />
		<Field Name="Field_14" Type="byte" />
		<Field Name="Field_15" Type="byte" />
		<Field Name="Field_16" Type="int" />
		<Field Name="Field_17" Type="int" />
		<Field Name="Field_18" Type="float" />
	</Table>
	<Table Name="DeclinedWord" Build="27075">
		<Field Name="Word" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="DeclinedWordCases" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DeclinedWord" Type="string" />
		<Field Name="CaseIndex" Type="byte" />
	</Table>
	<Table Name="DestructibleModelData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="short" />
		<Field Name="Field_13" Type="byte" />
		<Field Name="Field_14" Type="byte" />
		<Field Name="Field_15" Type="byte" />
		<Field Name="Field_16" Type="byte" />
		<Field Name="Field_17" Type="short" />
		<Field Name="Field_18" Type="byte" />
		<Field Name="Field_19" Type="short" />
		<Field Name="Field_20" Type="byte" />
		<Field Name="Field_21" Type="byte" />
		<Field Name="Field_22" Type="byte" />
		<Field Name="Field_23" Type="byte" />
	</Table>
	<Table Name="DeviceBlacklist" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VendorID" Type="short" />
		<Field Name="DeviceID" Type="short" />
	</Table>
	<Table Name="DeviceDefaultSettings" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="short" />
		<Field Name="DefaultSetting" Type="byte" />
	</Table>
	<Table Name="Difficulty" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="short" />
		<Field Name="Field_13" Type="short" />
		<Field Name="Field_14" Type="short" />
	</Table>
	<Table Name="DissolveEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" />
		<Field Name="Field_3" Type="float" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="int" />
		<Field Name="Field_15" Type="float" />
	</Table>
	<Table Name="DriverBlacklist" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="byte" />
	</Table>
	<Table Name="DungeonEncounter" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapID" Type="short" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="int" />
	</Table>
	<Table Name="DurabilityCosts" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WeaponSubClassCost" Type="short" ArraySize="21" />
		<Field Name="ArmorSubClassCost" Type="short" ArraySize="8" />
	</Table>
	<Table Name="DurabilityQuality" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="EdgeGlowEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" />
		<Field Name="Field_3" Type="float" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Field_12" Type="float" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="int" />
	</Table>
	<Table Name="Emotes" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="long" />
		<Field Name="EmoteSlashCommand" Type="string" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="int" />
	</Table>
	<Table Name="EmotesText" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="EmoteID" Type="short" />
	</Table>
	<Table Name="EmotesTextData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text_Lang" Type="string" />
		<Field Name="RelationshipFlags" Type="byte" />
	</Table>
	<Table Name="EmotesTextSound" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="SoundID" Type="int" />
	</Table>
	<Table Name="EnvironmentalDamage" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EnumID" Type="byte" />
		<Field Name="VisualkitID" Type="short" />
	</Table>
	<Table Name="Exhaustion" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="CombatLogText" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Xp" Type="int" />
		<Field Name="Factor" Type="float" />
		<Field Name="OutdoorHours" Type="float" />
		<Field Name="InnHours" Type="float" />
		<Field Name="Threshold" Type="float" />
	</Table>
	<Table Name="ExpectedStat" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ExpansionID" Type="int" />
		<Field Name="CreatureHealth" Type="float" />
		<Field Name="PlayerHealth" Type="float" />
		<Field Name="CreatureAutoAttackDps" Type="float" />
		<Field Name="CreatureArmor" Type="float" />
		<Field Name="PlayerMana" Type="float" />
		<Field Name="PlayerPrimaryStat" Type="float" />
		<Field Name="PlayerSecondaryStat" Type="float" />
		<Field Name="ArmorConstant" Type="float" />
		<Field Name="CreatureSpellDamage" Type="float" />
	</Table>
	<Table Name="ExpectedStatMod" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureHealthMod" Type="float" />
		<Field Name="PlayerHealthMod" Type="float" />
		<Field Name="CreatureAutoAttackDPSMod" Type="float" />
		<Field Name="CreatureArmorMod" Type="float" />
		<Field Name="PlayerManaMod" Type="float" />
		<Field Name="PlayerPrimaryStatMod" Type="float" />
		<Field Name="PlayerSecondaryStatMod" Type="float" />
		<Field Name="ArmorConstantMod" Type="float" />
		<Field Name="CreatureSpellDamageMod" Type="float" />
	</Table>
	<Table Name="Faction" Build="27075">
		<Field Name="ReputationRaceMask" Type="long" ArraySize="4" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ReputationIndex" Type="short" />
		<Field Name="ParentFactionID" Type="short" />
		<Field Name="Expansion" Type="byte" />
		<Field Name="FriendshipRepID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ParagonFactionID" Type="short" />
		<Field Name="ReputationClassMask" Type="short" ArraySize="4" />
		<Field Name="ReputationFlags" Type="short" ArraySize="4" />
		<Field Name="ReputationBase" Type="int" ArraySize="4" />
		<Field Name="ReputationMax" Type="int" ArraySize="4" />
		<Field Name="ParentFactionMod" Type="float" ArraySize="2" />
		<Field Name="ParentFactionCap" Type="byte" ArraySize="2" />
	</Table>
	<Table Name="FactionGroup" Build="27075">
		<Field Name="InternalName" Type="string" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaskID" Type="byte" />
		<Field Name="HonorCurrencyTextureFileID" Type="int" />
		<Field Name="ConquestCurrencyTextureFileID" Type="int" />
	</Table>
	<Table Name="FactionTemplate" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Faction" Type="short" />
		<Field Name="Flags" Type="short" />
		<Field Name="FactionGroup" Type="byte" />
		<Field Name="FriendGroup" Type="byte" />
		<Field Name="EnemyGroup" Type="byte" />
		<Field Name="Enemies" Type="short" ArraySize="4" />
		<Field Name="Friend" Type="short" ArraySize="4" />
	</Table>
	<Table Name="FootprintTextures" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="TextureBlendsetID" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="FootstepTerrainLookup" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureFootstepID" Type="short" />
		<Field Name="TerrainSoundID" Type="byte" />
		<Field Name="SoundID" Type="int" />
		<Field Name="SoundIDSplash" Type="int" />
	</Table>
	<Table Name="FriendshipRepReaction" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Reaction_Lang" Type="string" />
		<Field Name="FriendshipRepID" Type="byte" />
		<Field Name="ReactionThreshold" Type="short" />
	</Table>
	<Table Name="FriendshipReputation" Build="27075">
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FactionID" Type="short" />
		<Field Name="TextureFileID" Type="int" />
	</Table>
	<Table Name="FullScreenEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="float" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="float" />
		<Field Name="Field_13" Type="float" />
		<Field Name="Field_14" Type="float" />
		<Field Name="Field_15" Type="float" />
		<Field Name="Field_16" Type="float" />
		<Field Name="Field_17" Type="float" />
		<Field Name="Field_18" Type="float" />
		<Field Name="Field_19" Type="float" />
		<Field Name="Field_20" Type="float" />
		<Field Name="Field_21" Type="float" />
		<Field Name="Field_22" Type="int" />
		<Field Name="Field_23" Type="float" />
		<Field Name="Field_24" Type="float" />
		<Field Name="Field_25" Type="float" />
		<Field Name="Field_26" Type="float" />
		<Field Name="Field_27" Type="int" />
		<Field Name="Field_28" Type="int" />
		<Field Name="Field_29" Type="int" />
	</Table>
	<Table Name="GameObjectArtKit" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AttachModelFileID" Type="int" />
		<Field Name="TextureVariationFileID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="GameObjectDiffAnimMap" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="Animation" Type="byte" />
		<Field Name="AttachmentDisplayID" Type="short" />
	</Table>
	<Table Name="GameObjectDisplayInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GeoBox" Type="float" ArraySize="6" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="ObjectEffectPackageID" Type="short" />
		<Field Name="OverrideLootEffectScale" Type="float" />
		<Field Name="OverrideNameScale" Type="float" />
	</Table>
	<Table Name="GameObjectDisplayInfoXSoundKit" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundKitID" Type="int" />
		<Field Name="EventIndex" Type="byte" />
	</Table>
	<Table Name="GameObjects" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="Rot" Type="float" ArraySize="4" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OwnerID" Type="short" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Scale" Type="float" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="short" />
		<Field Name="Field_11" Type="short" />
		<Field Name="PropValue" Type="int" ArraySize="8" />
	</Table>
	<Table Name="GameTips" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text_Lang" Type="string" />
		<Field Name="SortIndex" Type="byte" />
		<Field Name="Min_Level" Type="short" />
		<Field Name="Max_Level" Type="short" />
	</Table>
	<Table Name="GarrAbility" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="short" />
	</Table>
	<Table Name="GarrAbilityCategory" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="GarrAbilityEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrAbilityID" Type="short" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="byte" />
	</Table>
	<Table Name="GarrBuilding" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="string" />
		<Field Name="Field_5" Type="string" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="short" />
		<Field Name="Field_14" Type="int" />
		<Field Name="Field_15" Type="short" />
		<Field Name="Field_16" Type="short" />
		<Field Name="Field_17" Type="int" />
		<Field Name="Field_18" Type="short" />
		<Field Name="Field_19" Type="short" />
		<Field Name="Field_20" Type="int" />
		<Field Name="Field_21" Type="byte" />
		<Field Name="Field_22" Type="short" />
		<Field Name="Field_23" Type="short" />
		<Field Name="Field_24" Type="short" />
		<Field Name="Field_25" Type="byte" />
	</Table>
	<Table Name="GarrBuildingDoodadSet" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrBuildingID" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="HordeDoodadSet" Type="byte" />
		<Field Name="AllianceDoodadSet" Type="byte" />
		<Field Name="SpecializationID" Type="byte" />
	</Table>
	<Table Name="GarrBuildingPlotInst" Build="27075">
		<Field Name="Field_1" Type="float" ArraySize="2" />
		<Field Name="Field_2" Type="int" IsIndex="true" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="short" />
	</Table>
	<Table Name="GarrClassSpec" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="int" IsIndex="true" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="byte" />
	</Table>
	<Table Name="GarrClassSpecPlayerCond" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="byte" />
	</Table>
	<Table Name="GarrEncounter" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="int" IsIndex="true" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
	</Table>
	<Table Name="GarrEncounterSetXEncounter" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrEncounterID" Type="int" />
	</Table>
	<Table Name="GarrEncounterXMechanic" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrMechanicID" Type="byte" />
		<Field Name="GarrMechanicSetID" Type="byte" />
	</Table>
	<Table Name="GarrFollItemSetMember" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
	</Table>
	<Table Name="GarrFollower" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="int" IsIndex="true" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="byte" />
		<Field Name="Field_13" Type="byte" />
		<Field Name="Field_14" Type="byte" />
		<Field Name="Field_15" Type="short" />
		<Field Name="Field_16" Type="short" />
		<Field Name="Field_17" Type="byte" />
		<Field Name="Field_18" Type="byte" />
		<Field Name="Field_19" Type="int" />
		<Field Name="Field_20" Type="int" />
		<Field Name="Field_21" Type="short" />
		<Field Name="Field_22" Type="short" />
		<Field Name="Field_23" Type="short" />
		<Field Name="Field_24" Type="short" />
		<Field Name="Field_25" Type="byte" />
		<Field Name="Field_26" Type="byte" />
		<Field Name="Field_27" Type="byte" />
		<Field Name="Field_28" Type="int" />
		<Field Name="Field_29" Type="int" />
		<Field Name="Field_30" Type="byte" />
		<Field Name="Field_31" Type="byte" />
		<Field Name="Field_32" Type="byte" />
	</Table>
	<Table Name="GarrFollowerLevelXP" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="short" />
	</Table>
	<Table Name="GarrFollowerQuality" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="int" />
	</Table>
	<Table Name="GarrFollowerSetXFollower" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrFollowerID" Type="int" />
	</Table>
	<Table Name="GarrFollowerType" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="byte" />
	</Table>
	<Table Name="GarrFollowerUICreature" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="byte" />
	</Table>
	<Table Name="GarrFollowerXAbility" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
	</Table>
	<Table Name="GarrFollSupportSpell" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
	</Table>
	<Table Name="GarrItemLevelUpgradeData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Operation" Type="int" />
		<Field Name="MinItemLevel" Type="int" />
		<Field Name="MaxItemLevel" Type="int" />
		<Field Name="FollowerTypeID" Type="int" />
	</Table>
	<Table Name="GarrMechanic" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="float" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="GarrMechanicSetXMechanic" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
	</Table>
	<Table Name="GarrMechanicType" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="int" IsIndex="true" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="byte" />
	</Table>
	<Table Name="GarrMission" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="float" ArraySize="2" />
		<Field Name="Field_5" Type="float" ArraySize="2" />
		<Field Name="Field_6" Type="int" IsIndex="true" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="short" />
		<Field Name="Field_13" Type="byte" />
		<Field Name="Field_14" Type="short" />
		<Field Name="Field_15" Type="int" />
		<Field Name="Field_16" Type="byte" />
		<Field Name="Field_17" Type="int" />
		<Field Name="Field_18" Type="byte" />
		<Field Name="Field_19" Type="short" />
		<Field Name="Field_20" Type="int" />
		<Field Name="Field_21" Type="int" />
		<Field Name="Field_22" Type="int" />
		<Field Name="Field_23" Type="byte" />
		<Field Name="Field_24" Type="int" />
		<Field Name="Field_25" Type="int" />
		<Field Name="Field_26" Type="byte" />
		<Field Name="Field_27" Type="int" />
		<Field Name="Field_28" Type="int" />
	</Table>
	<Table Name="GarrMissionTexture" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="2" />
		<Field Name="UiTextureKitID" Type="short" />
	</Table>
	<Table Name="GarrMissionType" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="UiTextureAtlasMemberID" Type="short" />
		<Field Name="UiTextureKitID" Type="short" />
	</Table>
	<Table Name="GarrMissionXEncounter" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
	</Table>
	<Table Name="GarrMissionXFollower" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrFollowerID" Type="int" />
		<Field Name="GarrFollowerSetID" Type="int" />
	</Table>
	<Table Name="GarrMssnBonusAbility" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="int" />
	</Table>
	<Table Name="GarrPlot" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="PlotType" Type="byte" />
		<Field Name="HordeConstructObjID" Type="int" />
		<Field Name="AllianceConstructObjID" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UiCategoryID" Type="byte" />
		<Field Name="UpgradeRequirement" Type="int" ArraySize="2" />
	</Table>
	<Table Name="GarrPlotBuilding" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrPlotID" Type="byte" />
		<Field Name="GarrBuildingID" Type="byte" />
	</Table>
	<Table Name="GarrPlotInstance" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="GarrPlotID" Type="byte" />
	</Table>
	<Table Name="GarrPlotUICategory" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CategoryName_Lang" Type="string" />
		<Field Name="PlotType" Type="byte" />
	</Table>
	<Table Name="GarrSiteLevel" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TownHallUiPos" Type="float" ArraySize="2" />
		<Field Name="GarrSiteID" Type="int" />
		<Field Name="GarrLevel" Type="byte" />
		<Field Name="MapID" Type="short" />
		<Field Name="UpgradeMovieID" Type="short" />
		<Field Name="UiTextureKitID" Type="short" />
		<Field Name="MaxBuildingLevel" Type="byte" />
		<Field Name="UpgradeCost" Type="short" />
		<Field Name="UpgradeGoldCost" Type="short" />
	</Table>
	<Table Name="GarrSiteLevelPlotInst" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiMarkerPos" Type="float" ArraySize="2" />
		<Field Name="GarrSiteLevelID" Type="short" />
		<Field Name="GarrPlotInstanceID" Type="byte" />
		<Field Name="UiMarkerSize" Type="byte" />
	</Table>
	<Table Name="GarrSpecialization" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Tooltip_Lang" Type="string" />
		<Field Name="BuildingType" Type="byte" />
		<Field Name="SpecType" Type="byte" />
		<Field Name="RequiredUpgradeLevel" Type="byte" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="Param" Type="float" ArraySize="2" />
	</Table>
	<Table Name="GarrString" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text_Lang" Type="string" />
	</Table>
	<Table Name="GarrTalent" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrTalentTreeID" Type="int" />
		<Field Name="Tier" Type="byte" />
		<Field Name="UiOrder" Type="byte" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="GarrAbilityID" Type="int" />
		<Field Name="PerkSpellID" Type="int" />
		<Field Name="PerkPlayerConditionID" Type="int" />
		<Field Name="ResearchDurationSecs" Type="int" />
		<Field Name="ResearchGoldCost" Type="int" />
		<Field Name="ResearchCost" Type="int" />
		<Field Name="ResearchCostCurrencyTypesID" Type="int" />
		<Field Name="RespecDurationSecs" Type="int" />
		<Field Name="RespecGoldCost" Type="int" />
		<Field Name="RespecCost" Type="int" />
		<Field Name="RespecCostCurrencyTypesID" Type="int" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="GarrTalentTree" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="short" />
	</Table>
	<Table Name="GarrType" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PrimaryCurrencyTypeID" Type="int" />
		<Field Name="SecondaryCurrencyTypeID" Type="int" />
		<Field Name="ExpansionID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="MapIDs" Type="int" ArraySize="2" />
	</Table>
	<Table Name="GarrUiAnimClassInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrClassSpecID" Type="byte" />
		<Field Name="MovementType" Type="byte" />
		<Field Name="ImpactDelaySecs" Type="float" />
		<Field Name="CastKit" Type="int" />
		<Field Name="ImpactKit" Type="int" />
		<Field Name="TargetImpactKit" Type="int" />
	</Table>
	<Table Name="GarrUiAnimRaceInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrFollRaceID" Type="byte" />
		<Field Name="MaleScale" Type="float" />
		<Field Name="MaleHeight" Type="float" />
		<Field Name="FemaleScale" Type="float" />
		<Field Name="FemaleHeight" Type="float" />
		<Field Name="MaleSingleModelScale" Type="float" />
		<Field Name="MaleSingleModelHeight" Type="float" />
		<Field Name="FemaleSingleModelScale" Type="float" />
		<Field Name="FemaleSingleModelHeight" Type="float" />
		<Field Name="MaleFollowerPageScale" Type="float" />
		<Field Name="MaleFollowerPageHeight" Type="float" />
		<Field Name="FemaleFollowerPageScale" Type="float" />
		<Field Name="FemaleFollowerPageHeight" Type="float" />
	</Table>
	<Table Name="GemProperties" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Enchant_ID" Type="short" />
		<Field Name="Type" Type="int" />
		<Field Name="Min_Item_Level" Type="short" />
	</Table>
	<Table Name="GlobalStrings" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseTag" Type="string" />
		<Field Name="TagText_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="GlyphBindableSpell" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
	</Table>
	<Table Name="GlyphExclusiveCategory" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="GlyphProperties" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="SpellIconID" Type="short" />
		<Field Name="GlyphType" Type="byte" />
		<Field Name="GlyphExclusiveCategoryID" Type="byte" />
	</Table>
	<Table Name="GlyphRequiredSpec" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrSpecializationID" Type="short" />
	</Table>
	<Table Name="GMSurveyAnswers" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Answer_Lang" Type="string" />
		<Field Name="Sort_Index" Type="byte" />
	</Table>
	<Table Name="GMSurveyCurrentSurvey" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GMSURVEY_ID" Type="byte" />
	</Table>
	<Table Name="GMSurveyQuestions" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Question_Lang" Type="string" />
	</Table>
	<Table Name="GMSurveySurveys" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Q" Type="byte" ArraySize="15" />
	</Table>
	<Table Name="GroundEffectDoodad" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelFileID" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Animscale" Type="float" />
		<Field Name="Pushscale" Type="float" />
	</Table>
	<Table Name="GroundEffectTexture" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Density" Type="int" />
		<Field Name="Sound" Type="byte" />
		<Field Name="DoodadID" Type="short" ArraySize="4" />
		<Field Name="DoodadWeight" Type="byte" ArraySize="4" />
	</Table>
	<Table Name="GroupFinderActivity" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FullName_Lang" Type="string" />
		<Field Name="ShortName_Lang" Type="string" />
		<Field Name="GroupFinderCategoryID" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="GroupFinderActivityGrpID" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevelSuggestion" Type="byte" />
		<Field Name="Flags" Type="int" />
		<Field Name="MinGearLevelSuggestion" Type="short" />
		<Field Name="MapID" Type="short" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="AreaID" Type="short" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="DisplayType" Type="byte" />
	</Table>
	<Table Name="GroupFinderActivityGrp" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="GroupFinderCategory" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="GuildColorBackground" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Blue" Type="byte" />
		<Field Name="Green" Type="byte" />
	</Table>
	<Table Name="GuildColorBorder" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Blue" Type="byte" />
		<Field Name="Green" Type="byte" />
	</Table>
	<Table Name="GuildColorEmblem" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Blue" Type="byte" />
		<Field Name="Green" Type="byte" />
	</Table>
	<Table Name="GuildPerkSpells" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
	</Table>
	<Table Name="Heirloom" Build="27075">
		<Field Name="SourceText_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
		<Field Name="LegacyUpgradedItemID" Type="int" />
		<Field Name="StaticUpgradedItemID" Type="int" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="LegacyItemID" Type="int" />
		<Field Name="UpgradeItemID" Type="int" ArraySize="3" />
		<Field Name="UpgradeItemBonusListID" Type="short" ArraySize="3" />
	</Table>
	<Table Name="HelmetAnimScaling" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceID" Type="int" />
		<Field Name="Amount" Type="float" />
	</Table>
	<Table Name="HelmetGeosetVisData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HideGeoset" Type="int" ArraySize="9" />
	</Table>
	<Table Name="HighlightColor" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="byte" />
		<Field Name="StartColor" Type="int" />
		<Field Name="MidColor" Type="int" />
		<Field Name="EndColor" Type="int" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="HolidayDescriptions" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
	</Table>
	<Table Name="HolidayNames" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="Holidays" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Region" Type="short" />
		<Field Name="Looping" Type="byte" />
		<Field Name="HolidayNameID" Type="int" />
		<Field Name="HolidayDescriptionID" Type="int" />
		<Field Name="Priority" Type="byte" />
		<Field Name="CalendarFilterType" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Duration" Type="short" ArraySize="10" />
		<Field Name="Date" Type="int" ArraySize="16" />
		<Field Name="CalendarFlags" Type="byte" ArraySize="10" />
		<Field Name="TextureFileDataID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="Hotfixes" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Tablename" Type="string" />
		<Field Name="Object_ID" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="ImportPriceArmor" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClothModifier" Type="float" />
		<Field Name="LeatherModifier" Type="float" />
		<Field Name="ChainModifier" Type="float" />
		<Field Name="PlateModifier" Type="float" />
	</Table>
	<Table Name="ImportPriceQuality" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="ImportPriceShield" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="ImportPriceWeapon" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="InvasionClientData" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="IconLocation" Type="float" ArraySize="2" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WorldStateID" Type="int" />
		<Field Name="UiTextureAtlasMemberID" Type="int" />
		<Field Name="ScenarioID" Type="int" />
		<Field Name="WorldQuestID" Type="int" />
		<Field Name="WorldStateValue" Type="int" />
		<Field Name="InvasionEnabledWorldStateID" Type="int" />
	</Table>
	<Table Name="Item" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="SubclassID" Type="byte" />
		<Field Name="Material" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="SheatheType" Type="byte" />
		<Field Name="Sound_Override_SubclassID" Type="byte" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="ItemGroupSoundsID" Type="byte" />
	</Table>
	<Table Name="ItemAppearance" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayType" Type="byte" />
		<Field Name="ItemDisplayInfoID" Type="int" />
		<Field Name="DefaultIconFileDataID" Type="int" />
		<Field Name="UiOrder" Type="int" />
	</Table>
	<Table Name="ItemAppearanceXUiCamera" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemAppearanceID" Type="short" />
		<Field Name="UiCameraID" Type="short" />
	</Table>
	<Table Name="ItemArmorQuality" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Qualitymod" Type="float" ArraySize="7" />
	</Table>
	<Table Name="ItemArmorShield" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="short" />
	</Table>
	<Table Name="ItemArmorTotal" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="Cloth" Type="float" />
		<Field Name="Leather" Type="float" />
		<Field Name="Mail" Type="float" />
		<Field Name="Plate" Type="float" />
	</Table>
	<Table Name="ItemBagFamily" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="ItemBonus" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="int" ArraySize="3" />
		<Field Name="ParentItemBonusListID" Type="short" />
		<Field Name="Type" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="ItemBonusListLevelDelta" Build="27075">
		<Field Name="ItemLevelDelta" Type="short" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ItemBonusTreeNode" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemContext" Type="byte" />
		<Field Name="ChildItemBonusTreeID" Type="short" />
		<Field Name="ChildItemBonusListID" Type="short" />
		<Field Name="ChildItemLevelSelectorID" Type="short" />
	</Table>
	<Table Name="ItemChildEquipment" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChildItemID" Type="int" />
		<Field Name="ChildItemEquipSlot" Type="byte" />
	</Table>
	<Table Name="ItemClass" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClassName_Lang" Type="string" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="PriceModifier" Type="float" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ItemContextPickerEntry" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemCreationContext" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PVal" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="ItemCurrencyCost" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
	</Table>
	<Table Name="ItemDamageAmmo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="Quality" Type="float" ArraySize="7" />
	</Table>
	<Table Name="ItemDamageOneHand" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="Quality" Type="float" ArraySize="7" />
	</Table>
	<Table Name="ItemDamageOneHandCaster" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="Quality" Type="float" ArraySize="7" />
	</Table>
	<Table Name="ItemDamageTwoHand" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="Quality" Type="float" ArraySize="7" />
	</Table>
	<Table Name="ItemDamageTwoHandCaster" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="Quality" Type="float" ArraySize="7" />
	</Table>
	<Table Name="ItemDisenchantLoot" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Subclass" Type="byte" />
		<Field Name="Quality" Type="byte" />
		<Field Name="MinLevel" Type="short" />
		<Field Name="MaxLevel" Type="short" />
		<Field Name="SkillRequired" Type="short" />
		<Field Name="ExpansionID" Type="byte" />
	</Table>
	<Table Name="ItemDisplayInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelType1" Type="int" />
		<Field Name="ItemVisual" Type="int" />
		<Field Name="ParticleColorID" Type="int" />
		<Field Name="ItemRangedDisplayInfoID" Type="int" />
		<Field Name="OverrideSwooshSoundKitID" Type="int" />
		<Field Name="SheatheTransformMatrixID" Type="int" />
		<Field Name="StateSpellVisualKitID" Type="int" />
		<Field Name="SheathedSpellVisualKitID" Type="int" />
		<Field Name="UnsheathedSpellVisualKitID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="ModelResourcesID" Type="int" ArraySize="2" />
		<Field Name="ModelMaterialResourcesID" Type="int" ArraySize="2" />
		<Field Name="GeosetGroup" Type="int" ArraySize="4" />
		<Field Name="AttachmentGeosetGroup" Type="int" ArraySize="4" />
		<Field Name="HelmetGeosetVis" Type="int" ArraySize="2" />
	</Table>
	<Table Name="ItemDisplayInfoMaterialRes" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ComponentSection" Type="byte" />
		<Field Name="MaterialResourcesID" Type="int" />
	</Table>
	<Table Name="ItemDisplayXUiCamera" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemDisplayInfoID" Type="int" />
		<Field Name="UiCameraID" Type="short" />
	</Table>
	<Table Name="ItemEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LegacySlotIndex" Type="byte" />
		<Field Name="TriggerType" Type="byte" />
		<Field Name="Charges" Type="short" />
		<Field Name="CoolDownMSec" Type="int" />
		<Field Name="CategoryCoolDownMSec" Type="int" />
		<Field Name="SpellCategoryID" Type="short" />
		<Field Name="SpellID" Type="int" />
		<Field Name="ChrSpecializationID" Type="short" />
	</Table>
	<Table Name="ItemExtendedCost" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RequiredArenaRating" Type="short" />
		<Field Name="ArenaBracket" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MinFactionID" Type="byte" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="RequiredAchievement" Type="byte" />
		<Field Name="ItemID" Type="int" ArraySize="5" />
		<Field Name="ItemCount" Type="short" ArraySize="5" />
		<Field Name="CurrencyID" Type="short" ArraySize="5" />
		<Field Name="CurrencyCount" Type="int" ArraySize="5" />
	</Table>
	<Table Name="ItemGroupSounds" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Sound" Type="int" ArraySize="4" />
	</Table>
	<Table Name="ItemLevelSelector" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinItemLevel" Type="short" />
		<Field Name="ItemLevelSelectorQualitySetID" Type="short" />
	</Table>
	<Table Name="ItemLevelSelectorQuality" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QualityItemBonusListID" Type="int" />
		<Field Name="Quality" Type="byte" />
	</Table>
	<Table Name="ItemLevelSelectorQualitySet" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IlvlRare" Type="short" />
		<Field Name="IlvlEpic" Type="short" />
	</Table>
	<Table Name="ItemLimitCategory" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Quantity" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ItemLimitCategoryCondition" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AddQuantity" Type="byte" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="ItemModifiedAppearance" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
		<Field Name="ItemAppearanceModifierID" Type="byte" />
		<Field Name="ItemAppearanceID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="TransmogSourceTypeEnum" Type="byte" />
	</Table>
	<Table Name="ItemModifiedAppearanceExtra" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="UnequippedIconFileDataID" Type="int" />
		<Field Name="SheatheType" Type="byte" />
		<Field Name="DisplayWeaponSubclassID" Type="byte" />
		<Field Name="DisplayInventoryType" Type="byte" />
	</Table>
	<Table Name="ItemNameDescription" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Color" Type="int" />
	</Table>
	<Table Name="ItemPetFood" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="ItemPriceBase" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="Armor" Type="float" />
		<Field Name="Weapon" Type="float" />
	</Table>
	<Table Name="ItemRandomProperties" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Enchantment" Type="short" ArraySize="5" />
	</Table>
	<Table Name="ItemRandomSuffix" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Enchantment" Type="short" ArraySize="5" />
		<Field Name="AllocationPct" Type="short" ArraySize="5" />
	</Table>
	<Table Name="ItemRangedDisplayInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CastSpellVisualID" Type="int" />
		<Field Name="AutoAttackSpellVisualID" Type="int" />
		<Field Name="QuiverFileDataID" Type="int" />
		<Field Name="MissileSpellVisualEffectNameID" Type="int" />
	</Table>
	<Table Name="ItemSearchName" Build="27075">
		<Field Name="AllowableRace" Type="long" />
		<Field Name="Display_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OverallQualityID" Type="byte" />
		<Field Name="ExpansionID" Type="byte" />
		<Field Name="MinFactionID" Type="short" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="AllowableClass" Type="int" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="RequiredSkill" Type="short" />
		<Field Name="RequiredSkillRank" Type="short" />
		<Field Name="RequiredAbility" Type="int" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="Flags" Type="int" ArraySize="3" />
	</Table>
	<Table Name="ItemSet" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="SetFlags" Type="int" />
		<Field Name="RequiredSkill" Type="int" />
		<Field Name="RequiredSkillRank" Type="short" />
		<Field Name="ItemID" Type="int" ArraySize="17" />
	</Table>
	<Table Name="ItemSetSpell" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrSpecID" Type="short" />
		<Field Name="SpellID" Type="int" />
		<Field Name="Threshold" Type="byte" />
	</Table>
	<Table Name="ItemSparse" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AllowableRace" Type="long" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Display3_Lang" Type="string" />
		<Field Name="Display2_Lang" Type="string" />
		<Field Name="Display1_Lang" Type="string" />
		<Field Name="Display_Lang" Type="string" />
		<Field Name="DmgVariance" Type="float" />
		<Field Name="DurationInInventory" Type="int" />
		<Field Name="QualityModifier" Type="float" />
		<Field Name="BagFamily" Type="int" />
		<Field Name="ItemRange" Type="float" />
		<Field Name="StatPercentageOfSocket" Type="float" ArraySize="10" />
		<Field Name="StatPercentEditor" Type="int" ArraySize="10" />
		<Field Name="Stackable" Type="int" />
		<Field Name="MaxCount" Type="int" />
		<Field Name="RequiredAbility" Type="int" />
		<Field Name="SellPrice" Type="int" />
		<Field Name="BuyPrice" Type="int" />
		<Field Name="VendorStackCount" Type="int" />
		<Field Name="PriceVariance" Type="float" />
		<Field Name="PriceRandomValue" Type="float" />
		<Field Name="Flags" Type="int" ArraySize="4" />
		<Field Name="OppositeFactionItemID" Type="int" />
		<Field Name="ItemNameDescriptionID" Type="short" />
		<Field Name="RequiredTransmogHoliday" Type="short" />
		<Field Name="RequiredHoliday" Type="short" />
		<Field Name="LimitCategory" Type="short" />
		<Field Name="Gem_Properties" Type="short" />
		<Field Name="Socket_Match_Enchantment_ID" Type="short" />
		<Field Name="TotemCategoryID" Type="short" />
		<Field Name="InstanceBound" Type="short" />
		<Field Name="ZoneBound" Type="short" />
		<Field Name="ItemSet" Type="short" />
		<Field Name="ItemRandomSuffixGroupID" Type="short" />
		<Field Name="RandomSelect" Type="short" />
		<Field Name="LockID" Type="short" />
		<Field Name="StartQuestID" Type="short" />
		<Field Name="PageID" Type="short" />
		<Field Name="ItemDelay" Type="short" />
		<Field Name="ScalingStatDistributionID" Type="short" />
		<Field Name="MinFactionID" Type="short" />
		<Field Name="RequiredSkillRank" Type="short" />
		<Field Name="RequiredSkill" Type="short" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="AllowableClass" Type="short" />
		<Field Name="ExpansionID" Type="byte" />
		<Field Name="ArtifactID" Type="byte" />
		<Field Name="SpellWeight" Type="byte" />
		<Field Name="SpellWeightCategory" Type="byte" />
		<Field Name="SocketType" Type="byte" ArraySize="3" />
		<Field Name="SheatheType" Type="byte" />
		<Field Name="Material" Type="byte" />
		<Field Name="PageMaterialID" Type="byte" />
		<Field Name="LanguageID" Type="byte" />
		<Field Name="Bonding" Type="byte" />
		<Field Name="Damage_DamageType" Type="byte" />
		<Field Name="StatModifier_BonusStat" Type="byte" ArraySize="10" />
		<Field Name="ContainerSlots" Type="byte" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="RequiredPVPMedal" Type="byte" />
		<Field Name="RequiredPVPRank" Type="byte" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="OverallQualityID" Type="byte" />
	</Table>
	<Table Name="ItemSpec" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
		<Field Name="ItemType" Type="byte" />
		<Field Name="PrimaryStat" Type="byte" />
		<Field Name="SecondaryStat" Type="byte" />
		<Field Name="SpecializationID" Type="short" />
	</Table>
	<Table Name="ItemSpecOverride" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpecID" Type="short" />
	</Table>
	<Table Name="ItemSubClass" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayName_Lang" Type="string" />
		<Field Name="VerboseName_Lang" Type="string" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="SubClassID" Type="byte" />
		<Field Name="AuctionHouseSortOrder" Type="byte" />
		<Field Name="PrerequisiteProficiency" Type="byte" />
		<Field Name="Flags" Type="short" />
		<Field Name="DisplayFlags" Type="byte" />
		<Field Name="WeaponSwingSize" Type="byte" />
		<Field Name="PostrequisiteProficiency" Type="byte" />
	</Table>
	<Table Name="ItemSubClassMask" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="Mask" Type="int" />
	</Table>
	<Table Name="ItemUpgrade" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemUpgradePathID" Type="byte" />
		<Field Name="ItemLevelIncrement" Type="byte" />
		<Field Name="PrerequisiteID" Type="short" />
		<Field Name="CurrencyType" Type="short" />
		<Field Name="CurrencyAmount" Type="int" />
	</Table>
	<Table Name="ItemVisuals" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelFileID" Type="int" ArraySize="5" />
	</Table>
	<Table Name="ItemXBonusTree" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemBonusTreeID" Type="short" />
	</Table>
	<Table Name="JournalEncounter" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="float" ArraySize="2" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="byte" />
	</Table>
	<Table Name="JournalEncounterCreature" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="int" IsIndex="true" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="int" />
	</Table>
	<Table Name="JournalEncounterItem" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
	</Table>
	<Table Name="JournalEncounterSection" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="short" />
		<Field Name="Field_15" Type="short" />
		<Field Name="Field_16" Type="byte" />
	</Table>
	<Table Name="JournalEncounterXDifficulty" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyID" Type="byte" />
	</Table>
	<Table Name="JournalEncounterXMapLoc" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" ArraySize="2" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="byte" />
	</Table>
	<Table Name="JournalInstance" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="int" IsIndex="true" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="short" />
	</Table>
	<Table Name="JournalItemXDifficulty" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyID" Type="byte" />
	</Table>
	<Table Name="JournalSectionXDifficulty" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyID" Type="byte" />
	</Table>
	<Table Name="JournalTier" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="JournalTierXInstance" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="JournalTierID" Type="short" />
		<Field Name="JournalInstanceID" Type="short" />
	</Table>
	<Table Name="KeystoneAffix" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FiledataID" Type="int" />
	</Table>
	<Table Name="Languages" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="LanguageWords" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Word" Type="string" />
		<Field Name="LanguageID" Type="byte" />
	</Table>
	<Table Name="LfgDungeonsGroupingMap" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Random_LfgDungeonsID" Type="short" />
		<Field Name="Group_ID" Type="byte" />
	</Table>
	<Table Name="Light" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GameCoords" Type="float" ArraySize="3" />
		<Field Name="GameFalloffStart" Type="float" />
		<Field Name="GameFalloffEnd" Type="float" />
		<Field Name="ContinentID" Type="short" />
		<Field Name="LightParamsID" Type="short" ArraySize="8" />
	</Table>
	<Table Name="LightData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="int" />
		<Field Name="Field_15" Type="int" />
		<Field Name="Field_16" Type="int" />
		<Field Name="Field_17" Type="int" />
		<Field Name="Field_18" Type="int" />
		<Field Name="Field_19" Type="int" />
		<Field Name="Field_20" Type="int" />
		<Field Name="Field_21" Type="int" />
		<Field Name="Field_22" Type="float" />
		<Field Name="Field_23" Type="float" />
		<Field Name="Field_24" Type="float" />
		<Field Name="Field_25" Type="float" />
		<Field Name="Field_26" Type="float" />
		<Field Name="Field_27" Type="float" />
		<Field Name="Field_28" Type="float" />
		<Field Name="Field_29" Type="float" />
		<Field Name="Field_30" Type="int" />
		<Field Name="Field_31" Type="int" />
		<Field Name="Field_32" Type="int" />
		<Field Name="Field_33" Type="int" />
		<Field Name="Field_34" Type="float" />
		<Field Name="Field_35" Type="int" />
		<Field Name="Field_36" Type="float" />
		<Field Name="Field_37" Type="int" />
	</Table>
	<Table Name="Lightning" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" ArraySize="2" />
		<Field Name="Field_3" Type="int" ArraySize="3" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="float" />
		<Field Name="Field_13" Type="float" />
		<Field Name="Field_14" Type="int" />
		<Field Name="Field_15" Type="float" />
		<Field Name="Field_16" Type="float" />
		<Field Name="Field_17" Type="float" />
		<Field Name="Field_18" Type="float" />
		<Field Name="Field_19" Type="float" />
		<Field Name="Field_20" Type="float" />
		<Field Name="Field_21" Type="float" />
		<Field Name="Field_22" Type="float" />
		<Field Name="Field_23" Type="int" />
		<Field Name="Field_24" Type="int" />
		<Field Name="Field_25" Type="float" />
		<Field Name="Field_26" Type="float" />
		<Field Name="Field_27" Type="float" />
		<Field Name="Field_28" Type="float" />
		<Field Name="Field_29" Type="float" />
		<Field Name="Field_30" Type="float" />
		<Field Name="Field_31" Type="float" />
		<Field Name="Field_32" Type="float" />
		<Field Name="Field_33" Type="float" />
		<Field Name="Field_34" Type="float" />
		<Field Name="Field_35" Type="float" />
	</Table>
	<Table Name="LightParams" Build="27075">
		<Field Name="Field_1" Type="float" ArraySize="3" />
		<Field Name="Field_2" Type="int" IsIndex="true" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="int" />
	</Table>
	<Table Name="LightSkybox" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
	</Table>
	<Table Name="LiquidMaterial" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="LVF" Type="byte" />
	</Table>
	<Table Name="LiquidObject" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FlowDirection" Type="float" />
		<Field Name="FlowSpeed" Type="float" />
		<Field Name="LiquidTypeID" Type="short" />
		<Field Name="Fishable" Type="byte" />
		<Field Name="Reflection" Type="byte" />
	</Table>
	<Table Name="LiquidType" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" ArraySize="6" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="short" />
		<Field Name="Field_13" Type="float" />
		<Field Name="Field_14" Type="byte" />
		<Field Name="Field_15" Type="byte" />
		<Field Name="Field_16" Type="byte" />
		<Field Name="Field_17" Type="int" />
		<Field Name="Field_18" Type="byte" ArraySize="6" />
		<Field Name="Field_19" Type="int" ArraySize="2" />
		<Field Name="Field_20" Type="float" ArraySize="18" />
		<Field Name="Field_21" Type="int" ArraySize="4" />
		<Field Name="Field_22" Type="float" ArraySize="4" />
	</Table>
	<Table Name="LoadingScreens" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="NarrowScreenFileDataID" Type="int" />
		<Field Name="WideScreenFileDataID" Type="int" />
		<Field Name="WideScreen169FileDataID" Type="int" />
	</Table>
	<Table Name="LoadingScreenTaxiSplines" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="float" ArraySize="10" />
		<Field Name="Field_6" Type="float" ArraySize="10" />
	</Table>
	<Table Name="Locale" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
	</Table>
	<Table Name="Location" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="Rot" Type="float" ArraySize="3" />
	</Table>
	<Table Name="Lock" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Index" Type="int" ArraySize="8" />
		<Field Name="Skill" Type="short" ArraySize="8" />
		<Field Name="Type" Type="byte" ArraySize="8" />
		<Field Name="Action" Type="byte" ArraySize="8" />
	</Table>
	<Table Name="LockType" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ResourceName_Lang" Type="string" />
		<Field Name="Verb_Lang" Type="string" />
		<Field Name="CursorName" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="LookAtController" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="short" />
		<Field Name="Field_10" Type="short" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="byte" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="int" />
		<Field Name="Field_15" Type="byte" />
		<Field Name="Field_16" Type="byte" />
		<Field Name="Field_17" Type="float" />
		<Field Name="Field_18" Type="float" />
		<Field Name="Field_19" Type="byte" />
	</Table>
	<Table Name="MailTemplate" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Body_Lang" Type="string" />
	</Table>
	<Table Name="ManagedWorldState" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="int" ArraySize="4" />
	</Table>
	<Table Name="ManagedWorldStateBuff" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="ManagedWorldStateInput" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ManagedWorldStateID" Type="int" />
		<Field Name="QuestID" Type="int" />
		<Field Name="ValidInputConditionID" Type="int" />
	</Table>
	<Table Name="ManifestInterfaceActionIcon" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ManifestInterfaceData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FilePath" Type="string" />
		<Field Name="FileName" Type="string" />
	</Table>
	<Table Name="ManifestInterfaceItemIcon" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ManifestInterfaceTOCData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FilePath" Type="string" />
	</Table>
	<Table Name="ManifestMP3" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Map" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Directory" Type="string" />
		<Field Name="MapName_Lang" Type="string" />
		<Field Name="MapDescription0_Lang" Type="string" />
		<Field Name="MapDescription1_Lang" Type="string" />
		<Field Name="PvpShortDescription_Lang" Type="string" />
		<Field Name="PvpLongDescription_Lang" Type="string" />
		<Field Name="Corpse" Type="float" ArraySize="2" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="short" />
		<Field Name="Field_13" Type="short" />
		<Field Name="Field_14" Type="short" />
		<Field Name="Field_15" Type="short" />
		<Field Name="Field_16" Type="short" />
		<Field Name="Field_17" Type="byte" />
		<Field Name="Field_18" Type="float" />
		<Field Name="Field_19" Type="short" />
		<Field Name="Field_20" Type="byte" />
		<Field Name="Field_21" Type="short" />
		<Field Name="Field_22" Type="int" />
		<Field Name="Flags" Type="int" ArraySize="2" />
	</Table>
	<Table Name="MapCelestialBody" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CelestialBodyID" Type="short" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="MapChallengeMode" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="int" IsIndex="true" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="short" ArraySize="3" />
	</Table>
	<Table Name="MapDifficulty" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="byte" />
	</Table>
	<Table Name="MapDifficultyXCondition" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FailureDescription_Lang" Type="string" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="MapLoadingScreen" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Min" Type="float" ArraySize="2" />
		<Field Name="Max" Type="float" ArraySize="2" />
		<Field Name="LoadingScreenID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="MarketingPromotionsXLocale" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
	</Table>
	<Table Name="Material" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="FoleySoundID" Type="int" />
		<Field Name="SheatheSoundID" Type="int" />
		<Field Name="UnsheatheSoundID" Type="int" />
	</Table>
	<Table Name="MinorTalent" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="MissileTargeting" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" />
		<Field Name="Field_3" Type="float" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="float" ArraySize="2" />
		<Field Name="Field_13" Type="int" ArraySize="2" />
	</Table>
	<Table Name="ModelAnimCloakDampening" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AnimationDataID" Type="int" />
		<Field Name="CloakDampeningID" Type="int" />
	</Table>
	<Table Name="ModelFileData" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="ModelRibbonQuality" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RibbonQualityID" Type="byte" />
	</Table>
	<Table Name="ModifierTree" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="byte" />
	</Table>
	<Table Name="Mount" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="int" IsIndex="true" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="int" />
	</Table>
	<Table Name="MountCapability" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="short" />
	</Table>
	<Table Name="MountTypeXCapability" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MountTypeID" Type="short" />
		<Field Name="MountCapabilityID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="MountXDisplay" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureDisplayInfoID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="Movie" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
	</Table>
	<Table Name="MovieFileData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Resolution" Type="short" />
	</Table>
	<Table Name="MovieVariation" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="OverlayFileDataID" Type="int" />
	</Table>
	<Table Name="MultiStateProperties" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" ArraySize="3" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="int" />
	</Table>
	<Table Name="MultiTransitionProperties" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
	</Table>
	<Table Name="MythicPlusSeasonRewardLevels" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="NameGen" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="Sex" Type="byte" />
	</Table>
	<Table Name="NamesProfanity" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Language" Type="byte" />
	</Table>
	<Table Name="NamesReserved" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="NamesReservedLocale" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="LocaleMask" Type="byte" />
	</Table>
	<Table Name="NPCModelItemSlotDisplayInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemDisplayInfoID" Type="int" />
		<Field Name="ItemSlot" Type="byte" />
	</Table>
	<Table Name="NPCSounds" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundID" Type="int" ArraySize="4" />
	</Table>
	<Table Name="NumTalentsAtLevel" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="NumTalents" Type="int" />
		<Field Name="NumTalentsDeathKnight" Type="int" />
		<Field Name="NumTalentsDemonHunter" Type="int" />
	</Table>
	<Table Name="ObjectEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" ArraySize="3" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="int" />
	</Table>
	<Table Name="ObjectEffectModifier" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Param" Type="float" ArraySize="4" />
		<Field Name="InputType" Type="byte" />
		<Field Name="MapType" Type="byte" />
		<Field Name="OutputType" Type="byte" />
	</Table>
	<Table Name="ObjectEffectPackageElem" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ObjectEffectPackageID" Type="short" />
		<Field Name="ObjectEffectGroupID" Type="short" />
		<Field Name="StateType" Type="short" />
	</Table>
	<Table Name="Occluder" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="byte" />
	</Table>
	<Table Name="OccluderLocation" Build="27075">
		<Field Name="Field_1" Type="float" ArraySize="3" />
		<Field Name="Field_2" Type="float" ArraySize="3" />
		<Field Name="Field_3" Type="int" IsIndex="true" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="OccluderNode" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="OutlineEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PassiveHighlightColorID" Type="int" />
		<Field Name="HighlightColorID" Type="int" />
		<Field Name="Priority" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="Range" Type="float" />
		<Field Name="UnitConditionID" Type="int" ArraySize="2" />
	</Table>
	<Table Name="OverrideSpellData" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Spells" Type="int" ArraySize="10" />
		<Field Name="PlayerActionbarFileDataID" Type="int" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="PageTextMaterial" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="PaperDollItemFrame" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
	</Table>
	<Table Name="ParagonReputation" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="ParticleColor" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Start" Type="int" ArraySize="3" />
		<Field Name="MID" Type="int" ArraySize="3" />
		<Field Name="End" Type="int" ArraySize="3" />
	</Table>
	<Table Name="ParticulateSound" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
	</Table>
	<Table Name="Path" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="byte" />
		<Field Name="SplineType" Type="byte" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
		<Field Name="Alpha" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="PathNode" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="PathNodeProperty" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PathID" Type="short" />
		<Field Name="Sequence" Type="short" />
		<Field Name="PropertyIndex" Type="byte" />
		<Field Name="Value" Type="int" />
	</Table>
	<Table Name="PathProperty" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="Phase" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="short" />
	</Table>
	<Table Name="PhaseShiftZoneSounds" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="short" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="short" />
		<Field Name="Field_14" Type="byte" />
	</Table>
	<Table Name="PhaseXPhaseGroup" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PhaseID" Type="short" />
	</Table>
	<Table Name="PlayerCondition" Build="27075">
		<Field Name="Field_1" Type="long" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="int" IsIndex="true" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="short" />
		<Field Name="Field_12" Type="byte" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="byte" />
		<Field Name="Field_15" Type="byte" />
		<Field Name="Field_16" Type="int" />
		<Field Name="Field_17" Type="int" />
		<Field Name="Field_18" Type="int" />
		<Field Name="Field_19" Type="int" />
		<Field Name="Field_20" Type="int" />
		<Field Name="Field_21" Type="byte" />
		<Field Name="Field_22" Type="int" />
		<Field Name="Field_23" Type="short" />
		<Field Name="Field_24" Type="byte" />
		<Field Name="Field_25" Type="byte" />
		<Field Name="Field_26" Type="byte" />
		<Field Name="Field_27" Type="int" />
		<Field Name="Field_28" Type="byte" />
		<Field Name="Field_29" Type="byte" />
		<Field Name="Field_30" Type="int" />
		<Field Name="Field_31" Type="int" />
		<Field Name="Field_32" Type="int" />
		<Field Name="Field_33" Type="short" />
		<Field Name="Field_34" Type="int" />
		<Field Name="Field_35" Type="byte" />
		<Field Name="Field_36" Type="byte" />
		<Field Name="Field_37" Type="int" />
		<Field Name="Field_38" Type="int" />
		<Field Name="Field_39" Type="short" />
		<Field Name="Field_40" Type="short" />
		<Field Name="Field_41" Type="byte" />
		<Field Name="Field_42" Type="short" />
		<Field Name="Field_43" Type="int" />
		<Field Name="Field_44" Type="byte" />
		<Field Name="Field_45" Type="byte" />
		<Field Name="Field_46" Type="byte" />
		<Field Name="Field_47" Type="int" />
		<Field Name="Field_48" Type="byte" />
		<Field Name="Field_49" Type="byte" />
		<Field Name="Field_50" Type="byte" />
		<Field Name="Field_51" Type="int" />
		<Field Name="Field_52" Type="byte" />
		<Field Name="Field_53" Type="byte" />
		<Field Name="Field_54" Type="byte" />
		<Field Name="Field_55" Type="byte" />
		<Field Name="Field_56" Type="byte" />
		<Field Name="Field_57" Type="byte" />
		<Field Name="Field_58" Type="short" ArraySize="4" />
		<Field Name="Field_59" Type="short" ArraySize="4" />
		<Field Name="Field_60" Type="short" ArraySize="4" />
		<Field Name="Field_61" Type="int" ArraySize="3" />
		<Field Name="Field_62" Type="byte" ArraySize="3" />
		<Field Name="Field_63" Type="short" ArraySize="4" />
		<Field Name="Field_64" Type="short" ArraySize="4" />
		<Field Name="Field_65" Type="short" ArraySize="4" />
		<Field Name="Field_66" Type="int" ArraySize="4" />
		<Field Name="Field_67" Type="int" ArraySize="4" />
		<Field Name="Field_68" Type="int" ArraySize="4" />
		<Field Name="Field_69" Type="short" ArraySize="2" />
		<Field Name="Field_70" Type="int" ArraySize="2" />
		<Field Name="Field_71" Type="int" ArraySize="4" />
		<Field Name="Field_72" Type="byte" ArraySize="4" />
		<Field Name="Field_73" Type="short" ArraySize="4" />
		<Field Name="Field_74" Type="short" ArraySize="4" />
		<Field Name="Field_75" Type="byte" ArraySize="4" />
		<Field Name="Field_76" Type="byte" ArraySize="4" />
		<Field Name="Field_77" Type="int" ArraySize="4" />
		<Field Name="Field_78" Type="int" ArraySize="4" />
		<Field Name="Field_79" Type="int" ArraySize="4" />
		<Field Name="Field_80" Type="int" ArraySize="6" />
		<Field Name="Field_81" Type="int" ArraySize="2" />
	</Table>
	<Table Name="Positioner" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="byte" />
	</Table>
	<Table Name="PositionerState" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="byte" />
	</Table>
	<Table Name="PositionerStateEntry" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" />
		<Field Name="Field_3" Type="float" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="byte" />
	</Table>
	<Table Name="PowerDisplay" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GlobalStringBaseTag" Type="string" />
		<Field Name="ActualType" Type="byte" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="PowerType" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="short" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="float" />
		<Field Name="Field_13" Type="short" />
	</Table>
	<Table Name="PrestigeLevelInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="int" />
	</Table>
	<Table Name="PVPBracketTypes" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BracketID" Type="byte" />
		<Field Name="WeeklyQuestID" Type="int" ArraySize="4" />
	</Table>
	<Table Name="PvpScalingEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="float" />
	</Table>
	<Table Name="PvpScalingEffectType" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="PvpTalent" Build="27075">
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpecID" Type="int" />
		<Field Name="SpellID" Type="int" />
		<Field Name="OverridesSpellID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="ActionBarSpellID" Type="int" />
		<Field Name="PvpTalentCategoryID" Type="int" />
		<Field Name="LevelRequired" Type="int" />
	</Table>
	<Table Name="PvpTalentCategory" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TalentSlotMask" Type="byte" />
	</Table>
	<Table Name="PvpTalentSlotUnlock" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Slot" Type="byte" />
		<Field Name="LevelRequired" Type="int" />
		<Field Name="DeathKnightLevelRequired" Type="int" />
		<Field Name="DemonHunterLevelRequired" Type="int" />
	</Table>
	<Table Name="PvpTier" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="int" />
	</Table>
	<Table Name="QuestFactionReward" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="short" ArraySize="10" />
	</Table>
	<Table Name="QuestFeedbackEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="MinimapAtlasMemberID" Type="short" />
		<Field Name="AttachPoint" Type="byte" />
		<Field Name="PassiveHighlightColorType" Type="byte" />
		<Field Name="Priority" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="QuestInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="short" />
	</Table>
	<Table Name="QuestLine" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="QuestLineXQuest" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QuestLineID" Type="int" />
		<Field Name="QuestID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="QuestMoneyReward" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="int" ArraySize="10" />
	</Table>
	<Table Name="QuestObjective" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="byte" />
	</Table>
	<Table Name="QuestPackageItem" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="byte" />
	</Table>
	<Table Name="QuestPOIBlob" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapID" Type="short" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
	</Table>
	<Table Name="QuestPOIPoint" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="X" Type="short" />
		<Field Name="Y" Type="short" />
	</Table>
	<Table Name="QuestSort" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SortName_Lang" Type="string" />
		<Field Name="UiOrderIndex" Type="byte" />
	</Table>
	<Table Name="QuestV2" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UniqueBitFlag" Type="short" />
	</Table>
	<Table Name="QuestV2CliTask" Build="27075">
		<Field Name="Field_1" Type="long" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="int" IsIndex="true" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="int" />
		<Field Name="Field_15" Type="int" />
		<Field Name="Field_16" Type="int" />
		<Field Name="Field_17" Type="int" />
		<Field Name="Field_18" Type="int" />
		<Field Name="Field_19" Type="int" />
		<Field Name="Field_20" Type="int" />
		<Field Name="Field_21" Type="short" />
		<Field Name="Field_22" Type="int" />
		<Field Name="Field_23" Type="int" />
		<Field Name="Field_24" Type="int" ArraySize="3" />
	</Table>
	<Table Name="QuestXGroupActivity" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QuestID" Type="int" />
		<Field Name="GroupFinderActivityID" Type="int" />
	</Table>
	<Table Name="QuestXP" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="short" ArraySize="10" />
	</Table>
	<Table Name="RandPropPoints" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DamageReplaceStat" Type="int" />
		<Field Name="Epic" Type="int" ArraySize="5" />
		<Field Name="Superior" Type="int" ArraySize="5" />
		<Field Name="Good" Type="int" ArraySize="5" />
	</Table>
	<Table Name="RelicSlotTierRequirement" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="RelicTalent" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
	</Table>
	<Table Name="ResearchBranch" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
	</Table>
	<Table Name="ResearchField" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="int" IsIndex="true" />
		<Field Name="Field_3" Type="byte" />
	</Table>
	<Table Name="ResearchProject" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="int" IsIndex="true" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
	</Table>
	<Table Name="ResearchSite" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
	</Table>
	<Table Name="Resistances" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="FizzleSoundID" Type="int" />
	</Table>
	<Table Name="RewardPack" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="int" />
	</Table>
	<Table Name="RewardPackXCurrencyType" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CurrencyTypeID" Type="int" />
		<Field Name="Quantity" Type="int" />
	</Table>
	<Table Name="RewardPackXItem" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
		<Field Name="ItemQuantity" Type="int" />
	</Table>
	<Table Name="RibbonQuality" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="float" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="int" />
	</Table>
	<Table Name="RulesetItemUpgrade" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
		<Field Name="ItemUpgradeID" Type="short" />
	</Table>
	<Table Name="ScalingStatDistribution" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerLevelToItemLevelCurveID" Type="short" />
		<Field Name="Minlevel" Type="int" />
		<Field Name="Maxlevel" Type="int" />
	</Table>
	<Table Name="Scenario" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="int" />
	</Table>
	<Table Name="ScenarioEventEntry" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TriggerType" Type="byte" />
		<Field Name="TriggerAsset" Type="int" />
	</Table>
	<Table Name="ScenarioStep" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="short" />
	</Table>
	<Table Name="SceneScript" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FirstSceneScriptID" Type="short" />
		<Field Name="NextSceneScriptID" Type="short" />
	</Table>
	<Table Name="SceneScriptGlobalText" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Script" Type="string" />
	</Table>
	<Table Name="SceneScriptPackage" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SceneScriptPackageMember" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SceneScriptPackageID" Type="short" />
		<Field Name="SceneScriptID" Type="short" />
		<Field Name="ChildSceneScriptPackageID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="SceneScriptText" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Script" Type="string" />
	</Table>
	<Table Name="ScheduledInterval" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="RepeatType" Type="int" />
		<Field Name="DurationSecs" Type="int" />
		<Field Name="OffsetSecs" Type="int" />
		<Field Name="DateAlignmentType" Type="int" />
	</Table>
	<Table Name="ScheduledWorldState" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ScheduledWorldStateGroupID" Type="int" />
		<Field Name="WorldStateID" Type="int" />
		<Field Name="Value" Type="int" />
		<Field Name="DurationSecs" Type="int" />
		<Field Name="Weight" Type="int" />
		<Field Name="UniqueCategory" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="ScheduledWorldStateGroup" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="ScheduledIntervalID" Type="int" />
		<Field Name="SelectionType" Type="int" />
		<Field Name="SelectionCount" Type="int" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="ScheduledWorldStateXUniqCat" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ScheduledUniqueCategoryID" Type="int" />
	</Table>
	<Table Name="ScreenEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="int" ArraySize="4" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="short" />
		<Field Name="Field_12" Type="byte" />
		<Field Name="Field_13" Type="byte" />
	</Table>
	<Table Name="ScreenLocation" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SDReplacementModel" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SdFileDataID" Type="int" />
	</Table>
	<Table Name="SeamlessSite" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapID" Type="int" />
	</Table>
	<Table Name="ServerMessages" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text_Lang" Type="string" />
	</Table>
	<Table Name="ShadowyEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="float" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="int" />
	</Table>
	<Table Name="SiegeableProperties" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Health" Type="int" />
		<Field Name="DamageSpellVisualKitID" Type="int" />
		<Field Name="HealingSpellVisualKitID" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="SkillLine" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="string" />
		<Field Name="Field_5" Type="string" />
		<Field Name="Field_6" Type="int" IsIndex="true" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="short" />
		<Field Name="Field_13" Type="int" />
	</Table>
	<Table Name="SkillLineAbility" Build="27075">
		<Field Name="Field_1" Type="long" />
		<Field Name="Field_2" Type="int" IsIndex="true" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="short" />
		<Field Name="Field_10" Type="short" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="byte" />
		<Field Name="Field_13" Type="short" />
		<Field Name="Field_14" Type="short" />
		<Field Name="Field_15" Type="short" />
	</Table>
	<Table Name="SkillRaceClassInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="long" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="short" />
	</Table>
	<Table Name="SoundAmbience" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="SoundFilterID" Type="int" />
		<Field Name="FlavorSoundFilterID" Type="int" />
		<Field Name="AmbienceID" Type="int" ArraySize="2" />
		<Field Name="AmbienceStartID" Type="int" ArraySize="2" />
		<Field Name="AmbienceStopID" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SoundAmbienceFlavor" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundEntriesIDDay" Type="int" />
		<Field Name="SoundEntriesIDNight" Type="int" />
	</Table>
	<Table Name="SoundBus" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
	</Table>
	<Table Name="SoundBusOverride" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
	</Table>
	<Table Name="SoundEmitterPillPoints" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="SoundEmittersID" Type="short" />
	</Table>
	<Table Name="SoundEmitters" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="float" ArraySize="3" />
		<Field Name="Field_3" Type="float" ArraySize="3" />
		<Field Name="Field_4" Type="int" IsIndex="true" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="byte" />
	</Table>
	<Table Name="SoundEnvelope" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundKitID" Type="int" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="short" />
	</Table>
	<Table Name="SoundFilter" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SoundFilterElem" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Params" Type="float" ArraySize="9" />
		<Field Name="FilterType" Type="byte" />
	</Table>
	<Table Name="SoundKit" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="float" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="float" />
		<Field Name="Field_13" Type="byte" />
		<Field Name="Field_14" Type="float" />
		<Field Name="Field_15" Type="short" />
		<Field Name="Field_16" Type="byte" />
	</Table>
	<Table Name="SoundKitAdvanced" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="float" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="int" />
		<Field Name="Field_15" Type="byte" />
		<Field Name="Field_16" Type="float" />
		<Field Name="Field_17" Type="float" />
		<Field Name="Field_18" Type="float" />
		<Field Name="Field_19" Type="float" />
		<Field Name="Field_20" Type="float" />
		<Field Name="Field_21" Type="float" />
		<Field Name="Field_22" Type="float" />
		<Field Name="Field_23" Type="float" />
		<Field Name="Field_24" Type="float" />
		<Field Name="Field_25" Type="int" />
		<Field Name="Field_26" Type="int" />
		<Field Name="Field_27" Type="float" />
		<Field Name="Field_28" Type="float" />
		<Field Name="Field_29" Type="float" />
		<Field Name="Field_30" Type="byte" />
		<Field Name="Field_31" Type="short" />
		<Field Name="Field_32" Type="int" />
		<Field Name="Field_33" Type="int" />
		<Field Name="Field_34" Type="int" />
		<Field Name="Field_35" Type="byte" />
		<Field Name="Field_36" Type="byte" />
		<Field Name="Field_37" Type="int" />
		<Field Name="Field_38" Type="int" />
		<Field Name="Field_39" Type="int" />
		<Field Name="Field_40" Type="int" />
		<Field Name="Field_41" Type="int" />
		<Field Name="Field_42" Type="int" />
		<Field Name="Field_43" Type="float" />
	</Table>
	<Table Name="SoundKitChild" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundKitID" Type="int" />
		<Field Name="ParentSoundKitID" Type="int" />
	</Table>
	<Table Name="SoundKitEntry" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundKitID" Type="int" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="Frequency" Type="byte" />
		<Field Name="Volume" Type="float" />
	</Table>
	<Table Name="SoundKitFallback" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundKitID" Type="int" />
		<Field Name="FallbackSoundKitID" Type="int" />
	</Table>
	<Table Name="SoundKitName" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SoundOverride" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
	</Table>
	<Table Name="SoundProviderPreferences" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="short" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="short" />
		<Field Name="Field_13" Type="float" />
		<Field Name="Field_14" Type="float" />
		<Field Name="Field_15" Type="float" />
		<Field Name="Field_16" Type="byte" />
		<Field Name="Field_17" Type="float" />
		<Field Name="Field_18" Type="float" />
		<Field Name="Field_19" Type="float" />
		<Field Name="Field_20" Type="float" />
		<Field Name="Field_21" Type="float" />
		<Field Name="Field_22" Type="float" />
		<Field Name="Field_23" Type="float" />
		<Field Name="Field_24" Type="short" />
	</Table>
	<Table Name="SourceInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SourceText_Lang" Type="string" />
		<Field Name="PvpFaction" Type="byte" />
		<Field Name="SourceTypeEnum" Type="byte" />
	</Table>
	<Table Name="SpamMessages" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text" Type="string" />
	</Table>
	<Table Name="SpecializationSpells" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="int" IsIndex="true" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="byte" />
	</Table>
	<Table Name="SpecializationSpellsDisplay" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="short" />
		<Field Name="Field_3" Type="int" ArraySize="6" />
	</Table>
	<Table Name="SpecSetMember" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrSpecializationID" Type="int" />
	</Table>
	<Table Name="Spell" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="NameSubtext_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="AuraDescription_Lang" Type="string" />
	</Table>
	<Table Name="SpellActionBarPref" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="PreferredActionBarMask" Type="short" />
	</Table>
	<Table Name="SpellActivationOverlay" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" ArraySize="4" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="byte" />
	</Table>
	<Table Name="SpellAuraOptions" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SpellAuraRestrictions" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="int" />
	</Table>
	<Table Name="SpellAuraVisibility" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
	</Table>
	<Table Name="SpellAuraVisXChrSpec" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrSpecializationID" Type="short" />
	</Table>
	<Table Name="SpellCastingRequirements" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="short" />
	</Table>
	<Table Name="SpellCastTimes" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="SpellCategories" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="short" />
	</Table>
	<Table Name="SpellCategory" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
	</Table>
	<Table Name="SpellChainEffects" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" />
		<Field Name="Field_3" Type="float" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="byte" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="float" />
		<Field Name="Field_13" Type="float" />
		<Field Name="Field_14" Type="float" />
		<Field Name="Field_15" Type="float" />
		<Field Name="Field_16" Type="float" />
		<Field Name="Field_17" Type="float" />
		<Field Name="Field_18" Type="float" />
		<Field Name="Field_19" Type="float" />
		<Field Name="Field_20" Type="float" />
		<Field Name="Field_21" Type="float" />
		<Field Name="Field_22" Type="float" />
		<Field Name="Field_23" Type="float" />
		<Field Name="Field_24" Type="float" />
		<Field Name="Field_25" Type="float" />
		<Field Name="Field_26" Type="float" />
		<Field Name="Field_27" Type="float" />
		<Field Name="Field_28" Type="float" />
		<Field Name="Field_29" Type="float" />
		<Field Name="Field_30" Type="float" />
		<Field Name="Field_31" Type="float" />
		<Field Name="Field_32" Type="float" />
		<Field Name="Field_33" Type="float" />
		<Field Name="Field_34" Type="float" />
		<Field Name="Field_35" Type="float" />
		<Field Name="Field_36" Type="float" />
		<Field Name="Field_37" Type="float" />
		<Field Name="Field_38" Type="byte" />
		<Field Name="Field_39" Type="byte" />
		<Field Name="Field_40" Type="byte" />
		<Field Name="Field_41" Type="byte" />
		<Field Name="Field_42" Type="byte" />
		<Field Name="Field_43" Type="byte" />
		<Field Name="Field_44" Type="float" />
		<Field Name="Field_45" Type="float" />
		<Field Name="Field_46" Type="float" />
		<Field Name="Field_47" Type="int" />
		<Field Name="Field_48" Type="float" />
		<Field Name="Field_49" Type="float" />
		<Field Name="Field_50" Type="short" />
		<Field Name="Field_51" Type="byte" />
		<Field Name="Field_52" Type="byte" />
		<Field Name="Field_53" Type="int" />
		<Field Name="Field_54" Type="float" />
		<Field Name="Field_55" Type="float" />
		<Field Name="Field_56" Type="short" ArraySize="11" />
		<Field Name="Field_57" Type="float" ArraySize="3" />
		<Field Name="Field_58" Type="float" ArraySize="3" />
		<Field Name="Field_59" Type="float" ArraySize="3" />
		<Field Name="Field_60" Type="float" ArraySize="3" />
		<Field Name="Field_61" Type="int" ArraySize="3" />
	</Table>
	<Table Name="SpellClassOptions" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="int" ArraySize="4" />
	</Table>
	<Table Name="SpellCooldowns" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
	</Table>
	<Table Name="SpellDescriptionVariables" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Variables" Type="string" />
	</Table>
	<Table Name="SpellDispelType" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="InternalName" Type="string" />
		<Field Name="ImmunityPossible" Type="byte" />
		<Field Name="Mask" Type="byte" />
	</Table>
	<Table Name="SpellDuration" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="SpellEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="float" />
		<Field Name="Field_15" Type="float" />
		<Field Name="Field_16" Type="float" />
		<Field Name="Field_17" Type="int" />
		<Field Name="Field_18" Type="float" />
		<Field Name="Field_19" Type="float" />
		<Field Name="Field_20" Type="float" />
		<Field Name="Field_21" Type="float" />
		<Field Name="Field_22" Type="float" />
		<Field Name="Field_23" Type="float" />
		<Field Name="Field_24" Type="float" />
		<Field Name="Field_25" Type="int" ArraySize="2" />
		<Field Name="Field_26" Type="int" ArraySize="2" />
		<Field Name="Field_27" Type="int" ArraySize="4" />
		<Field Name="Field_28" Type="short" ArraySize="2" />
	</Table>
	<Table Name="SpellEffectAutoDescription" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="byte" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="int" />
	</Table>
	<Table Name="SpellEffectEmission" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EmissionRate" Type="float" />
		<Field Name="ModelScale" Type="float" />
		<Field Name="AreaModelID" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellEquippedItems" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
	</Table>
	<Table Name="SpellFlyout" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="long" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="SpellIconFileID" Type="int" />
	</Table>
	<Table Name="SpellFlyoutItem" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="Slot" Type="byte" />
	</Table>
	<Table Name="SpellFocusObject" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="SpellInterrupts" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="InterruptFlags" Type="short" />
		<Field Name="AuraInterruptFlags" Type="int" ArraySize="2" />
		<Field Name="ChannelInterruptFlags" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SpellItemEnchantment" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="int" ArraySize="3" />
		<Field Name="Field_5" Type="float" ArraySize="3" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="short" ArraySize="3" />
		<Field Name="Field_10" Type="short" />
		<Field Name="Field_11" Type="short" />
		<Field Name="Field_12" Type="short" />
		<Field Name="Field_13" Type="short" />
		<Field Name="Field_14" Type="short" />
		<Field Name="Field_15" Type="byte" />
		<Field Name="Field_16" Type="byte" ArraySize="3" />
		<Field Name="Field_17" Type="byte" />
		<Field Name="Field_18" Type="byte" />
		<Field Name="Field_19" Type="byte" />
		<Field Name="Field_20" Type="byte" />
		<Field Name="Field_21" Type="byte" />
	</Table>
	<Table Name="SpellItemEnchantmentCondition" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" ArraySize="5" />
		<Field Name="Field_3" Type="int" ArraySize="5" />
		<Field Name="Field_4" Type="byte" ArraySize="5" />
		<Field Name="Field_5" Type="byte" ArraySize="5" />
		<Field Name="Field_6" Type="byte" ArraySize="5" />
		<Field Name="Field_7" Type="byte" ArraySize="5" />
	</Table>
	<Table Name="SpellKeyboundOverride" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="SpellLabel" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LabelID" Type="int" />
	</Table>
	<Table Name="SpellLearnSpell" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="LearnSpellID" Type="int" />
		<Field Name="OverridesSpellID" Type="int" />
	</Table>
	<Table Name="SpellLevels" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Field_6" Type="byte" />
	</Table>
	<Table Name="SpellMechanic" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StateName_Lang" Type="string" />
	</Table>
	<Table Name="SpellMisc" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="CastingTimeIndex" Type="short" />
		<Field Name="DurationIndex" Type="short" />
		<Field Name="RangeIndex" Type="short" />
		<Field Name="SchoolMask" Type="byte" />
		<Field Name="Speed" Type="float" />
		<Field Name="LaunchDelay" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="SpellIconFileDataID" Type="int" />
		<Field Name="ActiveIconFileDataID" Type="int" />
		<Field Name="Attributes" Type="int" ArraySize="14" />
	</Table>
	<Table Name="SpellMissile" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="float" />
		<Field Name="Field_13" Type="float" />
		<Field Name="Field_14" Type="float" />
		<Field Name="Field_15" Type="float" />
		<Field Name="Field_16" Type="float" />
	</Table>
	<Table Name="SpellMissileMotion" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="ScriptBody" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MissileCount" Type="byte" />
	</Table>
	<Table Name="SpellName" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="SpellPower" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="byte" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="int" />
	</Table>
	<Table Name="SpellPowerDifficulty" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
	</Table>
	<Table Name="SpellProceduralEffect" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="float" ArraySize="4" />
	</Table>
	<Table Name="SpellProcsPerMinute" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseProcRate" Type="float" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellProcsPerMinuteMod" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="short" />
		<Field Name="Field_4" Type="float" />
	</Table>
	<Table Name="SpellRadius" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Radius" Type="float" />
		<Field Name="RadiusPerLevel" Type="float" />
		<Field Name="RadiusMin" Type="float" />
		<Field Name="RadiusMax" Type="float" />
	</Table>
	<Table Name="SpellRange" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="string" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="float" ArraySize="2" />
		<Field Name="Field_6" Type="float" ArraySize="2" />
	</Table>
	<Table Name="SpellReagents" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="Reagent" Type="int" ArraySize="8" />
		<Field Name="ReagentCount" Type="short" ArraySize="8" />
	</Table>
	<Table Name="SpellReagentsCurrency" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="CurrencyTypesID" Type="short" />
		<Field Name="CurrencyCount" Type="short" />
	</Table>
	<Table Name="SpellScaling" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="short" />
	</Table>
	<Table Name="SpellShapeshift" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" ArraySize="2" />
		<Field Name="Field_5" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SpellShapeshiftForm" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="short" />
		<Field Name="Field_10" Type="int" ArraySize="4" />
		<Field Name="Field_11" Type="int" ArraySize="8" />
	</Table>
	<Table Name="SpellSpecialUnitEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellVisualEffectNameID" Type="short" />
		<Field Name="PositionerID" Type="int" />
	</Table>
	<Table Name="SpellTargetRestrictions" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="float" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="float" />
	</Table>
	<Table Name="SpellTotems" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="short" ArraySize="2" />
		<Field Name="Field_4" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SpellVisual" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MissileCastOffset" Type="float" ArraySize="3" />
		<Field Name="MissileImpactOffset" Type="float" ArraySize="3" />
		<Field Name="AnimEventSoundID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="MissileAttachment" Type="byte" />
		<Field Name="MissileDestinationAttachment" Type="byte" />
		<Field Name="MissileCastPositionerID" Type="int" />
		<Field Name="MissileImpactPositionerID" Type="int" />
		<Field Name="MissileTargetingKit" Type="int" />
		<Field Name="HostileSpellVisualID" Type="int" />
		<Field Name="CasterSpellVisualID" Type="int" />
		<Field Name="SpellVisualMissileSetID" Type="short" />
		<Field Name="DamageNumberDelay" Type="short" />
		<Field Name="LowViolenceSpellVisualID" Type="int" />
		<Field Name="RaidSpellVisualMissileSetID" Type="int" />
	</Table>
	<Table Name="SpellVisualAnim" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="short" />
	</Table>
	<Table Name="SpellVisualColorEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="short" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="short" />
		<Field Name="Field_10" Type="short" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="int" />
	</Table>
	<Table Name="SpellVisualEffectName" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelFileDataID" Type="int" />
		<Field Name="BaseMissileSpeed" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="MinAllowedScale" Type="float" />
		<Field Name="MaxAllowedScale" Type="float" />
		<Field Name="Alpha" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="TextureFileDataID" Type="int" />
		<Field Name="EffectRadius" Type="float" />
		<Field Name="Type" Type="int" />
		<Field Name="GenericID" Type="int" />
		<Field Name="RibbonQualityID" Type="int" />
		<Field Name="DissolveEffectID" Type="int" />
		<Field Name="ModelPosition" Type="int" />
	</Table>
	<Table Name="SpellVisualEvent" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StartEvent" Type="int" />
		<Field Name="EndEvent" Type="int" />
		<Field Name="StartMinOffsetMs" Type="int" />
		<Field Name="StartMaxOffsetMs" Type="int" />
		<Field Name="EndMinOffsetMs" Type="int" />
		<Field Name="EndMaxOffsetMs" Type="int" />
		<Field Name="TargetType" Type="int" />
		<Field Name="SpellVisualKitID" Type="int" />
	</Table>
	<Table Name="SpellVisualKit" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="FallbackPriority" Type="float" />
		<Field Name="FallbackSpellVisualKitID" Type="int" />
		<Field Name="DelayMin" Type="short" />
		<Field Name="DelayMax" Type="short" />
	</Table>
	<Table Name="SpellVisualKitAreaModel" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="float" />
		<Field Name="Field_6" Type="float" />
		<Field Name="Field_7" Type="float" />
	</Table>
	<Table Name="SpellVisualKitEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EffectType" Type="int" />
		<Field Name="Effect" Type="int" />
	</Table>
	<Table Name="SpellVisualKitModelAttach" Build="27075">
		<Field Name="Offset" Type="float" ArraySize="3" />
		<Field Name="OffsetVariation" Type="float" ArraySize="3" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellVisualEffectNameID" Type="short" />
		<Field Name="AttachmentID" Type="byte" />
		<Field Name="PositionerID" Type="short" />
		<Field Name="Yaw" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Roll" Type="float" />
		<Field Name="YawVariation" Type="float" />
		<Field Name="PitchVariation" Type="float" />
		<Field Name="RollVariation" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="ScaleVariation" Type="float" />
		<Field Name="StartAnimID" Type="short" />
		<Field Name="AnimID" Type="short" />
		<Field Name="EndAnimID" Type="short" />
		<Field Name="AnimKitID" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="LowDefModelAttachID" Type="int" />
		<Field Name="StartDelay" Type="float" />
	</Table>
	<Table Name="SpellVisualMissile" Build="27075">
		<Field Name="Field_1" Type="float" ArraySize="3" />
		<Field Name="Field_2" Type="float" ArraySize="3" />
		<Field Name="Field_3" Type="int" IsIndex="true" />
		<Field Name="Field_4" Type="short" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="short" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="int" />
		<Field Name="Field_12" Type="short" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="short" />
		<Field Name="Field_15" Type="int" />
	</Table>
	<Table Name="SpellXDescriptionVariables" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="SpellDescriptionVariablesID" Type="int" />
	</Table>
	<Table Name="SpellXSpellVisual" Build="27075">
		<Field Name="Field_1" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="float" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="short" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="short" />
		<Field Name="Field_12" Type="int" />
	</Table>
	<Table Name="Startup_Strings" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Message_Lang" Type="string" />
	</Table>
	<Table Name="StartupFiles" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="Locale" Type="int" />
		<Field Name="BytesRequired" Type="int" />
	</Table>
	<Table Name="Stationery" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SummonProperties" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
	</Table>
	<Table Name="TactKey" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Key" Type="byte" ArraySize="16" />
	</Table>
	<Table Name="TactKeyLookup" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TACTID" Type="byte" ArraySize="8" />
	</Table>
	<Table Name="Talent" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="int" />
		<Field Name="Field_9" Type="int" />
		<Field Name="Field_10" Type="byte" ArraySize="2" />
	</Table>
	<Table Name="TaxiNodes" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Field_2" Type="float" ArraySize="3" />
		<Field Name="Field_3" Type="float" ArraySize="2" />
		<Field Name="Field_4" Type="float" ArraySize="2" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ContinentID" Type="short" />
		<Field Name="Field_7" Type="short" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="int" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="int" />
		<Field Name="Field_13" Type="int" />
		<Field Name="Field_14" Type="int" ArraySize="2" />
	</Table>
	<Table Name="TaxiPath" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FromTaxiNode" Type="short" />
		<Field Name="ToTaxiNode" Type="short" />
		<Field Name="Cost" Type="int" />
	</Table>
	<Table Name="TaxiPathNode" Build="27075">
		<Field Name="Loc" Type="float" ArraySize="3" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PathID" Type="short" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="short" />
	</Table>
	<Table Name="TerrainMaterial" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Shader" Type="byte" />
		<Field Name="EnvMapDiffuseFileID" Type="int" />
		<Field Name="EnvMapSpecularFileID" Type="int" />
	</Table>
	<Table Name="TerrainType" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TerrainDesc" Type="string" />
		<Field Name="FootstepSprayRun" Type="short" />
		<Field Name="FootstepSprayWalk" Type="short" />
		<Field Name="SoundID" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="TerrainTypeSounds" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="TextureBlendSet" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" ArraySize="3" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="float" ArraySize="3" />
		<Field Name="Field_8" Type="float" ArraySize="3" />
		<Field Name="Field_9" Type="float" ArraySize="3" />
		<Field Name="Field_10" Type="float" ArraySize="3" />
		<Field Name="Field_11" Type="float" ArraySize="4" />
	</Table>
	<Table Name="TextureFileData" Build="27075">
		<Field Name="FileDataID" Type="int" IsIndex="true" />
		<Field Name="UsageType" Type="byte" />
		<Field Name="MaterialResourcesID" Type="int" />
	</Table>
	<Table Name="TotemCategory" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="Toy" Build="27075">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="int" IsIndex="true" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="byte" />
	</Table>
	<Table Name="TradeSkillCategory" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="HordeName_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ParenttradeskillcategoryID" Type="short" />
		<Field Name="SkilllineID" Type="short" />
		<Field Name="Orderindex" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="TradeSkillItem" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="RequiredLevel" Type="byte" />
	</Table>
	<Table Name="TransformMatrix" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="Yaw" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Roll" Type="float" />
		<Field Name="Scale" Type="float" />
	</Table>
	<Table Name="TransmogHoliday" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RequiredTransmogHoliday" Type="int" />
	</Table>
	<Table Name="TransmogSet" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="ParentTransmogSetID" Type="short" />
		<Field Name="ExpansionID" Type="byte" />
		<Field Name="UiOrder" Type="short" />
	</Table>
	<Table Name="TransmogSetGroup" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="TransmogSetItem" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TransmogSetID" Type="int" />
		<Field Name="ItemModifiedAppearanceID" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="TransportAnimation" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="SequenceID" Type="byte" />
		<Field Name="TimeIndex" Type="int" />
	</Table>
	<Table Name="TransportPhysics" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WaveAmp" Type="float" />
		<Field Name="WaveTimeScale" Type="float" />
		<Field Name="RollAmp" Type="float" />
		<Field Name="RollTimeScale" Type="float" />
		<Field Name="PitchAmp" Type="float" />
		<Field Name="PitchTimeScale" Type="float" />
		<Field Name="MaxBank" Type="float" />
		<Field Name="MaxBankTurnSpeed" Type="float" />
		<Field Name="SpeedDampThresh" Type="float" />
		<Field Name="SpeedDamp" Type="float" />
	</Table>
	<Table Name="TransportRotation" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Rot" Type="float" ArraySize="4" />
		<Field Name="TimeIndex" Type="int" />
	</Table>
	<Table Name="Trophy" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="TrophyTypeID" Type="byte" />
		<Field Name="GameObjectDisplayInfoID" Type="short" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="UiCamera" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="string" />
		<Field Name="Field_3" Type="float" ArraySize="3" />
		<Field Name="Field_4" Type="float" ArraySize="3" />
		<Field Name="Field_5" Type="float" ArraySize="3" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Field_8" Type="short" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="byte" />
	</Table>
	<Table Name="UiCameraType" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Width" Type="int" />
		<Field Name="Height" Type="int" />
	</Table>
	<Table Name="UiCamFbackTransmogChrRace" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrRaceID" Type="byte" />
		<Field Name="Gender" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="Variation" Type="byte" />
		<Field Name="UiCameraID" Type="short" />
	</Table>
	<Table Name="UiCamFbackTransmogWeapon" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemClass" Type="byte" />
		<Field Name="ItemSubclass" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="UiCameraID" Type="short" />
	</Table>
	<Table Name="UiCanvas" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Width" Type="short" />
		<Field Name="Height" Type="short" />
	</Table>
	<Table Name="UIExpansionDisplayInfo" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ExpansionLogo" Type="int" />
		<Field Name="ExpansionBanner" Type="int" />
		<Field Name="ExpansionLevel" Type="int" />
	</Table>
	<Table Name="UIExpansionDisplayInfoIcon" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FeatureDescription_Lang" Type="string" />
		<Field Name="ParentID" Type="int" />
		<Field Name="FeatureIcon" Type="int" />
	</Table>
	<Table Name="UiMap" Build="27075">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ParentUiMapID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="System" Type="int" />
		<Field Name="Type" Type="int" />
		<Field Name="LevelRangeMin" Type="int" />
		<Field Name="LevelRangeMax" Type="int" />
		<Field Name="BountySetID" Type="int" />
		<Field Name="BountyDisplayLocation" Type="int" />
		<Field Name="VisibilityPlayerConditionID" Type="int" />
		<Field Name="HelpTextPosition" Type="byte" />
		<Field Name="BkgAtlasID" Type="int" />
	</Table>
	<Table Name="UiMapArt" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HighlightFileDataID" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="UiMapArtStyleID" Type="int" />
	</Table>
	<Table Name="UiMapArtStyleLayer" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LayerIndex" Type="byte" />
		<Field Name="LayerWidth" Type="short" />
		<Field Name="LayerHeight" Type="short" />
		<Field Name="TileWidth" Type="short" />
		<Field Name="TileHeight" Type="short" />
		<Field Name="MinScale" Type="float" />
		<Field Name="MaxScale" Type="float" />
		<Field Name="AdditionalZoomSteps" Type="int" />
	</Table>
	<Table Name="UiMapArtTile" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RowIndex" Type="byte" />
		<Field Name="ColIndex" Type="byte" />
		<Field Name="LayerIndex" Type="byte" />
		<Field Name="FileDataID" Type="int" />
	</Table>
	<Table Name="UiMapAssignment" Build="27075">
		<Field Name="UiMin" Type="float" ArraySize="2" />
		<Field Name="UiMax" Type="float" ArraySize="2" />
		<Field Name="Region" Type="float" ArraySize="6" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiMapID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
		<Field Name="MapID" Type="int" />
		<Field Name="AreaID" Type="int" />
		<Field Name="WMODoodadPlacementID" Type="int" />
		<Field Name="WMOGroupID" Type="int" />
	</Table>
	<Table Name="UiMapFogOfWar" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiMapID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="UiMapFogOfWarVisID" Type="int" />
	</Table>
	<Table Name="UiMapFogOfWarVisualization" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BackgroundAtlasID" Type="int" />
		<Field Name="MaskAtlasID" Type="int" />
		<Field Name="MaskScalar" Type="float" />
	</Table>
	<Table Name="UiMapGroupMember" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="UiMapGroupID" Type="int" />
		<Field Name="UiMapID" Type="int" />
		<Field Name="FloorIndex" Type="int" />
		<Field Name="RelativeHeightIndex" Type="byte" />
	</Table>
	<Table Name="UiMapLink" Build="27075">
		<Field Name="UiMin" Type="float" ArraySize="2" />
		<Field Name="UiMax" Type="float" ArraySize="2" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ParentUiMapID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
		<Field Name="ChildUiMapID" Type="int" />
	</Table>
	<Table Name="UiMapXMapArt" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PhaseID" Type="int" />
		<Field Name="UiMapArtID" Type="int" />
	</Table>
	<Table Name="UiModelScene" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiSystemType" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="UiModelSceneActor" Build="27075">
		<Field Name="ScriptTag" Type="string" />
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UiModelSceneActorDisplayID" Type="int" />
		<Field Name="OrientationYaw" Type="float" />
		<Field Name="OrientationPitch" Type="float" />
		<Field Name="OrientationRoll" Type="float" />
		<Field Name="NormalizedScale" Type="float" />
	</Table>
	<Table Name="UiModelSceneActorDisplay" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AnimationID" Type="int" />
		<Field Name="SequenceVariation" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Alpha" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="AnimSpeed" Type="float" />
	</Table>
	<Table Name="UiModelSceneCamera" Build="27075">
		<Field Name="ScriptTag" Type="string" />
		<Field Name="Target" Type="float" ArraySize="3" />
		<Field Name="ZoomedTargetOffset" Type="float" ArraySize="3" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CameraType" Type="byte" />
		<Field Name="Yaw" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Roll" Type="float" />
		<Field Name="ZoomedYawOffset" Type="float" />
		<Field Name="ZoomedPitchOffset" Type="float" />
		<Field Name="ZoomedRollOffset" Type="float" />
		<Field Name="ZoomDistance" Type="float" />
		<Field Name="MinZoomDistance" Type="float" />
		<Field Name="MaxZoomDistance" Type="float" />
	</Table>
	<Table Name="UiPartyPose" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiWidgetSetID" Type="int" />
		<Field Name="UiModelSceneID" Type="int" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
	</Table>
	<Table Name="UiTextureAtlas" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="AtlasWidth" Type="short" />
		<Field Name="AtlasHeight" Type="short" />
		<Field Name="UiCanvasID" Type="byte" />
	</Table>
	<Table Name="UiTextureAtlasElement" Build="27075">
		<Field Name="Name" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="UiTextureAtlasMember" Build="27075">
		<Field Name="CommittedName" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiTextureAtlasID" Type="short" />
		<Field Name="CommittedLeft" Type="short" />
		<Field Name="CommittedRight" Type="short" />
		<Field Name="CommittedTop" Type="short" />
		<Field Name="CommittedBottom" Type="short" />
		<Field Name="UiTextureAtlasElementID" Type="short" />
		<Field Name="CommittedFlags" Type="byte" />
		<Field Name="UiCanvasID" Type="byte" />
	</Table>
	<Table Name="UiTextureKit" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="KitPrefix" Type="string" />
	</Table>
	<Table Name="UiWidget" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WidgetTag" Type="string" />
		<Field Name="ParentSetID" Type="short" />
		<Field Name="VisID" Type="int" />
		<Field Name="MapID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="UiWidgetConstantSource" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ReqID" Type="short" />
		<Field Name="Value" Type="int" />
	</Table>
	<Table Name="UiWidgetDataSource" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SourceID" Type="short" />
		<Field Name="SourceType" Type="byte" />
		<Field Name="ReqID" Type="short" />
	</Table>
	<Table Name="UiWidgetStringSource" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value_Lang" Type="string" />
		<Field Name="ReqID" Type="short" />
	</Table>
	<Table Name="UiWidgetVisualization" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VisType" Type="byte" />
		<Field Name="TextureKit" Type="int" />
		<Field Name="FrameTextureKit" Type="int" />
		<Field Name="Field_5" Type="short" />
	</Table>
	<Table Name="UnitBlood" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerCritBloodSpurtID" Type="int" />
		<Field Name="PlayerHitBloodSpurtID" Type="int" />
		<Field Name="DefaultBloodSpurtID" Type="int" />
		<Field Name="PlayerOmniCritBloodSpurtID" Type="int" />
		<Field Name="PlayerOmniHitBloodSpurtID" Type="int" />
		<Field Name="DefaultOmniBloodSpurtID" Type="int" />
	</Table>
	<Table Name="UnitBloodLevels" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Violencelevel" Type="byte" ArraySize="3" />
	</Table>
	<Table Name="UnitCondition" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Variable" Type="byte" ArraySize="8" />
		<Field Name="Op" Type="byte" ArraySize="8" />
		<Field Name="Value" Type="int" ArraySize="8" />
	</Table>
	<Table Name="UnitPowerBar" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Cost_Lang" Type="string" />
		<Field Name="OutOfError_Lang" Type="string" />
		<Field Name="ToolTip_Lang" Type="string" />
		<Field Name="MinPower" Type="int" />
		<Field Name="MaxPower" Type="int" />
		<Field Name="StartPower" Type="short" />
		<Field Name="CenterPower" Type="byte" />
		<Field Name="RegenerationPeace" Type="float" />
		<Field Name="RegenerationCombat" Type="float" />
		<Field Name="BarType" Type="byte" />
		<Field Name="Flags" Type="short" />
		<Field Name="StartInset" Type="float" />
		<Field Name="EndInset" Type="float" />
		<Field Name="FileDataID" Type="int" ArraySize="6" />
		<Field Name="Color" Type="int" ArraySize="6" />
	</Table>
	<Table Name="Vehicle" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="FlagsB" Type="byte" />
		<Field Name="TurnSpeed" Type="float" />
		<Field Name="PitchSpeed" Type="float" />
		<Field Name="PitchMin" Type="float" />
		<Field Name="PitchMax" Type="float" />
		<Field Name="MouseLookOffsetPitch" Type="float" />
		<Field Name="CameraFadeDistScalarMin" Type="float" />
		<Field Name="CameraFadeDistScalarMax" Type="float" />
		<Field Name="CameraPitchOffset" Type="float" />
		<Field Name="FacingLimitRight" Type="float" />
		<Field Name="FacingLimitLeft" Type="float" />
		<Field Name="CameraYawOffset" Type="float" />
		<Field Name="UiLocomotionType" Type="byte" />
		<Field Name="VehicleUIIndicatorID" Type="short" />
		<Field Name="MissileTargetingID" Type="int" />
		<Field Name="SeatID" Type="short" ArraySize="8" />
		<Field Name="PowerDisplayID" Type="short" ArraySize="3" />
	</Table>
	<Table Name="VehicleSeat" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="float" ArraySize="3" />
		<Field Name="Field_3" Type="float" ArraySize="3" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="byte" />
		<Field Name="Field_8" Type="float" />
		<Field Name="Field_9" Type="float" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="float" />
		<Field Name="Field_12" Type="float" />
		<Field Name="Field_13" Type="float" />
		<Field Name="Field_14" Type="float" />
		<Field Name="Field_15" Type="int" />
		<Field Name="Field_16" Type="int" />
		<Field Name="Field_17" Type="int" />
		<Field Name="Field_18" Type="int" />
		<Field Name="Field_19" Type="int" />
		<Field Name="Field_20" Type="int" />
		<Field Name="Field_21" Type="float" />
		<Field Name="Field_22" Type="float" />
		<Field Name="Field_23" Type="float" />
		<Field Name="Field_24" Type="float" />
		<Field Name="Field_25" Type="float" />
		<Field Name="Field_26" Type="float" />
		<Field Name="Field_27" Type="float" />
		<Field Name="Field_28" Type="int" />
		<Field Name="Field_29" Type="int" />
		<Field Name="Field_30" Type="int" />
		<Field Name="Field_31" Type="short" />
		<Field Name="Field_32" Type="byte" />
		<Field Name="Field_33" Type="short" />
		<Field Name="Field_34" Type="byte" />
		<Field Name="Field_35" Type="short" />
		<Field Name="Field_36" Type="byte" />
		<Field Name="Field_37" Type="byte" />
		<Field Name="Field_38" Type="float" />
		<Field Name="Field_39" Type="float" />
		<Field Name="Field_40" Type="float" />
		<Field Name="Field_41" Type="float" />
		<Field Name="Field_42" Type="float" />
		<Field Name="Field_43" Type="byte" />
		<Field Name="Field_44" Type="int" />
		<Field Name="Field_45" Type="int" />
		<Field Name="Field_46" Type="int" />
		<Field Name="Field_47" Type="float" />
		<Field Name="Field_48" Type="float" />
		<Field Name="Field_49" Type="float" />
		<Field Name="Field_50" Type="float" />
		<Field Name="Field_51" Type="float" />
		<Field Name="Field_52" Type="float" />
		<Field Name="Field_53" Type="float" />
		<Field Name="Field_54" Type="float" />
		<Field Name="Field_55" Type="float" />
		<Field Name="Field_56" Type="short" />
		<Field Name="Field_57" Type="short" />
		<Field Name="Field_58" Type="short" />
		<Field Name="Field_59" Type="short" />
		<Field Name="Field_60" Type="short" />
		<Field Name="Field_61" Type="short" />
		<Field Name="Field_62" Type="short" />
	</Table>
	<Table Name="VehicleUIIndicator" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BackgroundTextureFileID" Type="int" />
	</Table>
	<Table Name="VehicleUIIndSeat" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VirtualSeatIndex" Type="byte" />
		<Field Name="XPos" Type="float" />
		<Field Name="YPos" Type="float" />
	</Table>
	<Table Name="Vignette" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="VisibleTrackingQuestID" Type="int" />
		<Field Name="QuestFeedbackEffectID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="MaxHeight" Type="float" />
		<Field Name="MinHeight" Type="float" />
		<Field Name="Field_9" Type="byte" />
		<Field Name="Field_10" Type="int" />
	</Table>
	<Table Name="VirtualAttachment" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="PositionerID" Type="short" />
	</Table>
	<Table Name="VirtualAttachmentCustomization" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VirtualAttachmentID" Type="short" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="PositionerID" Type="short" />
	</Table>
	<Table Name="VocalUISounds" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VocalUIEnum" Type="byte" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="NormalSoundID" Type="int" ArraySize="2" />
	</Table>
	<Table Name="WbAccessControlList" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="URL" Type="string" />
		<Field Name="GrantFlags" Type="short" />
		<Field Name="RevokeFlags" Type="byte" />
		<Field Name="WowEditInternal" Type="byte" />
		<Field Name="RegionID" Type="byte" />
	</Table>
	<Table Name="WbCertWhitelist" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Domain" Type="string" />
		<Field Name="GrantAccess" Type="byte" />
		<Field Name="RevokeAccess" Type="byte" />
		<Field Name="WowEditInternal" Type="byte" />
	</Table>
	<Table Name="WeaponImpactSounds" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WeaponSubClassID" Type="byte" />
		<Field Name="ParrySoundType" Type="byte" />
		<Field Name="ImpactSource" Type="byte" />
		<Field Name="ImpactSoundID" Type="int" ArraySize="11" />
		<Field Name="CritImpactSoundID" Type="int" ArraySize="11" />
		<Field Name="PierceImpactSoundID" Type="int" ArraySize="11" />
		<Field Name="PierceCritImpactSoundID" Type="int" ArraySize="11" />
	</Table>
	<Table Name="WeaponSwingSounds2" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SwingType" Type="byte" />
		<Field Name="Crit" Type="byte" />
		<Field Name="SoundID" Type="int" />
	</Table>
	<Table Name="WeaponTrail" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="Roll" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Yaw" Type="float" />
		<Field Name="TextureFileDataID" Type="int" ArraySize="3" />
		<Field Name="TextureScrollRateU" Type="float" ArraySize="3" />
		<Field Name="TextureScrollRateV" Type="float" ArraySize="3" />
		<Field Name="TextureScaleU" Type="float" ArraySize="3" />
		<Field Name="TextureScaleV" Type="float" ArraySize="3" />
	</Table>
	<Table Name="WeaponTrailModelDef" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LowDefFileDataID" Type="int" />
		<Field Name="WeaponTrailID" Type="short" />
	</Table>
	<Table Name="WeaponTrailParam" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Hand" Type="byte" />
		<Field Name="Duration" Type="float" />
		<Field Name="FadeOutTime" Type="float" />
		<Field Name="EdgeLifeSpan" Type="float" />
		<Field Name="InitialDelay" Type="float" />
		<Field Name="SmoothSampleAngle" Type="float" />
		<Field Name="OverrideAttachTop" Type="byte" />
		<Field Name="OverrideAttachBot" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Weather" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="byte" />
		<Field Name="TransitionSkyBox" Type="float" />
		<Field Name="AmbienceID" Type="int" />
		<Field Name="SoundAmbienceID" Type="short" />
		<Field Name="Field_6" Type="byte" />
		<Field Name="Field_7" Type="int" />
		<Field Name="WindSettingsID" Type="byte" />
		<Field Name="Scale" Type="float" />
		<Field Name="Field_10" Type="float" />
		<Field Name="Field_11" Type="float" />
		<Field Name="FallModifier" Type="float" />
		<Field Name="RotationalSpeed" Type="float" />
		<Field Name="Field_14" Type="int" />
		<Field Name="Field_15" Type="float" />
		<Field Name="OverrideColor" Type="int" />
		<Field Name="Field_17" Type="float" />
		<Field Name="Field_18" Type="float" />
		<Field Name="Field_19" Type="float" />
		<Field Name="VolumeFlags" Type="int" />
		<Field Name="LightningID" Type="int" />
		<Field Name="Intensity" Type="float" ArraySize="2" />
		<Field Name="EffectColor" Type="float" ArraySize="3" />
	</Table>
	<Table Name="WeatherXParticulate" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
	</Table>
	<Table Name="WindSettings" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseDir" Type="float" ArraySize="3" />
		<Field Name="VarianceDir" Type="float" ArraySize="3" />
		<Field Name="MaxStepDir" Type="float" ArraySize="3" />
		<Field Name="BaseMag" Type="float" />
		<Field Name="VarianceMagOver" Type="float" />
		<Field Name="VarianceMagUnder" Type="float" />
		<Field Name="MaxStepMag" Type="float" />
		<Field Name="Frequency" Type="float" />
		<Field Name="Duration" Type="float" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="WMOAreaTable" Build="27075">
		<Field Name="AreaName_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WMOID" Type="short" />
		<Field Name="NameSetID" Type="byte" />
		<Field Name="WMOGroupID" Type="int" />
		<Field Name="SoundProviderPref" Type="byte" />
		<Field Name="SoundProviderPrefUnderwater" Type="byte" />
		<Field Name="AmbienceID" Type="short" />
		<Field Name="UwAmbience" Type="short" />
		<Field Name="ZoneMusic" Type="short" />
		<Field Name="UwZoneMusic" Type="int" />
		<Field Name="IntroSound" Type="short" />
		<Field Name="UwIntroSound" Type="short" />
		<Field Name="AreaTableID" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="WMOMinimapTexture" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GroupNum" Type="short" />
		<Field Name="BlockX" Type="byte" />
		<Field Name="BlockY" Type="byte" />
		<Field Name="FileDataID" Type="int" />
	</Table>
	<Table Name="World_PVP_Area" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Area_ID" Type="short" />
		<Field Name="Next_Time_Worldstate" Type="short" />
		<Field Name="Game_Time_Worldstate" Type="short" />
		<Field Name="Battle_Populate_Time" Type="short" />
		<Field Name="Min_Level" Type="byte" />
		<Field Name="Max_Level" Type="byte" />
		<Field Name="Map_ID" Type="short" />
	</Table>
	<Table Name="WorldBossLockout" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="TrackingQuestID" Type="short" />
	</Table>
	<Table Name="WorldChunkSounds" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapID" Type="short" />
		<Field Name="SoundOverrideID" Type="int" />
		<Field Name="ChunkX" Type="byte" />
		<Field Name="ChunkY" Type="byte" />
		<Field Name="SubchunkX" Type="byte" />
		<Field Name="SubchunkY" Type="byte" />
	</Table>
	<Table Name="WorldEffect" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QuestFeedbackEffectID" Type="int" />
		<Field Name="WhenToDisplay" Type="byte" />
		<Field Name="TargetType" Type="byte" />
		<Field Name="TargetAsset" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="CombatConditionID" Type="short" />
	</Table>
	<Table Name="WorldElapsedTimer" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Type" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="WorldMapOverlay" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiMapArtID" Type="int" />
		<Field Name="TextureWidth" Type="short" />
		<Field Name="TextureHeight" Type="short" />
		<Field Name="OffsetX" Type="int" />
		<Field Name="OffsetY" Type="int" />
		<Field Name="HitRectTop" Type="int" />
		<Field Name="HitRectBottom" Type="int" />
		<Field Name="HitRectLeft" Type="int" />
		<Field Name="HitRectRight" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="AreaID" Type="int" ArraySize="4" />
	</Table>
	<Table Name="WorldMapOverlayTile" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="byte" />
		<Field Name="Field_3" Type="byte" />
		<Field Name="Field_4" Type="byte" />
		<Field Name="Field_5" Type="int" />
	</Table>
	<Table Name="WorldStateExpression" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Expression" Type="string" />
	</Table>
	<Table Name="WorldStateUI" Build="27075">
		<Field Name="Icon" Type="string" />
		<Field Name="String_Lang" Type="string" />
		<Field Name="Tooltip_Lang" Type="string" />
		<Field Name="DynamicTooltip_Lang" Type="string" />
		<Field Name="ExtendedUI" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapID" Type="short" />
		<Field Name="AreaID" Type="short" />
		<Field Name="StateVariable" Type="short" />
		<Field Name="Type" Type="byte" />
		<Field Name="DynamicIconFileID" Type="int" />
		<Field Name="DynamicFlashIconFileID" Type="int" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="PhaseID" Type="short" />
		<Field Name="PhaseGroupID" Type="short" />
		<Field Name="ExtendedUIStateVariable" Type="short" ArraySize="3" />
	</Table>
	<Table Name="WorldStateZoneSounds" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WorldStateID" Type="short" />
		<Field Name="Field_3" Type="short" />
		<Field Name="AreaID" Type="short" />
		<Field Name="Field_5" Type="int" />
		<Field Name="ZoneIntroMusicID" Type="short" />
		<Field Name="Field_7" Type="short" />
		<Field Name="SoundAmbienceID" Type="short" />
		<Field Name="SoundProviderPreferencesID" Type="byte" />
	</Table>
	<Table Name="ZoneIntroMusicTable" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="SoundID" Type="int" />
		<Field Name="Priority" Type="byte" />
		<Field Name="MinDelayMinutes" Type="short" />
	</Table>
	<Table Name="ZoneLight" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="MapID" Type="short" />
		<Field Name="LightID" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ZoneLightPoint" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="2" />
		<Field Name="PointOrder" Type="byte" />
	</Table>
	<Table Name="ZoneMusic" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SetName" Type="string" />
		<Field Name="SilenceIntervalMin" Type="int" ArraySize="2" />
		<Field Name="SilenceIntervalMax" Type="int" ArraySize="2" />
		<Field Name="Sounds" Type="int" ArraySize="2" />
	</Table>
	<Table Name="ZoneStory" Build="27075">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerFactionGroupID" Type="byte" />
		<Field Name="DisplayAchievementID" Type="int" />
		<Field Name="DisplayUIMapID" Type="int" />
	</Table>
</Definition>
