<?xml version="1.0" encoding="UTF-8"?>
<Definition>
  <Table Name="CreatureCache" Build="12340">
    <Field Name="Entry" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Name2" Type="string" />
    <Field Name="Name3" Type="string" />
    <Field Name="Name4" Type="string" />
    <Field Name="SubName" Type="string" />
    <Field Name="IconName" Type="string" />
    <Field Name="Flags" Type="uint" />
    <Field Name="Type" Type="uint" />
    <Field Name="Family" Type="int" />
    <Field Name="Rank" Type="uint" />
    <Field Name="KillCredit1" Type="uint" />
    <Field Name="KillCredit2" Type="uint" />
    <Field Name="DisplayID1" Type="uint" />
    <Field Name="DisplayID2" Type="uint" />
    <Field Name="DisplayID3" Type="uint" />
    <Field Name="DisplayID4" Type="uint" />
    <Field Name="HealthModifier" Type="float" />
    <Field Name="PowerModifier" Type="float" />
    <Field Name="RacialLeader" Type="byte" />
    <Field Name="QuestItem1" Type="uint" />
    <Field Name="QuestItem2" Type="uint" />
    <Field Name="QuestItem3" Type="uint" />
    <Field Name="QuestItem4" Type="uint" />
    <Field Name="QuestItem5" Type="uint" />
    <Field Name="QuestItem6" Type="uint" />
    <Field Name="MovementId" Type="uint" />
    <Field Name="exp" Type="uint" />
  </Table>
  <Table Name="QuestCache" Build="12340">
    <Field Name="Entry" Type="int" IsIndex="true" />
    <Field Name="Method" Type="uint" />
    <Field Name="QuestLevel" Type="int" />
    <Field Name="MinLevel" Type="uint" />
    <Field Name="ZoneOrSort" Type="int" />
    <Field Name="Type" Type="uint" />
    <Field Name="SuggestedPlayers" Type="uint" />
    <Field Name="RepObjectiveFaction1" Type="uint" />
    <Field Name="RepObjectiveValue1" Type="int" />
    <Field Name="RepObjectiveFaction2" Type="uint" />
    <Field Name="RepObjectiveValue2" Type="int" />
    <Field Name="NextQuestInChain" Type="uint" />
    <Field Name="XPID" Type="int" />
    <Field Name="RewOrReqMoney" Type="int" />
    <Field Name="RewMoneyMaxLevel" Type="uint" /> 
    <Field Name="RewSpell" Type="uint" />
    <Field Name="RewSpellCast" Type="uint" />
    <Field Name="Honor1" Type="uint" />
    <Field Name="Honor2" Type="float" />
    <Field Name="SrcItemId" Type="uint" />
    <Field Name="QuestFlags" Type="uint" />
    <Field Name="QuestFlags2" Type="uint" />
    <Field Name="CharTitleId" Type="uint" />
    <Field Name="PlayersSlain" Type="int" />
    <Field Name="BonusTalents" Type="int" />
    <Field Name="BonusArenaPoints" Type="int" />
    <Field Name="RewardSkillLineID" Type="int" />
    <Field Name="RewardSkillPoints" Type="int" />
    <Field Name="RewardFactionMask" Type="int" />
    <Field Name="QuestGiverPortraitID" Type="int" />
    <Field Name="QuestTurnInPortraitID" Type="int" />
    <Field Name="RewItemId1" Type="uint" /> 
    <Field Name="RewItemCount1" Type="uint" />
    <Field Name="RewItemId2" Type="uint" />
    <Field Name="RewItemCount2" Type="uint" />
    <Field Name="RewItemId3" Type="uint" />
    <Field Name="RewItemCount3" Type="uint" />
    <Field Name="RewItemId4" Type="uint" />
    <Field Name="RewItemCount4" Type="uint" />
    <Field Name="RewChoiceItemId1" Type="uint" />
    <Field Name="RewChoiceItemCount1" Type="uint" />
    <Field Name="RewChoiceItemId2" Type="uint" />
    <Field Name="RewChoiceItemCount2" Type="uint" />
    <Field Name="RewChoiceItemId3" Type="uint" />
    <Field Name="RewChoiceItemCount3" Type="uint" />
    <Field Name="RewChoiceItemId4" Type="uint" />
    <Field Name="RewChoiceItemCount4" Type="uint" />
    <Field Name="RewChoiceItemId5" Type="uint" />
    <Field Name="RewChoiceItemCount5" Type="uint" />
    <Field Name="RewChoiceItemId6" Type="uint" />
    <Field Name="RewChoiceItemCount6" Type="uint" />
    <Field Name="RewFactionId1" Type="uint" />
    <Field Name="RewFactionId2" Type="uint" />
    <Field Name="RewFactionId3" Type="uint" />
    <Field Name="RewFactionId4" Type="uint" />
    <Field Name="RewFactionId5" Type="uint" />
    <Field Name="RewFactionVal1" Type="int" />
    <Field Name="RewFactionVal2" Type="int" />
    <Field Name="RewFactionVal3" Type="int" />
    <Field Name="RewFactionVal4" Type="int" />
    <Field Name="RewFactionVal5" Type="int" />
    <Field Name="RewFactionValOverride1" Type="uint" />
    <Field Name="RewFactionValOverride2" Type="uint" />
    <Field Name="RewFactionValOverride3" Type="uint" />
    <Field Name="RewFactionValOverride4" Type="uint" />
    <Field Name="RewFactionValOverride5" Type="uint" />
    <Field Name="PointMapId" Type="uint" />
    <Field Name="PointX" Type="float" />
    <Field Name="PointY" Type="float" />
    <Field Name="PointOption" Type="int" />
    <Field Name="Title" Type="string" />
    <Field Name="Objectives" Type="string" />
    <Field Name="Details" Type="string" />
    <Field Name="EndText" Type="string" />
    <Field Name="CompletionText" Type="string" />
    <Field Name="ReqCreatureOrGOId1" Type="int" />
    <Field Name="ReqCreatureOrGOCount1" Type="uint" />
    <Field Name="ReqSourceId1" Type="uint" />
    <Field Name="ReqSourceIdMaxCount1" Type="uint" />
    <Field Name="ReqCreatureOrGOId2" Type="int" />
    <Field Name="ReqCreatureOrGOCount2" Type="uint" />
    <Field Name="ReqSourceId2" Type="uint" />
    <Field Name="ReqSourceIdMaxCount2" Type="uint" />
    <Field Name="ReqCreatureOrGOId3" Type="int" />
    <Field Name="ReqCreatureOrGOCount3" Type="uint" />
    <Field Name="ReqSourceId3" Type="uint" />
    <Field Name="ReqSourceIdMaxCount3" Type="uint" />
    <Field Name="ReqCreatureOrGOId4" Type="int" />
    <Field Name="ReqCreatureOrGOCount4" Type="uint" />
    <Field Name="ReqSourceId4" Type="uint" />
    <Field Name="ReqSourceIdMaxCount4" Type="uint" />
    <Field Name="ReqItemId1" Type="uint" />
    <Field Name="ReqItemCount1" Type="uint" />
    <Field Name="ReqItemId2" Type="uint" />
    <Field Name="ReqItemCount2" Type="uint" />
    <Field Name="ReqItemId3" Type="uint" />
    <Field Name="ReqItemCount3" Type="uint" />
    <Field Name="ReqItemId4" Type="uint" />
    <Field Name="ReqItemCount4" Type="uint" />
    <Field Name="ReqItemId5" Type="uint" />
    <Field Name="ReqItemCount5" Type="uint" />
    <Field Name="ReqItemId6" Type="uint" />
    <Field Name="ReqItemCount6" Type="uint" />
    <Field Name="CriteriaSpellID" Type="uint" />
    <Field Name="ObjectiveText1" Type="string" />
    <Field Name="ObjectiveText2" Type="string" />
    <Field Name="ObjectiveText3" Type="string" />
    <Field Name="ObjectiveText4" Type="string" />
    <Field Name="RewCurrency1" Type="uint" />
    <Field Name="RewCurrencyCount1" Type="uint" />
    <Field Name="RewCurrency2" Type="uint" />
    <Field Name="RewCurrencyCount2" Type="uint" />
    <Field Name="RewCurrency3" Type="uint" />
    <Field Name="RewCurrencyCount3" Type="uint" />
    <Field Name="RewCurrency4" Type="uint" />
    <Field Name="RewCurrencyCount4" Type="uint" />
    <Field Name="ReqCurrency1" Type="uint" />
    <Field Name="ReqCurrencyCount1" Type="uint" />
    <Field Name="ReqCurrency2" Type="uint" />
    <Field Name="ReqCurrencyCount2" Type="uint" />
    <Field Name="ReqCurrency3" Type="uint" />
    <Field Name="ReqCurrencyCount3" Type="uint" />
    <Field Name="ReqCurrency4" Type="uint" />
    <Field Name="ReqCurrencyCount4" Type="uint" />
    <Field Name="npcframe_accept_text1" Type="string" />
    <Field Name="npcframe_accept_text2" Type="string" />
    <Field Name="npcframe_handin_text1" Type="string" />
    <Field Name="npcframe_handin_text2" Type="string" />
    <Field Name="SoundID1" Type="uint" />
    <Field Name="SoundID2" Type="uint" />
  </Table>
  <Table Name="NpcCache" Build="12340">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="prob0" Type="float" />
    <Field Name="text0_0" Type="string" />
    <Field Name="text0_1" Type="string" />
    <Field Name="lang0" Type="uint" />
    <Field Name="em0_0" Type="uint" />
    <Field Name="em0_1" Type="uint" />
    <Field Name="em0_2" Type="uint" />
    <Field Name="em0_3" Type="uint" />
    <Field Name="em0_4" Type="uint" />
    <Field Name="em0_5" Type="uint" />
    <Field Name="prob1" Type="float" />
    <Field Name="text1_0" Type="string" />
    <Field Name="text1_1" Type="string" />
    <Field Name="lang1" Type="uint" />
    <Field Name="em1_0" Type="uint" />
    <Field Name="em1_1" Type="uint" />
    <Field Name="em1_2" Type="uint" />
    <Field Name="em1_3" Type="uint" />
    <Field Name="em1_4" Type="uint" />
    <Field Name="em1_5" Type="uint" />
    <Field Name="prob2" Type="float" />
    <Field Name="text2_0" Type="string" />
    <Field Name="text2_1" Type="string" />
    <Field Name="lang2" Type="uint" />
    <Field Name="em2_0" Type="uint" />
    <Field Name="em2_1" Type="uint" />
    <Field Name="em2_2" Type="uint" />
    <Field Name="em2_3" Type="uint" />
    <Field Name="em2_4" Type="uint" />
    <Field Name="em2_5" Type="uint" />
    <Field Name="prob3" Type="float" />
    <Field Name="text3_0" Type="string" />
    <Field Name="text3_1" Type="string" />
    <Field Name="lang3" Type="uint" />
    <Field Name="em3_0" Type="uint" />
    <Field Name="em3_1" Type="uint" />
    <Field Name="em3_2" Type="uint" />
    <Field Name="em3_3" Type="uint" />
    <Field Name="em3_4" Type="uint" />
    <Field Name="em3_5" Type="uint" />
    <Field Name="prob4" Type="float" />
    <Field Name="text4_0" Type="string" />
    <Field Name="text4_1" Type="string" />
    <Field Name="lang4" Type="uint" />
    <Field Name="em4_0" Type="uint" />
    <Field Name="em4_1" Type="uint" />
    <Field Name="em4_2" Type="uint" />
    <Field Name="em4_3" Type="uint" />
    <Field Name="em4_4" Type="uint" />
    <Field Name="em4_5" Type="uint" />
    <Field Name="prob5" Type="float" />
    <Field Name="text5_0" Type="string" />
    <Field Name="text5_1" Type="string" />
    <Field Name="lang5" Type="uint" />
    <Field Name="em5_0" Type="uint" />
    <Field Name="em5_1" Type="uint" />
    <Field Name="em5_2" Type="uint" />
    <Field Name="em5_3" Type="uint" />
    <Field Name="em5_4" Type="uint" />
    <Field Name="em5_5" Type="uint" />
    <Field Name="prob6" Type="float" />
    <Field Name="text6_0" Type="string" />
    <Field Name="text6_1" Type="string" />
    <Field Name="lang6" Type="uint" />
    <Field Name="em6_0" Type="uint" />
    <Field Name="em6_1" Type="uint" />
    <Field Name="em6_2" Type="uint" />
    <Field Name="em6_3" Type="uint" />
    <Field Name="em6_4" Type="uint" />
    <Field Name="em6_5" Type="uint" />
    <Field Name="prob7" Type="float" />
    <Field Name="text7_0" Type="string" />
    <Field Name="text7_1" Type="string" />
    <Field Name="lang7" Type="uint" />
    <Field Name="em7_0" Type="uint" />
    <Field Name="em7_1" Type="uint" />
    <Field Name="em7_2" Type="uint" />
    <Field Name="em7_3" Type="uint" />
    <Field Name="em7_4" Type="uint" />
    <Field Name="em7_5" Type="uint" />
  </Table>
  <Table Name="ItemNameCache" Build="12340">
    <Field Name="Entry" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="InventoryType" Type="uint" />
  </Table>
  <Table Name="PageTextCache" Build="12340">
    <Field Name="Entry" Type="int" IsIndex="true" />
    <Field Name="Text" Type="string" />
    <Field Name="NextPage" Type="uint" />
  </Table>
  <Table Name="ItemTextCache" Build="12340">
    <Field Name="Id" Type="int" IsIndex="true" />
    <Field Name="Text" Type="string" />
  </Table>
  <Table Name="GameobjectCache" Build="12340">
    <Field Name="Entry" Type="int" IsIndex="true" />
    <Field Name="Type" Type="uint" />
    <Field Name="DisplayId" Type="uint" />
    <Field Name="Name" Type="string" />
    <Field Name="Name2" Type="string" />
    <Field Name="Name3" Type="string" />
    <Field Name="Name4" Type="string" />
    <Field Name="IconName" Type="string" />
    <Field Name="CastBarCaption" Type="string" />
    <Field Name="wdb_Unk1" Type="string" />
    <Field Name="data0" Type="uint" />
    <Field Name="data1" Type="uint" />
    <Field Name="data2" Type="uint" />
    <Field Name="data3" Type="uint" />
    <Field Name="data4" Type="uint" />
    <Field Name="data5" Type="uint" />
    <Field Name="data6" Type="uint" />
    <Field Name="data7" Type="uint" />
    <Field Name="data8" Type="uint" />
    <Field Name="data9" Type="uint" />
    <Field Name="data10" Type="uint" />
    <Field Name="data11" Type="uint" />
    <Field Name="data12" Type="uint" />
    <Field Name="data13" Type="uint" />
    <Field Name="data14" Type="uint" />
    <Field Name="data15" Type="uint" />
    <Field Name="data16" Type="uint" />
    <Field Name="data17" Type="uint" />
    <Field Name="data18" Type="uint" />
    <Field Name="data19" Type="uint" />
    <Field Name="data20" Type="uint" />
    <Field Name="data21" Type="uint" />
    <Field Name="data22" Type="uint" />
    <Field Name="data23" Type="uint" />
    <Field Name="Size" Type="float" />
    <Field Name="QuestItem1" Type="uint" />
    <Field Name="QuestItem2" Type="uint" />
    <Field Name="QuestItem3" Type="uint" />
    <Field Name="QuestItem4" Type="uint" />
    <Field Name="QuestItem5" Type="uint" />
    <Field Name="QuestItem6" Type="uint" />
    <Field Name="exp" Type="uint" />
  </Table>
  <Table Name="ItemCache" Build="12340">
    <Field Name="Entry" Type="int" IsIndex="true" />
    <Field Name="class" Type="uint" />
    <Field Name="subclass" Type="uint" />
    <Field Name="field4" Type="int" />
    <Field Name="Name1" Type="string" />
    <Field Name="displayid" Type="uint" />
    <Field Name="quality" Type="uint" />
    <Field Name="flags" Type="uint" />
    <Field Name="faction" Type="uint" />
    <Field Name="buyprice" Type="uint" />
    <Field Name="sellprice" Type="uint" />
    <Field Name="inventoryType" Type="uint" />
    <Field Name="allowableclass" Type="int" />
    <Field Name="allowablerace" Type="int" />
    <Field Name="itemlevel" Type="uint" />
    <Field Name="requiredlevel" Type="uint" />
    <Field Name="RequiredSkill" Type="uint" />
    <Field Name="RequiredSkillRank" Type="uint" />
    <Field Name="RequiredSpell" Type="uint" />
    <Field Name="RequiredPlayerRank1" Type="uint" />
    <Field Name="RequiredPlayerRank2" Type="uint" />
    <Field Name="RequiredFaction" Type="uint" />
    <Field Name="RequiredFactionStanding" Type="uint" />
    <Field Name="Unique" Type="uint" />
    <Field Name="maxcount" Type="int" />
    <Field Name="ContainerSlots" Type="uint" />
    <Field Name="itemstatscount" Type="int" />
    <Field Name="stat_type1" Type="int" />
    <Field Name="stat_value1" Type="int" />
	<Field Name="stat_type2" Type="int" />
    <Field Name="stat_value2" Type="int" />
	<Field Name="stat_type3" Type="int" />
    <Field Name="stat_value3" Type="int" />
	<Field Name="stat_type4" Type="int" />
    <Field Name="stat_value4" Type="int" />
	<Field Name="stat_type5" Type="int" />
    <Field Name="stat_value5" Type="int" />
	<Field Name="stat_type6" Type="int" />
    <Field Name="stat_value6" Type="int" />
	<Field Name="stat_type7" Type="int" />
    <Field Name="stat_value7" Type="int" />
	<Field Name="stat_type8" Type="int" />
    <Field Name="stat_value8" Type="int" />
	<Field Name="stat_type9" Type="int" />
    <Field Name="stat_value9" Type="int" />
	<Field Name="stat_type10" Type="int" />
    <Field Name="stat_value10" Type="int" />	
    <Field Name="ScaledStatsDistributionId" Type="int" />
    <Field Name="ScaledStatsDistributionFlags" Type="uint" />
    <Field Name="dmg_min1" Type="float" />
    <Field Name="dmg_max1" Type="float" />
    <Field Name="dmg_Type1" Type="uint" />
    <Field Name="dmg_min2" Type="float" />
    <Field Name="dmg_max2" Type="float" />
    <Field Name="dmg_Type2" Type="uint" />
    <Field Name="armor" Type="uint" />
    <Field Name="holy_res" Type="uint" />
    <Field Name="fire_res" Type="uint" />
    <Field Name="nature_res" Type="uint" />
    <Field Name="frost_res" Type="uint" />
    <Field Name="shadow_res" Type="uint" />
    <Field Name="arcane_res" Type="uint" />
    <Field Name="delay" Type="uint" />
    <Field Name="ammo_Type" Type="uint" />
    <Field Name="range" Type="float" />
    <Field Name="spellid_1" Type="int" />
    <Field Name="spelltrigger_1" Type="uint" />
    <Field Name="spellcharges_1" Type="int" />
    <Field Name="spellcooldown_1" Type="int" />
    <Field Name="spellcategory_1" Type="uint" />
    <Field Name="spellcategorycooldown_1" Type="int" />
    <Field Name="spellid_2" Type="int" />
    <Field Name="spelltrigger_2" Type="uint" />
    <Field Name="spellcharges_2" Type="int" />
    <Field Name="spellcooldown_2" Type="int" />
    <Field Name="spellcategory_2" Type="uint" />
    <Field Name="spellcategorycooldown_2" Type="int" />
    <Field Name="spellid_3" Type="int" />
    <Field Name="spelltrigger_3" Type="uint" />
    <Field Name="spellcharges_3" Type="int" />
    <Field Name="spellcooldown_3" Type="int" />
    <Field Name="spellcategory_3" Type="uint" />
    <Field Name="spellcategorycooldown_3" Type="int" />
    <Field Name="spellid_4" Type="int" />
    <Field Name="spelltrigger_4" Type="uint" />
    <Field Name="spellcharges_4" Type="int" />
    <Field Name="spellcooldown_4" Type="int" />
    <Field Name="spellcategory_4" Type="uint" />
    <Field Name="spellcategorycooldown_4" Type="int" />
    <Field Name="spellid_5" Type="int" />
    <Field Name="spelltrigger_5" Type="uint" />
    <Field Name="spellcharges_5" Type="int" />
    <Field Name="spellcooldown_5" Type="int" />
    <Field Name="spellcategory_5" Type="uint" />
    <Field Name="spellcategorycooldown_5" Type="int" />
    <Field Name="bonding" Type="uint" />
    <Field Name="description" Type="loc" />
    <Field Name="page_id" Type="uint" />
    <Field Name="page_language" Type="uint" />
    <Field Name="page_material" Type="uint" />
    <Field Name="quest_id" Type="uint" />
    <Field Name="lock_id" Type="uint" />
    <Field Name="lock_material" Type="int" />
    <Field Name="sheathID" Type="uint" />
    <Field Name="randomprop" Type="int" />
    <Field Name="randomsuffix" Type="uint" />
    <Field Name="block" Type="uint" />
    <Field Name="itemset" Type="uint" />
    <Field Name="MaxDurability" Type="uint" />
    <Field Name="ZoneNameID" Type="uint" />
    <Field Name="mapid" Type="int" />
    <Field Name="bagfamily" Type="int" />
    <Field Name="TotemCategory" Type="int" />
    <Field Name="socket_color_1" Type="int" />
    <Field Name="unk201_3" Type="int" />
    <Field Name="socket_color_2" Type="int" />
    <Field Name="unk201_5" Type="int" />
    <Field Name="socket_color_3" Type="int" />
    <Field Name="unk201_7" Type="int" />
    <Field Name="socket_bonus" Type="int" />
    <Field Name="GemProperties" Type="int" />
    <Field Name="ReqDisenchantSkill" Type="int" />
    <Field Name="ArmorDamageModifier" Type="float" />
    <Field Name="existingduration" Type="int" />
    <Field Name="ItemLimitCategoryId" Type="int" />
    <Field Name="HolidayId" Type="uint" />
  </Table>
</Definition>