<?xml version="1.0"?>
<Definition xmlns:xsi="http://www.w3.org/2001/XMLSchema-instanc" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
<Table Name="Achievement" Build="26806">
   <Field Name="Description" Type="string" />
   <Field Name="Title" Type="string" />
   <Field Name="Reward" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="InstanceId" Type="short" />
   <Field Name="Faction" Type="byte" />
   <Field Name="Supercedes" Type="ushort" />
   <Field Name="Category" Type="ushort" />
   <Field Name="MinimumCriteria" Type="byte" />
   <Field Name="Points" Type="byte" />
   <Field Name="Flags" Type="int" />
   <Field Name="UiOrder" Type="short" />
   <Field Name="IconFileId" Type="int" />
   <Field Name="CriteriaTree" Type="uint" />
   <Field Name="SharesCriteria" Type="short" />
</Table>
<Table Name="Achievement_Category" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Parent" Type="short" />
   <Field Name="UiOrder" Type="byte" />
</Table>
<Table Name="AdventureJournal" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ButtonText" Type="string" />
   <Field Name="RewardDescription" Type="string" />
   <Field Name="ContinueDescription" Type="string" />
   <Field Name="Type" Type="byte" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="Flags" Type="byte" />
   <Field Name="ButtonActionType" Type="byte" />
   <Field Name="TextureFileDataId" Type="int" />
   <Field Name="LfgDungeonId" Type="ushort" />
   <Field Name="QuestId" Type="ushort" />
   <Field Name="BattleMasterListId" Type="ushort" />
   <Field Name="PriorityMin" Type="byte" />
   <Field Name="PriorityMax" Type="byte" />
   <Field Name="ItemId" Type="int" />
   <Field Name="ItemQuantity" Type="uint" />
   <Field Name="CurrencyType" Type="ushort" />
   <Field Name="CurrencyQuantity" Type="byte" />
   <Field Name="WorldMapAreaId" Type="ushort" />
   <Field Name="BonusPlayerConditionId" Type="int" ArraySize="2" />
   <Field Name="BonusValue" Type="byte" ArraySize="2" />
</Table>
<Table Name="AdventureMapPOI" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Title" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="WorldPosition" Type="float" ArraySize="2" />
   <Field Name="Type" Type="byte" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="QuestId" Type="int" />
   <Field Name="LfgDungeonId" Type="uint" />
   <Field Name="RewardItemId" Type="int" />
   <Field Name="UiTextureAtlasMemberId" Type="uint" />
   <Field Name="UiTextureKitId" Type="uint" />
   <Field Name="MapId" Type="int" />
   <Field Name="AreaTableId" Type="uint" />
</Table>
<Table Name="AlliedRace" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RaceId" Type="int" />
   <Field Name="BannerColor" Type="uint" />
   <Field Name="CrestTextureId" Type="int" />
   <Field Name="ModelBackgroundTextureId" Type="int" />
   <Field Name="MaleCreatureDisplayId" Type="int" />
   <Field Name="FemaleCreatureDisplayId" Type="int" />
   <Field Name="UiUnlockAchievementId" Type="int" />
</Table>
<Table Name="AlliedRaceRacialAbility" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="AnimationData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BehaviorId" Type="ushort" />
   <Field Name="BehaviorTier" Type="byte" />
   <Field Name="Fallback" Type="int" />
   <Field Name="Flags" Type="uint" ArraySize="2" />
</Table>
<Table Name="AnimKit" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="OneShotDuration" Type="int" />
   <Field Name="OneShotStopAnimKitId" Type="ushort" />
   <Field Name="LowDefAnimKitId" Type="ushort" />
</Table>
<Table Name="AnimKitBoneSet" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="BoneDataId" Type="byte" />
   <Field Name="ParentAnimKitBoneSetId" Type="byte" />
   <Field Name="ExtraBoneCount" Type="byte" />
   <Field Name="AltAnimKitBoneSetId" Type="byte" />
</Table>
<Table Name="AnimKitBoneSetAlias" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BoneDataId" Type="byte" />
   <Field Name="AnimKitBoneSetId" Type="byte" />
</Table>
<Table Name="AnimKitConfig" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ConfigFlags" Type="uint" />
</Table>
<Table Name="AnimKitConfigBoneSet" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AnimKitBoneSetId" Type="byte" />
   <Field Name="AnimKitPriorityId" Type="ushort" />
</Table>
<Table Name="AnimKitPriority" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Priority" Type="byte" />
</Table>
<Table Name="AnimKitReplacement" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SrcAnimKitId" Type="ushort" />
   <Field Name="DstAnimKitId" Type="ushort" />
   <Field Name="Flags" Type="ushort" />
</Table>
<Table Name="AnimKitSegment" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ParentAnimKitId" Type="ushort" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="AnimId" Type="ushort" />
   <Field Name="AnimStartTime" Type="int" />
   <Field Name="AnimKitConfigId" Type="ushort" />
   <Field Name="StartCondition" Type="byte" />
   <Field Name="StartConditionParam" Type="byte" />
   <Field Name="StartConditionDelay" Type="int" />
   <Field Name="EndCondition" Type="byte" />
   <Field Name="EndConditionParam" Type="int" />
   <Field Name="EndConditionDelay" Type="int" />
   <Field Name="Speed" Type="float" />
   <Field Name="SegmentFlags" Type="ushort" />
   <Field Name="ForcedVariation" Type="byte" />
   <Field Name="OverrideConfigFlags" Type="int" />
   <Field Name="LoopToSegmentIndex" Type="byte" />
   <Field Name="BlendInTimeMs" Type="ushort" />
   <Field Name="BlendOutTimeMs" Type="ushort" />
</Table>
<Table Name="AnimReplacement" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SrcAnimId" Type="ushort" />
   <Field Name="DstAnimId" Type="ushort" />
   <Field Name="Flags" Type="ushort" />
</Table>
<Table Name="AnimReplacementSet" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ExecOrder" Type="byte" />
</Table>
<Table Name="AoiBox" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Field_01" Type="float" ArraySize="6" />
   <Field Name="Field_02" Type="int" />
   <Field Name="Field_03" Type="int" />
   <Field Name="Field_04" Type="int" />
</Table>
<Table Name="AreaConditionalData" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PlayerConditionId" Type="int" />
   <Field Name="Field_03" Type="int" />
</Table>
<Table Name="AreaFarClipOverride" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AreaId" Type="int" />
   <Field Name="MinFarClip" Type="float" />
   <Field Name="MinHorizonStart" Type="float" />
   <Field Name="Flags" Type="int" />
</Table>
<Table Name="AreaGroupMember" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AreaId" Type="ushort" />
</Table>
<Table Name="AreaPOI" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Pos" Type="float" ArraySize="3" />
   <Field Name="PortLocId" Type="int" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="UiTextureAtlasMemberId" Type="uint" />
   <Field Name="Flags" Type="int" />
   <Field Name="WmoGroupId" Type="int" />
   <Field Name="PoiDataType" Type="int" />
   <Field Name="PoiData" Type="int" />
   <Field Name="ContinentId" Type="ushort" />
   <Field Name="AreaId" Type="ushort" />
   <Field Name="WorldStateId" Type="ushort" />
   <Field Name="WidgetSetId" Type="ushort" />
   <Field Name="Importance" Type="byte" />
   <Field Name="Icon" Type="byte" />
</Table>
<Table Name="AreaPOIState" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Description" Type="string" />
   <Field Name="WorldStateValue" Type="byte" />
   <Field Name="IconEnumValue" Type="byte" />
   <Field Name="UiTextureAtlasMemberId" Type="uint" />
</Table>
<Table Name="AreaTable" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ZoneName" Type="string" />
   <Field Name="AreaName" Type="string" />
   <Field Name="ContinentId" Type="ushort" />
   <Field Name="ParentAreaId" Type="ushort" />
   <Field Name="AreaBit" Type="short" />
   <Field Name="SoundProviderPref" Type="byte" />
   <Field Name="SoundProviderPrefUnderwater" Type="byte" />
   <Field Name="AmbienceId" Type="ushort" />
   <Field Name="UwAmbience" Type="ushort" />
   <Field Name="ZoneMusic" Type="ushort" />
   <Field Name="UwZoneMusic" Type="ushort" />
   <Field Name="ExplorationLevel" Type="byte" />
   <Field Name="IntroSound" Type="ushort" />
   <Field Name="UwIntroSound" Type="uint" />
   <Field Name="FactionGroupMask" Type="byte" />
   <Field Name="AmbientMultiplier" Type="float" />
   <Field Name="MountFlags" Type="byte" />
   <Field Name="PvpCombatWorldStateId" Type="ushort" />
   <Field Name="WildBattlePetLevelMin" Type="byte" />
   <Field Name="WildBattlePetLevelMax" Type="byte" />
   <Field Name="WindSettingsId" Type="byte" />
   <Field Name="Flags" Type="int" ArraySize="2" />
   <Field Name="LiquidTypeId" Type="ushort" ArraySize="4" />
</Table>
<Table Name="AreaTrigger" Build="26806">
   <Field Name="Pos" Type="float" ArraySize="3" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ContinentId" Type="short" />
   <Field Name="PhaseUseFlags" Type="byte" />
   <Field Name="PhaseId" Type="ushort" />
   <Field Name="PhaseGroupId" Type="ushort" />
   <Field Name="Radius" Type="float" />
   <Field Name="BoxLength" Type="float" />
   <Field Name="BoxWidth" Type="float" />
   <Field Name="BoxHeight" Type="float" />
   <Field Name="BoxYaw" Type="float" />
   <Field Name="ShapeType" Type="byte" />
   <Field Name="ShapeId" Type="ushort" />
   <Field Name="AreaTriggerActionSetId" Type="short" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="AreaTriggerActionSet" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="ushort" />
</Table>
<Table Name="AreaTriggerBox" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Extents" Type="float" ArraySize="3" />
</Table>
<Table Name="AreaTriggerCreateProperties" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ShapeType" Type="byte" />
   <Field Name="StartShapeId" Type="short" />
</Table>
<Table Name="AreaTriggerCylinder" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Radius" Type="float" />
   <Field Name="Height" Type="float" />
   <Field Name="ZOffset" Type="float" />
</Table>
<Table Name="AreaTriggerSphere" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MaxRadius" Type="float" />
</Table>
<Table Name="ArmorLocation" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Clothmodifier" Type="float" />
   <Field Name="Leathermodifier" Type="float" />
   <Field Name="Chainmodifier" Type="float" />
   <Field Name="Platemodifier" Type="float" />
   <Field Name="Modifier" Type="float" />
</Table>
<Table Name="Artifact" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UiTextureKitId" Type="ushort" />
   <Field Name="UiNameColor" Type="uint" />
   <Field Name="UiBarOverlayColor" Type="uint" />
   <Field Name="UiBarBackgroundColor" Type="uint" />
   <Field Name="ChrSpecializationId" Type="ushort" />
   <Field Name="Flags" Type="byte" />
   <Field Name="ArtifactCategoryId" Type="byte" />
   <Field Name="UiModelSceneId" Type="uint" />
   <Field Name="SpellVisualKitId" Type="int" />
</Table>
<Table Name="ArtifactAppearance" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ArtifactAppearanceSetId" Type="ushort" />
   <Field Name="DisplayIndex" Type="byte" />
   <Field Name="UnlockPlayerConditionId" Type="int" />
   <Field Name="ItemAppearanceModifierId" Type="byte" />
   <Field Name="UiSwatchColor" Type="int" />
   <Field Name="UiModelSaturation" Type="float" />
   <Field Name="UiModelOpacity" Type="float" />
   <Field Name="OverrideShapeshiftFormId" Type="byte" />
   <Field Name="OverrideShapeshiftDisplayId" Type="int" />
   <Field Name="UiItemAppearanceId" Type="int" />
   <Field Name="UiAltItemAppearanceId" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="UiCameraId" Type="ushort" />
</Table>
<Table Name="ArtifactAppearanceSet" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DisplayIndex" Type="byte" />
   <Field Name="UiCameraId" Type="ushort" />
   <Field Name="AltHandUICameraId" Type="ushort" />
   <Field Name="ForgeAttachmentOverride" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="ArtifactCategory" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="XpMultCurrencyId" Type="short" />
   <Field Name="XpMultCurveId" Type="short" />
</Table>
<Table Name="ArtifactItemToTransmog" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="int" />
   <Field Name="ArtifactId" Type="int" />
</Table>
<Table Name="ArtifactPower" Build="26806">
   <Field Name="DisplayPos" Type="float" ArraySize="2" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ArtifactId" Type="byte" />
   <Field Name="MaxPurchasableRank" Type="byte" />
   <Field Name="Label" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Tier" Type="byte" />
</Table>
<Table Name="ArtifactPowerLink" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PowerA" Type="ushort" />
   <Field Name="PowerB" Type="ushort" />
</Table>
<Table Name="ArtifactPowerPicker" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PlayerConditionId" Type="uint" />
</Table>
<Table Name="ArtifactPowerRank" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RankIndex" Type="byte" />
   <Field Name="SpellId" Type="int" />
   <Field Name="ItemBonusListId" Type="ushort" />
   <Field Name="AuraPointsOverride" Type="float" />
</Table>
<Table Name="ArtifactQuestXP" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Difficulty" Type="int" ArraySize="10" />
</Table>
<Table Name="ArtifactTier" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ArtifactTier" Type="uint" />
   <Field Name="MaxNumTraits" Type="uint" />
   <Field Name="MaxArtifactKnowledge" Type="uint" />
   <Field Name="KnowledgePlayerCondition" Type="uint" />
   <Field Name="MinimumEmpowerKnowledge" Type="uint" />
</Table>
<Table Name="ArtifactUnlock" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PowerId" Type="int" />
   <Field Name="PowerRank" Type="byte" />
   <Field Name="ItemBonusListId" Type="ushort" />
   <Field Name="PlayerConditionId" Type="uint" />
</Table>
<Table Name="AuctionHouse" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="FactionId" Type="ushort" />
   <Field Name="DepositRate" Type="byte" />
   <Field Name="ConsignmentRate" Type="byte" />
</Table>
<Table Name="AzeriteEmpoweredItem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="int" />
   <Field Name="AzeriteTierUnlockSetId" Type="int" />
   <Field Name="AzeritePowerSetId" Type="uint" />
</Table>
<Table Name="AzeriteItem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="int" />
</Table>
<Table Name="AzeriteItemMilestonePower" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RequiredLevel" Type="byte" />
   <Field Name="AzeritePowerId" Type="ushort" />
</Table>
<Table Name="AzeritePower" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="ItemBonusListId" Type="int" />
   <Field Name="SpecSetMemberId" Type="int" />
   <Field Name="Field_04" Type="int" />
</Table>
<Table Name="AzeritePowerSetMember" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AzeritePowerId" Type="ushort" />
   <Field Name="Class" Type="byte" />
   <Field Name="Tier" Type="byte" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="AzeriteTierUnlock" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemCreationContext" Type="byte" />
   <Field Name="Tier" Type="byte" />
   <Field Name="AzeriteLevel" Type="byte" />
</Table>
<Table Name="BankBagSlotPrices" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Cost" Type="uint" />
</Table>
<Table Name="BannedAddOns" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Version" Type="string" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="BarberShopStyle" Build="26806">
   <Field Name="DisplayName" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Type" Type="byte" />
   <Field Name="CostModifier" Type="float" />
   <Field Name="Race" Type="byte" />
   <Field Name="Sex" Type="byte" />
   <Field Name="Data" Type="byte" />
</Table>
<Table Name="BattlemasterList" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="GameType" Type="string" />
   <Field Name="ShortDescription" Type="string" />
   <Field Name="LongDescription" Type="string" />
   <Field Name="InstanceType" Type="byte" />
   <Field Name="MinLevel" Type="byte" />
   <Field Name="MaxLevel" Type="byte" />
   <Field Name="RatedPlayers" Type="byte" />
   <Field Name="MinPlayers" Type="byte" />
   <Field Name="MaxPlayers" Type="byte" />
   <Field Name="GroupsAllowed" Type="byte" />
   <Field Name="MaxGroupSize" Type="byte" />
   <Field Name="HolidayWorldState" Type="short" />
   <Field Name="Flags" Type="byte" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="RequiredPlayerConditionId" Type="ushort" />
   <Field Name="MapId" Type="ushort" ArraySize="16" />
</Table>
<Table Name="BattlePetAbility" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="PetTypeEnum" Type="byte" />
   <Field Name="Cooldown" Type="int" />
   <Field Name="BattlePetVisualId" Type="ushort" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="BattlePetAbilityEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BattlePetAbilityTurnId" Type="ushort" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="BattlePetEffectPropertiesId" Type="ushort" />
   <Field Name="AuraBattlePetAbilityId" Type="ushort" />
   <Field Name="BattlePetVisualId" Type="ushort" />
   <Field Name="Param" Type="ushort" ArraySize="6" />
</Table>
<Table Name="BattlePetAbilityState" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BattlePetStateId" Type="byte" />
   <Field Name="Value" Type="uint" />
</Table>
<Table Name="BattlePetAbilityTurn" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BattlePetAbilityId" Type="ushort" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="TurnTypeEnum" Type="byte" />
   <Field Name="EventTypeEnum" Type="byte" />
   <Field Name="BattlePetVisualId" Type="ushort" />
</Table>
<Table Name="BattlePetBreedQuality" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="StateMultiplier" Type="float" />
   <Field Name="QualityEnum" Type="byte" />
</Table>
<Table Name="BattlePetBreedState" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BattlePetStateId" Type="byte" />
   <Field Name="Value" Type="ushort" />
</Table>
<Table Name="BattlePetDisplayOverride" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BattlePetSpeciesId" Type="uint" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="CreatureDisplayInfoId" Type="uint" />
   <Field Name="PriorityCategory" Type="byte" />
</Table>
<Table Name="BattlePetEffectProperties" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ParamLabel" Type="string" ArraySize="6" />
   <Field Name="BattlePetVisualId" Type="ushort" />
   <Field Name="ParamTypeEnum" Type="byte" ArraySize="6" />
</Table>
<Table Name="BattlePetNPCTeamMember" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="BattlePetSpecies" Build="26806">
   <Field Name="Description" Type="string" />
   <Field Name="SourceText" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CreatureId" Type="int" />
   <Field Name="SummonSpellId" Type="int" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="PetTypeEnum" Type="byte" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="SourceTypeEnum" Type="byte" />
   <Field Name="CardUIModelSceneId" Type="int" />
   <Field Name="LoadoutUIModelSceneId" Type="int" />
</Table>
<Table Name="BattlePetSpeciesState" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BattlePetStateId" Type="byte" />
   <Field Name="Value" Type="uint" />
</Table>
<Table Name="BattlePetSpeciesXAbility" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BattlePetAbilityId" Type="ushort" />
   <Field Name="RequiredLevel" Type="byte" />
   <Field Name="SlotEnum" Type="byte" />
</Table>
<Table Name="BattlePetState" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="LuaName" Type="string" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="BattlePetVisualId" Type="ushort" />
</Table>
<Table Name="BattlePetVisual" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SceneScriptFunction" Type="string" />
   <Field Name="SpellVisualId" Type="uint" />
   <Field Name="CastMilliSeconds" Type="ushort" />
   <Field Name="ImpactMilliSeconds" Type="ushort" />
   <Field Name="RangeTypeEnum" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="SceneScriptPackageId" Type="ushort" />
</Table>
<Table Name="BeamEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BeamId" Type="int" />
   <Field Name="SourceMinDistance" Type="float" />
   <Field Name="FixedLength" Type="float" />
   <Field Name="Flags" Type="int" />
   <Field Name="SourceOffset" Type="int" />
   <Field Name="DestOffset" Type="int" />
   <Field Name="SourceAttachId" Type="int" />
   <Field Name="DestAttachId" Type="int" />
   <Field Name="SourcePositionerId" Type="int" />
   <Field Name="DestPositionerId" Type="int" />
</Table>
<Table Name="BoneWindModifierModel" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FileDataId" Type="int" />
   <Field Name="BoneWindModifierId" Type="int" />
</Table>
<Table Name="BoneWindModifiers" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Multiplier" Type="float" ArraySize="3" />
   <Field Name="PhaseMultiplier" Type="float" />
</Table>
<Table Name="BonusRoll" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CurrencyTypesId" Type="uint" />
   <Field Name="CurrencyCost" Type="uint" />
   <Field Name="JournalEncounterId" Type="uint" />
   <Field Name="JournalInstanceId" Type="uint" />
</Table>
<Table Name="Bounty" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="QuestId" Type="ushort" />
   <Field Name="FactionId" Type="ushort" />
   <Field Name="IconFileDataId" Type="uint" />
   <Field Name="TurninPlayerConditionId" Type="uint" />
</Table>
<Table Name="BountySet" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="VisiblePlayerConditionId" Type="uint" />
   <Field Name="LockedQuestId" Type="ushort" />
</Table>
<Table Name="BroadcastText" Build="26806">
   <Field Name="Text" Type="string" />
   <Field Name="Text1" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="LanguageId" Type="byte" />
   <Field Name="ConditionId" Type="int" />
   <Field Name="EmotesId" Type="ushort" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Field_07" Type="int" />
   <Field Name="SoundEntriesId" Type="int" ArraySize="2" />
   <Field Name="EmoteId" Type="ushort" ArraySize="3" />
   <Field Name="EmoteDelay" Type="ushort" ArraySize="3" />
</Table>
<Table Name="BroadcastTextVOState" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="State" Type="byte" />
</Table>
<Table Name="CameraEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="CameraEffectEntry" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="AmplitudeCurveId" Type="ushort" />
   <Field Name="Duration" Type="float" />
   <Field Name="Delay" Type="float" />
   <Field Name="Phase" Type="float" />
   <Field Name="Amplitude" Type="float" />
   <Field Name="AmplitudeB" Type="float" />
   <Field Name="Frequency" Type="float" />
   <Field Name="RadiusMin" Type="float" />
   <Field Name="RadiusMax" Type="float" />
   <Field Name="Flags" Type="byte" />
   <Field Name="EffectType" Type="byte" />
   <Field Name="DirectionType" Type="byte" />
   <Field Name="MovementType" Type="byte" />
   <Field Name="AttenuationType" Type="byte" />
</Table>
<Table Name="CameraMode" Build="26806">
   <Field Name="PositionOffset" Type="float" ArraySize="3" />
   <Field Name="TargetOffset" Type="float" ArraySize="3" />
   <Field Name="Type" Type="byte" />
   <Field Name="Flags" Type="int" />
   <Field Name="PositionSmoothing" Type="float" />
   <Field Name="RotationSmoothing" Type="float" />
   <Field Name="FieldOfView" Type="float" />
   <Field Name="LockedPositionOffsetBase" Type="byte" />
   <Field Name="LockedPositionOffsetDirection" Type="byte" />
   <Field Name="LockedTargetOffsetBase" Type="byte" />
   <Field Name="LockedTargetOffsetDirection" Type="byte" />
</Table>
<Table Name="Campaign" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Title" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UiTextureKitId" Type="uint" />
   <Field Name="QuestId" Type="uint" />
   <Field Name="PlayerConditionId" Type="uint" />
</Table>
<Table Name="CampaignXCondition" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="OrderIndex" Type="uint" />
</Table>
<Table Name="CampaignXQuestLine" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CampaignId" Type="uint" />
   <Field Name="QuestLineId" Type="uint" />
   <Field Name="ChapterId" Type="uint" />
</Table>
<Table Name="CastableRaidBuffs" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CastingSpellId" Type="uint" />
</Table>
<Table Name="CelestialBody" Build="26806">
   <Field Name="Position" Type="float" ArraySize="3" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BaseFileDataId" Type="int" />
   <Field Name="BodyBaseScale" Type="float" />
   <Field Name="Field_04" Type="int" />
   <Field Name="Field_05" Type="int" />
   <Field Name="RotateRate" Type="float" />
   <Field Name="AtmosphericMaskScale" Type="float" />
   <Field Name="SkyArrayBand" Type="ushort" />
   <Field Name="Field_09" Type="int" />
   <Field Name="GlowMaskFileDataId" Type="int" ArraySize="2" />
   <Field Name="GlowMaskScale" Type="float" ArraySize="2" />
   <Field Name="GlowModifiedFileDataId" Type="int" ArraySize="2" />
   <Field Name="ScrollURate" Type="float" ArraySize="2" />
   <Field Name="ScrollVRate" Type="float" ArraySize="2" />
</Table>
<Table Name="Cfg_Categories" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="LocaleMask" Type="ushort" />
   <Field Name="CreateCharsetMask" Type="byte" />
   <Field Name="ExistingCharsetMask" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="Cfg_Configs" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PlayerKillingAllowed" Type="byte" />
   <Field Name="Roleplaying" Type="byte" />
   <Field Name="PlayerAttackSpeedBase" Type="ushort" />
   <Field Name="MaxDamageReductionPctPhysical" Type="float" />
</Table>
<Table Name="Cfg_Regions" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Tag" Type="string" />
   <Field Name="RegionId" Type="ushort" />
   <Field Name="Raidorigin" Type="int" />
   <Field Name="RegionGroupMask" Type="byte" />
   <Field Name="ChallengeOrigin" Type="int" />
</Table>
<Table Name="CharacterFaceBoneSet" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SexId" Type="byte" />
   <Field Name="ModelFileDataId" Type="int" />
   <Field Name="FaceVariationIndex" Type="byte" />
   <Field Name="BoneSetFileDataId" Type="int" />
</Table>
<Table Name="CharacterFacialHairStyles" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Geoset" Type="uint" ArraySize="5" />
   <Field Name="RaceId" Type="byte" />
   <Field Name="SexId" Type="byte" />
   <Field Name="VariationId" Type="byte" />
</Table>
<Table Name="CharacterLoadout" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RaceMask" Type="ulong" />
   <Field Name="ChrClassId" Type="byte" />
   <Field Name="Purpose" Type="byte" />
</Table>
<Table Name="CharacterLoadoutItem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CharacterLoadoutId" Type="ushort" />
   <Field Name="ItemId" Type="uint" />
</Table>
<Table Name="CharacterServiceInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FlowTitle" Type="string" />
   <Field Name="PopupTitle" Type="string" />
   <Field Name="PopupDescription" Type="string" />
   <Field Name="BoostType" Type="int" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="Priority" Type="int" />
   <Field Name="Flags" Type="uint" />
   <Field Name="ProfessionLevel" Type="int" />
   <Field Name="BoostLevel" Type="int" />
   <Field Name="Expansion" Type="int" />
   <Field Name="PopupUITextureKitId" Type="uint" />
</Table>
<Table Name="CharBaseInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RaceId" Type="byte" />
   <Field Name="ClassId" Type="byte" />
</Table>
<Table Name="CharBaseSection" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="LayoutResType" Type="byte" />
   <Field Name="VariationEnum" Type="byte" />
   <Field Name="ResolutionVariationEnum" Type="byte" />
</Table>
<Table Name="CharComponentTextureLayouts" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Width" Type="short" />
   <Field Name="Height" Type="short" />
</Table>
<Table Name="CharComponentTextureSections" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CharComponentTextureLayoutId" Type="byte" />
   <Field Name="SectionType" Type="byte" />
   <Field Name="X" Type="short" />
   <Field Name="Y" Type="ushort" />
   <Field Name="Width" Type="short" />
   <Field Name="Height" Type="ushort" />
   <Field Name="OverlapSectionMask" Type="int" />
</Table>
<Table Name="CharHairGeosets" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RaceId" Type="byte" />
   <Field Name="SexId" Type="byte" />
   <Field Name="VariationId" Type="byte" />
   <Field Name="GeosetId" Type="byte" />
   <Field Name="Showscalp" Type="byte" />
   <Field Name="VariationType" Type="byte" />
   <Field Name="GeosetType" Type="byte" />
   <Field Name="ColorIndex" Type="byte" />
   <Field Name="CustomGeoFileDataId" Type="int" />
   <Field Name="HdCustomGeoFileDataId" Type="int" />
</Table>
<Table Name="CharSections" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RaceId" Type="byte" />
   <Field Name="SexId" Type="byte" />
   <Field Name="BaseSection" Type="byte" />
   <Field Name="VariationIndex" Type="byte" />
   <Field Name="ColorIndex" Type="byte" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="MaterialResourcesId" Type="int" ArraySize="3" />
</Table>
<Table Name="CharShipment" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ContainerId" Type="ushort" />
   <Field Name="DummyItemId" Type="int" />
   <Field Name="TreasureId" Type="uint" />
   <Field Name="SpellId" Type="int" />
   <Field Name="OnCompleteSpellId" Type="int" />
   <Field Name="Duration" Type="uint" />
   <Field Name="MaxShipments" Type="byte" />
   <Field Name="GarrFollowerId" Type="ushort" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="CharShipmentContainer" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Description" Type="string" />
   <Field Name="PendingText" Type="string" />
   <Field Name="UiTextureKitId" Type="ushort" />
   <Field Name="GarrTypeId" Type="byte" />
   <Field Name="GarrBuildingType" Type="byte" />
   <Field Name="BaseCapacity" Type="byte" />
   <Field Name="SmallDisplayInfoId" Type="ushort" />
   <Field Name="MediumDisplayInfoId" Type="ushort" />
   <Field Name="LargeDisplayInfoId" Type="ushort" />
   <Field Name="WorkingDisplayInfoId" Type="ushort" />
   <Field Name="WorkingSpellVisualId" Type="uint" />
   <Field Name="CompleteSpellVisualId" Type="uint" />
   <Field Name="MediumThreshold" Type="byte" />
   <Field Name="LargeThreshold" Type="byte" />
   <Field Name="Faction" Type="byte" />
   <Field Name="CrossFactionId" Type="ushort" />
</Table>
<Table Name="CharStartOutfit" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="SexId" Type="byte" />
   <Field Name="OutfitId" Type="byte" />
   <Field Name="PetDisplayId" Type="uint" />
   <Field Name="PetFamilyId" Type="byte" />
   <Field Name="ItemId" Type="int" ArraySize="24" />
</Table>
<Table Name="CharTitles" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Name1" Type="string" />
   <Field Name="MaskId" Type="short" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="ChatChannels" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Shortcut" Type="string" />
   <Field Name="Flags" Type="int" />
   <Field Name="FactionGroup" Type="byte" />
</Table>
<Table Name="ChatProfanity" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Text" Type="string" />
   <Field Name="Language" Type="byte" />
</Table>
<Table Name="ChrClasses" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Filename" Type="string" />
   <Field Name="NameMale" Type="string" />
   <Field Name="NameFemale" Type="string" />
   <Field Name="PetNameToken" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CreateScreenFileDataId" Type="int" />
   <Field Name="SelectScreenFileDataId" Type="int" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="LowResScreenFileDataId" Type="int" />
   <Field Name="StartingLevel" Type="int" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="CinematicSequenceId" Type="ushort" />
   <Field Name="DefaultSpec" Type="ushort" />
   <Field Name="PrimaryStatPriority" Type="byte" />
   <Field Name="DisplayPower" Type="byte" />
   <Field Name="RangedAttackPowerPerAgility" Type="byte" />
   <Field Name="AttackPowerPerAgility" Type="byte" />
   <Field Name="AttackPowerPerStrength" Type="byte" />
   <Field Name="SpellClassSet" Type="byte" />
</Table>
<Table Name="ChrClassesXPowerTypes" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PowerType" Type="byte" />
</Table>
<Table Name="ChrClassRaceSex" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="RaceId" Type="byte" />
   <Field Name="Sex" Type="byte" />
   <Field Name="Flags" Type="int" />
   <Field Name="SoundId" Type="uint" />
   <Field Name="VoiceSoundFilterId" Type="uint" />
</Table>
<Table Name="ChrClassTitle" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="NameMale" Type="string" />
   <Field Name="NameFemale" Type="string" />
   <Field Name="ChrClassId" Type="byte" />
</Table>
<Table Name="ChrClassUIDisplay" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ChrClassesId" Type="byte" />
   <Field Name="AdvGuidePlayerConditionId" Type="uint" />
   <Field Name="SplashPlayerConditionId" Type="uint" />
</Table>
<Table Name="ChrClassVillain" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="ChrClassId" Type="byte" />
   <Field Name="Gender" Type="byte" />
</Table>
<Table Name="ChrCustomization" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Sex" Type="int" />
   <Field Name="BaseSection" Type="int" />
   <Field Name="UiCustomizationType" Type="int" />
   <Field Name="Flags" Type="int" />
   <Field Name="ComponentSection" Type="int" ArraySize="3" />
</Table>
<Table Name="ChrRaces" Build="26806">
   <Field Name="ClientPrefix" Type="string" />
   <Field Name="ClientFileString" Type="string" />
   <Field Name="Name" Type="string" />
   <Field Name="NameFemale" Type="string" />
   <Field Name="NameLowercase" Type="string" />
   <Field Name="NameFemaleLowercase" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="int" />
   <Field Name="MaleDisplayId" Type="int" />
   <Field Name="FemaleDisplayId" Type="int" />
   <Field Name="HighResMaleDisplayId" Type="uint" />
   <Field Name="HighResFemaleDisplayId" Type="int" />
   <Field Name="CreateScreenFileDataId" Type="int" />
   <Field Name="SelectScreenFileDataId" Type="int" />
   <Field Name="MaleCustomizeOffset" Type="float" ArraySize="3" />
   <Field Name="FemaleCustomizeOffset" Type="float" ArraySize="3" />
   <Field Name="LowResScreenFileDataId" Type="int" />
   <Field Name="AlteredFormStartVisualKitId" Type="int" ArraySize="3" />
   <Field Name="AlteredFormFinishVisualKitId" Type="int" ArraySize="3" />
   <Field Name="HeritageArmorAchievementId" Type="int" />
   <Field Name="StartingLevel" Type="int" />
   <Field Name="UiDisplayOrder" Type="int" />
   <Field Name="FemaleSkeletonFileDataId" Type="int" />
   <Field Name="MaleSkeletonFileDataId" Type="int" />
   <Field Name="BaseRaceId" Type="int" />
   <Field Name="FactionId" Type="ushort" />
   <Field Name="CinematicSequenceId" Type="ushort" />
   <Field Name="ResSicknessSpellId" Type="ushort" />
   <Field Name="SplashSoundId" Type="ushort" />
   <Field Name="BaseLanguage" Type="byte" />
   <Field Name="CreatureType" Type="byte" />
   <Field Name="Alliance" Type="byte" />
   <Field Name="RaceRelated" Type="byte" />
   <Field Name="UnalteredVisualRaceId" Type="byte" />
   <Field Name="CharComponentTextureLayoutId" Type="byte" />
   <Field Name="CharComponentTexLayoutHiResId" Type="byte" />
   <Field Name="DefaultClassId" Type="byte" />
   <Field Name="NeutralRaceId" Type="byte" />
   <Field Name="MaleModelFallbackRaceId" Type="byte" />
   <Field Name="MaleModelFallbackSex" Type="byte" />
   <Field Name="FemaleModelFallbackRaceId" Type="byte" />
   <Field Name="FemaleModelFallbackSex" Type="byte" />
   <Field Name="MaleTextureFallbackRaceId" Type="byte" />
   <Field Name="MaleTextureFallbackSex" Type="byte" />
   <Field Name="FemaleTextureFallbackRaceId" Type="byte" />
   <Field Name="FemaleTextureFallbackSex" Type="byte" />
</Table>
<Table Name="ChrSpecialization" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="FemaleName" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="PetTalentType" Type="byte" />
   <Field Name="Role" Type="byte" />
   <Field Name="Flags" Type="uint" />
   <Field Name="SpellIconFileId" Type="int" />
   <Field Name="PrimaryStatPriority" Type="byte" />
   <Field Name="AnimReplacements" Type="int" />
   <Field Name="MasterySpellId" Type="int" ArraySize="2" />
</Table>
<Table Name="ChrUpgradeBucket" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ChrSpecializationId" Type="ushort" />
</Table>
<Table Name="ChrUpgradeBucketSpell" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
</Table>
<Table Name="ChrUpgradeTier" Build="26806">
   <Field Name="DisplayName" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="NumTalents" Type="byte" />
</Table>
<Table Name="CinematicCamera" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Origin" Type="float" ArraySize="3" />
   <Field Name="SoundId" Type="uint" />
   <Field Name="OriginFacing" Type="float" />
   <Field Name="FileDataId" Type="uint" />
</Table>
<Table Name="CinematicSequences" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundId" Type="uint" />
   <Field Name="Camera" Type="ushort" ArraySize="8" />
</Table>
<Table Name="ClientSceneEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SceneScriptPackageId" Type="int" />
</Table>
<Table Name="CloakDampening" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="TabardAngle" Type="float" />
   <Field Name="TabardDampening" Type="float" />
   <Field Name="ExpectedWeaponSize" Type="float" />
   <Field Name="Angle" Type="float" ArraySize="5" />
   <Field Name="Dampening" Type="float" ArraySize="5" />
   <Field Name="TailAngle" Type="float" ArraySize="2" />
   <Field Name="TailDampening" Type="float" ArraySize="2" />
</Table>
<Table Name="CombatCondition" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="WorldStateExpressionId" Type="ushort" />
   <Field Name="SelfConditionId" Type="ushort" />
   <Field Name="TargetConditionId" Type="ushort" />
   <Field Name="FriendConditionLogic" Type="byte" />
   <Field Name="EnemyConditionLogic" Type="byte" />
   <Field Name="FriendConditionId" Type="ushort" ArraySize="2" />
   <Field Name="FriendConditionOp" Type="byte" ArraySize="2" />
   <Field Name="FriendConditionCount" Type="byte" ArraySize="2" />
   <Field Name="EnemyConditionId" Type="ushort" ArraySize="2" />
   <Field Name="EnemyConditionOp" Type="byte" ArraySize="2" />
   <Field Name="EnemyConditionCount" Type="byte" ArraySize="2" />
</Table>
<Table Name="CommentatorStartLocation" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Pos" Type="float" ArraySize="3" />
   <Field Name="MapId" Type="int" />
</Table>
<Table Name="CommentatorTrackedCooldown" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="Priority" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="CommunityIcon" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="OrderIndex" Type="int" />
</Table>
<Table Name="ComponentModelFileData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GenderIndex" Type="byte" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="RaceId" Type="byte" />
   <Field Name="PositionIndex" Type="byte" />
</Table>
<Table Name="ComponentTextureFileData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GenderIndex" Type="byte" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="RaceId" Type="byte" />
</Table>
<Table Name="ConfigurationWarning" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Warning" Type="string" />
   <Field Name="Type" Type="uint" />
</Table>
<Table Name="ContentTuning" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MinLevel" Type="int" />
   <Field Name="MaxLevel" Type="int" />
   <Field Name="Flags" Type="int" />
   <Field Name="ExpectedStatModId" Type="int" />
   <Field Name="Field_05" Type="int" />
</Table>
<Table Name="ContentTuningDescription" Build="26806">
   <Field Name="Field_00" Type="string" />
</Table>
<Table Name="Contribution" Build="26806">
   <Field Name="Description" Type="string" />
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ManagedWorldStateInputId" Type="int" />
   <Field Name="OrderIndex" Type="int" />
   <Field Name="Field_05" Type="int" />
   <Field Name="UiTextureAtlasMemberId" Type="int" ArraySize="4" />
</Table>
<Table Name="ContributionStyle" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Field_01" Type="string" />
   <Field Name="Field_02" Type="string" />
   <Field Name="Field_03" Type="int" />
   <Field Name="Field_04" Type="uint" />
   <Field Name="Field_05" Type="int" />
   <Field Name="Field_06" Type="int" />
   <Field Name="Field_07" Type="int" />
</Table>
<Table Name="ContributionStyleContainer" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Field_01" Type="int" ArraySize="5" />
</Table>
<Table Name="ConversationLine" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BroadcastTextId" Type="int" />
   <Field Name="SpellVisualKitId" Type="int" />
   <Field Name="AdditionalDuration" Type="uint" />
   <Field Name="NextConversationLineId" Type="ushort" />
   <Field Name="AnimKitId" Type="ushort" />
   <Field Name="SpeechType" Type="byte" />
   <Field Name="StartAnimation" Type="byte" />
   <Field Name="EndAnimation" Type="byte" />
</Table>
<Table Name="Creature" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="NameAlt" Type="string" />
   <Field Name="Title" Type="string" />
   <Field Name="TitleAlt" Type="string" />
   <Field Name="Classification" Type="byte" />
   <Field Name="CreatureType" Type="byte" />
   <Field Name="CreatureFamily" Type="ushort" />
   <Field Name="StartAnimState" Type="byte" />
   <Field Name="DisplayId" Type="int" ArraySize="4" />
   <Field Name="DisplayProbability" Type="float" ArraySize="4" />
   <Field Name="AlwaysItem" Type="int" ArraySize="3" />
</Table>
<Table Name="CreatureDifficulty" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ExpansionId" Type="byte" />
   <Field Name="MinLevel" Type="byte" />
   <Field Name="MaxLevel" Type="byte" />
   <Field Name="FactionId" Type="ushort" />
   <Field Name="ContentTuningId" Type="int" />
   <Field Name="Flags" Type="uint" ArraySize="7" />
</Table>
<Table Name="CreatureDisplayInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ModelId" Type="ushort" />
   <Field Name="SoundId" Type="ushort" />
   <Field Name="SizeClass" Type="byte" />
   <Field Name="CreatureModelScale" Type="float" />
   <Field Name="CreatureModelAlpha" Type="byte" />
   <Field Name="BloodId" Type="byte" />
   <Field Name="ExtendedDisplayInfoId" Type="int" />
   <Field Name="NPCSoundId" Type="ushort" />
   <Field Name="ParticleColorId" Type="ushort" />
   <Field Name="PortraitCreatureDisplayInfoId" Type="int" />
   <Field Name="PortraitTextureFileDataId" Type="int" />
   <Field Name="ObjectEffectPackageId" Type="ushort" />
   <Field Name="AnimReplacementSetId" Type="ushort" />
   <Field Name="Flags" Type="byte" />
   <Field Name="StateSpellVisualKitId" Type="int" />
   <Field Name="PlayerOverrideScale" Type="float" />
   <Field Name="PetInstanceScale" Type="float" />
   <Field Name="UnarmedWeaponType" Type="byte" />
   <Field Name="MountPoofSpellVisualKitId" Type="int" />
   <Field Name="DissolveEffectId" Type="int" />
   <Field Name="Gender" Type="byte" />
   <Field Name="DissolveOutEffectId" Type="int" />
   <Field Name="CreatureModelMinLod" Type="byte" />
   <Field Name="TextureVariationFileDataId" Type="int" ArraySize="3" />
</Table>
<Table Name="CreatureDisplayInfoCond" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RaceMask" Type="ulong" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="Gender" Type="byte" />
   <Field Name="ClassMask" Type="uint" />
   <Field Name="SkinColorMask" Type="uint" />
   <Field Name="HairColorMask" Type="uint" />
   <Field Name="HairStyleMask" Type="uint" />
   <Field Name="FaceStyleMask" Type="uint" />
   <Field Name="FacialHairStyleMask" Type="uint" />
   <Field Name="CreatureModelDataId" Type="int" />
   <Field Name="CustomOption0Mask" Type="uint" ArraySize="2" />
   <Field Name="CustomOption1Mask" Type="int" ArraySize="2" />
   <Field Name="CustomOption2Mask" Type="int" ArraySize="2" />
   <Field Name="TextureVariationFileDataId" Type="int" ArraySize="3" />
</Table>
<Table Name="CreatureDisplayInfoEvt" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Fourcc" Type="int" />
   <Field Name="SpellVisualKitId" Type="int" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="CreatureDisplayInfoExtra" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DisplayRaceId" Type="byte" />
   <Field Name="DisplaySexId" Type="byte" />
   <Field Name="DisplayClassId" Type="byte" />
   <Field Name="SkinId" Type="byte" />
   <Field Name="FaceId" Type="byte" />
   <Field Name="HairStyleId" Type="byte" />
   <Field Name="HairColorId" Type="byte" />
   <Field Name="FacialHairId" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="BakeMaterialResourcesId" Type="int" />
   <Field Name="HDBakeMaterialResourcesId" Type="int" />
   <Field Name="CustomDisplayOption" Type="byte" ArraySize="3" />
</Table>
<Table Name="CreatureDisplayInfoGeosetData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GeosetIndex" Type="byte" />
   <Field Name="GeosetValue" Type="byte" />
</Table>
<Table Name="CreatureDisplayInfoTrn" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DstCreatureDisplayInfoId" Type="int" />
   <Field Name="DissolveEffectId" Type="uint" />
   <Field Name="StartVisualKitId" Type="uint" />
   <Field Name="MaxTime" Type="float" />
   <Field Name="FinishVisualKitId" Type="int" />
</Table>
<Table Name="CreatureDispXUiCamera" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CreatureDisplayInfoId" Type="uint" />
   <Field Name="UiCameraId" Type="ushort" />
</Table>
<Table Name="CreatureFamily" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="MinScale" Type="float" />
   <Field Name="MinScaleLevel" Type="byte" />
   <Field Name="MaxScale" Type="float" />
   <Field Name="MaxScaleLevel" Type="byte" />
   <Field Name="PetFoodMask" Type="short" />
   <Field Name="PetTalentType" Type="byte" />
   <Field Name="IconFileId" Type="int" />
   <Field Name="SkillLine" Type="ushort" ArraySize="2" />
</Table>
<Table Name="CreatureImmunities" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="School" Type="byte" />
   <Field Name="DispelType" Type="uint" />
   <Field Name="MechanicsAllowed" Type="byte" />
   <Field Name="EffectsAllowed" Type="byte" />
   <Field Name="StatesAllowed" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Mechanic" Type="int" ArraySize="2" />
   <Field Name="Effect" Type="int" ArraySize="9" />
   <Field Name="State" Type="int" ArraySize="16" />
</Table>
<Table Name="CreatureModelData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GeoBox" Type="float" ArraySize="6" />
   <Field Name="Flags" Type="int" />
   <Field Name="FileDataId" Type="uint" />
   <Field Name="BloodId" Type="int" />
   <Field Name="FootprintTextureId" Type="uint" />
   <Field Name="FootprintTextureLength" Type="float" />
   <Field Name="FootprintTextureWidth" Type="float" />
   <Field Name="FootprintParticleScale" Type="float" />
   <Field Name="FoleyMaterialId" Type="uint" />
   <Field Name="FootstepCameraEffectId" Type="uint" />
   <Field Name="DeathThudCameraEffectId" Type="int" />
   <Field Name="SoundId" Type="uint" />
   <Field Name="SizeClass" Type="uint" />
   <Field Name="CollisionWidth" Type="float" />
   <Field Name="CollisionHeight" Type="float" />
   <Field Name="WorldEffectScale" Type="float" />
   <Field Name="CreatureGeosetDataId" Type="int" />
   <Field Name="HoverHeight" Type="float" />
   <Field Name="AttachedEffectScale" Type="float" />
   <Field Name="ModelScale" Type="float" />
   <Field Name="MissileCollisionRadius" Type="float" />
   <Field Name="MissileCollisionPush" Type="float" />
   <Field Name="MissileCollisionRaise" Type="float" />
   <Field Name="MountHeight" Type="float" />
   <Field Name="OverrideLootEffectScale" Type="float" />
   <Field Name="OverrideNameScale" Type="float" />
   <Field Name="OverrideSelectionRadius" Type="float" />
   <Field Name="TamedPetBaseScale" Type="float" />
</Table>
<Table Name="CreatureMovementInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SmoothFacingChaseRate" Type="float" />
</Table>
<Table Name="CreatureSoundData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundExertionId" Type="uint" />
   <Field Name="SoundExertionCriticalId" Type="uint" />
   <Field Name="SoundInjuryId" Type="uint" />
   <Field Name="SoundInjuryCriticalId" Type="uint" />
   <Field Name="SoundInjuryCrushingBlowId" Type="uint" />
   <Field Name="SoundDeathId" Type="uint" />
   <Field Name="SoundStunId" Type="int" />
   <Field Name="SoundStandId" Type="uint" />
   <Field Name="SoundFootstepId" Type="uint" />
   <Field Name="SoundAggroId" Type="uint" />
   <Field Name="SoundWingFlapId" Type="uint" />
   <Field Name="SoundWingGlideId" Type="int" />
   <Field Name="SoundAlertId" Type="uint" />
   <Field Name="SoundJumpStartId" Type="int" />
   <Field Name="SoundJumpEndId" Type="int" />
   <Field Name="SoundPetAttackId" Type="int" />
   <Field Name="SoundPetOrderId" Type="int" />
   <Field Name="SoundPetDismissId" Type="uint" />
   <Field Name="LoopSoundId" Type="uint" />
   <Field Name="BirthSoundId" Type="int" />
   <Field Name="SpellCastDirectedSoundId" Type="uint" />
   <Field Name="SubmergeSoundId" Type="int" />
   <Field Name="SubmergedSoundId" Type="int" />
   <Field Name="WindupSoundId" Type="int" />
   <Field Name="WindupCriticalSoundId" Type="int" />
   <Field Name="ChargeSoundId" Type="uint" />
   <Field Name="ChargeCriticalSoundId" Type="int" />
   <Field Name="BattleShoutSoundId" Type="uint" />
   <Field Name="BattleShoutCriticalSoundId" Type="int" />
   <Field Name="TauntSoundId" Type="uint" />
   <Field Name="CreatureSoundDataIDPet" Type="uint" />
   <Field Name="NPCSoundId" Type="uint" />
   <Field Name="FidgetDelaySecondsMin" Type="float" />
   <Field Name="FidgetDelaySecondsMax" Type="float" />
   <Field Name="CreatureImpactType" Type="byte" />
   <Field Name="SoundFidget" Type="int" ArraySize="5" />
   <Field Name="CustomAttack" Type="int" ArraySize="4" />
</Table>
<Table Name="CreatureType" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="CreatureXContribution" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ContributionId" Type="int" />
</Table>
<Table Name="CreatureXDisplayInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CreatureDisplayInfoId" Type="int" />
   <Field Name="Probability" Type="float" />
   <Field Name="Scale" Type="float" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="Criteria" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Type" Type="ushort" />
   <Field Name="Asset" Type="int" />
   <Field Name="ModifierTreeId" Type="uint" />
   <Field Name="StartEvent" Type="byte" />
   <Field Name="StartAsset" Type="int" />
   <Field Name="StartTimer" Type="ushort" />
   <Field Name="FailEvent" Type="byte" />
   <Field Name="FailAsset" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="EligibilityWorldStateId" Type="ushort" />
   <Field Name="EligibilityWorldStateValue" Type="byte" />
</Table>
<Table Name="CriteriaTree" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Description" Type="string" />
   <Field Name="Parent" Type="uint" />
   <Field Name="Amount" Type="int" />
   <Field Name="Operator" Type="byte" />
   <Field Name="CriteriaId" Type="uint" />
   <Field Name="OrderIndex" Type="int" />
   <Field Name="Flags" Type="ushort" />
</Table>
<Table Name="CriteriaTreeXEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="WorldEffectId" Type="short" />
</Table>
<Table Name="CurrencyCategory" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Flags" Type="byte" />
   <Field Name="ExpansionId" Type="byte" />
</Table>
<Table Name="CurrencyContainer" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ContainerName" Type="string" />
   <Field Name="ContainerDescription" Type="string" />
   <Field Name="MinAmount" Type="int" />
   <Field Name="MaxAmount" Type="int" />
   <Field Name="ContainerIconId" Type="int" />
   <Field Name="ContainerQuality" Type="int" />
   <Field Name="Field_07" Type="int" />
</Table>
<Table Name="CurrencyTypes" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="CategoryId" Type="byte" />
   <Field Name="InventoryIconFileId" Type="int" />
   <Field Name="SpellWeight" Type="uint" />
   <Field Name="SpellCategory" Type="byte" />
   <Field Name="MaxQty" Type="uint" />
   <Field Name="MaxEarnablePerWeek" Type="int" />
   <Field Name="Flags" Type="uint" />
   <Field Name="Quality" Type="byte" />
</Table>
<Table Name="Curve" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Type" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="CurvePoint" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Pos" Type="float" ArraySize="2" />
   <Field Name="CurveId" Type="ushort" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="DeathThudLookups" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SizeClass" Type="byte" />
   <Field Name="TerrainTypeSoundId" Type="byte" />
   <Field Name="SoundEntryId" Type="uint" />
   <Field Name="SoundEntryIDWater" Type="int" />
</Table>
<Table Name="DecalProperties" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FileDataId" Type="int" />
   <Field Name="TopTextureBlendSetId" Type="int" />
   <Field Name="BotTextureBlendSetId" Type="int" />
   <Field Name="ModX" Type="float" />
   <Field Name="InnerRadius" Type="float" />
   <Field Name="OuterRadius" Type="float" />
   <Field Name="Rim" Type="float" />
   <Field Name="Gain" Type="float" />
   <Field Name="Flags" Type="int" />
   <Field Name="Scale" Type="float" />
   <Field Name="FadeIn" Type="float" />
   <Field Name="FadeOut" Type="float" />
   <Field Name="Priority" Type="byte" />
   <Field Name="BlendMode" Type="byte" />
   <Field Name="GameFlags" Type="int" />
   <Field Name="CasterDecalPropertiesId" Type="int" />
   <Field Name="Field_11" Type="float" />
</Table>
<Table Name="DeclinedWord" Build="26806">
   <Field Name="Word" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
</Table>
<Table Name="DeclinedWordCases" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DeclinedWord" Type="string" />
   <Field Name="CaseIndex" Type="byte" />
</Table>
<Table Name="DestructibleModelData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="State0ImpactEffectDoodadSet" Type="byte" />
   <Field Name="State0AmbientDoodadSet" Type="byte" />
   <Field Name="State1Wmo" Type="ushort" />
   <Field Name="State1DestructionDoodadSet" Type="byte" />
   <Field Name="State1ImpactEffectDoodadSet" Type="byte" />
   <Field Name="State1AmbientDoodadSet" Type="byte" />
   <Field Name="State2Wmo" Type="ushort" />
   <Field Name="State2DestructionDoodadSet" Type="byte" />
   <Field Name="State2ImpactEffectDoodadSet" Type="byte" />
   <Field Name="State2AmbientDoodadSet" Type="byte" />
   <Field Name="State3Wmo" Type="ushort" />
   <Field Name="State3InitDoodadSet" Type="byte" />
   <Field Name="State3AmbientDoodadSet" Type="byte" />
   <Field Name="EjectDirection" Type="byte" />
   <Field Name="DoNotHighlight" Type="byte" />
   <Field Name="State0Wmo" Type="ushort" />
   <Field Name="HealEffect" Type="byte" />
   <Field Name="HealEffectSpeed" Type="ushort" />
   <Field Name="State0NameSet" Type="byte" />
   <Field Name="State1NameSet" Type="byte" />
   <Field Name="State2NameSet" Type="byte" />
   <Field Name="State3NameSet" Type="byte" />
</Table>
<Table Name="DeviceBlacklist" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="VendorId" Type="ushort" />
   <Field Name="DeviceId" Type="ushort" />
</Table>
<Table Name="DeviceDefaultSettings" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="VendorId" Type="ushort" />
   <Field Name="DeviceId" Type="ushort" />
   <Field Name="DefaultSetting" Type="byte" />
</Table>
<Table Name="Difficulty" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="InstanceType" Type="byte" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="OldEnumValue" Type="byte" />
   <Field Name="FallbackDifficultyId" Type="byte" />
   <Field Name="MinPlayers" Type="byte" />
   <Field Name="MaxPlayers" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="ItemContext" Type="byte" />
   <Field Name="ToggleDifficultyId" Type="byte" />
   <Field Name="GroupSizeHealthCurveId" Type="ushort" />
   <Field Name="GroupSizeDmgCurveId" Type="ushort" />
   <Field Name="GroupSizeSpellPointsCurveId" Type="ushort" />
</Table>
<Table Name="DissolveEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Ramp" Type="float" />
   <Field Name="StartValue" Type="float" />
   <Field Name="EndValue" Type="float" />
   <Field Name="FadeInTime" Type="float" />
   <Field Name="FadeOutTime" Type="float" />
   <Field Name="Duration" Type="float" />
   <Field Name="AttachId" Type="byte" />
   <Field Name="ProjectionType" Type="byte" />
   <Field Name="TextureBlendSetId" Type="int" />
   <Field Name="Scale" Type="float" />
   <Field Name="Flags" Type="int" />
   <Field Name="CurveId" Type="int" />
   <Field Name="Priority" Type="uint" />
   <Field Name="FresnelIntensity" Type="float" />
</Table>
<Table Name="DriverBlacklist" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="VendorId" Type="ushort" />
   <Field Name="DeviceId" Type="byte" />
   <Field Name="DriverVersionHi" Type="int" />
   <Field Name="DriverVersionLow" Type="uint" />
   <Field Name="OsVersion" Type="byte" />
   <Field Name="OsBits" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="DungeonEncounter" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MapId" Type="short" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="OrderIndex" Type="int" />
   <Field Name="CreatureDisplayId" Type="byte" />
   <Field Name="Field_06" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="SpellIconFileId" Type="int" />
</Table>
<Table Name="DurabilityCosts" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="WeaponSubClassCost" Type="ushort" ArraySize="21" />
   <Field Name="ArmorSubClassCost" Type="ushort" ArraySize="8" />
</Table>
<Table Name="DurabilityQuality" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Data" Type="float" />
</Table>
<Table Name="EdgeGlowEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Duration" Type="float" />
   <Field Name="FadeIn" Type="float" />
   <Field Name="FadeOut" Type="float" />
   <Field Name="FresnelCoefficient" Type="float" />
   <Field Name="GlowRed" Type="float" />
   <Field Name="GlowGreen" Type="float" />
   <Field Name="GlowBlue" Type="float" />
   <Field Name="GlowAlpha" Type="float" />
   <Field Name="GlowMultiplier" Type="float" />
   <Field Name="Flags" Type="byte" />
   <Field Name="InitialDelay" Type="float" />
   <Field Name="CurveId" Type="int" />
   <Field Name="Priority" Type="int" />
</Table>
<Table Name="Emotes" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RaceMask" Type="ulong" />
   <Field Name="EmoteSlashCommand" Type="string" />
   <Field Name="AnimId" Type="int" />
   <Field Name="EmoteFlags" Type="int" />
   <Field Name="EmoteSpecProc" Type="byte" />
   <Field Name="EmoteSpecProcParam" Type="uint" />
   <Field Name="EventSoundId" Type="uint" />
   <Field Name="SpellVisualKitId" Type="int" />
   <Field Name="ClassMask" Type="int" />
</Table>
<Table Name="EmotesText" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="EmoteId" Type="ushort" />
</Table>
<Table Name="EmotesTextData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Text" Type="string" />
   <Field Name="RelationshipFlags" Type="byte" />
</Table>
<Table Name="EmotesTextSound" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RaceId" Type="byte" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="SexId" Type="byte" />
   <Field Name="SoundId" Type="uint" />
</Table>
<Table Name="EnvironmentalDamage" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="EnumId" Type="byte" />
   <Field Name="VisualKitId" Type="ushort" />
</Table>
<Table Name="Exhaustion" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="CombatLogText" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Xp" Type="int" />
   <Field Name="Factor" Type="float" />
   <Field Name="OutdoorHours" Type="float" />
   <Field Name="InnHours" Type="float" />
   <Field Name="Threshold" Type="float" />
</Table>
<Table Name="ExpectedStat" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ExpansionId" Type="uint" />
   <Field Name="CreatureHealth" Type="float" />
   <Field Name="PlayerHealth" Type="float" />
   <Field Name="CreatureAutoAttackDps" Type="float" />
   <Field Name="CreatureArmor" Type="float" />
   <Field Name="PlayerMana" Type="float" />
   <Field Name="PlayerPrimaryStat" Type="float" />
   <Field Name="PlayerSecondaryStat" Type="float" />
   <Field Name="ArmorConstant" Type="float" />
   <Field Name="CreatureSpellDamage" Type="float" />
</Table>
<Table Name="ExpectedStatMod" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CreatureHealthMod" Type="float" />
   <Field Name="PlayerHealthMod" Type="float" />
   <Field Name="CreatureAutoAttackDPSMod" Type="float" />
   <Field Name="CreatureArmorMod" Type="float" />
   <Field Name="PlayerManaMod" Type="float" />
   <Field Name="PlayerPrimaryStatMod" Type="float" />
   <Field Name="PlayerSecondaryStatMod" Type="float" />
   <Field Name="ArmorConstantMod" Type="float" />
   <Field Name="CreatureSpellDamageMod" Type="float" />
</Table>
<Table Name="Faction" Build="26806">
   <Field Name="ReputationRaceMask" Type="ulong" ArraySize="4" />
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ReputationIndex" Type="short" />
   <Field Name="ParentFactionId" Type="ushort" />
   <Field Name="Expansion" Type="byte" />
   <Field Name="FriendshipRepId" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="ParagonFactionId" Type="ushort" />
   <Field Name="ReputationClassMask" Type="ushort" ArraySize="4" />
   <Field Name="ReputationFlags" Type="ushort" ArraySize="4" />
   <Field Name="ReputationBase" Type="int" ArraySize="4" />
   <Field Name="ReputationMax" Type="int" ArraySize="4" />
   <Field Name="ParentFactionMod" Type="float" ArraySize="2" />
   <Field Name="ParentFactionCap" Type="byte" ArraySize="2" />
</Table>
<Table Name="FactionGroup" Build="26806">
   <Field Name="InternalName" Type="string" />
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MaskId" Type="byte" />
   <Field Name="HonorCurrencyTextureFileId" Type="int" />
   <Field Name="ConquestCurrencyTextureFileId" Type="int" />
</Table>
<Table Name="FactionTemplate" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Faction" Type="ushort" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="FactionGroup" Type="byte" />
   <Field Name="FriendGroup" Type="byte" />
   <Field Name="EnemyGroup" Type="byte" />
   <Field Name="Enemies" Type="ushort" ArraySize="4" />
   <Field Name="Friend" Type="ushort" ArraySize="4" />
</Table>
<Table Name="FootprintTextures" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FileDataId" Type="int" />
   <Field Name="TextureBlendsetId" Type="int" />
   <Field Name="Flags" Type="int" />
</Table>
<Table Name="FootstepTerrainLookup" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CreatureFootstepId" Type="ushort" />
   <Field Name="TerrainSoundId" Type="byte" />
   <Field Name="SoundId" Type="uint" />
   <Field Name="SoundIDSplash" Type="uint" />
</Table>
<Table Name="FriendshipRepReaction" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Reaction" Type="string" />
   <Field Name="FriendshipRepId" Type="byte" />
   <Field Name="ReactionThreshold" Type="ushort" />
</Table>
<Table Name="FriendshipReputation" Build="26806">
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="TextureFileId" Type="uint" />
   <Field Name="FactionId" Type="ushort" />
</Table>
<Table Name="FullScreenEffect" Build="26806">
   <Field Name="Flags" Type="uint" />
   <Field Name="Saturation" Type="float" />
   <Field Name="GammaRed" Type="float" />
   <Field Name="GammaGreen" Type="float" />
   <Field Name="GammaBlue" Type="float" />
   <Field Name="MaskOffsetY" Type="float" />
   <Field Name="MaskSizeMultiplier" Type="float" />
   <Field Name="MaskPower" Type="float" />
   <Field Name="ColorMultiplyRed" Type="float" />
   <Field Name="ColorMultiplyGreen" Type="float" />
   <Field Name="ColorMultiplyBlue" Type="float" />
   <Field Name="ColorMultiplyOffsetY" Type="float" />
   <Field Name="ColorMultiplyMultiplier" Type="float" />
   <Field Name="ColorMultiplyPower" Type="float" />
   <Field Name="ColorAdditionRed" Type="float" />
   <Field Name="ColorAdditionGreen" Type="float" />
   <Field Name="ColorAdditionBlue" Type="float" />
   <Field Name="ColorAdditionOffsetY" Type="float" />
   <Field Name="ColorAdditionMultiplier" Type="float" />
   <Field Name="ColorAdditionPower" Type="float" />
   <Field Name="Field_014" Type="int" />
   <Field Name="BlurIntensity" Type="float" />
   <Field Name="BlurOffsetY" Type="float" />
   <Field Name="BlurMultiplier" Type="float" />
   <Field Name="BlurPower" Type="float" />
   <Field Name="EffectFadeInMs" Type="uint" />
   <Field Name="EffectFadeOutMs" Type="uint" />
   <Field Name="TextureBlendSetId" Type="uint" />
</Table>
<Table Name="GameObjectArtKit" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AttachModelFileId" Type="int" />
   <Field Name="TextureVariationFileId" Type="int" ArraySize="3" />
</Table>
<Table Name="GameObjectDiffAnimMap" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="Animation" Type="byte" />
   <Field Name="AttachmentDisplayId" Type="ushort" />
</Table>
<Table Name="GameObjectDisplayInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GeoBox" Type="float" ArraySize="6" />
   <Field Name="FileDataId" Type="int" />
   <Field Name="ObjectEffectPackageId" Type="ushort" />
   <Field Name="OverrideLootEffectScale" Type="float" />
   <Field Name="OverrideNameScale" Type="float" />
</Table>
<Table Name="GameObjectDisplayInfoXSoundKit" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundKitId" Type="uint" />
   <Field Name="EventIndex" Type="byte" />
</Table>
<Table Name="GameObjects" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Pos" Type="float" ArraySize="3" />
   <Field Name="Rot" Type="float" ArraySize="4" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="OwnerId" Type="ushort" />
   <Field Name="DisplayId" Type="ushort" />
   <Field Name="Scale" Type="float" />
   <Field Name="TypeId" Type="byte" />
   <Field Name="PhaseUseFlags" Type="byte" />
   <Field Name="PhaseId" Type="ushort" />
   <Field Name="PhaseGroupId" Type="ushort" />
   <Field Name="PropValue" Type="uint" ArraySize="8" />
</Table>
<Table Name="GameTips" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Text" Type="string" />
   <Field Name="SortIndex" Type="byte" />
   <Field Name="MinLevel" Type="ushort" />
   <Field Name="MaxLevel" Type="ushort" />
</Table>
<Table Name="GarrAbility" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrAbilityCategoryId" Type="byte" />
   <Field Name="GarrFollowerTypeId" Type="byte" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="FactionChangeGarrAbilityId" Type="ushort" />
   <Field Name="Flags" Type="ushort" />
</Table>
<Table Name="GarrAbilityCategory" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="GarrAbilityEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrAbilityId" Type="ushort" />
   <Field Name="AbilityAction" Type="byte" />
   <Field Name="AbilityTargetType" Type="byte" />
   <Field Name="GarrMechanicTypeId" Type="byte" />
   <Field Name="CombatWeightBase" Type="float" />
   <Field Name="CombatWeightMax" Type="float" />
   <Field Name="ActionValueFlat" Type="float" />
   <Field Name="ActionRace" Type="byte" />
   <Field Name="ActionHours" Type="byte" />
   <Field Name="ActionRecordId" Type="int" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="GarrBuilding" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="HordeName" Type="string" />
   <Field Name="AllianceName" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="Tooltip" Type="string" />
   <Field Name="GarrTypeId" Type="byte" />
   <Field Name="BuildingType" Type="byte" />
   <Field Name="HordeGameObjectId" Type="int" />
   <Field Name="AllianceGameObjectId" Type="int" />
   <Field Name="GarrSiteId" Type="byte" />
   <Field Name="UpgradeLevel" Type="byte" />
   <Field Name="BuildSeconds" Type="int" />
   <Field Name="CurrencyTypeId" Type="ushort" />
   <Field Name="CurrencyQty" Type="int" />
   <Field Name="HordeUiTextureKitId" Type="ushort" />
   <Field Name="AllianceUiTextureKitId" Type="ushort" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="AllianceSceneScriptPackageId" Type="ushort" />
   <Field Name="HordeSceneScriptPackageId" Type="ushort" />
   <Field Name="MaxAssignments" Type="int" />
   <Field Name="ShipmentCapacity" Type="byte" />
   <Field Name="GarrAbilityId" Type="ushort" />
   <Field Name="BonusGarrAbilityId" Type="ushort" />
   <Field Name="GoldCost" Type="ushort" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="GarrBuildingDoodadSet" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrBuildingId" Type="byte" />
   <Field Name="Type" Type="byte" />
   <Field Name="HordeDoodadSet" Type="byte" />
   <Field Name="AllianceDoodadSet" Type="byte" />
   <Field Name="SpecializationId" Type="byte" />
</Table>
<Table Name="GarrBuildingPlotInst" Build="26806">
   <Field Name="MapOffset" Type="float" ArraySize="2" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrBuildingId" Type="byte" />
   <Field Name="GarrSiteLevelPlotInstId" Type="ushort" />
   <Field Name="UiTextureAtlasMemberId" Type="ushort" />
</Table>
<Table Name="GarrClassSpec" Build="26806">
   <Field Name="ClassSpec" Type="string" />
   <Field Name="ClassSpecMale" Type="string" />
   <Field Name="ClassSpecFemale" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UiTextureAtlasMemberId" Type="ushort" />
   <Field Name="GarrFollItemSetId" Type="ushort" />
   <Field Name="FollowerClassLimit" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="GarrClassSpecPlayerCond" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="GarrClassSpecId" Type="uint" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="FlavorGarrStringId" Type="int" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="GarrEncounter" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CreatureId" Type="int" />
   <Field Name="PortraitFileDataId" Type="int" />
   <Field Name="UiTextureKitId" Type="uint" />
   <Field Name="UiAnimScale" Type="float" />
   <Field Name="UiAnimHeight" Type="float" />
</Table>
<Table Name="GarrEncounterSetXEncounter" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrEncounterId" Type="uint" />
</Table>
<Table Name="GarrEncounterXMechanic" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrMechanicId" Type="byte" />
   <Field Name="GarrMechanicSetId" Type="byte" />
</Table>
<Table Name="GarrFollItemSetMember" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="int" />
   <Field Name="ItemSlot" Type="byte" />
   <Field Name="MinItemLevel" Type="ushort" />
</Table>
<Table Name="GarrFollower" Build="26806">
   <Field Name="HordeSourceText" Type="string" />
   <Field Name="AllianceSourceText" Type="string" />
   <Field Name="TitleName" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrTypeId" Type="byte" />
   <Field Name="GarrFollowerTypeId" Type="byte" />
   <Field Name="HordeCreatureId" Type="int" />
   <Field Name="AllianceCreatureId" Type="int" />
   <Field Name="HordeGarrFollRaceId" Type="byte" />
   <Field Name="AllianceGarrFollRaceId" Type="byte" />
   <Field Name="HordeGarrClassSpecId" Type="byte" />
   <Field Name="AllianceGarrClassSpecId" Type="byte" />
   <Field Name="Quality" Type="byte" />
   <Field Name="FollowerLevel" Type="byte" />
   <Field Name="ItemLevelWeapon" Type="ushort" />
   <Field Name="ItemLevelArmor" Type="ushort" />
   <Field Name="HordeSourceTypeEnum" Type="byte" />
   <Field Name="AllianceSourceTypeEnum" Type="byte" />
   <Field Name="HordeIconFileDataId" Type="int" />
   <Field Name="AllianceIconFileDataId" Type="int" />
   <Field Name="HordeGarrFollItemSetId" Type="ushort" />
   <Field Name="AllianceGarrFollItemSetId" Type="ushort" />
   <Field Name="HordeUITextureKitId" Type="ushort" />
   <Field Name="AllianceUITextureKitId" Type="ushort" />
   <Field Name="Vitality" Type="byte" />
   <Field Name="HordeFlavorGarrStringId" Type="byte" />
   <Field Name="AllianceFlavorGarrStringId" Type="byte" />
   <Field Name="HordeSlottingBroadcastTextId" Type="int" />
   <Field Name="AllySlottingBroadcastTextId" Type="int" />
   <Field Name="ChrClassId" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Gender" Type="byte" />
</Table>
<Table Name="GarrFollowerLevelXP" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrFollowerTypeId" Type="byte" />
   <Field Name="FollowerLevel" Type="byte" />
   <Field Name="XpToNextLevel" Type="ushort" />
   <Field Name="ShipmentXP" Type="ushort" />
</Table>
<Table Name="GarrFollowerQuality" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Quality" Type="byte" />
   <Field Name="XpToNextQuality" Type="uint" />
   <Field Name="GarrFollowerTypeId" Type="byte" />
   <Field Name="AbilityCount" Type="byte" />
   <Field Name="TraitCount" Type="byte" />
   <Field Name="ShipmentXP" Type="ushort" />
   <Field Name="ClassSpecId" Type="int" />
</Table>
<Table Name="GarrFollowerSetXFollower" Build="26806">
   <Field Name="GarrFollowerId" Type="int" />
</Table>
<Table Name="GarrFollowerType" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrTypeId" Type="byte" />
   <Field Name="MaxFollowers" Type="byte" />
   <Field Name="MaxFollowerBuildingType" Type="byte" />
   <Field Name="MaxItemLevel" Type="ushort" />
   <Field Name="LevelRangeBias" Type="byte" />
   <Field Name="ItemLevelRangeBias" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="GarrFollowerUICreature" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="FactionIndex" Type="byte" />
   <Field Name="CreatureId" Type="int" />
   <Field Name="Scale" Type="float" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="GarrFollowerXAbility" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="FactionIndex" Type="byte" />
   <Field Name="GarrAbilityId" Type="ushort" />
</Table>
<Table Name="GarrFollSupportSpell" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="HordeSpellId" Type="int" />
   <Field Name="AllianceSpellId" Type="int" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="GarrItemLevelUpgradeData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Operation" Type="int" />
   <Field Name="MinItemLevel" Type="int" />
   <Field Name="MaxItemLevel" Type="int" />
   <Field Name="FollowerTypeId" Type="int" />
</Table>
<Table Name="GarrMechanic" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrMechanicTypeId" Type="byte" />
   <Field Name="Factor" Type="float" />
   <Field Name="GarrAbilityId" Type="int" />
</Table>
<Table Name="GarrMechanicSetXMechanic" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrMechanicId" Type="byte" />
</Table>
<Table Name="GarrMechanicType" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="Category" Type="byte" />
</Table>
<Table Name="GarrMission" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Location" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="MapPos" Type="float" ArraySize="2" />
   <Field Name="WorldPos" Type="float" ArraySize="2" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrTypeId" Type="byte" />
   <Field Name="GarrMissionTypeId" Type="byte" />
   <Field Name="GarrFollowerTypeId" Type="byte" />
   <Field Name="MaxFollowers" Type="byte" />
   <Field Name="MissionCost" Type="int" />
   <Field Name="MissionCostCurrencyTypesId" Type="ushort" />
   <Field Name="OfferedGarrMissionTextureId" Type="byte" />
   <Field Name="UiTextureKitId" Type="ushort" />
   <Field Name="EnvGarrMechanicId" Type="int" />
   <Field Name="EnvGarrMechanicTypeId" Type="byte" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="TargetLevel" Type="byte" />
   <Field Name="TargetItemLevel" Type="ushort" />
   <Field Name="MissionDuration" Type="int" />
   <Field Name="TravelDuration" Type="int" />
   <Field Name="OfferDuration" Type="int" />
   <Field Name="BaseCompletionChance" Type="byte" />
   <Field Name="BaseFollowerXP" Type="int" />
   <Field Name="OvermaxRewardPackId" Type="int" />
   <Field Name="FollowerDeathChance" Type="byte" />
   <Field Name="AreaId" Type="int" />
   <Field Name="Flags" Type="uint" />
</Table>
<Table Name="GarrMissionTexture" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Pos" Type="float" ArraySize="2" />
   <Field Name="UiTextureKitId" Type="ushort" />
</Table>
<Table Name="GarrMissionType" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="UiTextureAtlasMemberId" Type="ushort" />
   <Field Name="UiTextureKitId" Type="ushort" />
</Table>
<Table Name="GarrMissionXEncounter" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrEncounterId" Type="uint" />
   <Field Name="GarrEncounterSetId" Type="uint" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="GarrMissionXFollower" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrFollowerId" Type="int" />
   <Field Name="GarrFollowerSetId" Type="int" />
</Table>
<Table Name="GarrMssnBonusAbility" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrFollowerTypeId" Type="byte" />
   <Field Name="GarrMissionTextureId" Type="byte" />
   <Field Name="GarrAbilityId" Type="ushort" />
   <Field Name="Radius" Type="float" />
   <Field Name="DurationSecs" Type="int" />
</Table>
<Table Name="GarrPlot" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="PlotType" Type="byte" />
   <Field Name="HordeConstructObjId" Type="int" />
   <Field Name="AllianceConstructObjId" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="UiCategoryId" Type="byte" />
   <Field Name="UpgradeRequirement" Type="int" ArraySize="2" />
</Table>
<Table Name="GarrPlotBuilding" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrPlotId" Type="byte" />
   <Field Name="GarrBuildingId" Type="byte" />
</Table>
<Table Name="GarrPlotInstance" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="GarrPlotId" Type="byte" />
</Table>
<Table Name="GarrPlotUICategory" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CategoryName" Type="string" />
   <Field Name="PlotType" Type="byte" />
</Table>
<Table Name="GarrSiteLevel" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="TownHallUiPos" Type="float" ArraySize="2" />
   <Field Name="GarrSiteId" Type="uint" />
   <Field Name="GarrLevel" Type="byte" />
   <Field Name="MapId" Type="ushort" />
   <Field Name="UpgradeMovieId" Type="ushort" />
   <Field Name="UiTextureKitId" Type="ushort" />
   <Field Name="MaxBuildingLevel" Type="byte" />
   <Field Name="UpgradeCost" Type="ushort" />
   <Field Name="UpgradeGoldCost" Type="ushort" />
</Table>
<Table Name="GarrSiteLevelPlotInst" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UiMarkerPos" Type="float" ArraySize="2" />
   <Field Name="GarrSiteLevelId" Type="ushort" />
   <Field Name="GarrPlotInstanceId" Type="byte" />
   <Field Name="UiMarkerSize" Type="byte" />
</Table>
<Table Name="GarrSpecialization" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Tooltip" Type="string" />
   <Field Name="BuildingType" Type="byte" />
   <Field Name="SpecType" Type="byte" />
   <Field Name="RequiredUpgradeLevel" Type="byte" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="Param" Type="float" ArraySize="2" />
</Table>
<Table Name="GarrString" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Text" Type="string" />
</Table>
<Table Name="GarrTalent" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrTalentTreeId" Type="uint" />
   <Field Name="Tier" Type="byte" />
   <Field Name="UiOrder" Type="byte" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="GarrAbilityId" Type="int" />
   <Field Name="PerkSpellId" Type="int" />
   <Field Name="PerkPlayerConditionId" Type="int" />
   <Field Name="ResearchDurationSecs" Type="int" />
   <Field Name="ResearchGoldCost" Type="int" />
   <Field Name="ResearchCost" Type="int" />
   <Field Name="ResearchCostCurrencyTypesId" Type="uint" />
   <Field Name="RespecDurationSecs" Type="int" />
   <Field Name="RespecGoldCost" Type="int" />
   <Field Name="RespecCost" Type="int" />
   <Field Name="RespecCostCurrencyTypesId" Type="uint" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="GarrTalentTree" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrTypeId" Type="int" />
   <Field Name="ClassId" Type="int" />
   <Field Name="MaxTiers" Type="byte" />
   <Field Name="UiOrder" Type="byte" />
   <Field Name="UiTextureKitId" Type="ushort" />
</Table>
<Table Name="GarrType" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PrimaryCurrencyTypeId" Type="uint" />
   <Field Name="SecondaryCurrencyTypeId" Type="uint" />
   <Field Name="ExpansionId" Type="uint" />
   <Field Name="Flags" Type="uint" />
   <Field Name="MapIDs" Type="int" ArraySize="2" />
</Table>
<Table Name="GarrUiAnimClassInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrClassSpecId" Type="byte" />
   <Field Name="MovementType" Type="byte" />
   <Field Name="ImpactDelaySecs" Type="float" />
   <Field Name="CastKit" Type="uint" />
   <Field Name="ImpactKit" Type="uint" />
   <Field Name="TargetImpactKit" Type="int" />
</Table>
<Table Name="GarrUiAnimRaceInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GarrFollRaceId" Type="byte" />
   <Field Name="MaleScale" Type="float" />
   <Field Name="MaleHeight" Type="float" />
   <Field Name="FemaleScale" Type="float" />
   <Field Name="FemaleHeight" Type="float" />
   <Field Name="MaleSingleModelScale" Type="float" />
   <Field Name="MaleSingleModelHeight" Type="float" />
   <Field Name="FemaleSingleModelScale" Type="float" />
   <Field Name="FemaleSingleModelHeight" Type="float" />
   <Field Name="MaleFollowerPageScale" Type="float" />
   <Field Name="MaleFollowerPageHeight" Type="float" />
   <Field Name="FemaleFollowerPageScale" Type="float" />
   <Field Name="FemaleFollowerPageHeight" Type="float" />
</Table>
<Table Name="GemProperties" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="EnchantId" Type="ushort" />
   <Field Name="Type" Type="int" />
   <Field Name="MinItemLevel" Type="ushort" />
</Table>
<Table Name="GlobalStrings" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BaseTag" Type="string" />
   <Field Name="TagText" Type="string" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="GlyphBindableSpell" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
</Table>
<Table Name="GlyphExclusiveCategory" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="GlyphProperties" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="uint" />
   <Field Name="SpellIconId" Type="ushort" />
   <Field Name="GlyphType" Type="byte" />
   <Field Name="GlyphExclusiveCategoryId" Type="byte" />
</Table>
<Table Name="GlyphRequiredSpec" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ChrSpecializationId" Type="ushort" />
</Table>
<Table Name="GMSurveyAnswers" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Answer" Type="string" />
   <Field Name="SortIndex" Type="byte" />
</Table>
<Table Name="GMSurveyCurrentSurvey" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GmsurveyId" Type="byte" />
</Table>
<Table Name="GMSurveyQuestions" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Question" Type="string" />
</Table>
<Table Name="GMSurveySurveys" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Q" Type="byte" ArraySize="15" />
</Table>
<Table Name="GroundEffectDoodad" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ModelFileId" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Animscale" Type="float" />
   <Field Name="PushScale" Type="float" />
</Table>
<Table Name="GroundEffectTexture" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Density" Type="uint" />
   <Field Name="Sound" Type="byte" />
   <Field Name="DoodadId" Type="ushort" ArraySize="4" />
   <Field Name="DoodadWeight" Type="byte" ArraySize="4" />
</Table>
<Table Name="GroupFinderActivity" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FullName" Type="string" />
   <Field Name="ShortName" Type="string" />
   <Field Name="GroupFinderCategoryId" Type="byte" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="GroupFinderActivityGrpId" Type="byte" />
   <Field Name="MinLevel" Type="byte" />
   <Field Name="MaxLevelSuggestion" Type="byte" />
   <Field Name="Flags" Type="int" />
   <Field Name="MinGearLevelSuggestion" Type="ushort" />
   <Field Name="MapId" Type="ushort" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="AreaId" Type="ushort" />
   <Field Name="MaxPlayers" Type="byte" />
   <Field Name="DisplayType" Type="byte" />
</Table>
<Table Name="GroupFinderActivityGrp" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="GroupFinderCategory" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="GuildColorBackground" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Red" Type="byte" />
   <Field Name="Blue" Type="byte" />
   <Field Name="Green" Type="byte" />
</Table>
<Table Name="GuildColorBorder" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Red" Type="byte" />
   <Field Name="Blue" Type="byte" />
   <Field Name="Green" Type="byte" />
</Table>
<Table Name="GuildColorEmblem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Red" Type="byte" />
   <Field Name="Blue" Type="byte" />
   <Field Name="Green" Type="byte" />
</Table>
<Table Name="GuildPerkSpells" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
</Table>
<Table Name="Heirloom" Build="26806">
   <Field Name="SourceText" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="int" />
   <Field Name="LegacyUpgradedItemId" Type="int" />
   <Field Name="StaticUpgradedItemId" Type="int" />
   <Field Name="SourceTypeEnum" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="LegacyItemId" Type="int" />
   <Field Name="UpgradeItemId" Type="int" ArraySize="3" />
   <Field Name="UpgradeItemBonusListId" Type="ushort" ArraySize="3" />
</Table>
<Table Name="HelmetAnimScaling" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RaceId" Type="int" />
   <Field Name="Amount" Type="float" />
</Table>
<Table Name="HelmetGeosetVisData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="HideGeoset" Type="uint" ArraySize="9" />
</Table>
<Table Name="HighlightColor" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Type" Type="byte" />
   <Field Name="StartColor" Type="uint" />
   <Field Name="MidColor" Type="uint" />
   <Field Name="EndColor" Type="int" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="HolidayDescriptions" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Description" Type="string" />
</Table>
<Table Name="HolidayNames" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="Holidays" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Region" Type="ushort" />
   <Field Name="Looping" Type="byte" />
   <Field Name="HolidayNameId" Type="uint" />
   <Field Name="HolidayDescriptionId" Type="uint" />
   <Field Name="Priority" Type="byte" />
   <Field Name="CalendarFilterType" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Duration" Type="ushort" ArraySize="10" />
   <Field Name="Date" Type="int" ArraySize="16" />
   <Field Name="CalendarFlags" Type="byte" ArraySize="10" />
   <Field Name="TextureFileDataId" Type="int" ArraySize="3" />
</Table>
<Table Name="Hotfixes" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Tablename" Type="string" />
   <Field Name="ObjectId" Type="int" />
   <Field Name="Flags" Type="int" />
</Table>
<Table Name="ImportPriceArmor" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ClothModifier" Type="float" />
   <Field Name="LeatherModifier" Type="float" />
   <Field Name="ChainModifier" Type="float" />
   <Field Name="PlateModifier" Type="float" />
</Table>
<Table Name="ImportPriceQuality" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Data" Type="float" />
</Table>
<Table Name="ImportPriceShield" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Data" Type="float" />
</Table>
<Table Name="ImportPriceWeapon" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Data" Type="float" />
</Table>
<Table Name="InvasionClientData" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="IconLocation" Type="float" ArraySize="2" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="WorldStateId" Type="int" />
   <Field Name="UiTextureAtlasMemberId" Type="int" />
   <Field Name="ScenarioId" Type="int" />
   <Field Name="WorldQuestId" Type="int" />
   <Field Name="WorldStateValue" Type="int" />
   <Field Name="InvasionEnabledWorldStateId" Type="int" />
</Table>
<Table Name="Item" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="SubclassId" Type="byte" />
   <Field Name="Material" Type="byte" />
   <Field Name="InventoryType" Type="byte" />
   <Field Name="SheatheType" Type="byte" />
   <Field Name="SoundOverrideSubclassId" Type="byte" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="ItemGroupSoundsId" Type="byte" />
</Table>
<Table Name="ItemAppearance" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DisplayType" Type="byte" />
   <Field Name="ItemDisplayInfoId" Type="int" />
   <Field Name="DefaultIconFileDataId" Type="int" />
   <Field Name="UiOrder" Type="int" />
</Table>
<Table Name="ItemAppearanceXUiCamera" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemAppearanceId" Type="ushort" />
   <Field Name="UiCameraId" Type="ushort" />
</Table>
<Table Name="ItemArmorQuality" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Qualitymod" Type="float" ArraySize="7" />
</Table>
<Table Name="ItemArmorShield" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Quality" Type="float" ArraySize="7" />
   <Field Name="ItemLevel" Type="ushort" />
</Table>
<Table Name="ItemArmorTotal" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemLevel" Type="short" />
   <Field Name="Cloth" Type="float" />
   <Field Name="Leather" Type="float" />
   <Field Name="Mail" Type="float" />
   <Field Name="Plate" Type="float" />
</Table>
<Table Name="ItemBagFamily" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="ItemBonus" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Value" Type="uint" ArraySize="3" />
   <Field Name="ParentItemBonusListId" Type="ushort" />
   <Field Name="Type" Type="byte" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="ItemBonusListLevelDelta" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemLevelDelta" Type="short" />
</Table>
<Table Name="ItemBonusTreeNode" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemContext" Type="byte" />
   <Field Name="ChildItemBonusTreeId" Type="ushort" />
   <Field Name="ChildItemBonusListId" Type="ushort" />
   <Field Name="ChildItemLevelSelectorId" Type="ushort" />
</Table>
<Table Name="ItemChildEquipment" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ChildItemId" Type="int" />
   <Field Name="ChildItemEquipSlot" Type="byte" />
</Table>
<Table Name="ItemClass" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ClassName" Type="string" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="PriceModifier" Type="float" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="ItemContextPickerEntry" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemCreationContext" Type="byte" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="PVal" Type="int" />
   <Field Name="Flags" Type="uint" />
   <Field Name="PlayerConditionId" Type="uint" />
</Table>
<Table Name="ItemCurrencyCost" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="int" />
</Table>
<Table Name="ItemDamageAmmo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemLevel" Type="ushort" />
   <Field Name="Quality" Type="float" ArraySize="7" />
</Table>
<Table Name="ItemDamageOneHand" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemLevel" Type="ushort" />
   <Field Name="Quality" Type="float" ArraySize="7" />
</Table>
<Table Name="ItemDamageOneHandCaster" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemLevel" Type="ushort" />
   <Field Name="Quality" Type="float" ArraySize="7" />
</Table>
<Table Name="ItemDamageTwoHand" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemLevel" Type="ushort" />
   <Field Name="Quality" Type="float" ArraySize="7" />
</Table>
<Table Name="ItemDamageTwoHandCaster" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemLevel" Type="ushort" />
   <Field Name="Quality" Type="float" ArraySize="7" />
</Table>
<Table Name="ItemDisenchantLoot" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Subclass" Type="byte" />
   <Field Name="Quality" Type="byte" />
   <Field Name="MinLevel" Type="ushort" />
   <Field Name="MaxLevel" Type="ushort" />
   <Field Name="SkillRequired" Type="ushort" />
   <Field Name="ExpansionId" Type="byte" />
</Table>
<Table Name="ItemDisplayInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ModelType1" Type="int" />
   <Field Name="ItemVisual" Type="int" />
   <Field Name="ParticleColorId" Type="int" />
   <Field Name="ItemRangedDisplayInfoId" Type="int" />
   <Field Name="OverrideSwooshSoundKitId" Type="int" />
   <Field Name="SheatheTransformMatrixId" Type="int" />
   <Field Name="StateSpellVisualKitId" Type="int" />
   <Field Name="SheathedSpellVisualKitId" Type="int" />
   <Field Name="UnsheathedSpellVisualKitId" Type="uint" />
   <Field Name="Flags" Type="int" />
   <Field Name="ModelResourcesId" Type="int" ArraySize="2" />
   <Field Name="ModelMaterialResourcesId" Type="int" ArraySize="2" />
   <Field Name="GeosetGroup" Type="int" ArraySize="6" />
   <Field Name="AttachmentGeosetGroup" Type="int" ArraySize="6" />
   <Field Name="HelmetGeosetVis" Type="int" ArraySize="2" />
</Table>
<Table Name="ItemDisplayInfoMaterialRes" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ComponentSection" Type="byte" />
   <Field Name="MaterialResourcesId" Type="int" />
</Table>
<Table Name="ItemDisplayXUiCamera" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemDisplayInfoId" Type="int" />
   <Field Name="UiCameraId" Type="ushort" />
</Table>
<Table Name="ItemEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="LegacySlotIndex" Type="byte" />
   <Field Name="TriggerType" Type="byte" />
   <Field Name="Charges" Type="ushort" />
   <Field Name="CoolDownMSec" Type="uint" />
   <Field Name="CategoryCoolDownMSec" Type="uint" />
   <Field Name="SpellCategoryId" Type="ushort" />
   <Field Name="SpellId" Type="int" />
   <Field Name="ChrSpecializationId" Type="ushort" />
</Table>
<Table Name="ItemExtendedCost" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RequiredArenaRating" Type="ushort" />
   <Field Name="ArenaBracket" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="MinFactionId" Type="byte" />
   <Field Name="MinReputation" Type="byte" />
   <Field Name="RequiredAchievement" Type="byte" />
   <Field Name="ItemId" Type="int" ArraySize="5" />
   <Field Name="ItemCount" Type="ushort" ArraySize="5" />
   <Field Name="CurrencyId" Type="ushort" ArraySize="5" />
   <Field Name="CurrencyCount" Type="int" ArraySize="5" />
</Table>
<Table Name="ItemGroupSounds" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Sound" Type="int" ArraySize="4" />
</Table>
<Table Name="ItemLevelSelector" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MinItemLevel" Type="ushort" />
   <Field Name="ItemLevelSelectorQualitySetId" Type="ushort" />
</Table>
<Table Name="ItemLevelSelectorQuality" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="QualityItemBonusListId" Type="int" />
   <Field Name="Quality" Type="byte" />
</Table>
<Table Name="ItemLevelSelectorQualitySet" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="IlvlRare" Type="short" />
   <Field Name="IlvlEpic" Type="short" />
</Table>
<Table Name="ItemLimitCategory" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Quantity" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="ItemLimitCategoryCondition" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AddQuantity" Type="byte" />
   <Field Name="PlayerConditionId" Type="uint" />
</Table>
<Table Name="ItemModifiedAppearance" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="int" />
   <Field Name="ItemAppearanceModifierId" Type="byte" />
   <Field Name="ItemAppearanceId" Type="ushort" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="TransmogSourceTypeEnum" Type="byte" />
</Table>
<Table Name="ItemModifiedAppearanceExtra" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="UnequippedIconFileDataId" Type="int" />
   <Field Name="SheatheType" Type="byte" />
   <Field Name="DisplayWeaponSubclassId" Type="byte" />
   <Field Name="DisplayInventoryType" Type="byte" />
</Table>
<Table Name="ItemNameDescription" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Description" Type="string" />
   <Field Name="Color" Type="uint" />
</Table>
<Table Name="ItemPetFood" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="ItemPriceBase" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemLevel" Type="ushort" />
   <Field Name="Armor" Type="float" />
   <Field Name="Weapon" Type="float" />
</Table>
<Table Name="ItemRandomProperties" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Enchantment" Type="ushort" ArraySize="5" />
</Table>
<Table Name="ItemRandomSuffix" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Enchantment" Type="ushort" ArraySize="5" />
   <Field Name="AllocationPct" Type="ushort" ArraySize="5" />
</Table>
<Table Name="ItemRangedDisplayInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CastSpellVisualId" Type="uint" />
   <Field Name="AutoAttackSpellVisualId" Type="uint" />
   <Field Name="QuiverFileDataId" Type="uint" />
   <Field Name="MissileSpellVisualEffectNameId" Type="int" />
</Table>
<Table Name="ItemSearchName" Build="26806">
   <Field Name="AllowableRace" Type="ulong" />
   <Field Name="Display" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="OverallQualityId" Type="byte" />
   <Field Name="ExpansionId" Type="byte" />
   <Field Name="MinFactionId" Type="ushort" />
   <Field Name="MinReputation" Type="byte" />
   <Field Name="AllowableClass" Type="int" />
   <Field Name="RequiredLevel" Type="byte" />
   <Field Name="RequiredSkill" Type="ushort" />
   <Field Name="RequiredSkillRank" Type="ushort" />
   <Field Name="RequiredAbility" Type="int" />
   <Field Name="ItemLevel" Type="ushort" />
   <Field Name="Flags" Type="uint" ArraySize="3" />
</Table>
<Table Name="ItemSet" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="SetFlags" Type="uint" />
   <Field Name="RequiredSkill" Type="uint" />
   <Field Name="RequiredSkillRank" Type="ushort" />
   <Field Name="ItemId" Type="int" ArraySize="17" />
</Table>
<Table Name="ItemSetSpell" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ChrSpecId" Type="ushort" />
   <Field Name="SpellId" Type="uint" />
   <Field Name="Threshold" Type="byte" />
</Table>
<Table Name="ItemSparse" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AllowableRace" Type="ulong" />
   <Field Name="Description" Type="string" />
   <Field Name="Display3" Type="string" />
   <Field Name="Display2" Type="string" />
   <Field Name="Display1" Type="string" />
   <Field Name="Display" Type="string" />
   <Field Name="DmgVariance" Type="float" />
   <Field Name="DurationInInventory" Type="int" />
   <Field Name="QualityModifier" Type="float" />
   <Field Name="BagFamily" Type="int" />
   <Field Name="ItemRange" Type="float" />
   <Field Name="StatPercentageOfSocket" Type="float" ArraySize="10" />
   <Field Name="StatPercentEditor" Type="int" ArraySize="10" />
   <Field Name="Stackable" Type="int" />
   <Field Name="MaxCount" Type="int" />
   <Field Name="RequiredAbility" Type="int" />
   <Field Name="SellPrice" Type="int" />
   <Field Name="BuyPrice" Type="int" />
   <Field Name="VendorStackCount" Type="int" />
   <Field Name="PriceVariance" Type="float" />
   <Field Name="PriceRandomValue" Type="float" />
   <Field Name="Flags" Type="uint" ArraySize="4" />
   <Field Name="Field_000017" Type="int" />
   <Field Name="ItemNameDescriptionId" Type="ushort" />
   <Field Name="RequiredTransmogHoliday" Type="ushort" />
   <Field Name="RequiredHoliday" Type="ushort" />
   <Field Name="LimitCategory" Type="ushort" />
   <Field Name="GemProperties" Type="ushort" />
   <Field Name="SocketMatchEnchantmentId" Type="ushort" />
   <Field Name="TotemCategoryId" Type="ushort" />
   <Field Name="InstanceBound" Type="ushort" />
   <Field Name="ZoneBound" Type="ushort" />
   <Field Name="ItemSet" Type="ushort" />
   <Field Name="ItemRandomSuffixGroupId" Type="ushort" />
   <Field Name="RandomSelect" Type="ushort" />
   <Field Name="LockId" Type="ushort" />
   <Field Name="StartQuestId" Type="ushort" />
   <Field Name="PageId" Type="ushort" />
   <Field Name="ItemDelay" Type="ushort" />
   <Field Name="ScalingStatDistributionId" Type="ushort" />
   <Field Name="MinFactionId" Type="ushort" />
   <Field Name="RequiredSkillRank" Type="ushort" />
   <Field Name="RequiredSkill" Type="ushort" />
   <Field Name="ItemLevel" Type="ushort" />
   <Field Name="AllowableClass" Type="ushort" />
   <Field Name="ExpansionId" Type="byte" />
   <Field Name="ArtifactId" Type="byte" />
   <Field Name="SpellWeight" Type="byte" />
   <Field Name="SpellWeightCategory" Type="byte" />
   <Field Name="SocketType" Type="byte" ArraySize="3" />
   <Field Name="SheatheType" Type="byte" />
   <Field Name="Material" Type="byte" />
   <Field Name="PageMaterialId" Type="byte" />
   <Field Name="LanguageId" Type="byte" />
   <Field Name="Bonding" Type="byte" />
   <Field Name="DamageDamageType" Type="byte" />
   <Field Name="StatModifierBonusStat" Type="byte" ArraySize="10" />
   <Field Name="ContainerSlots" Type="byte" />
   <Field Name="MinReputation" Type="byte" />
   <Field Name="RequiredPVPMedal" Type="byte" />
   <Field Name="RequiredPVPRank" Type="byte" />
   <Field Name="RequiredLevel" Type="byte" />
   <Field Name="InventoryType" Type="byte" />
   <Field Name="OverallQualityId" Type="byte" />
</Table>
<Table Name="ItemSpec" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MinLevel" Type="byte" />
   <Field Name="MaxLevel" Type="byte" />
   <Field Name="ItemType" Type="byte" />
   <Field Name="PrimaryStat" Type="byte" />
   <Field Name="SecondaryStat" Type="byte" />
   <Field Name="SpecializationId" Type="ushort" />
</Table>
<Table Name="ItemSpecOverride" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpecId" Type="ushort" />
</Table>
<Table Name="ItemSubClass" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DisplayName" Type="string" />
   <Field Name="VerboseName" Type="string" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="SubClassId" Type="byte" />
   <Field Name="AuctionHouseSortOrder" Type="byte" />
   <Field Name="PrerequisiteProficiency" Type="byte" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="DisplayFlags" Type="byte" />
   <Field Name="WeaponSwingSize" Type="byte" />
   <Field Name="PostrequisiteProficiency" Type="byte" />
</Table>
<Table Name="ItemSubClassMask" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="Mask" Type="uint" />
</Table>
<Table Name="ItemUpgrade" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemUpgradePathId" Type="byte" />
   <Field Name="ItemLevelIncrement" Type="byte" />
   <Field Name="PrerequisiteId" Type="ushort" />
   <Field Name="CurrencyType" Type="ushort" />
   <Field Name="CurrencyAmount" Type="int" />
</Table>
<Table Name="ItemVisuals" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ModelFileId" Type="int" ArraySize="5" />
</Table>
<Table Name="ItemXBonusTree" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemBonusTreeId" Type="ushort" />
</Table>
<Table Name="JournalEncounter" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="Map" Type="float" ArraySize="2" />
   <Field Name="JournalInstanceId" Type="ushort" />
   <Field Name="OrderIndex" Type="uint" />
   <Field Name="FirstSectionId" Type="ushort" />
   <Field Name="UiMapId" Type="ushort" />
   <Field Name="MapDisplayConditionId" Type="uint" />
   <Field Name="Flags" Type="byte" />
   <Field Name="DifficultyMask" Type="byte" />
</Table>
<Table Name="JournalEncounterCreature" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="JournalEncounterId" Type="ushort" />
   <Field Name="CreatureDisplayInfoId" Type="uint" />
   <Field Name="FileDataId" Type="int" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="UiModelSceneId" Type="uint" />
</Table>
<Table Name="JournalEncounterItem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="JournalEncounterId" Type="ushort" />
   <Field Name="ItemId" Type="uint" />
   <Field Name="FactionMask" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="DifficultyMask" Type="byte" />
</Table>
<Table Name="JournalEncounterSection" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Title" Type="string" />
   <Field Name="BodyText" Type="string" />
   <Field Name="JournalEncounterId" Type="ushort" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="ParentSectionId" Type="ushort" />
   <Field Name="FirstChildSectionId" Type="ushort" />
   <Field Name="NextSiblingSectionId" Type="ushort" />
   <Field Name="Type" Type="byte" />
   <Field Name="IconCreatureDisplayInfoId" Type="uint" />
   <Field Name="UiModelSceneId" Type="int" />
   <Field Name="SpellId" Type="int" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="IconFlags" Type="ushort" />
   <Field Name="DifficultyMask" Type="byte" />
</Table>
<Table Name="JournalEncounterXDifficulty" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
</Table>
<Table Name="JournalEncounterXMapLoc" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Map" Type="float" ArraySize="2" />
   <Field Name="JournalEncounterId" Type="int" />
   <Field Name="MapDisplayConditionId" Type="int" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="JournalInstance" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MapId" Type="ushort" />
   <Field Name="BackgroundFileDataId" Type="int" />
   <Field Name="ButtonFileDataId" Type="int" />
   <Field Name="ButtonSmallFileDataId" Type="int" />
   <Field Name="LoreFileDataId" Type="int" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="AreaId" Type="ushort" />
</Table>
<Table Name="JournalItemXDifficulty" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
</Table>
<Table Name="JournalSectionXDifficulty" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
</Table>
<Table Name="JournalTier" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="JournalTierXInstance" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="JournalTierId" Type="ushort" />
   <Field Name="JournalInstanceId" Type="ushort" />
</Table>
<Table Name="KeyChain" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Key" Type="byte" ArraySize="32" />
</Table>
<Table Name="KeystoneAffix" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="Filedataid" Type="int" />
   <Field Name="ID" Type="int" IsIndex="true" />
</Table>
<Table Name="Languages" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
</Table>
<Table Name="LanguageWords" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Word" Type="string" />
   <Field Name="LanguageId" Type="byte" />
</Table>
<Table Name="LfgDungeonExpansion" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ExpansionLevel" Type="byte" />
   <Field Name="RandomId" Type="ushort" />
   <Field Name="HardLevelMin" Type="byte" />
   <Field Name="HardLevelMax" Type="byte" />
   <Field Name="TargetLevelMin" Type="int" />
   <Field Name="TargetLevelMax" Type="int" />
</Table>
<Table Name="LfgDungeonGroup" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Typeid" Type="byte" />
   <Field Name="ParentGroupId" Type="byte" />
   <Field Name="OrderIndex" Type="ushort" />
</Table>
<Table Name="LfgDungeons" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="MinLevel" Type="byte" />
   <Field Name="MaxLevel" Type="ushort" />
   <Field Name="TypeId" Type="byte" />
   <Field Name="Subtype" Type="byte" />
   <Field Name="Faction" Type="byte" />
   <Field Name="IconTextureFileId" Type="int" />
   <Field Name="RewardsBgTextureFileId" Type="int" />
   <Field Name="PopupBgTextureFileId" Type="int" />
   <Field Name="ExpansionLevel" Type="byte" />
   <Field Name="MapId" Type="short" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="MinGear" Type="float" />
   <Field Name="GroupId" Type="byte" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="RequiredPlayerConditionId" Type="uint" />
   <Field Name="TargetLevel" Type="byte" />
   <Field Name="TargetLevelMin" Type="byte" />
   <Field Name="TargetLevelMax" Type="ushort" />
   <Field Name="RandomId" Type="ushort" />
   <Field Name="ScenarioId" Type="ushort" />
   <Field Name="FinalEncounterId" Type="ushort" />
   <Field Name="CountTank" Type="byte" />
   <Field Name="CountHealer" Type="byte" />
   <Field Name="CountDamage" Type="byte" />
   <Field Name="MinCountTank" Type="byte" />
   <Field Name="MinCountHealer" Type="byte" />
   <Field Name="MinCountDamage" Type="byte" />
   <Field Name="BonusReputationAmount" Type="ushort" />
   <Field Name="MentorItemLevel" Type="ushort" />
   <Field Name="MentorCharLevel" Type="byte" />
   <Field Name="Flags" Type="int" ArraySize="2" />
</Table>
<Table Name="LfgDungeonsGroupingMap" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RandomLfgDungeonsId" Type="ushort" />
   <Field Name="GroupId" Type="byte" />
</Table>
<Table Name="LfgRoleRequirement" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RoleType" Type="byte" />
   <Field Name="PlayerConditionId" Type="int" />
</Table>
<Table Name="Light" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GameCoords" Type="float" ArraySize="3" />
   <Field Name="GameFalloffStart" Type="float" />
   <Field Name="GameFalloffEnd" Type="float" />
   <Field Name="ContinentId" Type="ushort" />
   <Field Name="LightParamsId" Type="ushort" ArraySize="8" />
</Table>
<Table Name="LightData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="LightParamId" Type="ushort" />
   <Field Name="Time" Type="ushort" />
   <Field Name="DirectColor" Type="int" />
   <Field Name="AmbientColor" Type="int" />
   <Field Name="SkyTopColor" Type="int" />
   <Field Name="SkyMiddleColor" Type="int" />
   <Field Name="SkyBand1Color" Type="int" />
   <Field Name="SkyBand2Color" Type="int" />
   <Field Name="SkySmogColor" Type="int" />
   <Field Name="SkyFogColor" Type="int" />
   <Field Name="SunColor" Type="int" />
   <Field Name="CloudSunColor" Type="int" />
   <Field Name="CloudEmissiveColor" Type="int" />
   <Field Name="CloudLayer1AmbientColor" Type="int" />
   <Field Name="CloudLayer2AmbientColor" Type="int" />
   <Field Name="OceanCloseColor" Type="int" />
   <Field Name="OceanFarColor" Type="int" />
   <Field Name="RiverCloseColor" Type="int" />
   <Field Name="RiverFarColor" Type="int" />
   <Field Name="ShadowOpacity" Type="uint" />
   <Field Name="FogEnd" Type="float" />
   <Field Name="FogScaler" Type="float" />
   <Field Name="FogDensity" Type="float" />
   <Field Name="FogHeight" Type="float" />
   <Field Name="FogHeightScaler" Type="float" />
   <Field Name="FogHeightDensity" Type="float" />
   <Field Name="SunFogAngle" Type="float" />
   <Field Name="CloudDensity" Type="float" />
   <Field Name="ColorGradingFileDataId" Type="int" />
   <Field Name="HorizonAmbientColor" Type="int" />
   <Field Name="GroundAmbientColor" Type="int" />
   <Field Name="EndFogColor" Type="uint" />
   <Field Name="EndFogColorDistance" Type="float" />
   <Field Name="SunFogColor" Type="int" />
   <Field Name="Field_0023" Type="float" />
   <Field Name="FogHeightColor" Type="uint" />
</Table>
<Table Name="Lightning" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BoltDirection" Type="float" ArraySize="2" />
   <Field Name="Field_002" Type="int" ArraySize="3" />
   <Field Name="BoltDirectionVariance" Type="float" />
   <Field Name="MinDivergence" Type="float" />
   <Field Name="MaxDivergence" Type="float" />
   <Field Name="MinConvergenceSpeed" Type="float" />
   <Field Name="MaxConvergenceSpeed" Type="float" />
   <Field Name="SegmentSize" Type="float" />
   <Field Name="MinBoltWidth" Type="float" />
   <Field Name="MaxBoltWidth" Type="float" />
   <Field Name="MinBoltHeight" Type="float" />
   <Field Name="MaxBoltHeight" Type="float" />
   <Field Name="MaxSegmentCount" Type="int" />
   <Field Name="MinStrikeTime" Type="float" />
   <Field Name="MaxStrikeTime" Type="float" />
   <Field Name="MinEndTime" Type="float" />
   <Field Name="MaxEndTime" Type="float" />
   <Field Name="MinFadeTime" Type="float" />
   <Field Name="MaxFadeTime" Type="float" />
   <Field Name="FlashColor" Type="uint" />
   <Field Name="BoltColor" Type="int" />
   <Field Name="Brightness" Type="float" />
   <Field Name="MinCloudDepth" Type="float" />
   <Field Name="MaxCloudDepth" Type="float" />
   <Field Name="MinFadeInStrength" Type="float" />
   <Field Name="MaxFadeInStrength" Type="float" />
   <Field Name="MinStrikeStrength" Type="float" />
   <Field Name="MaxStrikeStrength" Type="float" />
   <Field Name="GroundBrightnessScalar" Type="float" />
   <Field Name="BoltBrightnessScalar" Type="float" />
   <Field Name="CloudBrightnessScalar" Type="float" />
   <Field Name="SoundEmitterDistance" Type="float" />
</Table>
<Table Name="LightParams" Build="26806">
   <Field Name="OverrideCelestialSphere" Type="float" ArraySize="3" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="HighlightSky" Type="byte" />
   <Field Name="LightSkyboxId" Type="ushort" />
   <Field Name="CloudTypeId" Type="byte" />
   <Field Name="Glow" Type="float" />
   <Field Name="WaterShallowAlpha" Type="float" />
   <Field Name="WaterDeepAlpha" Type="float" />
   <Field Name="OceanShallowAlpha" Type="float" />
   <Field Name="OceanDeepAlpha" Type="float" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Field_0B" Type="uint" />
</Table>
<Table Name="LightSkybox" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Flags" Type="byte" />
   <Field Name="SkyboxFileDataId" Type="int" />
   <Field Name="CelestialSkyboxFileDataId" Type="int" />
</Table>
<Table Name="LiquidMaterial" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="byte" />
   <Field Name="LVF" Type="byte" />
</Table>
<Table Name="LiquidObject" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FlowDirection" Type="float" />
   <Field Name="FlowSpeed" Type="float" />
   <Field Name="LiquidTypeId" Type="ushort" />
   <Field Name="Fishable" Type="byte" />
   <Field Name="Reflection" Type="byte" />
</Table>
<Table Name="LiquidType" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Texture" Type="string" ArraySize="6" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="SoundBank" Type="byte" />
   <Field Name="SoundId" Type="uint" />
   <Field Name="SpellId" Type="int" />
   <Field Name="MaxDarkenDepth" Type="float" />
   <Field Name="FogDarkenIntensity" Type="float" />
   <Field Name="AmbDarkenIntensity" Type="float" />
   <Field Name="DirDarkenIntensity" Type="float" />
   <Field Name="LightId" Type="ushort" />
   <Field Name="ParticleScale" Type="float" />
   <Field Name="ParticleMovement" Type="byte" />
   <Field Name="ParticleTexSlots" Type="byte" />
   <Field Name="MaterialId" Type="byte" />
   <Field Name="MinimapStaticCol" Type="uint" />
   <Field Name="FrameCountTexture" Type="byte" ArraySize="6" />
   <Field Name="Color" Type="int" ArraySize="2" />
   <Field Name="Float" Type="float" ArraySize="18" />
   <Field Name="Int" Type="int" ArraySize="4" />
   <Field Name="Coefficient" Type="float" ArraySize="4" />
</Table>
<Table Name="LoadingScreens" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="NarrowScreenFileDataId" Type="int" />
   <Field Name="WideScreenFileDataId" Type="int" />
   <Field Name="WideScreen169FileDataId" Type="int" />
</Table>
<Table Name="LoadingScreenTaxiSplines" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PathId" Type="ushort" />
   <Field Name="LegIndex" Type="byte" />
   <Field Name="LoadingScreenId" Type="ushort" />
   <Field Name="LocX" Type="float" ArraySize="10" />
   <Field Name="LocY" Type="float" ArraySize="10" />
</Table>
<Table Name="Locale" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="WowLocale" Type="byte" />
   <Field Name="FontFileDataId" Type="int" />
   <Field Name="ClientDisplayExpansion" Type="byte" />
   <Field Name="Secondary" Type="byte" />
</Table>
<Table Name="Location" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Pos" Type="float" ArraySize="3" />
   <Field Name="Rot" Type="float" ArraySize="3" />
</Table>
<Table Name="Lock" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Index" Type="int" ArraySize="8" />
   <Field Name="Skill" Type="ushort" ArraySize="8" />
   <Field Name="Type" Type="byte" ArraySize="8" />
   <Field Name="Action" Type="byte" ArraySize="8" />
</Table>
<Table Name="LockType" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ResourceName" Type="string" />
   <Field Name="Verb" Type="string" />
   <Field Name="CursorName" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
</Table>
<Table Name="LookAtController" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ReactionEnableDistance" Type="float" />
   <Field Name="ReactionWarmUpTimeMSMin" Type="uint" />
   <Field Name="ReactionWarmUpTimeMSMax" Type="uint" />
   <Field Name="ReactionEnableFOVDeg" Type="ushort" />
   <Field Name="ReactionGiveupDistance" Type="float" />
   <Field Name="ReactionGiveupFOVDeg" Type="uint" />
   <Field Name="ReactionGiveupTimeMS" Type="ushort" />
   <Field Name="ReactionIgnoreTimeMinMS" Type="ushort" />
   <Field Name="ReactionIgnoreTimeMaxMS" Type="ushort" />
   <Field Name="MaxTorsoYaw" Type="byte" />
   <Field Name="MaxTorsoYawWhileMoving" Type="byte" />
   <Field Name="MaxTorsoPitchUp" Type="uint" />
   <Field Name="MaxTorsoPitchDown" Type="int" />
   <Field Name="MaxHeadYaw" Type="byte" />
   <Field Name="MaxHeadPitch" Type="byte" />
   <Field Name="TorsoSpeedFactor" Type="float" />
   <Field Name="HeadSpeedFactor" Type="float" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="MailTemplate" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Body" Type="string" />
</Table>
<Table Name="ManagedWorldState" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CurrentStageWorldStateId" Type="int" />
   <Field Name="ProgressWorldStateId" Type="int" />
   <Field Name="UpTimeSecs" Type="uint" />
   <Field Name="DownTimeSecs" Type="int" />
   <Field Name="OccurrencesWorldStateId" Type="int" />
   <Field Name="AccumulationStateTargetValue" Type="int" />
   <Field Name="DepletionStateTargetValue" Type="int" />
   <Field Name="AccumulationAmountPerMinute" Type="int" />
   <Field Name="DepletionAmountPerMinute" Type="int" />
</Table>
<Table Name="ManagedWorldStateBuff" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BuffSpellId" Type="int" />
   <Field Name="PlayerConditionId" Type="int" />
   <Field Name="OccurrenceValue" Type="uint" />
</Table>
<Table Name="ManagedWorldStateInput" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ManagedWorldStateId" Type="int" />
   <Field Name="QuestId" Type="int" />
   <Field Name="ValidInputConditionId" Type="int" />
</Table>
<Table Name="ManifestInterfaceActionIcon" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
</Table>
<Table Name="ManifestInterfaceData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FilePath" Type="string" />
   <Field Name="FileName" Type="string" />
</Table>
<Table Name="ManifestInterfaceItemIcon" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
</Table>
<Table Name="ManifestInterfaceTOCData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FilePath" Type="string" />
</Table>
<Table Name="ManifestMP3" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
</Table>
<Table Name="Map" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Directory" Type="string" />
   <Field Name="MapName" Type="string" />
   <Field Name="MapDescription0" Type="string" />
   <Field Name="MapDescription1" Type="string" />
   <Field Name="PvpShortDescription" Type="string" />
   <Field Name="PvpLongDescription" Type="string" />
   <Field Name="Corpse" Type="float" ArraySize="2" />
   <Field Name="MapType" Type="byte" />
   <Field Name="InstanceType" Type="byte" />
   <Field Name="ExpansionId" Type="byte" />
   <Field Name="AreaTableId" Type="ushort" />
   <Field Name="LoadingScreenId" Type="short" />
   <Field Name="TimeOfDayOverride" Type="short" />
   <Field Name="ParentMapId" Type="short" />
   <Field Name="CosmeticParentMapId" Type="ushort" />
   <Field Name="TimeOffset" Type="byte" />
   <Field Name="MinimapIconScale" Type="float" />
   <Field Name="CorpseMapId" Type="short" />
   <Field Name="MaxPlayers" Type="byte" />
   <Field Name="WindSettingsId" Type="short" />
   <Field Name="ZmpFileDataId" Type="int" />
   <Field Name="Flags" Type="uint" ArraySize="2" />
</Table>
<Table Name="MapCelestialBody" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CelestialBodyId" Type="short" />
   <Field Name="PlayerConditionId" Type="uint" />
</Table>
<Table Name="MapChallengeMode" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MapId" Type="ushort" />
   <Field Name="Flags" Type="byte" />
   <Field Name="CriteriaCount" Type="ushort" ArraySize="3" />
</Table>
<Table Name="MapDifficulty" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Message" Type="string" />
   <Field Name="ItemContextPickerId" Type="int" />
   <Field Name="Field_03" Type="int" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="LockId" Type="byte" />
   <Field Name="ResetInterval" Type="byte" />
   <Field Name="MaxPlayers" Type="byte" />
   <Field Name="ItemContext" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="MapDifficultyXCondition" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FailureDescription" Type="string" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="OrderIndex" Type="int" />
</Table>
<Table Name="MapLoadingScreen" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Min" Type="float" ArraySize="2" />
   <Field Name="Max" Type="float" ArraySize="2" />
   <Field Name="LoadingScreenId" Type="int" />
   <Field Name="OrderIndex" Type="int" />
</Table>
<Table Name="MarketingPromotionsXLocale" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AcceptURL" Type="string" />
   <Field Name="PromotionId" Type="byte" />
   <Field Name="LocaleId" Type="byte" />
   <Field Name="AdTexture" Type="int" />
   <Field Name="LogoTexture" Type="int" />
   <Field Name="AcceptButtonTexture" Type="int" />
   <Field Name="DeclineButtonTexture" Type="int" />
</Table>
<Table Name="Material" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="byte" />
   <Field Name="FoleySoundId" Type="uint" />
   <Field Name="SheatheSoundId" Type="uint" />
   <Field Name="UnsheatheSoundId" Type="uint" />
</Table>
<Table Name="MinorTalent" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="OrderIndex" Type="int" />
</Table>
<Table Name="MissileTargeting" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="TurnLingering" Type="float" />
   <Field Name="PitchLingering" Type="float" />
   <Field Name="MouseLingering" Type="float" />
   <Field Name="EndOpacity" Type="float" />
   <Field Name="ArcSpeed" Type="float" />
   <Field Name="ArcRepeat" Type="float" />
   <Field Name="ArcWidth" Type="float" />
   <Field Name="ImpactTexRadius" Type="float" />
   <Field Name="ArcTextureFileId" Type="int" />
   <Field Name="ImpactTextureFileId" Type="int" />
   <Field Name="ImpactRadius" Type="float" ArraySize="2" />
   <Field Name="ImpactModelFileId" Type="int" ArraySize="2" />
</Table>
<Table Name="ModelAnimCloakDampening" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AnimationDataId" Type="uint" />
   <Field Name="CloakDampeningId" Type="uint" />
</Table>
<Table Name="ModelFileData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="byte" />
   <Field Name="LodCount" Type="byte" />
   <Field Name="ModelResourcesId" Type="uint" />
</Table>
<Table Name="ModelRibbonQuality" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RibbonQualityId" Type="byte" />
</Table>
<Table Name="ModifierTree" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Parent" Type="uint" />
   <Field Name="Operator" Type="byte" />
   <Field Name="Amount" Type="byte" />
   <Field Name="Type" Type="byte" />
   <Field Name="Asset" Type="uint" />
   <Field Name="SecondaryAsset" Type="int" />
   <Field Name="TertiaryAsset" Type="byte" />
</Table>
<Table Name="Mount" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="SourceText" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MountTypeId" Type="ushort" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="SourceTypeEnum" Type="byte" />
   <Field Name="SourceSpellId" Type="int" />
   <Field Name="PlayerConditionId" Type="int" />
   <Field Name="MountFlyRideHeight" Type="float" />
   <Field Name="UiModelSceneId" Type="int" />
</Table>
<Table Name="MountCapability" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="byte" />
   <Field Name="ReqRidingSkill" Type="ushort" />
   <Field Name="ReqAreaId" Type="ushort" />
   <Field Name="ReqSpellAuraId" Type="int" />
   <Field Name="ReqSpellKnownId" Type="int" />
   <Field Name="ModSpellAuraId" Type="int" />
   <Field Name="ReqMapId" Type="ushort" />
</Table>
<Table Name="MountTypeXCapability" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MountTypeId" Type="ushort" />
   <Field Name="MountCapabilityId" Type="ushort" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="MountXDisplay" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CreatureDisplayInfoId" Type="int" />
   <Field Name="PlayerConditionId" Type="int" />
</Table>
<Table Name="Movie" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Volume" Type="byte" />
   <Field Name="KeyId" Type="byte" />
   <Field Name="AudioFileDataId" Type="uint" />
   <Field Name="SubtitleFileDataId" Type="uint" />
</Table>
<Table Name="MovieFileData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Resolution" Type="ushort" />
</Table>
<Table Name="MovieVariation" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FileDataId" Type="uint" />
   <Field Name="OverlayFileDataId" Type="uint" />
</Table>
<Table Name="MultiStateProperties" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Offset" Type="float" ArraySize="3" />
   <Field Name="GameObjectId" Type="int" />
   <Field Name="StateIndex" Type="byte" />
   <Field Name="GameEventId" Type="int" />
   <Field Name="Facing" Type="float" />
   <Field Name="TransitionInId" Type="int" />
   <Field Name="TransitionOutId" Type="int" />
   <Field Name="CollisionHull" Type="int" />
   <Field Name="Flags" Type="uint" />
</Table>
<Table Name="MultiTransitionProperties" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="TransitionType" Type="uint" />
   <Field Name="DurationMS" Type="uint" />
   <Field Name="Field_03" Type="uint" />
   <Field Name="Field_04" Type="uint" />
   <Field Name="Field_05" Type="uint" />
</Table>
<Table Name="MythicPlusSeasonRewardLevels" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Field_01" Type="int" />
   <Field Name="Field_02" Type="int" />
   <Field Name="Field_03" Type="int" />
</Table>
<Table Name="NameGen" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="RaceId" Type="byte" />
   <Field Name="Sex" Type="byte" />
</Table>
<Table Name="NamesProfanity" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Language" Type="byte" />
</Table>
<Table Name="NamesReserved" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="NamesReservedLocale" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="LocaleMask" Type="byte" />
</Table>
<Table Name="NpcModelItemSlotDisplayInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemDisplayInfoId" Type="int" />
   <Field Name="ItemSlot" Type="byte" />
</Table>
<Table Name="NPCSounds" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundId" Type="int" ArraySize="4" />
</Table>
<Table Name="NumTalentsAtLevel" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="NumTalents" Type="int" />
   <Field Name="NumTalentsDeathKnight" Type="int" />
   <Field Name="NumTalentsDemonHunter" Type="int" />
</Table>
<Table Name="ObjectEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Offset" Type="float" ArraySize="3" />
   <Field Name="ObjectEffectGroupId" Type="ushort" />
   <Field Name="TriggerType" Type="byte" />
   <Field Name="EventType" Type="byte" />
   <Field Name="EffectRecType" Type="byte" />
   <Field Name="EffectRecId" Type="int" />
   <Field Name="Attachment" Type="byte" />
   <Field Name="ObjectEffectModifierId" Type="int" />
</Table>
<Table Name="ObjectEffectModifier" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Param" Type="float" ArraySize="4" />
   <Field Name="InputType" Type="byte" />
   <Field Name="MapType" Type="byte" />
   <Field Name="OutputType" Type="byte" />
</Table>
<Table Name="ObjectEffectPackageElem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ObjectEffectPackageId" Type="ushort" />
   <Field Name="ObjectEffectGroupId" Type="ushort" />
   <Field Name="StateType" Type="ushort" />
</Table>
<Table Name="Occluder" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Field_01" Type="int" />
   <Field Name="Field_02" Type="byte" />
   <Field Name="Field_03" Type="byte" />
   <Field Name="Field_04" Type="byte" />
   <Field Name="Field_05" Type="byte" />
   <Field Name="Field_06" Type="byte" />
   <Field Name="Field_07" Type="byte" />
   <Field Name="Field_08" Type="byte" />
</Table>
<Table Name="OccluderLocation" Build="26806">
   <Field Name="Field_00" Type="float" ArraySize="3" />
   <Field Name="Field_01" Type="float" ArraySize="3" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Field_03" Type="int" />
</Table>
<Table Name="OccluderNode" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Field_01" Type="ushort" />
   <Field Name="Field_02" Type="short" />
   <Field Name="Field_03" Type="int" />
</Table>
<Table Name="OutlineEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PassiveHighlightColorId" Type="uint" />
   <Field Name="HighlightColorId" Type="uint" />
   <Field Name="Priority" Type="int" />
   <Field Name="Flags" Type="int" />
   <Field Name="Range" Type="float" />
   <Field Name="UnitConditionId" Type="int" ArraySize="2" />
</Table>
<Table Name="OverrideSpellData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Spells" Type="int" ArraySize="10" />
   <Field Name="PlayerActionBarFileDataId" Type="uint" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="PageTextMaterial" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="PaperDollItemFrame" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemButtonName" Type="string" />
   <Field Name="SlotIconFileId" Type="int" />
   <Field Name="SlotNumber" Type="byte" />
</Table>
<Table Name="ParagonReputation" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FactionId" Type="uint" />
   <Field Name="LevelThreshold" Type="int" />
   <Field Name="QuestId" Type="int" />
</Table>
<Table Name="ParticleColor" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Start" Type="uint" ArraySize="3" />
   <Field Name="Mid" Type="uint" ArraySize="3" />
   <Field Name="End" Type="int" ArraySize="3" />
</Table>
<Table Name="ParticulateSound" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Field_01" Type="int" />
   <Field Name="Field_02" Type="int" />
   <Field Name="Field_03" Type="int" />
   <Field Name="Field_04" Type="int" />
   <Field Name="Field_05" Type="int" />
</Table>
<Table Name="Path" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Type" Type="byte" />
   <Field Name="SplineType" Type="byte" />
   <Field Name="Red" Type="byte" />
   <Field Name="Green" Type="byte" />
   <Field Name="Blue" Type="byte" />
   <Field Name="Alpha" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="PathNode" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PathId" Type="ushort" />
   <Field Name="Sequence" Type="ushort" />
   <Field Name="LocationId" Type="int" />
</Table>
<Table Name="PathNodeProperty" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PathId" Type="ushort" />
   <Field Name="Sequence" Type="ushort" />
   <Field Name="PropertyIndex" Type="byte" />
   <Field Name="Value" Type="int" />
</Table>
<Table Name="PathProperty" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PathId" Type="ushort" />
   <Field Name="PropertyIndex" Type="byte" />
   <Field Name="Value" Type="int" />
</Table>
<Table Name="Phase" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="ushort" />
</Table>
<Table Name="PhaseShiftZoneSounds" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AreaId" Type="ushort" />
   <Field Name="WmoAreaId" Type="byte" />
   <Field Name="PhaseId" Type="ushort" />
   <Field Name="PhaseGroupId" Type="ushort" />
   <Field Name="PhaseUseFlags" Type="byte" />
   <Field Name="ZoneIntroMusicId" Type="uint" />
   <Field Name="ZoneMusicId" Type="uint" />
   <Field Name="SoundAmbienceId" Type="ushort" />
   <Field Name="SoundProviderPreferencesId" Type="byte" />
   <Field Name="UwZoneIntroMusicId" Type="uint" />
   <Field Name="UwZoneMusicId" Type="uint" />
   <Field Name="UwSoundAmbienceId" Type="ushort" />
   <Field Name="UwSoundProviderPreferencesId" Type="byte" />
</Table>
<Table Name="PhaseXPhaseGroup" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PhaseId" Type="ushort" />
</Table>
<Table Name="PlayerCondition" Build="26806">
   <Field Name="RaceMask" Type="ulong" />
   <Field Name="FailureDescription" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MinLevel" Type="ushort" />
   <Field Name="MaxLevel" Type="ushort" />
   <Field Name="ClassMask" Type="int" />
   <Field Name="SkillLogic" Type="int" />
   <Field Name="LanguageId" Type="byte" />
   <Field Name="MinLanguage" Type="byte" />
   <Field Name="MaxLanguage" Type="int" />
   <Field Name="MaxFactionId" Type="ushort" />
   <Field Name="MaxReputation" Type="byte" />
   <Field Name="ReputationLogic" Type="int" />
   <Field Name="CurrentPvpFaction" Type="byte" />
   <Field Name="PvpMedal" Type="byte" />
   <Field Name="PrevQuestLogic" Type="int" />
   <Field Name="CurrQuestLogic" Type="int" />
   <Field Name="CurrentCompletedQuestLogic" Type="int" />
   <Field Name="SpellLogic" Type="int" />
   <Field Name="ItemLogic" Type="int" />
   <Field Name="ItemFlags" Type="byte" />
   <Field Name="AuraSpellLogic" Type="int" />
   <Field Name="WorldStateExpressionId" Type="ushort" />
   <Field Name="WeatherId" Type="byte" />
   <Field Name="PartyStatus" Type="byte" />
   <Field Name="LifetimeMaxPVPRank" Type="byte" />
   <Field Name="AchievementLogic" Type="int" />
   <Field Name="Gender" Type="byte" />
   <Field Name="NativeGender" Type="byte" />
   <Field Name="AreaLogic" Type="int" />
   <Field Name="LfgLogic" Type="int" />
   <Field Name="CurrencyLogic" Type="int" />
   <Field Name="QuestKillId" Type="ushort" />
   <Field Name="QuestKillLogic" Type="int" />
   <Field Name="MinExpansionLevel" Type="byte" />
   <Field Name="MaxExpansionLevel" Type="byte" />
   <Field Name="MinAvgItemLevel" Type="int" />
   <Field Name="MaxAvgItemLevel" Type="int" />
   <Field Name="MinAvgEquippedItemLevel" Type="ushort" />
   <Field Name="MaxAvgEquippedItemLevel" Type="ushort" />
   <Field Name="PhaseUseFlags" Type="byte" />
   <Field Name="PhaseId" Type="ushort" />
   <Field Name="PhaseGroupId" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="ChrSpecializationIndex" Type="byte" />
   <Field Name="ChrSpecializationRole" Type="byte" />
   <Field Name="ModifierTreeId" Type="int" />
   <Field Name="PowerType" Type="byte" />
   <Field Name="PowerTypeComp" Type="byte" />
   <Field Name="PowerTypeValue" Type="byte" />
   <Field Name="WeaponSubclassMask" Type="int" />
   <Field Name="MaxGuildLevel" Type="byte" />
   <Field Name="MinGuildLevel" Type="byte" />
   <Field Name="MaxExpansionTier" Type="byte" />
   <Field Name="MinExpansionTier" Type="byte" />
   <Field Name="MinPVPRank" Type="byte" />
   <Field Name="MaxPVPRank" Type="byte" />
   <Field Name="SkillId" Type="ushort" ArraySize="4" />
   <Field Name="MinSkill" Type="ushort" ArraySize="4" />
   <Field Name="MaxSkill" Type="ushort" ArraySize="4" />
   <Field Name="MinFactionId" Type="int" ArraySize="3" />
   <Field Name="MinReputation" Type="byte" ArraySize="3" />
   <Field Name="PrevQuestId" Type="ushort" ArraySize="4" />
   <Field Name="CurrQuestId" Type="ushort" ArraySize="4" />
   <Field Name="CurrentCompletedQuestId" Type="ushort" ArraySize="4" />
   <Field Name="SpellId" Type="int" ArraySize="4" />
   <Field Name="ItemId" Type="int" ArraySize="4" />
   <Field Name="ItemCount" Type="int" ArraySize="4" />
   <Field Name="Explored" Type="ushort" ArraySize="2" />
   <Field Name="Time" Type="int" ArraySize="2" />
   <Field Name="AuraSpellId" Type="int" ArraySize="4" />
   <Field Name="AuraStacks" Type="byte" ArraySize="4" />
   <Field Name="Achievement" Type="ushort" ArraySize="4" />
   <Field Name="AreaId" Type="ushort" ArraySize="4" />
   <Field Name="LfgStatus" Type="byte" ArraySize="4" />
   <Field Name="LfgCompare" Type="byte" ArraySize="4" />
   <Field Name="LfgValue" Type="int" ArraySize="4" />
   <Field Name="CurrencyId" Type="int" ArraySize="4" />
   <Field Name="CurrencyCount" Type="int" ArraySize="4" />
   <Field Name="QuestKillMonster" Type="int" ArraySize="6" />
   <Field Name="MovementFlags" Type="int" ArraySize="2" />
</Table>
<Table Name="Positioner" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FirstStateId" Type="ushort" />
   <Field Name="Flags" Type="byte" />
   <Field Name="StartLife" Type="float" />
   <Field Name="StartLifePercent" Type="byte" />
</Table>
<Table Name="PositionerState" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="NextStateId" Type="uint" />
   <Field Name="TransformMatrixId" Type="uint" />
   <Field Name="PosEntryId" Type="uint" />
   <Field Name="RotEntryId" Type="uint" />
   <Field Name="ScaleEntryId" Type="uint" />
   <Field Name="Flags" Type="uint" />
   <Field Name="EndLife" Type="float" />
   <Field Name="EndLifePercent" Type="byte" />
</Table>
<Table Name="PositionerStateEntry" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ParamA" Type="float" />
   <Field Name="ParamB" Type="float" />
   <Field Name="CurveId" Type="uint" />
   <Field Name="SrcValType" Type="ushort" />
   <Field Name="SrcVal" Type="ushort" />
   <Field Name="DstValType" Type="ushort" />
   <Field Name="DstVal" Type="ushort" />
   <Field Name="EntryType" Type="byte" />
   <Field Name="Style" Type="byte" />
   <Field Name="SrcType" Type="byte" />
   <Field Name="DstType" Type="byte" />
</Table>
<Table Name="PowerDisplay" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="GlobalStringBaseTag" Type="string" />
   <Field Name="ActualType" Type="byte" />
   <Field Name="Red" Type="byte" />
   <Field Name="Green" Type="byte" />
   <Field Name="Blue" Type="byte" />
</Table>
<Table Name="PowerType" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="NameGlobalStringTag" Type="string" />
   <Field Name="CostGlobalStringTag" Type="string" />
   <Field Name="PowerTypeEnum" Type="byte" />
   <Field Name="MinPower" Type="byte" />
   <Field Name="MaxBasePower" Type="short" />
   <Field Name="CenterPower" Type="byte" />
   <Field Name="DefaultPower" Type="byte" />
   <Field Name="DisplayModifier" Type="byte" />
   <Field Name="RegenInterruptTimeMS" Type="short" />
   <Field Name="RegenPeace" Type="float" />
   <Field Name="RegenCombat" Type="float" />
   <Field Name="Flags" Type="short" />
</Table>
<Table Name="PrestigeLevelInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="HonorLevel" Type="int" />
   <Field Name="BadgeTextureFileDataId" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Field_05" Type="int" />
</Table>
<Table Name="PvpBracketTypes" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BracketId" Type="byte" />
   <Field Name="WeeklyQuestId" Type="int" ArraySize="4" />
</Table>
<Table Name="PvpDifficulty" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RangeIndex" Type="byte" />
   <Field Name="MinLevel" Type="byte" />
   <Field Name="MaxLevel" Type="byte" />
</Table>
<Table Name="PvpItem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="int" />
   <Field Name="ItemLevelDelta" Type="byte" />
</Table>
<Table Name="PvpScalingEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpecializationId" Type="int" />
   <Field Name="PvpScalingEffectTypeId" Type="int" />
   <Field Name="Value" Type="float" />
</Table>
<Table Name="PvpScalingEffectType" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="PvpTalent" Build="26806">
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpecId" Type="int" />
   <Field Name="SpellId" Type="int" />
   <Field Name="OverridesSpellId" Type="int" />
   <Field Name="Flags" Type="int" />
   <Field Name="ActionBarSpellId" Type="int" />
   <Field Name="PvpTalentCategoryId" Type="int" />
   <Field Name="LevelRequired" Type="int" />
</Table>
<Table Name="PvpTalentCategory" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="TalentSlotMask" Type="byte" />
</Table>
<Table Name="PvpTalentSlotUnlock" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Slot" Type="byte" />
   <Field Name="LevelRequired" Type="int" />
   <Field Name="DeathKnightLevelRequired" Type="int" />
   <Field Name="DemonHunterLevelRequired" Type="int" />
</Table>
<Table Name="PvpTier" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="DescendRating" Type="short" />
   <Field Name="AscendRating" Type="short" />
   <Field Name="DescendTier" Type="int" />
   <Field Name="AscendTier" Type="int" />
   <Field Name="Type" Type="byte" />
   <Field Name="TierEnumId" Type="byte" />
   <Field Name="TierIconFileDataId" Type="int" />
</Table>
<Table Name="QuestFactionReward" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Difficulty" Type="ushort" ArraySize="10" />
</Table>
<Table Name="QuestFeedbackEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FileDataId" Type="uint" />
   <Field Name="MinimapAtlasMemberId" Type="ushort" />
   <Field Name="AttachPoint" Type="byte" />
   <Field Name="PassiveHighlightColorType" Type="byte" />
   <Field Name="Priority" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="QuestInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="InfoName" Type="string" />
   <Field Name="Type" Type="byte" />
   <Field Name="Modifiers" Type="byte" />
   <Field Name="Profession" Type="ushort" />
</Table>
<Table Name="QuestLine" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="QuestId" Type="int" />
</Table>
<Table Name="QuestLineXQuest" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="QuestLineId" Type="uint" />
   <Field Name="QuestId" Type="uint" />
   <Field Name="OrderIndex" Type="int" />
</Table>
<Table Name="QuestMoneyReward" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Difficulty" Type="int" ArraySize="10" />
</Table>
<Table Name="QuestObjective" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Description" Type="string" />
   <Field Name="Type" Type="byte" />
   <Field Name="Amount" Type="int" />
   <Field Name="ObjectId" Type="int" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="StorageIndex" Type="byte" />
</Table>
<Table Name="QuestPackageItem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PackageId" Type="ushort" />
   <Field Name="ItemId" Type="int" />
   <Field Name="ItemQuantity" Type="uint" />
   <Field Name="DisplayType" Type="byte" />
</Table>
<Table Name="QuestPOIBlob" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MapId" Type="short" />
   <Field Name="UiMapId" Type="int" />
   <Field Name="NumPoints" Type="byte" />
   <Field Name="QuestId" Type="uint" />
   <Field Name="ObjectiveIndex" Type="int" />
   <Field Name="PlayerConditionId" Type="int" />
</Table>
<Table Name="QuestPOIPoint" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="X" Type="short" />
   <Field Name="Y" Type="short" />
</Table>
<Table Name="QuestSort" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SortName" Type="string" />
   <Field Name="UiOrderIndex" Type="byte" />
</Table>
<Table Name="QuestV2" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UniqueBitFlag" Type="ushort" />
</Table>
<Table Name="QuestV2CliTask" Build="26806">
   <Field Name="FiltRaces" Type="ulong" />
   <Field Name="QuestTitle" Type="string" />
   <Field Name="BulletText" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UniqueBitFlag" Type="ushort" />
   <Field Name="ConditionId" Type="uint" />
   <Field Name="FiltActiveQuest" Type="int" />
   <Field Name="FiltClasses" Type="ushort" />
   <Field Name="FiltCompletedQuestLogic" Type="uint" />
   <Field Name="FiltMaxFactionId" Type="int" />
   <Field Name="FiltMaxFactionValue" Type="int" />
   <Field Name="FiltMaxLevel" Type="uint" />
   <Field Name="FiltMinFactionId" Type="int" />
   <Field Name="FiltMinFactionValue" Type="int" />
   <Field Name="FiltMinLevel" Type="uint" />
   <Field Name="FiltMinSkillId" Type="uint" />
   <Field Name="FiltMinSkillValue" Type="int" />
   <Field Name="FiltNonActiveQuest" Type="int" />
   <Field Name="BreadCrumbId" Type="int" />
   <Field Name="StartItem" Type="int" />
   <Field Name="WorldStateExpressionId" Type="ushort" />
   <Field Name="QuestInfoId" Type="int" />
   <Field Name="ContentTuningId" Type="int" />
   <Field Name="FiltCompletedQuest" Type="int" ArraySize="3" />
</Table>
<Table Name="QuestXGroupActivity" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="QuestId" Type="uint" />
   <Field Name="GroupFinderActivityId" Type="uint" />
</Table>
<Table Name="QuestXP" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Difficulty" Type="ushort" ArraySize="10" />
</Table>
<Table Name="RandPropPoints" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DamageReplaceStat" Type="int" />
   <Field Name="Epic" Type="int" ArraySize="5" />
   <Field Name="Superior" Type="int" ArraySize="5" />
   <Field Name="Good" Type="int" ArraySize="5" />
</Table>
<Table Name="RelicSlotTierRequirement" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RelicIndex" Type="byte" />
   <Field Name="RelicTier" Type="byte" />
   <Field Name="PlayerConditionId" Type="int" />
</Table>
<Table Name="RelicTalent" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Type" Type="int" />
   <Field Name="ArtifactPowerId" Type="ushort" />
   <Field Name="ArtifactPowerLabel" Type="byte" />
   <Field Name="PVal" Type="int" />
   <Field Name="Flags" Type="int" />
</Table>
<Table Name="ResearchBranch" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="ResearchFieldId" Type="byte" />
   <Field Name="CurrencyId" Type="ushort" />
   <Field Name="TextureFileId" Type="int" />
   <Field Name="BigTextureFileId" Type="int" />
   <Field Name="ItemId" Type="int" />
</Table>
<Table Name="ResearchField" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Slot" Type="byte" />
</Table>
<Table Name="ResearchProject" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Rarity" Type="byte" />
   <Field Name="SpellId" Type="int" />
   <Field Name="ResearchBranchId" Type="ushort" />
   <Field Name="NumSockets" Type="byte" />
   <Field Name="TextureFileId" Type="int" />
   <Field Name="RequiredWeight" Type="uint" />
</Table>
<Table Name="ResearchSite" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="MapId" Type="ushort" />
   <Field Name="QuestPoiBlobId" Type="int" />
   <Field Name="AreaPOIIconEnum" Type="uint" />
</Table>
<Table Name="Resistances" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Flags" Type="byte" />
   <Field Name="FizzleSoundId" Type="uint" />
</Table>
<Table Name="RewardPack" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CharTitleId" Type="int" />
   <Field Name="Money" Type="int" />
   <Field Name="ArtifactXPDifficulty" Type="byte" />
   <Field Name="ArtifactXPMultiplier" Type="float" />
   <Field Name="ArtifactXPCategoryId" Type="byte" />
   <Field Name="TreasurePickerId" Type="uint" />
</Table>
<Table Name="RewardPackXCurrencyType" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CurrencyTypeId" Type="uint" />
   <Field Name="Quantity" Type="int" />
</Table>
<Table Name="RewardPackXItem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="int" />
   <Field Name="ItemQuantity" Type="int" />
</Table>
<Table Name="RibbonQuality" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="NumStrips" Type="byte" />
   <Field Name="MaxSampleTimeDelta" Type="float" />
   <Field Name="AngleThreshold" Type="float" />
   <Field Name="MinDistancePerSlice" Type="float" />
   <Field Name="Flags" Type="uint" />
</Table>
<Table Name="RulesetItemUpgrade" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="int" />
   <Field Name="ItemUpgradeId" Type="ushort" />
</Table>
<Table Name="ScalingStatDistribution" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PlayerLevelToItemLevelCurveId" Type="ushort" />
   <Field Name="MinLevel" Type="int" />
   <Field Name="MaxLevel" Type="int" />
</Table>
<Table Name="Scenario" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="AreaTableId" Type="ushort" />
   <Field Name="Type" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Field_05" Type="uint" />
</Table>
<Table Name="ScenarioEventEntry" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="TriggerType" Type="byte" />
   <Field Name="TriggerAsset" Type="uint" />
</Table>
<Table Name="ScenarioStep" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Description" Type="string" />
   <Field Name="Title" Type="string" />
   <Field Name="ScenarioId" Type="ushort" />
   <Field Name="CriteriatreeId" Type="uint" />
   <Field Name="RewardQuestId" Type="ushort" />
   <Field Name="RelatedStep" Type="int" />
   <Field Name="Supersedes" Type="ushort" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="VisibilityPlayerConditionId" Type="uint" />
   <Field Name="Field_0B" Type="ushort" />
</Table>
<Table Name="SceneScript" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FirstSceneScriptId" Type="ushort" />
   <Field Name="NextSceneScriptId" Type="ushort" />
</Table>
<Table Name="SceneScriptGlobalText" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Script" Type="string" />
</Table>
<Table Name="SceneScriptPackage" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="SceneScriptPackageMember" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SceneScriptPackageId" Type="ushort" />
   <Field Name="SceneScriptId" Type="ushort" />
   <Field Name="ChildSceneScriptPackageId" Type="ushort" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="SceneScriptText" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Script" Type="string" />
</Table>
<Table Name="ScheduledInterval" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="int" />
   <Field Name="RepeatType" Type="int" />
   <Field Name="DurationSecs" Type="int" />
   <Field Name="OffsetSecs" Type="int" />
   <Field Name="DateAlignmentType" Type="int" />
</Table>
<Table Name="ScheduledWorldState" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ScheduledWorldStateGroupId" Type="int" />
   <Field Name="WorldStateId" Type="int" />
   <Field Name="Value" Type="int" />
   <Field Name="DurationSecs" Type="int" />
   <Field Name="Weight" Type="int" />
   <Field Name="UniqueCategory" Type="int" />
   <Field Name="Flags" Type="int" />
   <Field Name="OrderIndex" Type="int" />
</Table>
<Table Name="ScheduledWorldStateGroup" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="int" />
   <Field Name="ScheduledIntervalId" Type="int" />
   <Field Name="SelectionType" Type="int" />
   <Field Name="SelectionCount" Type="int" />
   <Field Name="Priority" Type="int" />
</Table>
<Table Name="ScheduledWorldStateXUniqCat" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ScheduledUniqueCategoryId" Type="int" />
</Table>
<Table Name="ScreenEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Param" Type="uint" ArraySize="4" />
   <Field Name="Effect" Type="byte" />
   <Field Name="FullScreenEffectId" Type="uint" />
   <Field Name="LightParamsId" Type="ushort" />
   <Field Name="LightParamsFadeIn" Type="ushort" />
   <Field Name="LightParamsFadeOut" Type="ushort" />
   <Field Name="SoundAmbienceId" Type="uint" />
   <Field Name="ZoneMusicId" Type="uint" />
   <Field Name="TimeOfDayOverride" Type="ushort" />
   <Field Name="EffectMask" Type="byte" />
   <Field Name="LightFlags" Type="byte" />
</Table>
<Table Name="ScreenLocation" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="SDReplacementModel" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SdFileDataId" Type="int" />
</Table>
<Table Name="SeamlessSite" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MapId" Type="int" />
</Table>
<Table Name="ServerMessages" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Text" Type="string" />
</Table>
<Table Name="ShadowyEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PrimaryColor" Type="uint" />
   <Field Name="SecondaryColor" Type="uint" />
   <Field Name="Duration" Type="float" />
   <Field Name="Value" Type="float" />
   <Field Name="FadeInTime" Type="float" />
   <Field Name="FadeOutTime" Type="float" />
   <Field Name="AttachPos" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="InnerStrength" Type="float" />
   <Field Name="OuterStrength" Type="float" />
   <Field Name="InitialDelay" Type="float" />
   <Field Name="CurveId" Type="int" />
   <Field Name="Priority" Type="uint" />
</Table>
<Table Name="SiegeableProperties" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Health" Type="uint" />
   <Field Name="DamageSpellVisualKitId" Type="int" />
   <Field Name="HealingSpellVisualKitId" Type="int" />
   <Field Name="Flags" Type="uint" />
</Table>
<Table Name="SkillLine" Build="26806">
   <Field Name="DisplayName" Type="string" />
   <Field Name="AlternateVerb" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="HordeDisplayName" Type="string" />
   <Field Name="ExpansionDisplayName" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="CategoryId" Type="byte" />
   <Field Name="SpellIconFileId" Type="int" />
   <Field Name="CanLink" Type="byte" />
   <Field Name="ParentSkillLineId" Type="int" />
   <Field Name="ParentTierIndex" Type="int" />
   <Field Name="Flags" Type="ushort" />
</Table>
<Table Name="SkillLineAbility" Build="26806">
   <Field Name="RaceMask" Type="ulong" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SkillLine" Type="short" />
   <Field Name="Spell" Type="int" />
   <Field Name="MinSkillLineRank" Type="short" />
   <Field Name="ClassMask" Type="int" />
   <Field Name="SupercedesSpell" Type="int" />
   <Field Name="AcquireMethod" Type="byte" />
   <Field Name="TrivialSkillLineRankHigh" Type="ushort" />
   <Field Name="TrivialSkillLineRankLow" Type="ushort" />
   <Field Name="Flags" Type="byte" />
   <Field Name="NumSkillUps" Type="byte" />
   <Field Name="UniqueBit" Type="ushort" />
   <Field Name="TradeSkillCategoryId" Type="ushort" />
   <Field Name="SkillupSkillLineId" Type="ushort" />
</Table>
<Table Name="SkillRaceClassInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RaceMask" Type="ulong" />
   <Field Name="SkillId" Type="short" />
   <Field Name="ClassMask" Type="int" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="Availability" Type="byte" />
   <Field Name="MinLevel" Type="byte" />
   <Field Name="SkillTierId" Type="short" />
</Table>
<Table Name="SoundAmbience" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="byte" />
   <Field Name="SoundFilterId" Type="uint" />
   <Field Name="FlavorSoundFilterId" Type="uint" />
   <Field Name="AmbienceId" Type="int" ArraySize="2" />
   <Field Name="AmbienceStartId" Type="int" ArraySize="2" />
   <Field Name="AmbienceStopId" Type="int" ArraySize="2" />
</Table>
<Table Name="SoundAmbienceFlavor" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundEntriesIDDay" Type="uint" />
   <Field Name="SoundEntriesIDNight" Type="uint" />
</Table>
<Table Name="SoundBus" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="byte" />
   <Field Name="DefaultPriority" Type="byte" />
   <Field Name="DefaultPriorityPenalty" Type="byte" />
   <Field Name="DefaultVolume" Type="float" />
   <Field Name="DefaultPlaybackLimit" Type="byte" />
   <Field Name="BusEnumId" Type="byte" />
</Table>
<Table Name="SoundBusOverride" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundBusId" Type="int" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="PlaybackLimit" Type="byte" />
   <Field Name="Volume" Type="float" />
   <Field Name="Priority" Type="byte" />
   <Field Name="PriorityPenalty" Type="byte" />
</Table>
<Table Name="SoundEmitterPillPoints" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Position" Type="float" ArraySize="3" />
   <Field Name="SoundEmittersId" Type="ushort" />
</Table>
<Table Name="SoundEmitters" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Position" Type="float" ArraySize="3" />
   <Field Name="Direction" Type="float" ArraySize="3" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundEntriesId" Type="int" />
   <Field Name="WorldStateExpressionId" Type="ushort" />
   <Field Name="EmitterType" Type="byte" />
   <Field Name="PhaseId" Type="ushort" />
   <Field Name="PhaseGroupId" Type="int" />
   <Field Name="PhaseUseFlags" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="SoundEnvelope" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundKitId" Type="int" />
   <Field Name="EnvelopeType" Type="byte" />
   <Field Name="Flags" Type="uint" />
   <Field Name="CurveId" Type="int" />
   <Field Name="DecayIndex" Type="ushort" />
   <Field Name="SustainIndex" Type="ushort" />
   <Field Name="ReleaseIndex" Type="ushort" />
</Table>
<Table Name="SoundFilter" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="SoundFilterElem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Params" Type="float" ArraySize="9" />
   <Field Name="FilterType" Type="byte" />
</Table>
<Table Name="SoundKit" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundType" Type="byte" />
   <Field Name="VolumeFloat" Type="float" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="MinDistance" Type="float" />
   <Field Name="DistanceCutoff" Type="float" />
   <Field Name="EAXDef" Type="byte" />
   <Field Name="SoundKitAdvancedId" Type="uint" />
   <Field Name="VolumeVariationPlus" Type="float" />
   <Field Name="VolumeVariationMinus" Type="float" />
   <Field Name="PitchVariationPlus" Type="float" />
   <Field Name="PitchVariationMinus" Type="float" />
   <Field Name="DialogType" Type="byte" />
   <Field Name="PitchAdjust" Type="float" />
   <Field Name="BusOverwriteId" Type="ushort" />
   <Field Name="MaxInstances" Type="byte" />
</Table>
<Table Name="SoundKitAdvanced" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundKitId" Type="uint" />
   <Field Name="InnerRadius2D" Type="float" />
   <Field Name="OuterRadius2D" Type="float" />
   <Field Name="TimeA" Type="int" />
   <Field Name="TimeB" Type="int" />
   <Field Name="TimeC" Type="int" />
   <Field Name="TimeD" Type="int" />
   <Field Name="RandomOffsetRange" Type="int" />
   <Field Name="Usage" Type="byte" />
   <Field Name="TimeIntervalMin" Type="int" />
   <Field Name="TimeIntervalMax" Type="int" />
   <Field Name="DelayMin" Type="int" />
   <Field Name="DelayMax" Type="int" />
   <Field Name="VolumeSliderCategory" Type="byte" />
   <Field Name="DuckToAmbience" Type="float" />
   <Field Name="DuckToDialog" Type="float" />
   <Field Name="DuckToSuppressors" Type="float" />
   <Field Name="DuckToSFX" Type="float" />
   <Field Name="DuckToMusic" Type="float" />
   <Field Name="InnerRadiusOfInfluence" Type="float" />
   <Field Name="OuterRadiusOfInfluence" Type="float" />
   <Field Name="TimeToDuck" Type="int" />
   <Field Name="TimeToUnduck" Type="int" />
   <Field Name="InsideAngle" Type="float" />
   <Field Name="OutsideAngle" Type="float" />
   <Field Name="OutsideVolume" Type="float" />
   <Field Name="MinRandomPosOffset" Type="byte" />
   <Field Name="MaxRandomPosOffset" Type="ushort" />
   <Field Name="MsOffset" Type="int" />
   <Field Name="TimeCooldownMin" Type="int" />
   <Field Name="TimeCooldownMax" Type="int" />
   <Field Name="MaxInstancesBehavior" Type="byte" />
   <Field Name="VolumeControlType" Type="byte" />
   <Field Name="VolumeFadeInTimeMin" Type="int" />
   <Field Name="VolumeFadeInTimeMax" Type="int" />
   <Field Name="VolumeFadeInCurveId" Type="int" />
   <Field Name="VolumeFadeOutTimeMin" Type="int" />
   <Field Name="VolumeFadeOutTimeMax" Type="int" />
   <Field Name="VolumeFadeOutCurveId" Type="int" />
   <Field Name="ChanceToPlay" Type="float" />
</Table>
<Table Name="SoundKitChild" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundKitId" Type="uint" />
   <Field Name="ParentSoundKitId" Type="uint" />
</Table>
<Table Name="SoundKitEntry" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundKitId" Type="uint" />
   <Field Name="FileDataId" Type="int" />
   <Field Name="Frequency" Type="byte" />
   <Field Name="Volume" Type="float" />
</Table>
<Table Name="SoundKitFallback" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SoundKitId" Type="uint" />
   <Field Name="FallbackSoundKitId" Type="uint" />
</Table>
<Table Name="SoundKitName" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="SoundOverride" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ZoneIntroMusicId" Type="ushort" />
   <Field Name="ZoneMusicId" Type="ushort" />
   <Field Name="SoundAmbienceId" Type="ushort" />
   <Field Name="SoundProviderPreferencesId" Type="byte" />
</Table>
<Table Name="SoundProviderPreferences" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Description" Type="string" />
   <Field Name="EAXEnvironmentSelection" Type="byte" />
   <Field Name="EAXDecayTime" Type="float" />
   <Field Name="EAX2EnvironmentSize" Type="float" />
   <Field Name="EAX2EnvironmentDiffusion" Type="float" />
   <Field Name="EAX2Room" Type="ushort" />
   <Field Name="EAX2RoomHF" Type="ushort" />
   <Field Name="EAX2DecayHFRatio" Type="float" />
   <Field Name="EAX2Reflections" Type="short" />
   <Field Name="EAX2ReflectionsDelay" Type="float" />
   <Field Name="EAX2Reverb" Type="ushort" />
   <Field Name="EAX2ReverbDelay" Type="float" />
   <Field Name="EAX2RoomRolloff" Type="float" />
   <Field Name="EAX2AirAbsorption" Type="float" />
   <Field Name="EAX3RoomLF" Type="byte" />
   <Field Name="EAX3DecayLFRatio" Type="float" />
   <Field Name="EAX3EchoTime" Type="float" />
   <Field Name="EAX3EchoDepth" Type="float" />
   <Field Name="EAX3ModulationTime" Type="float" />
   <Field Name="EAX3ModulationDepth" Type="float" />
   <Field Name="EAX3HFReference" Type="float" />
   <Field Name="EAX3LFReference" Type="float" />
   <Field Name="Flags" Type="ushort" />
</Table>
<Table Name="SourceInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SourceText" Type="string" />
   <Field Name="PvpFaction" Type="byte" />
   <Field Name="SourceTypeEnum" Type="byte" />
</Table>
<Table Name="SpamMessages" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Text" Type="string" />
</Table>
<Table Name="SpecializationSpells" Build="26806">
   <Field Name="Description" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpecId" Type="ushort" />
   <Field Name="SpellId" Type="int" />
   <Field Name="OverridesSpellId" Type="int" />
   <Field Name="DisplayOrder" Type="byte" />
</Table>
<Table Name="SpecializationSpellsDisplay" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpecId" Type="ushort" />
   <Field Name="SpellId" Type="int" ArraySize="6" />
</Table>
<Table Name="SpecSetMember" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpecId" Type="int" />
</Table>
<Table Name="Spell" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="NameSubtext" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="AuraDescription" Type="string" />
</Table>
<Table Name="SpellActionBarPref" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="PreferredActionBarMask" Type="ushort" />
</Table>
<Table Name="SpellActivationOverlay" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="IconHighlightSpellClassMask" Type="uint" ArraySize="4" />
   <Field Name="SpellId" Type="int" />
   <Field Name="OverlayFileDataId" Type="int" />
   <Field Name="ScreenLocationId" Type="byte" />
   <Field Name="SoundEntriesId" Type="uint" />
   <Field Name="Color" Type="int" />
   <Field Name="Scale" Type="float" />
   <Field Name="TriggerType" Type="byte" />
</Table>
<Table Name="SpellAuraOptions" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="CumulativeAura" Type="ushort" />
   <Field Name="ProcCategoryRecovery" Type="int" />
   <Field Name="ProcChance" Type="byte" />
   <Field Name="ProcCharges" Type="int" />
   <Field Name="SpellProcsPerMinuteId" Type="ushort" />
   <Field Name="ProcTypeMask" Type="uint" ArraySize="2" />
</Table>
<Table Name="SpellAuraRestrictions" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="CasterAuraState" Type="byte" />
   <Field Name="TargetAuraState" Type="byte" />
   <Field Name="ExcludeCasterAuraState" Type="byte" />
   <Field Name="ExcludeTargetAuraState" Type="byte" />
   <Field Name="CasterAuraSpell" Type="int" />
   <Field Name="TargetAuraSpell" Type="int" />
   <Field Name="ExcludeCasterAuraSpell" Type="int" />
   <Field Name="ExcludeTargetAuraSpell" Type="int" />
</Table>
<Table Name="SpellAuraVisibility" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Type" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="SpellAuraVisXChrSpec" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ChrSpecializationId" Type="short" />
</Table>
<Table Name="SpellCastingRequirements" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="FacingCasterFlags" Type="byte" />
   <Field Name="MinFactionId" Type="ushort" />
   <Field Name="MinReputation" Type="byte" />
   <Field Name="RequiredAreasId" Type="ushort" />
   <Field Name="RequiredAuraVision" Type="byte" />
   <Field Name="RequiresSpellFocus" Type="ushort" />
</Table>
<Table Name="SpellCastTimes" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Base" Type="int" />
   <Field Name="PerLevel" Type="ushort" />
   <Field Name="Minimum" Type="int" />
</Table>
<Table Name="SpellCategories" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="Category" Type="ushort" />
   <Field Name="DefenseType" Type="byte" />
   <Field Name="DispelType" Type="byte" />
   <Field Name="Mechanic" Type="byte" />
   <Field Name="PreventionType" Type="byte" />
   <Field Name="StartRecoveryCategory" Type="ushort" />
   <Field Name="ChargeCategory" Type="ushort" />
</Table>
<Table Name="SpellCategory" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Flags" Type="byte" />
   <Field Name="UsesPerWeek" Type="byte" />
   <Field Name="MaxCharges" Type="byte" />
   <Field Name="ChargeRecoveryTime" Type="int" />
   <Field Name="TypeMask" Type="int" />
</Table>
<Table Name="SpellChainEffects" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AvgSegLen" Type="float" />
   <Field Name="NoiseScale" Type="float" />
   <Field Name="TexCoordScale" Type="float" />
   <Field Name="SegDuration" Type="uint" />
   <Field Name="SegDelay" Type="ushort" />
   <Field Name="Flags" Type="uint" />
   <Field Name="JointCount" Type="ushort" />
   <Field Name="JointOffsetRadius" Type="float" />
   <Field Name="JointsPerMinorJoint" Type="byte" />
   <Field Name="MinorJointsPerMajorJoint" Type="byte" />
   <Field Name="MinorJointScale" Type="float" />
   <Field Name="MajorJointScale" Type="float" />
   <Field Name="JointMoveSpeed" Type="float" />
   <Field Name="JointSmoothness" Type="float" />
   <Field Name="MinDurationBetweenJointJumps" Type="float" />
   <Field Name="MaxDurationBetweenJointJumps" Type="float" />
   <Field Name="WaveHeight" Type="float" />
   <Field Name="WaveFreq" Type="float" />
   <Field Name="WaveSpeed" Type="float" />
   <Field Name="MinWaveAngle" Type="float" />
   <Field Name="MaxWaveAngle" Type="float" />
   <Field Name="MinWaveSpin" Type="float" />
   <Field Name="MaxWaveSpin" Type="float" />
   <Field Name="ArcHeight" Type="float" />
   <Field Name="MinArcAngle" Type="float" />
   <Field Name="MaxArcAngle" Type="float" />
   <Field Name="MinArcSpin" Type="float" />
   <Field Name="MaxArcSpin" Type="float" />
   <Field Name="DelayBetweenEffects" Type="float" />
   <Field Name="MinFlickerOnDuration" Type="float" />
   <Field Name="MaxFlickerOnDuration" Type="float" />
   <Field Name="MinFlickerOffDuration" Type="float" />
   <Field Name="MaxFlickerOffDuration" Type="float" />
   <Field Name="PulseSpeed" Type="float" />
   <Field Name="PulseOnLength" Type="float" />
   <Field Name="PulseFadeLength" Type="float" />
   <Field Name="Alpha" Type="byte" />
   <Field Name="Red" Type="byte" />
   <Field Name="Green" Type="byte" />
   <Field Name="Blue" Type="byte" />
   <Field Name="BlendMode" Type="byte" />
   <Field Name="RenderLayer" Type="byte" />
   <Field Name="WavePhase" Type="float" />
   <Field Name="TimePerFlipFrame" Type="float" />
   <Field Name="VariancePerFlipFrame" Type="float" />
   <Field Name="TextureParticleFileDataId" Type="int" />
   <Field Name="StartWidth" Type="float" />
   <Field Name="EndWidth" Type="float" />
   <Field Name="WidthScaleCurveId" Type="ushort" />
   <Field Name="NumFlipFramesU" Type="byte" />
   <Field Name="NumFlipFramesV" Type="byte" />
   <Field Name="SoundKitId" Type="int" />
   <Field Name="ParticleScaleMultiplier" Type="float" />
   <Field Name="ParticleEmissionRateMultiplier" Type="float" />
   <Field Name="SpellChainEffectId" Type="ushort" ArraySize="11" />
   <Field Name="TextureCoordScaleU" Type="float" ArraySize="3" />
   <Field Name="TextureCoordScaleV" Type="float" ArraySize="3" />
   <Field Name="TextureRepeatLengthU" Type="float" ArraySize="3" />
   <Field Name="TextureRepeatLengthV" Type="float" ArraySize="3" />
   <Field Name="TextureFileDataId" Type="int" ArraySize="3" />
</Table>
<Table Name="SpellClassOptions" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="ModalNextSpell" Type="uint" />
   <Field Name="SpellClassSet" Type="byte" />
   <Field Name="SpellClassMask" Type="uint" ArraySize="4" />
</Table>
<Table Name="SpellCooldowns" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="CategoryRecoveryTime" Type="int" />
   <Field Name="RecoveryTime" Type="int" />
   <Field Name="StartRecoveryTime" Type="int" />
</Table>
<Table Name="SpellDescriptionVariables" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Variables" Type="string" />
</Table>
<Table Name="SpellDispelType" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="InternalName" Type="string" />
   <Field Name="ImmunityPossible" Type="byte" />
   <Field Name="Mask" Type="byte" />
</Table>
<Table Name="SpellDuration" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Duration" Type="uint" />
   <Field Name="DurationPerLevel" Type="int" />
   <Field Name="MaxDuration" Type="uint" />
</Table>
<Table Name="SpellEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="int" />
   <Field Name="EffectIndex" Type="int" />
   <Field Name="Effect" Type="uint" />
   <Field Name="EffectAmplitude" Type="float" />
   <Field Name="EffectAttributes" Type="uint" />
   <Field Name="EffectAura" Type="ushort" />
   <Field Name="EffectAuraPeriod" Type="uint" />
   <Field Name="EffectBonusCoefficient" Type="float" />
   <Field Name="EffectChainAmplitude" Type="float" />
   <Field Name="EffectChainTargets" Type="uint" />
   <Field Name="EffectItemType" Type="int" />
   <Field Name="EffectMechanic" Type="int" />
   <Field Name="EffectPointsPerResource" Type="float" />
   <Field Name="EffectPosFacing" Type="float" />
   <Field Name="EffectRealPointsPerLevel" Type="float" />
   <Field Name="EffectTriggerSpell" Type="int" />
   <Field Name="BonusCoefficientFromAP" Type="float" />
   <Field Name="PvpMultiplier" Type="float" />
   <Field Name="Coefficient" Type="float" />
   <Field Name="Variance" Type="float" />
   <Field Name="ResourceCoefficient" Type="float" />
   <Field Name="GroupSizeBasePointsCoefficient" Type="float" />
   <Field Name="EffectBasePoints" Type="float" />
   <Field Name="EffectMiscValue" Type="uint" ArraySize="2" />
   <Field Name="EffectRadiusIndex" Type="uint" ArraySize="2" />
   <Field Name="EffectSpellClassMask" Type="uint" ArraySize="4" />
   <Field Name="ImplicitTarget" Type="ushort" ArraySize="2" />
</Table>
<Table Name="SpellEffectAutoDescription" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="EffectDescription" Type="string" />
   <Field Name="AuraDescription" Type="string" />
   <Field Name="SpellEffectType" Type="int" />
   <Field Name="AuraEffectType" Type="int" />
   <Field Name="PointsSign" Type="byte" />
   <Field Name="TargetType" Type="byte" />
   <Field Name="SchoolMask" Type="byte" />
   <Field Name="EffectOrderIndex" Type="int" />
   <Field Name="AuraOrderIndex" Type="int" />
</Table>
<Table Name="SpellEffectEmission" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="EmissionRate" Type="float" />
   <Field Name="ModelScale" Type="float" />
   <Field Name="AreaModelId" Type="short" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="SpellEquippedItems" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="EquippedItemClass" Type="byte" />
   <Field Name="EquippedItemInvTypes" Type="int" />
   <Field Name="EquippedItemSubclass" Type="int" />
</Table>
<Table Name="SpellFlyout" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RaceMask" Type="ulong" />
   <Field Name="Name" Type="string" />
   <Field Name="Description" Type="string" />
   <Field Name="Flags" Type="byte" />
   <Field Name="ClassMask" Type="int" />
   <Field Name="SpellIconFileId" Type="int" />
</Table>
<Table Name="SpellFlyoutItem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="Slot" Type="byte" />
</Table>
<Table Name="SpellFocusObject" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="SpellInterrupts" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="InterruptFlags" Type="short" />
   <Field Name="AuraInterruptFlags" Type="int" ArraySize="2" />
   <Field Name="ChannelInterruptFlags" Type="int" ArraySize="2" />
</Table>
<Table Name="SpellItemEnchantment" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="HordeName" Type="string" />
   <Field Name="EffectArg" Type="int" ArraySize="3" />
   <Field Name="EffectScalingPoints" Type="float" ArraySize="3" />
   <Field Name="TransmogCost" Type="int" />
   <Field Name="IconFileDataId" Type="int" />
   <Field Name="TransmogPlayerConditionId" Type="uint" />
   <Field Name="EffectPointsMin" Type="ushort" ArraySize="3" />
   <Field Name="ItemVisual" Type="ushort" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="RequiredSkillId" Type="ushort" />
   <Field Name="RequiredSkillRank" Type="ushort" />
   <Field Name="ItemLevel" Type="ushort" />
   <Field Name="Charges" Type="byte" />
   <Field Name="Effect" Type="byte" ArraySize="3" />
   <Field Name="ScalingClass" Type="byte" />
   <Field Name="ScalingClassRestricted" Type="byte" />
   <Field Name="ConditionId" Type="byte" />
   <Field Name="MinLevel" Type="byte" />
   <Field Name="MaxLevel" Type="byte" />
</Table>
<Table Name="SpellItemEnchantmentCondition" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="LtOperandType" Type="byte" ArraySize="5" />
   <Field Name="LtOperand" Type="int" ArraySize="5" />
   <Field Name="Operator" Type="byte" ArraySize="5" />
   <Field Name="RtOperandType" Type="byte" ArraySize="5" />
   <Field Name="RtOperand" Type="byte" ArraySize="5" />
   <Field Name="Logic" Type="byte" ArraySize="5" />
</Table>
<Table Name="SpellKeyboundOverride" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Function" Type="string" />
   <Field Name="Type" Type="byte" />
   <Field Name="Data" Type="int" />
</Table>
<Table Name="SpellLabel" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="LabelId" Type="int" />
</Table>
<Table Name="SpellLearnSpell" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="LearnSpellId" Type="int" />
   <Field Name="OverridesSpellId" Type="int" />
</Table>
<Table Name="SpellLevels" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="BaseLevel" Type="ushort" />
   <Field Name="MaxLevel" Type="ushort" />
   <Field Name="SpellLevel" Type="ushort" />
   <Field Name="MaxPassiveAuraLevel" Type="byte" />
</Table>
<Table Name="SpellMechanic" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="StateName" Type="string" />
</Table>
<Table Name="SpellMisc" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="CastingTimeIndex" Type="ushort" />
   <Field Name="DurationIndex" Type="ushort" />
   <Field Name="RangeIndex" Type="ushort" />
   <Field Name="SchoolMask" Type="byte" />
   <Field Name="Speed" Type="float" />
   <Field Name="LaunchDelay" Type="float" />
   <Field Name="Field_08" Type="float" />
   <Field Name="SpellIconFileDataId" Type="int" />
   <Field Name="ActiveIconFileDataId" Type="int" />
   <Field Name="Attributes" Type="int" ArraySize="14" />
</Table>
<Table Name="SpellMissile" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="DefaultPitchMin" Type="float" />
   <Field Name="DefaultPitchMax" Type="float" />
   <Field Name="DefaultSpeedMin" Type="float" />
   <Field Name="DefaultSpeedMax" Type="float" />
   <Field Name="RandomizeFacingMin" Type="float" />
   <Field Name="RandomizeFacingMax" Type="float" />
   <Field Name="RandomizePitchMin" Type="float" />
   <Field Name="RandomizePitchMax" Type="float" />
   <Field Name="RandomizeSpeedMin" Type="float" />
   <Field Name="RandomizeSpeedMax" Type="float" />
   <Field Name="Gravity" Type="float" />
   <Field Name="MaxDuration" Type="float" />
   <Field Name="CollisionRadius" Type="float" />
</Table>
<Table Name="SpellMissileMotion" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="ScriptBody" Type="string" />
   <Field Name="Flags" Type="byte" />
   <Field Name="MissileCount" Type="byte" />
</Table>
<Table Name="SpellName" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="SpellPower" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="ManaCost" Type="int" />
   <Field Name="ManaCostPerLevel" Type="int" />
   <Field Name="ManaPerSecond" Type="int" />
   <Field Name="PowerDisplayId" Type="int" />
   <Field Name="AltPowerBarId" Type="int" />
   <Field Name="PowerCostPct" Type="float" />
   <Field Name="PowerCostMaxPct" Type="float" />
   <Field Name="PowerPctPerSecond" Type="float" />
   <Field Name="PowerType" Type="byte" />
   <Field Name="RequiredAuraSpellId" Type="int" />
   <Field Name="OptionalCost" Type="int" />
</Table>
<Table Name="SpellPowerDifficulty" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="OrderIndex" Type="byte" />
</Table>
<Table Name="SpellProceduralEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Type" Type="byte" />
   <Field Name="Value" Type="float" ArraySize="4" />
</Table>
<Table Name="SpellProcsPerMinute" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BaseProcRate" Type="float" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="SpellProcsPerMinuteMod" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Type" Type="byte" />
   <Field Name="Param" Type="short" />
   <Field Name="Coeff" Type="float" />
</Table>
<Table Name="SpellRadius" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Radius" Type="float" />
   <Field Name="RadiusPerLevel" Type="float" />
   <Field Name="RadiusMin" Type="float" />
   <Field Name="RadiusMax" Type="float" />
</Table>
<Table Name="SpellRange" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DisplayName" Type="string" />
   <Field Name="DisplayNameShort" Type="string" />
   <Field Name="Flags" Type="byte" />
   <Field Name="RangeMin" Type="float" ArraySize="2" />
   <Field Name="RangeMax" Type="float" ArraySize="2" />
</Table>
<Table Name="SpellReagents" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="Reagent" Type="int" ArraySize="8" />
   <Field Name="ReagentCount" Type="ushort" ArraySize="8" />
</Table>
<Table Name="SpellReagentsCurrency" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="CurrencyTypesId" Type="ushort" />
   <Field Name="CurrencyCount" Type="ushort" />
</Table>
<Table Name="SpellScaling" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="Class" Type="int" />
   <Field Name="MinScalingLevel" Type="uint" />
   <Field Name="MaxScalingLevel" Type="int" />
   <Field Name="ScalesFromItemLevel" Type="ushort" />
</Table>
<Table Name="SpellShapeshift" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="StanceBarOrder" Type="byte" />
   <Field Name="ShapeshiftExclude" Type="uint" ArraySize="2" />
   <Field Name="ShapeshiftMask" Type="uint" ArraySize="2" />
</Table>
<Table Name="SpellShapeshiftForm" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="CreatureType" Type="byte" />
   <Field Name="Flags" Type="int" />
   <Field Name="AttackIconFileId" Type="int" />
   <Field Name="BonusActionBar" Type="byte" />
   <Field Name="CombatRoundTime" Type="ushort" />
   <Field Name="DamageVariance" Type="float" />
   <Field Name="MountTypeId" Type="ushort" />
   <Field Name="CreatureDisplayId" Type="int" ArraySize="4" />
   <Field Name="PresetSpellId" Type="int" ArraySize="8" />
</Table>
<Table Name="SpellSpecialUnitEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellVisualEffectNameId" Type="ushort" />
   <Field Name="PositionerId" Type="uint" />
</Table>
<Table Name="SpellTargetRestrictions" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="ConeDegrees" Type="float" />
   <Field Name="MaxTargets" Type="byte" />
   <Field Name="MaxTargetLevel" Type="int" />
   <Field Name="TargetCreatureType" Type="ushort" />
   <Field Name="Targets" Type="int" />
   <Field Name="Width" Type="float" />
</Table>
<Table Name="SpellTotems" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="RequiredTotemCategoryId" Type="ushort" ArraySize="2" />
   <Field Name="Totem" Type="int" ArraySize="2" />
</Table>
<Table Name="SpellVisual" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MissileCastOffset" Type="float" ArraySize="3" />
   <Field Name="MissileImpactOffset" Type="float" ArraySize="3" />
   <Field Name="AnimEventSoundId" Type="int" />
   <Field Name="Flags" Type="int" />
   <Field Name="MissileAttachment" Type="byte" />
   <Field Name="MissileDestinationAttachment" Type="byte" />
   <Field Name="MissileCastPositionerId" Type="uint" />
   <Field Name="MissileImpactPositionerId" Type="uint" />
   <Field Name="MissileTargetingKit" Type="int" />
   <Field Name="HostileSpellVisualId" Type="uint" />
   <Field Name="CasterSpellVisualId" Type="int" />
   <Field Name="SpellVisualMissileSetId" Type="ushort" />
   <Field Name="DamageNumberDelay" Type="ushort" />
   <Field Name="LowViolenceSpellVisualId" Type="int" />
   <Field Name="RaidSpellVisualMissileSetId" Type="uint" />
</Table>
<Table Name="SpellVisualAnim" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="InitialAnimId" Type="int" />
   <Field Name="LoopAnimId" Type="int" />
   <Field Name="AnimKitId" Type="ushort" />
</Table>
<Table Name="SpellVisualColorEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Duration" Type="float" />
   <Field Name="Color" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Type" Type="byte" />
   <Field Name="RedCurveId" Type="ushort" />
   <Field Name="GreenCurveId" Type="ushort" />
   <Field Name="BlueCurveId" Type="ushort" />
   <Field Name="AlphaCurveId" Type="ushort" />
   <Field Name="OpacityCurveId" Type="ushort" />
   <Field Name="ColorMultiplier" Type="float" />
   <Field Name="PositionerId" Type="uint" />
</Table>
<Table Name="SpellVisualEffectName" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ModelFileDataId" Type="int" />
   <Field Name="BaseMissileSpeed" Type="float" />
   <Field Name="Scale" Type="float" />
   <Field Name="MinAllowedScale" Type="float" />
   <Field Name="MaxAllowedScale" Type="float" />
   <Field Name="Alpha" Type="float" />
   <Field Name="Flags" Type="int" />
   <Field Name="TextureFileDataId" Type="int" />
   <Field Name="EffectRadius" Type="float" />
   <Field Name="Type" Type="uint" />
   <Field Name="GenericId" Type="int" />
   <Field Name="RibbonQualityId" Type="uint" />
   <Field Name="DissolveEffectId" Type="int" />
   <Field Name="ModelPosition" Type="int" />
</Table>
<Table Name="SpellVisualEvent" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="StartEvent" Type="int" />
   <Field Name="EndEvent" Type="int" />
   <Field Name="StartMinOffsetMs" Type="int" />
   <Field Name="StartMaxOffsetMs" Type="int" />
   <Field Name="EndMinOffsetMs" Type="int" />
   <Field Name="EndMaxOffsetMs" Type="int" />
   <Field Name="TargetType" Type="int" />
   <Field Name="SpellVisualKitId" Type="int" />
</Table>
<Table Name="SpellVisualKit" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="int" />
   <Field Name="FallbackPriority" Type="float" />
   <Field Name="FallbackSpellVisualKitId" Type="uint" />
   <Field Name="DelayMin" Type="ushort" />
   <Field Name="DelayMax" Type="ushort" />
</Table>
<Table Name="SpellVisualKitAreaModel" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ModelFileDataId" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="LifeTime" Type="ushort" />
   <Field Name="EmissionRate" Type="float" />
   <Field Name="Spacing" Type="float" />
   <Field Name="ModelScale" Type="float" />
</Table>
<Table Name="SpellVisualKitEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="EffectType" Type="int" />
   <Field Name="Effect" Type="int" />
</Table>
<Table Name="SpellVisualKitModelAttach" Build="26806">
   <Field Name="Offset" Type="float" ArraySize="3" />
   <Field Name="OffsetVariation" Type="float" ArraySize="3" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellVisualEffectNameId" Type="ushort" />
   <Field Name="AttachmentId" Type="byte" />
   <Field Name="PositionerId" Type="ushort" />
   <Field Name="Yaw" Type="float" />
   <Field Name="Pitch" Type="float" />
   <Field Name="Roll" Type="float" />
   <Field Name="YawVariation" Type="float" />
   <Field Name="PitchVariation" Type="float" />
   <Field Name="RollVariation" Type="float" />
   <Field Name="Scale" Type="float" />
   <Field Name="ScaleVariation" Type="float" />
   <Field Name="StartAnimId" Type="ushort" />
   <Field Name="AnimId" Type="ushort" />
   <Field Name="EndAnimId" Type="ushort" />
   <Field Name="AnimKitId" Type="ushort" />
   <Field Name="Flags" Type="byte" />
   <Field Name="LowDefModelAttachId" Type="int" />
   <Field Name="StartDelay" Type="float" />
</Table>
<Table Name="SpellVisualMissile" Build="26806">
   <Field Name="CastOffset" Type="float" ArraySize="3" />
   <Field Name="ImpactOffset" Type="float" ArraySize="3" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellVisualEffectNameId" Type="ushort" />
   <Field Name="SoundEntriesId" Type="int" />
   <Field Name="Attachment" Type="byte" />
   <Field Name="DestinationAttachment" Type="byte" />
   <Field Name="CastPositionerId" Type="ushort" />
   <Field Name="ImpactPositionerId" Type="ushort" />
   <Field Name="FollowGroundHeight" Type="uint" />
   <Field Name="FollowGroundDropSpeed" Type="int" />
   <Field Name="FollowGroundApproach" Type="ushort" />
   <Field Name="Flags" Type="int" />
   <Field Name="SpellMissileMotionId" Type="ushort" />
   <Field Name="AnimKitId" Type="uint" />
</Table>
<Table Name="SpellXDescriptionVariables" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SpellId" Type="int" />
   <Field Name="SpellDescriptionVariablesId" Type="int" />
</Table>
<Table Name="SpellXSpellVisual" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="DifficultyId" Type="byte" />
   <Field Name="SpellVisualId" Type="uint" />
   <Field Name="Probability" Type="float" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Priority" Type="byte" />
   <Field Name="SpellIconFileId" Type="int" />
   <Field Name="ActiveIconFileId" Type="int" />
   <Field Name="ViewerUnitConditionId" Type="ushort" />
   <Field Name="ViewerPlayerConditionId" Type="int" />
   <Field Name="CasterUnitConditionId" Type="ushort" />
   <Field Name="CasterPlayerConditionId" Type="int" />
</Table>
<Table Name="StartupFiles" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FileDataId" Type="int" />
   <Field Name="Locale" Type="int" />
   <Field Name="BytesRequired" Type="int" />
</Table>
<Table Name="Startup_Strings" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Message" Type="string" />
</Table>
<Table Name="Stationery" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="uint" />
   <Field Name="Flags" Type="byte" />
   <Field Name="TextureFileDataId" Type="int" ArraySize="2" />
</Table>
<Table Name="SummonProperties" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Control" Type="int" />
   <Field Name="Faction" Type="int" />
   <Field Name="Title" Type="int" />
   <Field Name="Slot" Type="int" />
   <Field Name="Flags" Type="uint" />
</Table>
<Table Name="TactKey" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Key" Type="byte" ArraySize="16" />
</Table>
<Table Name="TactKeyLookup" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="TACTId" Type="byte" ArraySize="8" />
</Table>
<Table Name="Talent" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Description" Type="string" />
   <Field Name="TierId" Type="byte" />
   <Field Name="Flags" Type="byte" />
   <Field Name="ColumnIndex" Type="byte" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="SpecId" Type="ushort" />
   <Field Name="SpellId" Type="int" />
   <Field Name="OverridesSpellId" Type="int" />
   <Field Name="CategoryMask" Type="byte" ArraySize="2" />
</Table>
<Table Name="TaxiNodes" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="Pos" Type="float" ArraySize="3" />
   <Field Name="MapOffset" Type="float" ArraySize="2" />
   <Field Name="FlightMapOffset" Type="float" ArraySize="2" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ContinentId" Type="ushort" />
   <Field Name="ConditionId" Type="ushort" />
   <Field Name="CharacterBitNumber" Type="ushort" />
   <Field Name="Flags" Type="byte" />
   <Field Name="UiTextureKitId" Type="int" />
   <Field Name="Facing" Type="float" />
   <Field Name="SpecialIconConditionId" Type="int" />
   <Field Name="MountCreatureId" Type="int" ArraySize="2" />
</Table>
<Table Name="TaxiPath" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FromTaxiNode" Type="ushort" />
   <Field Name="ToTaxiNode" Type="ushort" />
   <Field Name="Cost" Type="int" />
</Table>
<Table Name="TaxiPathNode" Build="26806">
   <Field Name="Loc" Type="float" ArraySize="3" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PathId" Type="ushort" />
   <Field Name="NodeIndex" Type="int" />
   <Field Name="ContinentId" Type="ushort" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Delay" Type="int" />
   <Field Name="ArrivalEventId" Type="ushort" />
   <Field Name="DepartureEventId" Type="ushort" />
</Table>
<Table Name="TerrainMaterial" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Shader" Type="byte" />
   <Field Name="EnvMapDiffuseFileId" Type="int" />
   <Field Name="EnvMapSpecularFileId" Type="int" />
</Table>
<Table Name="TerrainType" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="TerrainDesc" Type="string" />
   <Field Name="FootstepSprayRun" Type="ushort" />
   <Field Name="FootstepSprayWalk" Type="ushort" />
   <Field Name="SoundId" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="TerrainTypeSounds" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
</Table>
<Table Name="TextureBlendSet" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="TextureFileDataId" Type="int" ArraySize="3" />
   <Field Name="SwizzleRed" Type="byte" />
   <Field Name="SwizzleGreen" Type="byte" />
   <Field Name="SwizzleBlue" Type="byte" />
   <Field Name="SwizzleAlpha" Type="byte" />
   <Field Name="TextureScrollRateU" Type="float" ArraySize="3" />
   <Field Name="TextureScrollRateV" Type="float" ArraySize="3" />
   <Field Name="TextureScaleU" Type="float" ArraySize="3" />
   <Field Name="TextureScaleV" Type="float" ArraySize="3" />
   <Field Name="ModX" Type="float" ArraySize="4" />
</Table>
<Table Name="TextureFileData" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UsageType" Type="byte" />
   <Field Name="MaterialResourcesId" Type="int" />
</Table>
<Table Name="TotemCategory" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="TotemCategoryType" Type="byte" />
   <Field Name="TotemCategoryMask" Type="uint" />
</Table>
<Table Name="Toy" Build="26806">
   <Field Name="SourceText" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemId" Type="int" />
   <Field Name="Flags" Type="byte" />
   <Field Name="SourceTypeEnum" Type="byte" />
</Table>
<Table Name="TradeSkillCategory" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="HordeName" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ParentTradeSkillCategoryId" Type="ushort" />
   <Field Name="SkillLineId" Type="ushort" />
   <Field Name="OrderIndex" Type="ushort" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="TradeSkillItem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemLevel" Type="ushort" />
   <Field Name="RequiredLevel" Type="byte" />
</Table>
<Table Name="TransformMatrix" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Pos" Type="float" ArraySize="3" />
   <Field Name="Yaw" Type="float" />
   <Field Name="Pitch" Type="float" />
   <Field Name="Roll" Type="float" />
   <Field Name="Scale" Type="float" />
</Table>
<Table Name="TransmogHoliday" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RequiredTransmogHoliday" Type="int" />
</Table>
<Table Name="TransmogSet" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ClassMask" Type="int" />
   <Field Name="TrackingQuestId" Type="int" />
   <Field Name="Flags" Type="int" />
   <Field Name="TransmogSetGroupId" Type="int" />
   <Field Name="ItemNameDescriptionId" Type="int" />
   <Field Name="ParentTransmogSetId" Type="ushort" />
   <Field Name="ExpansionId" Type="byte" />
   <Field Name="UiOrder" Type="short" />
</Table>
<Table Name="TransmogSetGroup" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
</Table>
<Table Name="TransmogSetItem" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="TransmogSetId" Type="uint" />
   <Field Name="ItemModifiedAppearanceId" Type="uint" />
   <Field Name="Flags" Type="int" />
</Table>
<Table Name="TransportAnimation" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Pos" Type="float" ArraySize="3" />
   <Field Name="SequenceId" Type="byte" />
   <Field Name="TimeIndex" Type="int" />
</Table>
<Table Name="TransportPhysics" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="WaveAmp" Type="float" />
   <Field Name="WaveTimeScale" Type="float" />
   <Field Name="RollAmp" Type="float" />
   <Field Name="RollTimeScale" Type="float" />
   <Field Name="PitchAmp" Type="float" />
   <Field Name="PitchTimeScale" Type="float" />
   <Field Name="MaxBank" Type="float" />
   <Field Name="MaxBankTurnSpeed" Type="float" />
   <Field Name="SpeedDampThresh" Type="float" />
   <Field Name="SpeedDamp" Type="float" />
</Table>
<Table Name="TransportRotation" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Rot" Type="float" ArraySize="4" />
   <Field Name="TimeIndex" Type="int" />
</Table>
<Table Name="Trophy" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="TrophyTypeId" Type="byte" />
   <Field Name="GameObjectDisplayInfoId" Type="ushort" />
   <Field Name="PlayerConditionId" Type="int" />
</Table>
<Table Name="UiCamera" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Pos" Type="float" ArraySize="3" />
   <Field Name="LookAt" Type="float" ArraySize="3" />
   <Field Name="Up" Type="float" ArraySize="3" />
   <Field Name="UiCameraTypeId" Type="byte" />
   <Field Name="AnimId" Type="int" />
   <Field Name="AnimFrame" Type="short" />
   <Field Name="AnimVariation" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="UiCameraType" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Width" Type="uint" />
   <Field Name="Height" Type="uint" />
</Table>
<Table Name="UiCamFbackTransmogChrRace" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ChrRaceId" Type="byte" />
   <Field Name="Gender" Type="byte" />
   <Field Name="InventoryType" Type="byte" />
   <Field Name="Variation" Type="byte" />
   <Field Name="UiCameraId" Type="ushort" />
</Table>
<Table Name="UiCamFbackTransmogWeapon" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ItemClass" Type="byte" />
   <Field Name="ItemSubclass" Type="byte" />
   <Field Name="InventoryType" Type="byte" />
   <Field Name="UiCameraId" Type="ushort" />
</Table>
<Table Name="UiCanvas" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Width" Type="short" />
   <Field Name="Height" Type="short" />
</Table>
<Table Name="UIExpansionDisplayInfo" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ExpansionLogo" Type="int" />
   <Field Name="ExpansionBanner" Type="int" />
   <Field Name="ExpansionLevel" Type="uint" />
</Table>
<Table Name="UIExpansionDisplayInfoIcon" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FeatureDescription" Type="string" />
   <Field Name="ParentId" Type="int" />
   <Field Name="FeatureIcon" Type="int" />
</Table>
<Table Name="UiMap" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ParentUiMapId" Type="int" />
   <Field Name="Flags" Type="int" />
   <Field Name="System" Type="int" />
   <Field Name="Type" Type="uint" />
   <Field Name="LevelRangeMin" Type="uint" />
   <Field Name="LevelRangeMax" Type="uint" />
   <Field Name="BountySetId" Type="int" />
   <Field Name="BountyDisplayLocation" Type="uint" />
   <Field Name="VisibilityPlayerConditionId" Type="int" />
   <Field Name="HelpTextPosition" Type="byte" />
   <Field Name="BkgAtlasId" Type="int" />
</Table>
<Table Name="UiMapArt" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="HighlightFileDataId" Type="int" />
   <Field Name="UiMapArtStyleId" Type="int" />
</Table>
<Table Name="UiMapArtStyleLayer" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="LayerIndex" Type="byte" />
   <Field Name="LayerWidth" Type="ushort" />
   <Field Name="LayerHeight" Type="ushort" />
   <Field Name="TileWidth" Type="ushort" />
   <Field Name="TileHeight" Type="ushort" />
   <Field Name="MinScale" Type="float" />
   <Field Name="MaxScale" Type="float" />
   <Field Name="AdditionalZoomSteps" Type="int" />
</Table>
<Table Name="UiMapArtTile" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RowIndex" Type="byte" />
   <Field Name="ColIndex" Type="byte" />
   <Field Name="LayerIndex" Type="byte" />
   <Field Name="FileDataId" Type="int" />
</Table>
<Table Name="UiMapAssignment" Build="26806">
   <Field Name="UiMin" Type="float" ArraySize="2" />
   <Field Name="UiMax" Type="float" ArraySize="2" />
   <Field Name="Region" Type="float" ArraySize="6" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UiMapId" Type="int" />
   <Field Name="OrderIndex" Type="int" />
   <Field Name="MapId" Type="int" />
   <Field Name="AreaId" Type="int" />
   <Field Name="WmoDoodadPlacementId" Type="int" />
   <Field Name="WmoGroupId" Type="int" />
</Table>
<Table Name="UiMapFogOfWar" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UiMapId" Type="int" />
   <Field Name="PlayerConditionId" Type="int" />
   <Field Name="UiMapFogOfWarVisId" Type="int" />
</Table>
<Table Name="UiMapFogOfWarVisualization" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BackgroundAtlasId" Type="uint" />
   <Field Name="MaskAtlasId" Type="uint" />
   <Field Name="MaskScalar" Type="float" />
</Table>
<Table Name="UiMapGroupMember" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="UiMapGroupId" Type="int" />
   <Field Name="UiMapId" Type="int" />
   <Field Name="FloorIndex" Type="int" />
   <Field Name="RelativeHeightIndex" Type="byte" />
</Table>
<Table Name="UiMapLink" Build="26806">
   <Field Name="UiMin" Type="float" ArraySize="2" />
   <Field Name="UiMax" Type="float" ArraySize="2" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ParentUiMapId" Type="int" />
   <Field Name="OrderIndex" Type="int" />
   <Field Name="ChildUiMapId" Type="int" />
</Table>
<Table Name="UiMapXMapArt" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PhaseId" Type="int" />
   <Field Name="UiMapArtId" Type="int" />
</Table>
<Table Name="UiModelScene" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UiSystemType" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="UiModelSceneActor" Build="26806">
   <Field Name="ScriptTag" Type="string" />
   <Field Name="Position" Type="float" ArraySize="3" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="byte" />
   <Field Name="UiModelSceneActorDisplayId" Type="int" />
   <Field Name="OrientationYaw" Type="float" />
   <Field Name="OrientationPitch" Type="float" />
   <Field Name="OrientationRoll" Type="float" />
   <Field Name="NormalizedScale" Type="float" />
</Table>
<Table Name="UiModelSceneActorDisplay" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AnimationId" Type="uint" />
   <Field Name="SequenceVariation" Type="uint" />
   <Field Name="Alpha" Type="float" />
   <Field Name="Scale" Type="float" />
   <Field Name="AnimSpeed" Type="float" />
</Table>
<Table Name="UiModelSceneCamera" Build="26806">
   <Field Name="ScriptTag" Type="string" />
   <Field Name="Target" Type="float" ArraySize="3" />
   <Field Name="ZoomedTargetOffset" Type="float" ArraySize="3" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="byte" />
   <Field Name="CameraType" Type="byte" />
   <Field Name="Yaw" Type="float" />
   <Field Name="Pitch" Type="float" />
   <Field Name="Roll" Type="float" />
   <Field Name="ZoomedYawOffset" Type="float" />
   <Field Name="ZoomedPitchOffset" Type="float" />
   <Field Name="ZoomedRollOffset" Type="float" />
   <Field Name="ZoomDistance" Type="float" />
   <Field Name="MinZoomDistance" Type="float" />
   <Field Name="MaxZoomDistance" Type="float" />
</Table>
<Table Name="UiPartyPose" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UiWidgetSetId" Type="int" />
   <Field Name="UiModelSceneId" Type="int" />
</Table>
<Table Name="UiTextureAtlas" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FileDataId" Type="int" />
   <Field Name="AtlasWidth" Type="ushort" />
   <Field Name="AtlasHeight" Type="ushort" />
   <Field Name="UiCanvasId" Type="byte" />
</Table>
<Table Name="UiTextureAtlasElement" Build="26806">
   <Field Name="Name" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
</Table>
<Table Name="UiTextureAtlasMember" Build="26806">
   <Field Name="CommittedName" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UiTextureAtlasId" Type="ushort" />
   <Field Name="CommittedLeft" Type="ushort" />
   <Field Name="CommittedRight" Type="ushort" />
   <Field Name="CommittedTop" Type="ushort" />
   <Field Name="CommittedBottom" Type="ushort" />
   <Field Name="UiTextureAtlasElementId" Type="ushort" />
   <Field Name="CommittedFlags" Type="byte" />
   <Field Name="UiCanvasId" Type="byte" />
</Table>
<Table Name="UiTextureKit" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="KitPrefix" Type="string" />
</Table>
<Table Name="UiWidget" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="WidgetTag" Type="string" />
   <Field Name="ParentSetId" Type="ushort" />
   <Field Name="VisId" Type="int" />
   <Field Name="MapId" Type="int" />
   <Field Name="PlayerConditionId" Type="int" />
   <Field Name="OrderIndex" Type="uint" />
</Table>
<Table Name="UiWidgetConstantSource" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="ReqId" Type="ushort" />
   <Field Name="Value" Type="int" />
</Table>
<Table Name="UiWidgetDataSource" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SourceId" Type="ushort" />
   <Field Name="SourceType" Type="byte" />
   <Field Name="ReqId" Type="ushort" />
</Table>
<Table Name="UiWidgetStringSource" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Value" Type="string" />
   <Field Name="ReqId" Type="ushort" />
</Table>
<Table Name="UiWidgetVisualization" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="VisType" Type="byte" />
   <Field Name="TextureKit" Type="int" />
   <Field Name="FrameTextureKit" Type="int" />
   <Field Name="Field_04" Type="short" />
</Table>
<Table Name="UnitBlood" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PlayerCritBloodSpurtId" Type="uint" />
   <Field Name="PlayerHitBloodSpurtId" Type="uint" />
   <Field Name="DefaultBloodSpurtId" Type="uint" />
   <Field Name="PlayerOmniCritBloodSpurtId" Type="uint" />
   <Field Name="PlayerOmniHitBloodSpurtId" Type="uint" />
   <Field Name="DefaultOmniBloodSpurtId" Type="uint" />
</Table>
<Table Name="UnitBloodLevels" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Violencelevel" Type="byte" ArraySize="3" />
</Table>
<Table Name="UnitCondition" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="byte" />
   <Field Name="Variable" Type="byte" ArraySize="8" />
   <Field Name="Op" Type="byte" ArraySize="8" />
   <Field Name="Value" Type="int" ArraySize="8" />
</Table>
<Table Name="UnitPowerBar" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Cost" Type="string" />
   <Field Name="OutOfError" Type="string" />
   <Field Name="ToolTip" Type="string" />
   <Field Name="MinPower" Type="uint" />
   <Field Name="MaxPower" Type="uint" />
   <Field Name="StartPower" Type="ushort" />
   <Field Name="CenterPower" Type="byte" />
   <Field Name="RegenerationPeace" Type="float" />
   <Field Name="RegenerationCombat" Type="float" />
   <Field Name="BarType" Type="byte" />
   <Field Name="Flags" Type="ushort" />
   <Field Name="StartInset" Type="float" />
   <Field Name="EndInset" Type="float" />
   <Field Name="FileDataId" Type="int" ArraySize="6" />
   <Field Name="Color" Type="uint" ArraySize="6" />
</Table>
<Table Name="Vehicle" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Flags" Type="uint" />
   <Field Name="FlagsB" Type="byte" />
   <Field Name="TurnSpeed" Type="float" />
   <Field Name="PitchSpeed" Type="float" />
   <Field Name="PitchMin" Type="float" />
   <Field Name="PitchMax" Type="float" />
   <Field Name="MouseLookOffsetPitch" Type="float" />
   <Field Name="CameraFadeDistScalarMin" Type="float" />
   <Field Name="CameraFadeDistScalarMax" Type="float" />
   <Field Name="CameraPitchOffset" Type="float" />
   <Field Name="FacingLimitRight" Type="float" />
   <Field Name="FacingLimitLeft" Type="float" />
   <Field Name="CameraYawOffset" Type="float" />
   <Field Name="UiLocomotionType" Type="byte" />
   <Field Name="VehicleUIIndicatorId" Type="ushort" />
   <Field Name="MissileTargetingId" Type="int" />
   <Field Name="SeatId" Type="ushort" ArraySize="8" />
   <Field Name="PowerDisplayId" Type="ushort" ArraySize="3" />
</Table>
<Table Name="VehicleSeat" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AttachmentOffset" Type="float" ArraySize="3" />
   <Field Name="CameraOffset" Type="float" ArraySize="3" />
   <Field Name="Flags" Type="uint" />
   <Field Name="FlagsB" Type="uint" />
   <Field Name="FlagsC" Type="uint" />
   <Field Name="AttachmentId" Type="byte" />
   <Field Name="EnterPreDelay" Type="float" />
   <Field Name="EnterSpeed" Type="float" />
   <Field Name="EnterGravity" Type="float" />
   <Field Name="EnterMinDuration" Type="float" />
   <Field Name="EnterMaxDuration" Type="float" />
   <Field Name="EnterMinArcHeight" Type="float" />
   <Field Name="EnterMaxArcHeight" Type="float" />
   <Field Name="EnterAnimStart" Type="int" />
   <Field Name="EnterAnimLoop" Type="int" />
   <Field Name="RideAnimStart" Type="int" />
   <Field Name="RideAnimLoop" Type="int" />
   <Field Name="RideUpperAnimStart" Type="int" />
   <Field Name="RideUpperAnimLoop" Type="int" />
   <Field Name="ExitPreDelay" Type="float" />
   <Field Name="ExitSpeed" Type="float" />
   <Field Name="ExitGravity" Type="float" />
   <Field Name="ExitMinDuration" Type="float" />
   <Field Name="ExitMaxDuration" Type="float" />
   <Field Name="ExitMinArcHeight" Type="float" />
   <Field Name="ExitMaxArcHeight" Type="float" />
   <Field Name="ExitAnimStart" Type="int" />
   <Field Name="ExitAnimLoop" Type="int" />
   <Field Name="ExitAnimEnd" Type="int" />
   <Field Name="VehicleEnterAnim" Type="short" />
   <Field Name="VehicleEnterAnimBone" Type="byte" />
   <Field Name="VehicleExitAnim" Type="short" />
   <Field Name="VehicleExitAnimBone" Type="byte" />
   <Field Name="VehicleRideAnimLoop" Type="short" />
   <Field Name="VehicleRideAnimLoopBone" Type="byte" />
   <Field Name="PassengerAttachmentId" Type="byte" />
   <Field Name="PassengerYaw" Type="float" />
   <Field Name="PassengerPitch" Type="float" />
   <Field Name="PassengerRoll" Type="float" />
   <Field Name="VehicleEnterAnimDelay" Type="float" />
   <Field Name="VehicleExitAnimDelay" Type="float" />
   <Field Name="VehicleAbilityDisplay" Type="byte" />
   <Field Name="EnterUISoundId" Type="int" />
   <Field Name="ExitUISoundId" Type="uint" />
   <Field Name="UiSkinFileDataId" Type="int" />
   <Field Name="CameraEnteringDelay" Type="float" />
   <Field Name="CameraEnteringDuration" Type="float" />
   <Field Name="CameraExitingDelay" Type="float" />
   <Field Name="CameraExitingDuration" Type="float" />
   <Field Name="CameraPosChaseRate" Type="float" />
   <Field Name="CameraFacingChaseRate" Type="float" />
   <Field Name="CameraEnteringZoom" Type="float" />
   <Field Name="CameraSeatZoomMin" Type="float" />
   <Field Name="CameraSeatZoomMax" Type="float" />
   <Field Name="EnterAnimKitId" Type="ushort" />
   <Field Name="RideAnimKitId" Type="short" />
   <Field Name="ExitAnimKitId" Type="ushort" />
   <Field Name="VehicleEnterAnimKitId" Type="short" />
   <Field Name="VehicleRideAnimKitId" Type="ushort" />
   <Field Name="VehicleExitAnimKitId" Type="short" />
   <Field Name="CameraModeId" Type="short" />
</Table>
<Table Name="VehicleUIIndicator" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BackgroundTextureFileId" Type="int" />
</Table>
<Table Name="VehicleUIIndSeat" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="VirtualSeatIndex" Type="byte" />
   <Field Name="XPos" Type="float" />
   <Field Name="YPos" Type="float" />
</Table>
<Table Name="Vignette" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="Field_03" Type="int" />
   <Field Name="QuestFeedbackEffectId" Type="int" />
   <Field Name="Flags" Type="uint" />
   <Field Name="MaxHeight" Type="float" />
   <Field Name="MinHeight" Type="float" />
   <Field Name="Field_08" Type="byte" />
   <Field Name="Field_09" Type="int" />
</Table>
<Table Name="VirtualAttachment" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="PositionerId" Type="short" />
</Table>
<Table Name="VirtualAttachmentCustomization" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="VirtualAttachmentId" Type="ushort" />
   <Field Name="FileDataId" Type="int" />
   <Field Name="PositionerId" Type="ushort" />
</Table>
<Table Name="VocalUISounds" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="VocalUIEnum" Type="byte" />
   <Field Name="RaceId" Type="byte" />
   <Field Name="ClassId" Type="byte" />
   <Field Name="NormalSoundId" Type="int" ArraySize="2" />
</Table>
<Table Name="WbAccessControlList" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="URL" Type="string" />
   <Field Name="GrantFlags" Type="ushort" />
   <Field Name="RevokeFlags" Type="byte" />
   <Field Name="WowEditInternal" Type="byte" />
   <Field Name="RegionId" Type="byte" />
</Table>
<Table Name="WbCertWhitelist" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Domain" Type="string" />
   <Field Name="GrantAccess" Type="byte" />
   <Field Name="RevokeAccess" Type="byte" />
   <Field Name="WowEditInternal" Type="byte" />
</Table>
<Table Name="WeaponImpactSounds" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="WeaponSubClassId" Type="byte" />
   <Field Name="ParrySoundType" Type="byte" />
   <Field Name="ImpactSource" Type="byte" />
   <Field Name="ImpactSoundId" Type="int" ArraySize="11" />
   <Field Name="CritImpactSoundId" Type="int" ArraySize="11" />
   <Field Name="PierceImpactSoundId" Type="int" ArraySize="11" />
   <Field Name="PierceCritImpactSoundId" Type="int" ArraySize="11" />
</Table>
<Table Name="WeaponSwingSounds2" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SwingType" Type="byte" />
   <Field Name="Crit" Type="byte" />
   <Field Name="SoundId" Type="uint" />
</Table>
<Table Name="WeaponTrail" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FileDataId" Type="int" />
   <Field Name="Roll" Type="float" />
   <Field Name="Pitch" Type="float" />
   <Field Name="Yaw" Type="float" />
   <Field Name="TextureFileDataId" Type="int" ArraySize="3" />
   <Field Name="TextureScrollRateU" Type="float" ArraySize="3" />
   <Field Name="TextureScrollRateV" Type="float" ArraySize="3" />
   <Field Name="TextureScaleU" Type="float" ArraySize="3" />
   <Field Name="TextureScaleV" Type="float" ArraySize="3" />
</Table>
<Table Name="WeaponTrailModelDef" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="LowDefFileDataId" Type="int" />
   <Field Name="WeaponTrailId" Type="ushort" />
</Table>
<Table Name="WeaponTrailParam" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Hand" Type="byte" />
   <Field Name="Duration" Type="float" />
   <Field Name="FadeOutTime" Type="float" />
   <Field Name="EdgeLifeSpan" Type="float" />
   <Field Name="InitialDelay" Type="float" />
   <Field Name="SmoothSampleAngle" Type="float" />
   <Field Name="OverrideAttachTop" Type="byte" />
   <Field Name="OverrideAttachBot" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="Weather" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Field_01" Type="byte" />
   <Field Name="Field_02" Type="float" />
   <Field Name="Field_03" Type="uint" />
   <Field Name="Field_04" Type="ushort" />
   <Field Name="Field_05" Type="byte" />
   <Field Name="Field_06" Type="int" />
   <Field Name="Field_07" Type="byte" />
   <Field Name="Field_08" Type="float" />
   <Field Name="Field_09" Type="float" />
   <Field Name="Field_0A" Type="float" />
   <Field Name="Field_0B" Type="float" />
   <Field Name="Field_0C" Type="float" />
   <Field Name="Field_0D" Type="int" />
   <Field Name="Field_0E" Type="float" />
   <Field Name="Field_0F" Type="int" />
   <Field Name="Field_10" Type="float" />
   <Field Name="Field_11" Type="float" />
   <Field Name="Field_12" Type="float" />
   <Field Name="Field_13" Type="int" />
   <Field Name="Field_14" Type="int" />
   <Field Name="Intensity" Type="float" ArraySize="2" />
   <Field Name="EffectColor" Type="float" ArraySize="3" />
</Table>
<Table Name="WeatherXParticulate" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FileDataId" Type="int" />
</Table>
<Table Name="WindSettings" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="BaseDir" Type="float" ArraySize="3" />
   <Field Name="VarianceDir" Type="float" ArraySize="3" />
   <Field Name="MaxStepDir" Type="float" ArraySize="3" />
   <Field Name="BaseMag" Type="float" />
   <Field Name="VarianceMagOver" Type="float" />
   <Field Name="VarianceMagUnder" Type="float" />
   <Field Name="MaxStepMag" Type="float" />
   <Field Name="Frequency" Type="float" />
   <Field Name="Duration" Type="float" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="WMOAreaTable" Build="26806">
   <Field Name="AreaName" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="WmoId" Type="ushort" />
   <Field Name="NameSetId" Type="byte" />
   <Field Name="WmoGroupId" Type="int" />
   <Field Name="SoundProviderPref" Type="byte" />
   <Field Name="SoundProviderPrefUnderwater" Type="byte" />
   <Field Name="AmbienceId" Type="ushort" />
   <Field Name="UwAmbience" Type="ushort" />
   <Field Name="ZoneMusic" Type="ushort" />
   <Field Name="UwZoneMusic" Type="int" />
   <Field Name="IntroSound" Type="ushort" />
   <Field Name="UwIntroSound" Type="ushort" />
   <Field Name="AreaTableId" Type="ushort" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="WmoMinimapTexture" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="FileDataId" Type="int" />
   <Field Name="GroupNum" Type="ushort" />
   <Field Name="BlockX" Type="byte" />
   <Field Name="BlockY" Type="byte" />
</Table>
<Table Name="WorldBossLockout" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="TrackingQuestId" Type="ushort" />
</Table>
<Table Name="WorldChunkSounds" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MapId" Type="ushort" />
   <Field Name="SoundOverrideId" Type="byte" />
   <Field Name="ChunkX" Type="byte" />
   <Field Name="ChunkY" Type="byte" />
   <Field Name="SubChunkX" Type="byte" />
   <Field Name="SubChunkY" Type="byte" />
</Table>
<Table Name="WorldEffect" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="QuestFeedbackEffectId" Type="int" />
   <Field Name="WhenToDisplay" Type="byte" />
   <Field Name="TargetType" Type="byte" />
   <Field Name="TargetAsset" Type="uint" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="CombatConditionId" Type="ushort" />
</Table>
<Table Name="WorldElapsedTimer" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Type" Type="byte" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="WorldMapOverlay" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="UiMapArtId" Type="uint" />
   <Field Name="TextureWidth" Type="ushort" />
   <Field Name="TextureHeight" Type="ushort" />
   <Field Name="OffsetX" Type="int" />
   <Field Name="OffsetY" Type="int" />
   <Field Name="HitRectTop" Type="int" />
   <Field Name="HitRectBottom" Type="int" />
   <Field Name="HitRectLeft" Type="int" />
   <Field Name="HitRectRight" Type="int" />
   <Field Name="PlayerConditionId" Type="uint" />
   <Field Name="Flags" Type="uint" />
   <Field Name="AreaId" Type="int" ArraySize="4" />
</Table>
<Table Name="WorldMapOverlayTile" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="RowIndex" Type="byte" />
   <Field Name="ColIndex" Type="byte" />
   <Field Name="LayerIndex" Type="byte" />
   <Field Name="FileDataId" Type="int" />
</Table>
<Table Name="WorldSafeLocs" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="Loc" Type="float" ArraySize="3" />
   <Field Name="MapId" Type="ushort" />
   <Field Name="Rotation" Type="float" />
</Table>
<Table Name="WorldState" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
</Table>
<Table Name="WorldStateExpression" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Expression" Type="string" />
</Table>
<Table Name="WorldStateUI" Build="26806">
   <Field Name="Icon" Type="string" />
   <Field Name="String" Type="string" />
   <Field Name="Tooltip" Type="string" />
   <Field Name="DynamicTooltip" Type="string" />
   <Field Name="ExtendedUI" Type="string" />
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="MapId" Type="short" />
   <Field Name="AreaId" Type="ushort" />
   <Field Name="StateVariable" Type="ushort" />
   <Field Name="Type" Type="byte" />
   <Field Name="DynamicIconFileId" Type="int" />
   <Field Name="DynamicFlashIconFileId" Type="int" />
   <Field Name="OrderIndex" Type="byte" />
   <Field Name="PhaseUseFlags" Type="byte" />
   <Field Name="PhaseId" Type="ushort" />
   <Field Name="PhaseGroupId" Type="ushort" />
   <Field Name="ExtendedUIStateVariable" Type="ushort" ArraySize="3" />
</Table>
<Table Name="WorldStateZoneSounds" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="WorldStateId" Type="ushort" />
   <Field Name="WorldStateValue" Type="ushort" />
   <Field Name="AreaId" Type="ushort" />
   <Field Name="WmoAreaId" Type="int" />
   <Field Name="ZoneIntroMusicId" Type="ushort" />
   <Field Name="ZoneMusicId" Type="ushort" />
   <Field Name="SoundAmbienceId" Type="ushort" />
   <Field Name="SoundProviderPreferencesId" Type="byte" />
</Table>
<Table Name="World_PVP_Area" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="AreaId" Type="ushort" />
   <Field Name="NextTimeWorldstate" Type="ushort" />
   <Field Name="GameTimeWorldstate" Type="ushort" />
   <Field Name="BattlePopulateTime" Type="ushort" />
   <Field Name="MinLevel" Type="byte" />
   <Field Name="MaxLevel" Type="byte" />
   <Field Name="MapId" Type="short" />
</Table>
<Table Name="ZoneIntroMusicTable" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="SoundId" Type="uint" />
   <Field Name="Priority" Type="byte" />
   <Field Name="MinDelayMinutes" Type="ushort" />
</Table>
<Table Name="ZoneLight" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Name" Type="string" />
   <Field Name="MapId" Type="ushort" />
   <Field Name="LightId" Type="ushort" />
   <Field Name="Flags" Type="byte" />
</Table>
<Table Name="ZoneLightPoint" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="Pos" Type="float" ArraySize="2" />
   <Field Name="PointOrder" Type="byte" />
</Table>
<Table Name="ZoneMusic" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="SetName" Type="string" />
   <Field Name="SilenceIntervalMin" Type="int" ArraySize="2" />
   <Field Name="SilenceIntervalMax" Type="int" ArraySize="2" />
   <Field Name="Sounds" Type="int" ArraySize="2" />
</Table>
<Table Name="ZoneStory" Build="26806">
   <Field Name="ID" Type="int" IsIndex="true" />
   <Field Name="PlayerFactionGroupId" Type="byte" />
   <Field Name="DisplayAchievementId" Type="uint" />
   <Field Name="DisplayWorldMapAreaId" Type="uint" />
</Table>
</Definition>
