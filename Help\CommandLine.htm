
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>Command Line</title>
<meta name="GENERATOR" content="WinCHM">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style>
html,body { 
	/* Default Font */
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11pt;
}
</style>




<style type="text/css">

#winchm_template_navigation{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-SIZE: 11px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}

#winchm_template_title{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-WEIGHT: bold; 
	FONT-SIZE: 14px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}


</style>
</head>

<body topMargin=0 marginheight="0" marginwidth="10">
<TABLE style="HEIGHT: 50px; WIDTH: 100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
  
  <TR>
    <TD>
      <DIV id=winchm_template_navigation><FONT color=#000000>WDBX 
      Editor&nbsp;Help </FONT> </DIV>
      <DIV id=winchm_template_title><FONT color=#000080>Command Line</FONT></DIV></TD>
    <TD align=right width=50><A href="Tools.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="Notes.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></TD></TR></TABLE>   

<HR>
<BR>
<P><STRONG>Command Line Arguments</STRONG> </P>

<STRONG>Console Mode</STRONG>
<P>
Opens the editor in a console window. Type help to see a list of commands.
<BR>
Arguments:
<UL>
<LI>
-console
</LI>
</UL>
<BR>
<CODE>Example: -console</CODE>
</P>


<STRONG>Extract</STRONG>
<P>
Extracts files matching the file filter from either an MPQ archive or a CASC directory.
<BR>
Arguments:
<UL>
<LI>
-f : File filter, * is used as a wildcard
<LI>
-s : Source, this is the location of the MPQ file or the CASC directory
<LI>
-o Output directory, this is the folder to export the files to
</LI>
</UL>
<BR>
<CODE>Example: -extract -f "*.dbc" -s "E:\WoW\Data\Patch-3.mpq" -o "C:\WotLK\"</CODE>
</P>

<STRONG>Export</STRONG>
<P>
Exports a specific file to either CSV, JSON or a SQL file.
<BR>
Arguments:
<UL>
<LI>
-f : File, if combined with a source this simply needs to be the filename otherwise the full file path
<LI>
-s : Source, this is the optional location of the MPQ file or the CASC directory
<LI>
-b : Build number to load the correct definition from
<LI>
-o Output directory, this is the output file name and location
</LI>
</UL>
<BR>
<CODE>Example: -export -f "Achievement.dbc" -s "E:\WoW\Data\Patch-3.mpq" -b 11802 -o "Achievement.csv"</CODE>
</P>

<STRONG>SQL Dump</STRONG>
<P>
Dumps a specific file's data into a MySQL  database.
<BR>
Arguments:
<UL>
<LI>
-f : File, if combined with a source this simply needs to be the filename otherwise the full file path
<LI>
-s : Source, this is the optional location of the MPQ file or the CASC directory
<LI>
-b : Build number to load the correct definition from
<LI>
-c Connection string, the MySQL connection string including database name
</LI>
</UL>
<BR>
<CODE>Example: -sqldump -f "Achievement.dbc" -s "E:\WoW\Data\Patch-3.mpq" -b 11802 -c "Server=myServerAddress;Database=myDataBase;Uid=myUsername;Pwd=myPassword;"</CODE>
</P>

<BR> <BR><BR>

<HR>

<P></FONT></P>
<P align=right><A href="Tools.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="Notes.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></P>
</body>
</html>
