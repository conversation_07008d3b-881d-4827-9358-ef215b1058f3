
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>Tools</title>
<meta name="GENERATOR" content="WinCHM">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style>
html,body { 
	/* Default Font */
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11pt;
}
</style>




<style type="text/css">

#winchm_template_navigation{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-SIZE: 11px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}

#winchm_template_title{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-WEIGHT: bold; 
	FONT-SIZE: 14px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}


</style>
</head>

<body topMargin=0 marginheight="0" marginwidth="10">
<TABLE style="HEIGHT: 50px; WIDTH: 100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
  
  <TR>
    <TD>
      <DIV id=winchm_template_navigation><FONT color=#000000>WDBX 
      Editor&nbsp;Help </FONT> </DIV>
      <DIV id=winchm_template_title><FONT color=#000080>Tips and Notes</FONT></DIV></TD>
    <TD align=right width=50><A href="CommandLine.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;</TD></TR></TABLE>   

<HR>
<BR>
<P><STRONG>Tips and Notes</STRONG> </P>
<P>Some things to remember and note:
<UL>
<LI><DIV>Importing gives you the option to import; new rows, changed and new rows and to override all data</DIV>
<LI><DIV>Legion ADB files MUST have the DB2 counterpart open BEFORE as required information is stored in the DB2 file. The program will prioritise DB2 if DB2 and ADB are opened at the same time</DIV>
<LI><DIV>Find and Replace can be set to case sensitive and match entire cell value</DIV>
<LI><DIV>Database connection details are saved on successful connection</DIV>
<LI><DIV>The WDB5 Parser works surprisingly well thanks to the new field structure data however it does trip up on the inline strings so definitions will need to be validated manually</DIV>
<LI><DIV>Undo, redo and copy data history are lost when changing the current file</DIV>
<LI><DIV>Everything is stored in memory so if your PC is ancient the program will crash attempting to read hundreds of files at once!</DIV>

</LI>
</UL>
</P>

<HR>

<P></FONT></P>
<P align=right><A href="CommandLine.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;</P>
</body>
</html>
