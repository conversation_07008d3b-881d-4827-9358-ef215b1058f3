
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>Introduction</title>
<meta name="GENERATOR" content="WinCHM">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style>
html,body { 
	/* Default Font */
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11pt;
}
</style>




<style type="text/css">

#winchm_template_navigation{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-SIZE: 11px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}

#winchm_template_title{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-WEIGHT: bold; 
	FONT-SIZE: 14px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}


</style>
</head>

<body topMargin=0 marginheight="0" marginwidth="10">
<TABLE style="HEIGHT: 50px; WIDTH: 100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
  
  <TR>
    <TD>
      <DIV id=winchm_template_navigation><FONT color=#000000>WDBX 
      Editor&nbsp;Help  </FONT> </DIV>
      <DIV id=winchm_template_title><FONT color=#000080>Introduction</FONT></DIV></TD>
    <TD align=right width=50>&nbsp;<A href="Menu_Items.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></TD></TR></TABLE>   

<HR>
<BR>
<P>WDBX Editor is an editor designed to open all release 
client database files implemented in World of Warcraft clients.</P>
<P>The currently supported file formats are:</P>
<UL>
  <LI>
<DIV> DBC</DIV>
  <LI>
<DIV> DB2</DIV>
  <LI>
<DIV> ADB</DIV>
  <LI>
<DIV> WDB</DIV></LI></UL>
<P> The editor works with all versions of World of Warcraft 
ranging from the Alpha (0.5.3) up to the latest build of Legion.</P>
<P> The base editor features includes:</P>
<UL>
<LI><DIV>Full support of release versions of DBC, DB2, WDB and ADB (WCH3 and WCH4 are not supported as I deem them depreciated)</DIV>
<LI><DIV>Opening and having open multiple files regardless of type and build</DIV>
<LI><DIV>Open DBC/DB2 files from both MPQ and CASC directories</DIV>
<LI><DIV>Save single (to file) and save all (to folder)</DIV>
<LI><DIV>Standard CRUD operations as well as go to, copy row, paste row, undo and redo</DIV>
<LI><DIV>Hide, show and sort columns</DIV>
<LI><DIV>A relatively powerful column filter system</DIV>
<LI><DIV>Displaying and editing columns in hex (supported on numeric columns only)</DIV>
<LI><DIV>Exporting to SQL Database, SQL file, CSV and MPQ</DIV>
<LI><DIV>Importing from SQL Database and CSV</DIV>
<LI><DIV>An Excel style Find and Replace</DIV>
<LI><DIV>Shortcuts for common tasks using commonly used key combinations</DIV>
</LI></UL>
<P> I've included some functionality around 
Importing, Exporting and also some tools to help do common tasks. The <a href="datagrid.htm">data grid</a> itself has also been custom built to implement common grid features.</P>

<P>The application works as a single instance so all files will open within the original instance of the program. There is however an option to open new files in a new instance. The program also has <a href="commandline.htm">command line</a> arguments which can be utalised with batch scripts and provides integration into other systems.</P>

<BR> <BR><BR>

<HR>

<P></FONT></P>
<P align=right>&nbsp;<A href="Menu_Items.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></P>
</body>
</html>
