
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>Tools</title>
<meta name="GENERATOR" content="WinCHM">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style>
html,body { 
	/* Default Font */
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11pt;
}
</style>




<style type="text/css">

#winchm_template_navigation{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-SIZE: 11px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}

#winchm_template_title{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-WEIGHT: bold; 
	FONT-SIZE: 14px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}


</style>
</head>

<body topMargin=0 marginheight="0" marginwidth="10">
<TABLE style="HEIGHT: 50px; WIDTH: 100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
  
  <TR>
    <TD>
      <DIV id=winchm_template_navigation><FONT color=#000000>WDBX 
      Editor&nbsp;Help </FONT> </DIV>
      <DIV id=winchm_template_title><FONT color=#000080>Data Grid</FONT></DIV></TD>
<TD align=right width=50><A href="Import_Export.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="Tools.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></TD></</TR></TABLE>   

<HR>
<BR>
<P><STRONG>Custom Header</STRONG> </P>
<P>The data grid has been custom built to support basic actions that should be included in any grid based program. These include:
<UL>
<LI><DIV>Sorting - Two way sorts for both numbers and strings</DIV>
<LI><DIV>Filter - A relatively complex filtering system is available whereby chaining AND or OR statements together can be achieved (similar to boolean search). There are several operators that are distinct to the data type</DIV>
<LI><DIV>Hex View - Integers also have the option to be displayed as hex which is in the format of <b>0x{0:X?}</b> with ? being the largest field length. With this turned on you will only be able to edit in hex (prefixing with '0x' isn't required)</DIV>
</LI>
</UL>
</P>

<P><STRONG>Column Filter</STRONG></P>
<P>The column filter allows the hiding of any specified columns and the option to restore all, this is particularly useful for localised strings in older DBC files when you are only working with one locale. This state is reset everytime the current file being edited is changed</P>

<HR>

<P></FONT></P>
<P align=right><A href="Import_Export.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="Tools.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></P>
</body>
</html>
