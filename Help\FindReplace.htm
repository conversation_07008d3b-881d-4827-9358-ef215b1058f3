
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>Tools</title>
<meta name="GENERATOR" content="WinCHM">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style>
html,body { 
	/* Default Font */
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11pt;
}
</style>




<style type="text/css">

#winchm_template_navigation{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-SIZE: 11px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}

#winchm_template_title{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-WEIGHT: bold; 
	FONT-SIZE: 14px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}


</style>
</head>

<body topMargin=0 marginheight="0" marginwidth="10">
<TABLE style="HEIGHT: 50px; WIDTH: 100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
  
  <TR>
    <TD>
      <DIV id=winchm_template_navigation><FONT color=#000000>WDBX 
      Editor&nbsp;Help </FONT> </DIV>
      <DIV id=winchm_template_title><FONT color=#000080>Find and Replace</FONT></DIV></TD>
    <TD align=right width=50><A href="Menu_Items.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="Import_Export.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></TD></TR></TABLE>   

<HR>
<BR>
<P><STRONG>Find and Replace</STRONG> </P>
<P>The Find and Replace window is designed to stay active when used and changes opacity in a similar fashion to Notepad++. There are several search options that are available:
<UL>
<LI><DIV>Match Case - Turn on/off case sensitivity</DIV>
<LI><DIV>Match Entire Cell Contents - Only returns if the search term is the cell's entire value</DIV>
</LI>
</UL>
</P>

<HR>

<P></FONT></P>
<P align=right><A href="Menu_Items.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="Import_Export.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></P>
</body>
</html>
