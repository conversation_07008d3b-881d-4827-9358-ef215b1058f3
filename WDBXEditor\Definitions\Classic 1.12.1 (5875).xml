<?xml version="1.0"?>
<Definition xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <Table Name="AnimationData" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="WeaponFlags" Type="int" />
    <Field Name="BodyFlags" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Fallback" Type="int" />
    <Field Name="BehaviourID" Type="int" />
  </Table>
  <Table Name="AreaPOI" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Importance" Type="int" />
    <Field Name="Icon" Type="int" />
    <Field Name="FactionID" Type="int" />
    <Field Name="X" Type="float" />
    <Field Name="Y" Type="float" />
    <Field Name="Z" Type="float" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="AreaID" Type="int" />
    <Field Name="Name" Type="loc" />
    <Field Name="Description" Type="loc" />
    <Field Name="WorldStateID" Type="int" />
  </Table>
  <Table Name="AreaTable" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="ParentAreaID" Type="int" />
    <Field Name="AreaBit" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="SoundProviderPref" Type="int" />
    <Field Name="SoundProviderPrefUnderWater" Type="int" />
    <Field Name="AmbienceID" Type="int" />
    <Field Name="ZoneMusic" Type="int" />
    <Field Name="IntroSound" Type="int" />
    <Field Name="ExplorationLevel" Type="int" />
    <Field Name="AreaName" Type="loc" />
    <Field Name="FactionGroupMask" Type="int" />
    <Field Name="LiquidTypeID" Type="int" />
    <Field Name="MinElevation" Type="int" />
    <Field Name="AmbientMultiplier" Type="float" />
    <Field Name="LightID" Type="int" />
  </Table>
  <Table Name="AreaTrigger" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="X" Type="float" />
    <Field Name="Y" Type="float" />
    <Field Name="Z" Type="float" />
    <Field Name="Radius" Type="float" />
    <Field Name="BoxLength" Type="int" />
    <Field Name="BoxWidth" Type="int" />
    <Field Name="BoxHeight" Type="int" />
    <Field Name="BoxYaw" Type="int" />
  </Table>
  <Table Name="AttackAnimKits" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AnimationID" Type="int" />
    <Field Name="AttackTypeID" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Field04" Type="int" />
  </Table>
  <Table Name="AttackAnimTypes" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="AuctionHouse" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FactionID" Type="int" />
    <Field Name="DepositRate" Type="int" />
    <Field Name="ConsignmentRate" Type="int" />
    <Field Name="Name" Type="loc" />
  </Table>
  <Table Name="BankBagSlotPrices" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Cost" Type="int" />
  </Table>
  <Table Name="CameraShakes" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ShakeType" Type="int" />
    <Field Name="Direction" Type="int" />
    <Field Name="Amplitude" Type="float" />
    <Field Name="Frequency" Type="float" />
    <Field Name="Duration" Type="float" />
    <Field Name="Phase" Type="float" />
    <Field Name="Coefficient" Type="float" />
  </Table>
  <Table Name="Cfg_Categories" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Group" Type="int" />
    <Field Name="Name" Type="loc" />
  </Table>
  <Table Name="Cfg_Configs" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RealmType" Type="int" />
    <Field Name="PlayerKillingAllowed" Type="int" />
    <Field Name="Roleplaying" Type="int" />
  </Table>
  <Table Name="CharacterFacialHairStyles" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" AutoGenerate="true" />
    <Field Name="RaceID" Type="int" />
    <Field Name="SexID" Type="int" />
    <Field Name="VariationID" Type="int" />
    <Field Name="Geoset" Type="int" ArraySize="6" />
  </Table>
  <Table Name="CharBaseInfo" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" AutoGenerate="true" />
    <Field Name="RaceID" Type="byte" />
    <Field Name="ClassID" Type="byte" />
  </Table>
  <Table Name="CharHairGeosets" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceID" Type="int" />
    <Field Name="SexID" Type="int" />
    <Field Name="VariationID" Type="int" />
    <Field Name="GeosetID" Type="int" />
    <Field Name="Showscalp" Type="int" />
  </Table>
  <Table Name="CharHairTextures" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceID" Type="int" />
    <Field Name="SexID" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
  </Table>
  <Table Name="CharSections" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceID" Type="int" />
    <Field Name="SexID" Type="int" />
    <Field Name="SectionType" Type="int" />
    <Field Name="VariationIndex" Type="int" />
    <Field Name="ColorIndex" Type="int" />
    <Field Name="TextureName1" Type="string" />
    <Field Name="TextureName2" Type="string" />
    <Field Name="TextureName3" Type="string" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="CharStartOutfit" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RaceID" Type="byte" />
    <Field Name="ClassID" Type="byte" />
    <Field Name="SexID" Type="byte" />
    <Field Name="OutfitID" Type="byte" />
    <Field Name="ItemID" Type="int" ArraySize="12" />
    <Field Name="DisplayItemID" Type="int" ArraySize="12" />
    <Field Name="InventoryType" Type="int" ArraySize="12" />
  </Table>
  <Table Name="CharVariations" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SexID" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Mask1" Type="int" />
    <Field Name="Mask2" Type="int" />
    <Field Name="Field05" Type="int" />
  </Table>
  <Table Name="ChatChannels" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="FactionGroup" Type="int" />
    <Field Name="Name" Type="loc" />
    <Field Name="Shortcut" Type="loc" />
  </Table>
  <Table Name="ChatProfanity" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Text" Type="string" />
  </Table>
  <Table Name="ChrClasses" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="PowerType" Type="int" />
    <Field Name="PetNameToken" Type="string" />
    <Field Name="Name" Type="loc" />
    <Field Name="FileName" Type="string" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="int" />
  </Table>
  <Table Name="ChrRaces" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="FactionID" Type="int" />
    <Field Name="ExplorationSoundID" Type="int" />
    <Field Name="MaleDisplayID" Type="int" />
    <Field Name="FemaleDisplayID" Type="int" />
    <Field Name="ClientPrefix" Type="string" />
    <Field Name="Speed" Type="float" />
    <Field Name="BaseLanguage" Type="int" />
    <Field Name="CreatureType" Type="int" />
    <Field Name="LoginEffect" Type="int" />
    <Field Name="UnalteredVisualRaceID" Type="int" />
    <Field Name="ResSicknessSpellID" Type="int" />
    <Field Name="SplashSoundID" Type="int" />
    <Field Name="Field14" Type="uint" />
    <Field Name="ClientFileString" Type="string" />
    <Field Name="CinematicSequenceID" Type="int" />
    <Field Name="Name" Type="loc" />
    <Field Name="FacialHairCustomization1" Type="string" />
    <Field Name="FacialHairCustomization2" Type="string" />
    <Field Name="HairCustomization" Type="string" />
  </Table>
  <Table Name="CinematicCamera" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Model" Type="string" />
    <Field Name="SoundID" Type="int" />
    <Field Name="OriginX" Type="float" />
    <Field Name="OriginY" Type="float" />
    <Field Name="OriginZ" Type="float" />
    <Field Name="OriginFacing" Type="float" />
  </Table>
  <Table Name="CinematicSequences" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundID" Type="int" />
    <Field Name="Camera" Type="int" ArraySize="8" />
  </Table>
  <Table Name="CreatureDisplayInfo" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ModelID" Type="int" />
    <Field Name="SoundID" Type="int" />
    <Field Name="ExtendedDisplayInfoID" Type="int" />
    <Field Name="CreatureModelScale" Type="float" />
    <Field Name="CreatureModelAlpha" Type="int" />
    <Field Name="TextureVariation1" Type="string" />
    <Field Name="TextureVariation2" Type="string" />
    <Field Name="TextureVariation3" Type="string" />
    <Field Name="SizeClass" Type="int" />
    <Field Name="BloodID" Type="int" />
    <Field Name="NPCSoundID" Type="int" />
  </Table>
  <Table Name="CreatureDisplayInfoExtra" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DisplayRaceID" Type="int" />
    <Field Name="DisplaySexID" Type="int" />
    <Field Name="SkinID" Type="int" />
    <Field Name="FaceID" Type="int" />
    <Field Name="HairStyleID" Type="int" />
    <Field Name="HairColorID" Type="int" />
    <Field Name="FacialHairID" Type="int" />
    <Field Name="NPCItemDisplayID" Type="int" ArraySize="9" />
    <Field Name="Flags" Type="int" />
    <Field Name="BakeName" Type="string" />
  </Table>
  <Table Name="CreatureFamily" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MinScale" Type="float" />
    <Field Name="MinScaleLevel" Type="int" />
    <Field Name="MaxScale" Type="float" />
    <Field Name="MaxScaleLevel" Type="int" />
    <Field Name="PetFoodMask" Type="int" />
    <Field Name="PetTalentType" Type="int" />
    <Field Name="CategoryEnumID" Type="int" />
    <Field Name="Name" Type="loc" />
    <Field Name="IconFile" Type="string" />
  </Table>
  <Table Name="CreatureModelData" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="ModelName" Type="string" />
    <Field Name="SizeClass" Type="int" />
    <Field Name="ModelScale" Type="float" />
    <Field Name="BloodID" Type="int" />
    <Field Name="FootprintTextureID" Type="int" />
    <Field Name="FootprintTextureLength" Type="float" />
    <Field Name="FootprintTextureWidth" Type="float" />
    <Field Name="FootprintTextureScale" Type="float" />
    <Field Name="FoleyMaterialID" Type="int" />
    <Field Name="FootstepShakeSize" Type="int" />
    <Field Name="DeathThudShakeSize" Type="int" />
    <Field Name="CollisionWidth" Type="float" />
    <Field Name="CollisionHeight" Type="float" />
    <Field Name="MountHeight" Type="float" />
  </Table>
  <Table Name="CreatureSoundData" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundExertionID" Type="int" />
    <Field Name="SoundExertionCriticalID" Type="int" />
    <Field Name="SoundInjuryID" Type="int" />
    <Field Name="SoundInjuryCriticalID" Type="int" />
    <Field Name="SoundInjuryCrushingBlowID" Type="int" />
    <Field Name="SoundDeathID" Type="int" />
    <Field Name="SoundStunID" Type="int" />
    <Field Name="SoundStandID" Type="int" />
    <Field Name="SoundFootstepID" Type="int" />
    <Field Name="SoundAggroID" Type="int" />
    <Field Name="SoundWingFlapID" Type="int" />
    <Field Name="SoundWingGlideID" Type="int" />
    <Field Name="SoundAlertID" Type="int" />
    <Field Name="SoundFidget" Type="int" ArraySize="4" />
    <Field Name="CustomAttack" Type="int" ArraySize="4" />
    <Field Name="NPCSoundID" Type="int" />
    <Field Name="LoopSoundID" Type="int" />
    <Field Name="CreatureImpactType" Type="int" />
    <Field Name="SoundJumpStartID" Type="int" />
    <Field Name="SoundJumpEndID" Type="int" />
    <Field Name="SoundPetAttackID" Type="int" />
    <Field Name="SoundPetOrderID" Type="int" />
    <Field Name="SoundPetDismissID" Type="int" />
  </Table>
  <Table Name="CreatureSpellData" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Spells" Type="int" ArraySize="4" />
    <Field Name="Availability" Type="int" ArraySize="4" />
  </Table>
  <Table Name="CreatureType" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="DeathThudLookups" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SizeClass" Type="int" />
    <Field Name="TerrainTypeSoundID" Type="int" />
    <Field Name="SoundEntryID" Type="int" />
    <Field Name="SoundEntryIDWater" Type="int" />
  </Table>
  <Table Name="DurabilityCosts" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WeaponSubClassCost" Type="int" ArraySize="21" />
    <Field Name="ArmorSubClassCost" Type="int" ArraySize="8" />
  </Table>
  <Table Name="DurabilityQuality" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Data" Type="float" />
  </Table>
  <Table Name="Emotes" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EmoteSlashCommand" Type="string" />
    <Field Name="AnimID" Type="int" />
    <Field Name="EmoteFlags" Type="int" />
    <Field Name="EmoteSpecProc" Type="int" />
    <Field Name="EmoteSpecProcParam" Type="int" />
    <Field Name="EventSoundID" Type="int" />
  </Table>
  <Table Name="EmotesText" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="EmoteID" Type="int" />
    <Field Name="EmoteText" Type="int" ArraySize="16" />
  </Table>
  <Table Name="EmotesTextData" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Text" Type="loc" />
  </Table>
  <Table Name="EmotesTextSound" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EmotesTextID" Type="int" />
    <Field Name="RaceID" Type="int" />
    <Field Name="SexID" Type="int" />
    <Field Name="SoundID" Type="int" />
  </Table>
  <Table Name="EnvironmentalDamage" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EnumID" Type="int" />
    <Field Name="VisualkitID" Type="int" />
  </Table>
  <Table Name="Exhaustion" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Xp" Type="int" />
    <Field Name="Factor" Type="float" />
    <Field Name="OutdoorHours" Type="float" />
    <Field Name="InnHours" Type="float" />
    <Field Name="Name" Type="loc" />
    <Field Name="Threshold" Type="int" />
  </Table>
  <Table Name="Faction" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ReputationIndex" Type="int" />
    <Field Name="ReputationRaceMask" Type="int" ArraySize="4" />
    <Field Name="ReputationClassMask" Type="int" ArraySize="4" />
    <Field Name="ReputationBase" Type="int" ArraySize="4" />
    <Field Name="ReputtationFlags" Type="int" ArraySize="4" />
    <Field Name="ParentFactionID" Type="int" />
    <Field Name="Name" Type="loc" />
    <Field Name="Description" Type="loc" />
  </Table>
  <Table Name="FactionGroup" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MaskID" Type="int" />
    <Field Name="InternalName" Type="string" />
    <Field Name="Name" Type="loc" />
  </Table>
  <Table Name="FactionTemplate" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Faction" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="FactionGroup" Type="int" />
    <Field Name="FriendGroup" Type="int" />
    <Field Name="EnemyGroup" Type="int" />
    <Field Name="Enemies" Type="int" ArraySize="4" />
    <Field Name="Friend" Type="int" ArraySize="4" />
  </Table>
  <Table Name="FootprintTextures" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FootstepFilename" Type="string" />
  </Table>
  <Table Name="FootstepTerrainLookup" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CreatureFootstepID" Type="int" />
    <Field Name="TerrainSoundID" Type="int" />
    <Field Name="SoundID" Type="int" />
    <Field Name="SoundIDSplash" Type="int" />
  </Table>
  <Table Name="GameObjectArtKit" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TextureVariation1" Type="string" />
    <Field Name="TextureVariation2" Type="string" />
    <Field Name="TextureVariation3" Type="string" />
    <Field Name="AttachModel1" Type="string" />
    <Field Name="AttachModel2" Type="string" />
    <Field Name="AttachModel3" Type="string" />
    <Field Name="AttachModel4" Type="string" />
  </Table>
  <Table Name="GameObjectDisplayInfo" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ModelName" Type="string" />
    <Field Name="Sound" Type="int" ArraySize="10" />
  </Table>
  <Table Name="GameTips" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Text" Type="loc" />
  </Table>
  <Table Name="GMSurveyCurrentSurvey" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="GMSURVEYID" Type="int" />
  </Table>
  <Table Name="GMSurveyQuestions" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Question" Type="loc" />
  </Table>
  <Table Name="GMSurveySurveys" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Q" Type="int" ArraySize="10" />
  </Table>
  <Table Name="GMTicketCategory" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Category" Type="loc" />
  </Table>
  <Table Name="GroundEffectDoodad" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="DoodadPath" Type="string" />
  </Table>
  <Table Name="GroundEffectTexture" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="DoodadID" Type="int" ArraySize="4" />
    <Field Name="Density" Type="int" />
    <Field Name="Sound" Type="int" />
  </Table>
  <Table Name="HelmetGeosetVisData" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="HideGeoset" Type="int" ArraySize="5" />
  </Table>
  <Table Name="ItemBagFamily" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
  </Table>
  <Table Name="ItemClass" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SubclassMapID" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="Classname" Type="loc" />
  </Table>
  <Table Name="ItemDisplayInfo" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ModelName1" Type="string" />
    <Field Name="ModelName2" Type="string" />
    <Field Name="ModelTexture1" Type="string" />
    <Field Name="ModelTexture2" Type="string" />
    <Field Name="InventoryIcon1" Type="string" />
    <Field Name="GeosetGroup" Type="int" ArraySize="3" />
    <Field Name="Flags" Type="int" />
    <Field Name="SpellVisualID" Type="int" />
    <Field Name="GroupSoundIndex" Type="int" />
    <Field Name="HelmetGeosetVis" Type="int" ArraySize="2" />
    <Field Name="Texture1" Type="string" />
    <Field Name="Texture2" Type="string" />
    <Field Name="Texture3" Type="string" />
    <Field Name="Texture4" Type="string" />
    <Field Name="Texture5" Type="string" />
    <Field Name="Texture6" Type="string" />
    <Field Name="Texture7" Type="string" />
    <Field Name="Texture8" Type="string" />
    <Field Name="ItemVisual" Type="int" />
  </Table>
  <Table Name="ItemGroupSounds" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundID" Type="int" ArraySize="4" />
  </Table>
  <Table Name="ItemPetFood" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
  </Table>
  <Table Name="ItemRandomProperties" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Enchantment" Type="int" ArraySize="5" />
    <Field Name="Suffix" Type="loc" />
  </Table>
  <Table Name="ItemSet" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
    <Field Name="ItemID" Type="int" ArraySize="10" />
    <Field Name="BankItemID" Type="int" ArraySize="7" />
    <Field Name="SetSpellID" Type="int" ArraySize="8" />
    <Field Name="SetThreshold" Type="int" ArraySize="8" />
    <Field Name="RequiredSkill" Type="int" />
    <Field Name="RequiredSkillRank" Type="int" />
  </Table>
  <Table Name="ItemSubClass" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SubClassID" Type="int" />
    <Field Name="PrerequisiteProficiency" Type="int" />
    <Field Name="PostrequisiteProficiency" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="DisplayFlags" Type="int" />
    <Field Name="WeaponParrySeq" Type="int" />
    <Field Name="WeaponReadySeq" Type="int" />
    <Field Name="WeaponAttackSeq" Type="int" />
    <Field Name="WeaponSwingSize" Type="int" />
    <Field Name="DisplayName" Type="loc" />
    <Field Name="VerboseName" Type="loc" />
  </Table>
  <Table Name="ItemSubClassMask" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Mask" Type="int" />
    <Field Name="Name" Type="loc" />
  </Table>
  <Table Name="ItemVisualEffects" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Model" Type="string" />
  </Table>
  <Table Name="ItemVisuals" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Slot" Type="int" ArraySize="5" />
  </Table>
  <Table Name="Languages" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
  </Table>
  <Table Name="LanguageWords" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LanguageID" Type="int" />
    <Field Name="Word" Type="string" />
  </Table>
  <Table Name="LFGDungeons" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
    <Field Name="HardLevelMin" Type="int" />
    <Field Name="HardLevelMax" Type="int" />
    <Field Name="InstanceType" Type="int" />
    <Field Name="Faction" Type="int" />
  </Table>
  <Table Name="Light" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="X" Type="float" />
    <Field Name="Y" Type="float" />
    <Field Name="Z" Type="float" />
    <Field Name="FalloffStart" Type="float" />
    <Field Name="FalloffEnd" Type="float" />
    <Field Name="LightParamsID" Type="int" ArraySize="5" />
  </Table>
  <Table Name="LightFloatBand" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Num" Type="int" />
    <Field Name="Time" Type="int" ArraySize="16" />
    <Field Name="Data" Type="float" ArraySize="16" />
  </Table>
  <Table Name="LightIntBand" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Num" Type="int" />
    <Field Name="Time" Type="int" ArraySize="16" />
    <Field Name="Data" Type="int" ArraySize="16" />
  </Table>
  <Table Name="LightParams" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="HighlightSky" Type="int" />
    <Field Name="LightSkyboxID" Type="int" />
    <Field Name="Glow" Type="float" />
    <Field Name="WaterShallowAlpha" Type="float" />
    <Field Name="WaterDeepAlpha" Type="float" />
    <Field Name="OceanShallowAlpha" Type="float" />
    <Field Name="OceanDeepAlpha" Type="float" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="LightSkybox" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="LiquidType" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="Type" Type="int" />
    <Field Name="SpellID" Type="int" />
  </Table>
  <Table Name="LoadingScreens" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="FileName" Type="string" />
  </Table>
  <Table Name="LoadingScreenTaxiSplines" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PathID" Type="int" />
    <Field Name="Locx" Type="float" ArraySize="8" />
    <Field Name="Locy" Type="float" ArraySize="8" />
    <Field Name="LegIndex" Type="int" />
  </Table>
  <Table Name="Lock" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Type" Type="int" ArraySize="8" />
    <Field Name="Index" Type="int" ArraySize="8" />
    <Field Name="Skill" Type="int" ArraySize="8" />
    <Field Name="Action" Type="int" ArraySize="8" />
  </Table>
  <Table Name="LockType" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
    <Field Name="ResourceName" Type="loc" />
    <Field Name="Verb" Type="loc" />
    <Field Name="CursorName" Type="string" />
  </Table>
  <Table Name="MailTemplate" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Body" Type="loc" />
  </Table>
  <Table Name="Map" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Directory" Type="string" />
    <Field Name="InstanceType" Type="int" />
    <Field Name="PVP" Type="int" />
    <Field Name="MapName" Type="loc" />
    <Field Name="MinLevel" Type="int" />
    <Field Name="MaxLevel" Type="int" />
    <Field Name="MaxPlayers" Type="int" />
    <Field Name="Field08" Type="int" />
    <Field Name="Field09" Type="int" />
    <Field Name="Field10" Type="int" />
    <Field Name="AreaTableID" Type="int" />
    <Field Name="MapDescription0" Type="loc" />
    <Field Name="MapDescription1" Type="loc" />
    <Field Name="LoadingScreenID" Type="int" />
    <Field Name="RaidOffset" Type="int" />
    <Field Name="Field16" Type="int" />
    <Field Name="Field17" Type="int" />
  </Table>
  <Table Name="Material" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="FoleySoundID" Type="int" />
  </Table>
  <Table Name="NameGen" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="RaceID" Type="int" />
    <Field Name="Sex" Type="int" />
  </Table>
  <Table Name="NamesProfanity" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="NamesReserved" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="NPCSounds" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundID" Type="int" ArraySize="4" />
  </Table>
  <Table Name="Package" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Icon" Type="string" />
    <Field Name="Cost" Type="int" />
    <Field Name="Name" Type="loc" />
  </Table>
  <Table Name="PageTextMaterial" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="PaperDollItemFrame" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SlotIcon" Type="string" />
    <Field Name="SlotNumber" Type="int" />
  </Table>
  <Table Name="PetLoyalty" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
  </Table>
  <Table Name="PetPersonality" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
    <Field Name="HappinessThreshold" Type="int" ArraySize="3" />
    <Field Name="HappinessDamage" Type="float" ArraySize="3" />
    <Field Name="DamageModifier" Type="float" ArraySize="3" />
  </Table>
  <Table Name="QuestInfo" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="InfoName" Type="loc" />
  </Table>
  <Table Name="QuestSort" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SortName" Type="loc" />
  </Table>
  <Table Name="Resistances" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
    <Field Name="FizzleSoundID" Type="int" />
    <Field Name="Name" Type="loc" />
  </Table>
  <Table Name="ServerMessages" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Text" Type="loc" />
  </Table>
  <Table Name="SheatheSoundLookups" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemClass" Type="int" />
    <Field Name="ItemSubclass" Type="int" />
    <Field Name="ItemEnvTypes" Type="int" />
    <Field Name="IsShield" Type="int" />
    <Field Name="SheathSoundID" Type="int" />
    <Field Name="UnsheathSoundID" Type="int" />
  </Table>
  <Table Name="SkillCostsData" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SkillCostsID" Type="int" />
    <Field Name="Cost" Type="int" ArraySize="3" />
  </Table>
  <Table Name="SkillLine" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CategoryID" Type="int" />
    <Field Name="SkillCostsID" Type="int" />
    <Field Name="DisplayName" Type="loc" />
    <Field Name="Description" Type="loc" />
    <Field Name="SpellIconID" Type="int" />
  </Table>
  <Table Name="SkillLineAbility" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SkillLine" Type="int" />
    <Field Name="Spell" Type="int" />
    <Field Name="RaceMask" Type="int" />
    <Field Name="ClassMask" Type="int" />
    <Field Name="ExcludeRace" Type="int" />
    <Field Name="ExcludeClass" Type="int" />
    <Field Name="MinSkillLineRank" Type="int" />
    <Field Name="SupercededBySpell" Type="int" />
    <Field Name="AquireMethod" Type="int" />
    <Field Name="TrivialSkillLineRankHigh" Type="int" />
    <Field Name="TrivialSkillLineRankLow" Type="int" />
    <Field Name="CharacterPoints" Type="int" ArraySize="2" />
    <Field Name="NumSkillUps" Type="int" />
  </Table>
  <Table Name="SkillLineCategory" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
    <Field Name="SortIndex" Type="int" />
  </Table>
  <Table Name="SkillRaceClassInfo" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SkillID" Type="int" />
    <Field Name="RaceMask" Type="int" />
    <Field Name="ClassMask" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="MinLevel" Type="int" />
    <Field Name="SkillTierID" Type="int" />
    <Field Name="SkillCostIndex" Type="int" />
  </Table>
  <Table Name="SkillTiers" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Cost" Type="int" ArraySize="16" />
    <Field Name="Value" Type="int" ArraySize="16" />
  </Table>
  <Table Name="SoundAmbience" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AmbienceID" Type="int" ArraySize="2" />
  </Table>
  <Table Name="SoundEntries" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundType" Type="int" />
    <Field Name="Name" Type="string" />
    <Field Name="File" Type="string" ArraySize="10" />
    <Field Name="Freq" Type="int" ArraySize="10" />
    <Field Name="DirectoryBase" Type="string" />
    <Field Name="VolumeFloat" Type="float" />
    <Field Name="Flags" Type="int" />
    <Field Name="MinDistance" Type="float" />
    <Field Name="DistanceCutoff" Type="float" />
    <Field Name="EAXDef" Type="int" />
  </Table>
  <Table Name="SoundProviderPreferences" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Description" Type="string" />
    <Field Name="Flags" Type="int" />
    <Field Name="EAXEnvironmentSelection" Type="int" />
    <Field Name="EAXDecayTime" Type="float" />
    <Field Name="EAX2EnvironmentSize" Type="float" />
    <Field Name="EAX2EnvironmentDiffusion" Type="float" />
    <Field Name="EAX2Room" Type="int" />
    <Field Name="EAX2RoomHF" Type="int" />
    <Field Name="EAX2DecayHFRatio" Type="float" />
    <Field Name="EAX2Reflections" Type="int" />
    <Field Name="EAX2ReflectionsDelay" Type="float" />
    <Field Name="EAX2Reverb" Type="int" />
    <Field Name="EAX2ReverbDelay" Type="float" />
    <Field Name="EAX2RoomRolloff" Type="float" />
    <Field Name="EAX2AirAbsorption" Type="float" />
    <Field Name="EAX3RoomLF" Type="int" />
    <Field Name="EAX3DecayLFRatio" Type="float" />
    <Field Name="EAX3EchoTime" Type="float" />
    <Field Name="EAX3EchoDepth" Type="float" />
    <Field Name="EAX3ModulationTime" Type="float" />
    <Field Name="EAX3ModulationDepth" Type="float" />
    <Field Name="EAX3HFReference" Type="float" />
    <Field Name="EAX3LFReference" Type="float" />
  </Table>
  <Table Name="SoundSamplePreferences" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="int" />
    <Field Name="Field02" Type="int" />
    <Field Name="Field03" Type="int" />
    <Field Name="Field04" Type="int" />
    <Field Name="Field05" Type="int" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="int" />
    <Field Name="Field08" Type="float" />
    <Field Name="Field09" Type="float" />
    <Field Name="Field10" Type="int" />
    <Field Name="Field11" Type="int" />
    <Field Name="Field12" Type="int" />
    <Field Name="Field13" Type="float" />
    <Field Name="Field14" Type="int" />
    <Field Name="Field15" Type="float" />
    <Field Name="Field16" Type="int" />
  </Table>
  <Table Name="SoundWaterType" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LiquidTypeID" Type="int" />
    <Field Name="FluidSpeed" Type="int" />
    <Field Name="SoundID" Type="int" />
  </Table>
  <Table Name="SpamMessages" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Text" Type="string" />
  </Table>
  <Table Name="Spell" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="School" Type="int" />
    <Field Name="Category" Type="int" />
    <Field Name="CastUI" Type="int" />
    <Field Name="DispelType" Type="int" />
    <Field Name="Mechanic" Type="int" />
    <Field Name="Attributes" Type="int" />
    <Field Name="AttributesEx" Type="int" />
    <Field Name="AttributesEx2" Type="int" />
    <Field Name="AttributesEx3" Type="int" />
    <Field Name="AttributesEx4" Type="int" />
    <Field Name="ShapeshiftMask" Type="int" />
    <Field Name="Shapeshiftexclude" Type="int" />
    <Field Name="Targets" Type="int" />
    <Field Name="TargetCreatureType" Type="int" />
    <Field Name="RequiresSpellFocus" Type="int" />
    <Field Name="CasterAuraState" Type="int" />
    <Field Name="TargetAuraState" Type="int" />
    <Field Name="CastingTimeIndex" Type="int" />
    <Field Name="RecoveryTime" Type="int" />
    <Field Name="CategoryRecoveryTime" Type="int" />
    <Field Name="InterruptFlags" Type="int" />
    <Field Name="AuraInterruptFlags" Type="int" />
    <Field Name="ChannelInterruptFlags" Type="int" />
    <Field Name="ProcTypeMask" Type="int" />
    <Field Name="ProcChance" Type="int" />
    <Field Name="ProcCharges" Type="int" />
    <Field Name="MaxLevel" Type="int" />
    <Field Name="BaseLevel" Type="int" />
    <Field Name="SpellLevel" Type="int" />
    <Field Name="DurationIndex" Type="int" />
    <Field Name="PowerType" Type="int" />
    <Field Name="ManaCost" Type="int" />
    <Field Name="ManaCostPerLevel" Type="int" />
    <Field Name="ManaCostPerSecond" Type="int" />
    <Field Name="ManaCostPerSecondPerLevel" Type="int" />
    <Field Name="RangeIndex" Type="int" />
    <Field Name="Speed" Type="float" />
    <Field Name="ModalNextSpell" Type="int" />
    <Field Name="StackAmount" Type="int" />
    <Field Name="Totem" Type="int" ArraySize="2" />
    <Field Name="Reagent" Type="int" ArraySize="8" />
    <Field Name="ReagentCount" Type="int" ArraySize="8" />
    <Field Name="EquippedItemClass" Type="int" />
    <Field Name="EquippedItemSubclass" Type="int" />
    <Field Name="EquippedItemInvType" Type="int" />
    <Field Name="Effect" Type="int" ArraySize="3" />
    <Field Name="EffectDieSides" Type="int" ArraySize="3" />
    <Field Name="EffectBaseDice" Type="int" ArraySize="3" />
    <Field Name="EffectDicePerLevel" Type="float" ArraySize="3" />
    <Field Name="EffectRealPointsPerLevel" Type="float" ArraySize="3" />
    <Field Name="EffectBasePoints" Type="int" ArraySize="3" />
    <Field Name="EffectMechanic" Type="int" ArraySize="3" />
    <Field Name="ImplicitTargetA" Type="int" ArraySize="3" />
    <Field Name="ImplicitTargetB" Type="int" ArraySize="3" />
    <Field Name="EffectRadiusIndex" Type="int" ArraySize="3" />
    <Field Name="EffectAura" Type="int" ArraySize="3" />
    <Field Name="EffectAmplitude" Type="int" ArraySize="3" />
    <Field Name="EffectMultipleValue" Type="float" ArraySize="3" />
    <Field Name="EffectChainTarget" Type="int" ArraySize="3" />
    <Field Name="EffectItemType" Type="uint" ArraySize="3" />
    <Field Name="EffectMiscValue" Type="int" ArraySize="3" />
    <Field Name="EffectTriggerSpell" Type="int" ArraySize="3" />
    <Field Name="EffectPointsPerCombo" Type="float" ArraySize="3" />
    <Field Name="SpellVisualID" Type="int" ArraySize="2" />
    <Field Name="SpellIconID" Type="int" />
    <Field Name="ActiveIconID" Type="int" />
    <Field Name="SpellPriority" Type="int" />
    <Field Name="Name" Type="loc" />
    <Field Name="NameSubtext" Type="loc" />
    <Field Name="Description" Type="loc" />
    <Field Name="AuraDescription" Type="loc" />
    <Field Name="ManaCostPct" Type="int" />
    <Field Name="StartRecoveryCategory" Type="int" />
    <Field Name="StartRecoveryTime" Type="int" />
    <Field Name="MaxTargetLevel" Type="int" />
    <Field Name="SpellClassSet" Type="int" />
    <Field Name="SpellClassMask" Type="int" ArraySize="2" />
    <Field Name="MaxTargets" Type="int" />
    <Field Name="DefenseType" Type="int" />
    <Field Name="PreventionType" Type="int" />
    <Field Name="StanceBarOrder" Type="int" />
    <Field Name="DamageMultiplier" Type="float" ArraySize="3" />
    <Field Name="MinFactionId" Type="int" />
    <Field Name="MinReputation" Type="int" />
    <Field Name="RequiredAuraVision" Type="int" />
  </Table>
  <Table Name="SpellCastTimes" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Base" Type="int" />
    <Field Name="PerLevel" Type="int" />
    <Field Name="Minimum" Type="int" />
  </Table>
  <Table Name="SpellCategory" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="SpellChainEffects" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="AvgSegLen" Type="float" />
    <Field Name="Width" Type="float" />
    <Field Name="NoiseScale" Type="float" />
    <Field Name="TexCoordScale" Type="float" />
    <Field Name="SegDuration" Type="int" />
    <Field Name="SegDelay" Type="int" />
    <Field Name="Texture" Type="string" />
  </Table>
  <Table Name="SpellDispelType" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
    <Field Name="Mask" Type="int" />
    <Field Name="ImmunityPossible" Type="int" />
  </Table>
  <Table Name="SpellDuration" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Duration" Type="int" />
    <Field Name="DurationPerLevel" Type="int" />
    <Field Name="MaxDuration" Type="int" />
  </Table>
  <Table Name="SpellEffectCameraShakes" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CameraShake" Type="int" ArraySize="3" />
  </Table>
  <Table Name="SpellFocusObject" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
  </Table>
  <Table Name="SpellIcon" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TextureFilename" Type="string" />
  </Table>
  <Table Name="SpellItemEnchantment" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="EnchantmentType" Type="int" ArraySize="3" />
    <Field Name="EffectPointsMin" Type="int" ArraySize="3" />
    <Field Name="EffectPointsMax" Type="int" ArraySize="3" />
    <Field Name="EffectArg" Type="int" ArraySize="3" />
    <Field Name="Name" Type="loc" />
    <Field Name="ItemVisual" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="SpellMechanic" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="StateName" Type="loc" />
  </Table>
  <Table Name="SpellRadius" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Radius" Type="float" />
    <Field Name="RadiusPerLevel" Type="float" />
    <Field Name="RadiusMax" Type="float" />
  </Table>
  <Table Name="SpellRange" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="RangeMin" Type="float" />
    <Field Name="RangeMax" Type="float" />
    <Field Name="Flags" Type="int" />
    <Field Name="DisplayName" Type="loc" />
    <Field Name="DisplayNameShort" Type="loc" />
  </Table>
  <Table Name="SpellShapeshiftForm" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="BonusActionBar" Type="int" />
    <Field Name="Name" Type="loc" />
    <Field Name="Flags" Type="int" />
    <Field Name="CreatureType" Type="int" />
    <Field Name="CombatRoundTime" Type="int" />
  </Table>
  <Table Name="SpellVisual" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PrecastKit" Type="int" />
    <Field Name="CastKit" Type="int" />
    <Field Name="ImpactKit" Type="int" />
    <Field Name="StateKit" Type="int" />
    <Field Name="StateDoneKit" Type="int" />
    <Field Name="ChannelKit" Type="int" />
    <Field Name="HasMissile" Type="int" />
    <Field Name="MissileModel" Type="int" />
    <Field Name="MissilePathType" Type="int" />
    <Field Name="MissileDestinationAttachment" Type="int" />
    <Field Name="MissileSound" Type="int" />
    <Field Name="AnimEventSoundID" Type="int" />
    <Field Name="Flags" Type="int" />
    <Field Name="CasterImpactKit" Type="int" />
    <Field Name="TargetImpactKit" Type="int" />
  </Table>
  <Table Name="SpellVisualEffectName" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="FileName" Type="string" />
    <Field Name="AreaEffectSize" Type="float" />
    <Field Name="Scale" Type="float" />
  </Table>
  <Table Name="SpellVisualKit" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="StartAnimID" Type="int" />
    <Field Name="AnimID" Type="int" />
    <Field Name="AnimKitID" Type="int" />
    <Field Name="HeadEffect" Type="int" />
    <Field Name="ChestEffect" Type="int" />
    <Field Name="BaseEffect" Type="int" />
    <Field Name="LeftHandEffect" Type="int" />
    <Field Name="RightHandEffect" Type="int" />
    <Field Name="BreathEffect" Type="int" />
    <Field Name="LeftWeaponEffect" Type="int" />
    <Field Name="RightWeaponEffect" Type="int" />
    <Field Name="SpecialEffect" Type="int" ArraySize="3" />
    <Field Name="WorldEffect" Type="int" />
    <Field Name="SoundID" Type="int" />
    <Field Name="ShakeID" Type="int" />
    <Field Name="CharProc" Type="int" ArraySize="4" />
    <Field Name="CharParamZero" Type="float" ArraySize="4" />
    <Field Name="CharParamOne" Type="float" ArraySize="4" />
    <Field Name="CharParamTwo" Type="float" ArraySize="4" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="SpellVisualPrecastTransitions" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="LoadAnimation" Type="string" />
    <Field Name="HoldAnimation" Type="string" />
  </Table>
  <Table Name="StableSlotPrices" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Cost" Type="int" />
  </Table>
  <Table Name="Startup_Strings" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="String" Type="loc" />
  </Table>
  <Table Name="Stationery" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ItemID" Type="int" />
    <Field Name="Texture" Type="string" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="StringLookups" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="String" Type="string" />
  </Table>
  <Table Name="Talent" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TabID" Type="int" />
    <Field Name="TierID" Type="int" />
    <Field Name="ColumnIndex" Type="int" />
    <Field Name="SpellRank" Type="int" ArraySize="9" />
    <Field Name="PrereqTalent" Type="int" ArraySize="3" />
    <Field Name="PrereqRank" Type="int" ArraySize="3" />
    <Field Name="Flags" Type="int" />
    <Field Name="RequiredSpellID" Type="int" />
  </Table>
  <Table Name="TalentTab" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="loc" />
    <Field Name="SpellIconID" Type="int" />
    <Field Name="RaceMask" Type="int" />
    <Field Name="ClassMask" Type="int" />
    <Field Name="OrderIndex" Type="uint" />
    <Field Name="BackgroundFile" Type="string" />
  </Table>
  <Table Name="TaxiNodes" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="X" Type="float" />
    <Field Name="Y" Type="float" />
    <Field Name="Z" Type="float" />
    <Field Name="Name" Type="loc" />
    <Field Name="MountCreatureID" Type="int" ArraySize="2" />
  </Table>
  <Table Name="TaxiPath" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="FromTaxiNode" Type="int" />
    <Field Name="ToTaxiNode" Type="int" />
    <Field Name="Cost" Type="int" />
  </Table>
  <Table Name="TaxiPathNode" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="PathID" Type="int" />
    <Field Name="NodeIndex" Type="int" />
    <Field Name="ContinentID" Type="int" />
    <Field Name="LocX" Type="float" />
    <Field Name="LocY" Type="float" />
    <Field Name="LocZ" Type="float" />
    <Field Name="Flags" Type="int" />
    <Field Name="Delay" Type="int" />
  </Table>
  <Table Name="TerrainType" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TerrainDesc" Type="string" />
    <Field Name="FootstepSprayRun" Type="int" />
    <Field Name="FootstepSprayWalk" Type="int" />
    <Field Name="SoundID" Type="int" />
    <Field Name="Flags" Type="int" />
  </Table>
  <Table Name="TerrainTypeSounds" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
  </Table>
  <Table Name="TransportAnimation" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="TransportID" Type="int" />
    <Field Name="TimeIndex" Type="int" />
    <Field Name="PosX" Type="int" />
    <Field Name="PosY" Type="int" />
    <Field Name="PosZ" Type="float" />
    <Field Name="SequenceID" Type="int" />
  </Table>
  <Table Name="UISoundLookups" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SoundID" Type="int" />
    <Field Name="Name" Type="string" />
  </Table>
  <Table Name="UnitBlood" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="CombatBloodSpurtFront" Type="int" ArraySize="2" />
    <Field Name="CombatBloodSpurtBack" Type="int" ArraySize="2" />
    <Field Name="GroundBlood1" Type="string" />
    <Field Name="GroundBlood2" Type="string" />
    <Field Name="GroundBlood3" Type="string" />
    <Field Name="GroundBlood4" Type="string" />
    <Field Name="GroundBlood5" Type="string" />
  </Table>
  <Table Name="UnitBloodLevels" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Violencelevel" Type="int" ArraySize="3" />
  </Table>
  <Table Name="VideoHardware" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="uint" />
    <Field Name="Field02" Type="uint" />
    <Field Name="Field03" Type="uint" />
    <Field Name="Field04" Type="uint" />
    <Field Name="Field05" Type="string" />
    <Field Name="Field06" Type="uint" />
    <Field Name="Field07" Type="string" />
    <Field Name="Field08" Type="string" />
    <Field Name="Field09" Type="string" />
    <Field Name="Field10" Type="uint" />
    <Field Name="Field11" Type="string" />
    <Field Name="Field12" Type="string" />
    <Field Name="Field13" Type="uint" />
    <Field Name="Field14" Type="uint" />
    <Field Name="Field15" Type="uint" />
    <Field Name="Field16" Type="uint" />
    <Field Name="Field17" Type="string" />
    <Field Name="Field18" Type="string" />
    <Field Name="Field19" Type="string" />
    <Field Name="Field20" Type="string" />
    <Field Name="Field21" Type="uint" />
  </Table>
  <Table Name="VocalUISounds" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="VocalUIEnum" Type="int" />
    <Field Name="RaceID" Type="int" />
    <Field Name="NormalSoundID" Type="int" ArraySize="2" />
    <Field Name="PissedSound" Type="int" ArraySize="2" />
  </Table>
  <Table Name="WeaponImpactSounds" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WeaponSubclassID" Type="int" />
    <Field Name="ParrySoundType" Type="int" />
    <Field Name="ImpactSoundID" Type="int" ArraySize="10" />
    <Field Name="CritImpactSoundID" Type="int" ArraySize="10" />
  </Table>
  <Table Name="WeaponSwingSounds2" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SwingType" Type="int" />
    <Field Name="Crit" Type="int" />
    <Field Name="SoundID" Type="int" />
  </Table>
  <Table Name="WMOAreaTable" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="WMOID" Type="int" />
    <Field Name="NameSetID" Type="int" />
    <Field Name="WMOGroupID" Type="int" />
    <Field Name="SoundProviderPref" Type="int" />
    <Field Name="SoundProviderPrefUnderwater" Type="int" />
    <Field Name="AmbienceID" Type="int" />
    <Field Name="ZoneMusic" Type="int" />
    <Field Name="IntroSound" Type="int" />
    <Field Name="Flags" Type="uint" />
    <Field Name="AreaTableID" Type="int" />
    <Field Name="AreaName" Type="loc" />
  </Table>
  <Table Name="WorldMapArea" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="AreaID" Type="int" />
    <Field Name="AreaName" Type="string" />
    <Field Name="LocLeft" Type="float" />
    <Field Name="LocRight" Type="float" />
    <Field Name="LocTop" Type="float" />
    <Field Name="LocBottom" Type="float" />
  </Table>
  <Table Name="WorldMapContinent" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapID" Type="int" />
    <Field Name="LeftBoundary" Type="uint" />
    <Field Name="RightBoundary" Type="uint" />
    <Field Name="TopBoundary" Type="uint" />
    <Field Name="BottomBoundary" Type="uint" />
    <Field Name="ContinentOffsetX" Type="float" />
    <Field Name="ContinentOffsetY" Type="float" />
    <Field Name="Scale" Type="float" />
    <Field Name="TaxiMinX" Type="float" />
    <Field Name="TaxiMinY" Type="float" />
    <Field Name="TaxiMaxX" Type="float" />
    <Field Name="TaxiMaxY" Type="float" />
  </Table>
  <Table Name="WorldMapOverlay" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="MapArea" Type="int" />
    <Field Name="AreaID" Type="int" ArraySize="4" />
    <Field Name="MapPointX" Type="uint" />
    <Field Name="MapPointY" Type="uint" />
    <Field Name="TextureName" Type="string" />
    <Field Name="TextureWidth" Type="uint" />
    <Field Name="TextureHeight" Type="uint" />
    <Field Name="OffsetX" Type="uint" />
    <Field Name="OffsetY" Type="uint" />
    <Field Name="HitRectTop" Type="uint" />
    <Field Name="HitRectLeft" Type="uint" />
    <Field Name="HitRectBottom" Type="uint" />
    <Field Name="HitRectRight" Type="uint" />
  </Table>
  <Table Name="WorldSafeLocs" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Continent" Type="int" />
    <Field Name="LocationX" Type="float" />
    <Field Name="LocationY" Type="float" />
    <Field Name="LocationZ" Type="float" />
    <Field Name="AreaName" Type="loc" />
  </Table>
  <Table Name="WorldStateUI" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Field01" Type="uint" />
    <Field Name="Field02" Type="uint" />
    <Field Name="Field03" Type="string" />
    <Field Name="Field04" Type="loc" />
    <Field Name="Field05" Type="loc" />
    <Field Name="Field06" Type="int" />
    <Field Name="Field07" Type="uint" />
    <Field Name="Field08" Type="uint" />
    <Field Name="Field09" Type="string" />
    <Field Name="Field10" Type="loc" />
    <Field Name="Field11" Type="string" />
    <Field Name="Field12" Type="uint" />
    <Field Name="Field13" Type="uint" />
    <Field Name="Field14" Type="uint" />
  </Table>
  <Table Name="WowError_Strings" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="ErrorName" Type="string" />
    <Field Name="ErrorString" Type="loc" />
  </Table>
  <Table Name="ZoneIntroMusicTable" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="Name" Type="string" />
    <Field Name="SoundID" Type="int" />
    <Field Name="Priority" Type="uint" />
    <Field Name="MinDelayMinutes" Type="uint" />
  </Table>
  <Table Name="ZoneMusic" Build="5875">
    <Field Name="ID" Type="int" IsIndex="true" />
    <Field Name="SetName" Type="string" />
    <Field Name="SilenceIntervalMinDay" Type="uint" />
    <Field Name="SilenceIntervalMinNight" Type="uint" />
    <Field Name="SilenceIntervalMaxDay" Type="uint" />
    <Field Name="SilenceIntervalMaxNight" Type="uint" />
    <Field Name="SoundsDay" Type="int" />
    <Field Name="SoundsNight" Type="int" />
  </Table>
</Definition>