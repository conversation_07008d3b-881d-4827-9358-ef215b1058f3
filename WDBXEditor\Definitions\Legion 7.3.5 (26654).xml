<?xml version="1.0"?>
<Definition xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<Table Name="Achievement" Build="26654">
		<Field Name="Title_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Reward_Lang" Type="string" />
		<Field Name="Flags" Type="int" />
		<Field Name="Instance_ID" Type="short" />
		<Field Name="Supercedes" Type="short" />
		<Field Name="Category" Type="short" />
		<Field Name="Ui_Order" Type="short" />
		<Field Name="Shares_Criteria" Type="short" />
		<Field Name="Faction" Type="byte" />
		<Field Name="Points" Type="byte" />
		<Field Name="Minimum_Criteria" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileID" Type="int" />
		<Field Name="Criteria_Tree" Type="int" />
	</Table>
	<Table Name="Achievement_Category" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Parent" Type="short" />
		<Field Name="Ui_Order" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="AdventureJournal" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ButtonText_Lang" Type="string" />
		<Field Name="RewardDescription_Lang" Type="string" />
		<Field Name="ContinueDescription_Lang" Type="string" />
		<Field Name="TextureFileDataID" Type="int" />
		<Field Name="ItemID" Type="int" />
		<Field Name="LfgDungeonID" Type="short" />
		<Field Name="QuestID" Type="short" />
		<Field Name="BattleMasterListID" Type="short" />
		<Field Name="BonusPlayerConditionID" Type="short" ArraySize="2" />
		<Field Name="CurrencyType" Type="short" />
		<Field Name="WorldMapAreaID" Type="short" />
		<Field Name="Type" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ButtonActionType" Type="byte" />
		<Field Name="PriorityMin" Type="byte" />
		<Field Name="PriorityMax" Type="byte" />
		<Field Name="BonusValue" Type="byte" ArraySize="2" />
		<Field Name="CurrencyQuantity" Type="byte" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="ItemQuantity" Type="int" />
	</Table>
	<Table Name="AdventureMapPOI" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Title_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="WorldPosition" Type="float" ArraySize="2" />
		<Field Name="RewardItemID" Type="int" />
		<Field Name="Type" Type="byte" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="QuestID" Type="int" />
		<Field Name="LfgDungeonID" Type="int" />
		<Field Name="UiTextureAtlasMemberID" Type="int" />
		<Field Name="UiTextureKitID" Type="int" />
		<Field Name="WorldMapAreaID" Type="int" />
		<Field Name="DungeonMapID" Type="int" />
		<Field Name="AreaTableID" Type="int" />
	</Table>
	<Table Name="AlliedRace" Build="26654">
		<Field Name="BannerColor" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceID" Type="int" />
		<Field Name="CrestTextureID" Type="int" />
		<Field Name="ModelBackgroundTextureID" Type="int" />
		<Field Name="MaleCreatureDisplayID" Type="int" />
		<Field Name="FemaleCreatureDisplayID" Type="int" />
		<Field Name="Ui_UnlockAchievementID" Type="int" />
	</Table>
	<Table Name="AlliedRaceRacialAbility" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="IconFileDataID" Type="int" />
	</Table>
	<Table Name="AnimationData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="Fallback" Type="short" />
		<Field Name="BehaviorID" Type="short" />
		<Field Name="BehaviorTier" Type="byte" />
	</Table>
	<Table Name="AnimKit" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OneShotDuration" Type="int" />
		<Field Name="OneShotStopAnimKitID" Type="short" />
		<Field Name="LowDefAnimKitID" Type="short" />
	</Table>
	<Table Name="AnimKitBoneSet" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="BoneDataID" Type="byte" />
		<Field Name="ParentAnimKitBoneSetID" Type="byte" />
		<Field Name="ExtraBoneCount" Type="byte" />
		<Field Name="AltAnimKitBoneSetID" Type="byte" />
	</Table>
	<Table Name="AnimKitBoneSetAlias" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BoneDataID" Type="byte" />
		<Field Name="AnimKitBoneSetID" Type="byte" />
	</Table>
	<Table Name="AnimKitConfig" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ConfigFlags" Type="int" />
	</Table>
	<Table Name="AnimKitConfigBoneSet" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AnimKitPriorityID" Type="short" />
		<Field Name="AnimKitBoneSetID" Type="byte" />
	</Table>
	<Table Name="AnimKitPriority" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Priority" Type="byte" />
	</Table>
	<Table Name="AnimKitReplacement" Build="26654">
		<Field Name="SrcAnimKitID" Type="short" />
		<Field Name="DstAnimKitID" Type="short" />
		<Field Name="Flags" Type="short" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="AnimKitSegment" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AnimStartTime" Type="int" />
		<Field Name="EndConditionParam" Type="int" />
		<Field Name="EndConditionDelay" Type="int" />
		<Field Name="Speed" Type="float" />
		<Field Name="OverrideConfigFlags" Type="int" />
		<Field Name="ParentAnimKitID" Type="short" />
		<Field Name="AnimID" Type="short" />
		<Field Name="AnimKitConfigID" Type="short" />
		<Field Name="SegmentFlags" Type="short" />
		<Field Name="BlendInTimeMs" Type="short" />
		<Field Name="BlendOutTimeMs" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="StartCondition" Type="byte" />
		<Field Name="StartConditionParam" Type="byte" />
		<Field Name="EndCondition" Type="byte" />
		<Field Name="ForcedVariation" Type="byte" />
		<Field Name="LoopToSegmentIndex" Type="byte" />
		<Field Name="StartConditionDelay" Type="int" />
	</Table>
	<Table Name="AnimReplacement" Build="26654">
		<Field Name="SrcAnimID" Type="short" />
		<Field Name="DstAnimID" Type="short" />
		<Field Name="Flags" Type="short" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="AnimReplacementSet" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ExecOrder" Type="byte" />
	</Table>
	<Table Name="AreaFarClipOverride" Build="26654">
		<Field Name="AreaID" Type="int" />
		<Field Name="MinFarClip" Type="float" />
		<Field Name="MinHorizonStart" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="AreaGroupMember" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AreaID" Type="short" />
	</Table>
	<Table Name="AreaPOI" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Flags" Type="int" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="PoiDataType" Type="int" />
		<Field Name="PoiData" Type="int" />
		<Field Name="ContinentID" Type="short" />
		<Field Name="AreaID" Type="short" />
		<Field Name="WorldStateID" Type="short" />
		<Field Name="PortLocID" Type="short" />
		<Field Name="Importance" Type="byte" />
		<Field Name="Icon" Type="byte" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="UiTextureAtlasMemberID" Type="int" />
		<Field Name="MapFloor" Type="int" />
		<Field Name="WMOGroupID" Type="int" />
	</Table>
	<Table Name="AreaPOIState" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="WorldStateValue" Type="byte" />
		<Field Name="IconEnumValue" Type="byte" />
		<Field Name="UiTextureAtlasMemberID" Type="int" />
	</Table>
	<Table Name="AreaTable" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ZoneName" Type="string" />
		<Field Name="AreaName_Lang" Type="string" />
		<Field Name="Flags" Type="int" ArraySize="2" />
		<Field Name="Ambient_Multiplier" Type="float" />
		<Field Name="ContinentID" Type="short" />
		<Field Name="ParentAreaID" Type="short" />
		<Field Name="AreaBit" Type="short" />
		<Field Name="AmbienceID" Type="short" />
		<Field Name="ZoneMusic" Type="short" />
		<Field Name="IntroSound" Type="short" />
		<Field Name="LiquidTypeID" Type="short" ArraySize="4" />
		<Field Name="UwZoneMusic" Type="short" />
		<Field Name="UwAmbience" Type="short" />
		<Field Name="PvpCombatWorldStateID" Type="short" />
		<Field Name="SoundProviderPref" Type="byte" />
		<Field Name="SoundProviderPrefUnderwater" Type="byte" />
		<Field Name="ExplorationLevel" Type="byte" />
		<Field Name="FactionGroupMask" Type="byte" />
		<Field Name="MountFlags" Type="byte" />
		<Field Name="WildBattlePetLevelMin" Type="byte" />
		<Field Name="WildBattlePetLevelMax" Type="byte" />
		<Field Name="WindSettingsID" Type="byte" />
		<Field Name="UwIntroSound" Type="int" />
	</Table>
	<Table Name="AreaTrigger" Build="26654">
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="Radius" Type="float" />
		<Field Name="Box_Length" Type="float" />
		<Field Name="Box_Width" Type="float" />
		<Field Name="Box_Height" Type="float" />
		<Field Name="Box_Yaw" Type="float" />
		<Field Name="ContinentID" Type="short" />
		<Field Name="PhaseID" Type="short" />
		<Field Name="PhaseGroupID" Type="short" />
		<Field Name="ShapeID" Type="short" />
		<Field Name="AreaTriggerActionSetID" Type="short" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="ShapeType" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="AreaTriggerActionSet" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="short" />
	</Table>
	<Table Name="AreaTriggerBox" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Extents" Type="float" ArraySize="3" />
	</Table>
	<Table Name="AreaTriggerCylinder" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Radius" Type="float" />
		<Field Name="Height" Type="float" />
		<Field Name="ZOffset" Type="float" />
	</Table>
	<Table Name="AreaTriggerSphere" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaxRadius" Type="float" />
	</Table>
	<Table Name="ArmorLocation" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Clothmodifier" Type="float" />
		<Field Name="Leathermodifier" Type="float" />
		<Field Name="Chainmodifier" Type="float" />
		<Field Name="Platemodifier" Type="float" />
		<Field Name="Modifier" Type="float" />
	</Table>
	<Table Name="Artifact" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="UiBarOverlayColor" Type="int" />
		<Field Name="UiBarBackgroundColor" Type="int" />
		<Field Name="UiNameColor" Type="int" />
		<Field Name="UiTextureKitID" Type="short" />
		<Field Name="ChrSpecializationID" Type="short" />
		<Field Name="ArtifactCategoryID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UiModelSceneID" Type="int" />
		<Field Name="SpellVisualKitID" Type="int" />
	</Table>
	<Table Name="ArtifactAppearance" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="UiSwatchColor" Type="int" />
		<Field Name="UiModelSaturation" Type="float" />
		<Field Name="UiModelOpacity" Type="float" />
		<Field Name="OverrideShapeshiftDisplayID" Type="int" />
		<Field Name="ArtifactAppearanceSetID" Type="short" />
		<Field Name="UiCameraID" Type="short" />
		<Field Name="DisplayIndex" Type="byte" />
		<Field Name="ItemAppearanceModifierID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="OverrideShapeshiftFormID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UnlockPlayerConditionID" Type="int" />
		<Field Name="UiItemAppearanceID" Type="int" />
		<Field Name="UiAltItemAppearanceID" Type="int" />
	</Table>
	<Table Name="ArtifactAppearanceSet" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="UiCameraID" Type="short" />
		<Field Name="AltHandUICameraID" Type="short" />
		<Field Name="DisplayIndex" Type="byte" />
		<Field Name="ForgeAttachmentOverride" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ArtifactCategory" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="XpMultCurrencyID" Type="short" />
		<Field Name="XpMultCurveID" Type="short" />
	</Table>
	<Table Name="ArtifactPower" Build="26654">
		<Field Name="DisplayPos" Type="float" ArraySize="2" />
		<Field Name="ArtifactID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MaxPurchasableRank" Type="byte" />
		<Field Name="Tier" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Label" Type="int" />
	</Table>
	<Table Name="ArtifactPowerLink" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PowerA" Type="short" />
		<Field Name="PowerB" Type="short" />
	</Table>
	<Table Name="ArtifactPowerPicker" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="ArtifactPowerRank" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="AuraPointsOverride" Type="float" />
		<Field Name="ItemBonusListID" Type="short" />
		<Field Name="RankIndex" Type="byte" />
	</Table>
	<Table Name="ArtifactQuestXP" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="int" ArraySize="10" />
	</Table>
	<Table Name="ArtifactTier" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ArtifactTier" Type="int" />
		<Field Name="MaxNumTraits" Type="int" />
		<Field Name="MaxArtifactKnowledge" Type="int" />
		<Field Name="KnowledgePlayerCondition" Type="int" />
		<Field Name="MinimumEmpowerKnowledge" Type="int" />
	</Table>
	<Table Name="ArtifactUnlock" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemBonusListID" Type="short" />
		<Field Name="PowerRank" Type="byte" />
		<Field Name="PowerID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="AuctionHouse" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="FactionID" Type="short" />
		<Field Name="DepositRate" Type="byte" />
		<Field Name="ConsignmentRate" Type="byte" />
	</Table>
	<Table Name="BankBagSlotPrices" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Cost" Type="int" />
	</Table>
	<Table Name="BarberShopStyle" Build="26654">
		<Field Name="DisplayName_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Cost_Modifier" Type="float" />
		<Field Name="Type" Type="byte" />
		<Field Name="Race" Type="byte" />
		<Field Name="Sex" Type="byte" />
		<Field Name="Data" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="BattlemasterList" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Gametype_Lang" Type="string" />
		<Field Name="ShortDescription_Lang" Type="string" />
		<Field Name="LongDescription_Lang" Type="string" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="MapID" Type="short" ArraySize="16" />
		<Field Name="HolidayWorldState" Type="short" />
		<Field Name="Required_Player_Condition_ID" Type="short" />
		<Field Name="InstanceType" Type="byte" />
		<Field Name="GroupsAllowed" Type="byte" />
		<Field Name="MaxGroupSize" Type="byte" />
		<Field Name="Minlevel" Type="byte" />
		<Field Name="Maxlevel" Type="byte" />
		<Field Name="RatedPlayers" Type="byte" />
		<Field Name="MinPlayers" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="BattlePetAbility" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="BattlePetVisualID" Type="short" />
		<Field Name="PetTypeEnum" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Cooldown" Type="int" />
	</Table>
	<Table Name="BattlePetAbilityEffect" Build="26654">
		<Field Name="BattlePetAbilityTurnID" Type="short" />
		<Field Name="BattlePetVisualID" Type="short" />
		<Field Name="AuraBattlePetAbilityID" Type="short" />
		<Field Name="BattlePetEffectPropertiesID" Type="short" />
		<Field Name="Param" Type="short" ArraySize="6" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="BattlePetAbilityState" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="int" />
		<Field Name="BattlePetStateID" Type="byte" />
	</Table>
	<Table Name="BattlePetAbilityTurn" Build="26654">
		<Field Name="BattlePetAbilityID" Type="short" />
		<Field Name="BattlePetVisualID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="TurnTypeEnum" Type="byte" />
		<Field Name="EventTypeEnum" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="BattlePetBreedQuality" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StateMultiplier" Type="float" />
		<Field Name="QualityEnum" Type="byte" />
	</Table>
	<Table Name="BattlePetBreedState" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="short" />
		<Field Name="BattlePetStateID" Type="byte" />
	</Table>
	<Table Name="BattlePetDisplayOverride" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BattlePetSpeciesID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="CreatureDisplayInfoID" Type="int" />
		<Field Name="PriorityCategory" Type="byte" />
	</Table>
	<Table Name="BattlePetEffectProperties" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ParamLabel" Type="string" ArraySize="6" />
		<Field Name="BattlePetVisualID" Type="short" />
		<Field Name="ParamTypeEnum" Type="byte" ArraySize="6" />
	</Table>
	<Table Name="BattlePetNPCTeamMember" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="BattlePetSpecies" Build="26654">
		<Field Name="SourceText_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="CreatureID" Type="int" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="SummonSpellID" Type="int" />
		<Field Name="Flags" Type="short" />
		<Field Name="PetTypeEnum" Type="byte" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CardUIModelSceneID" Type="int" />
		<Field Name="LoadoutUIModelSceneID" Type="int" />
	</Table>
	<Table Name="BattlePetSpeciesState" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="int" />
		<Field Name="BattlePetStateID" Type="byte" />
	</Table>
	<Table Name="BattlePetSpeciesXAbility" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BattlePetAbilityID" Type="short" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="SlotEnum" Type="byte" />
	</Table>
	<Table Name="BattlePetState" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LuaName" Type="string" />
		<Field Name="BattlePetVisualID" Type="short" />
		<Field Name="Flags" Type="short" />
	</Table>
	<Table Name="BattlePetVisual" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SceneScriptFunction" Type="string" />
		<Field Name="SpellVisualID" Type="int" />
		<Field Name="CastMilliSeconds" Type="short" />
		<Field Name="ImpactMilliSeconds" Type="short" />
		<Field Name="SceneScriptPackageID" Type="short" />
		<Field Name="RangeTypeEnum" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="BeamEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BeamID" Type="int" />
		<Field Name="SourceMinDistance" Type="float" />
		<Field Name="FixedLength" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="SourceOffset" Type="short" />
		<Field Name="DestOffset" Type="short" />
		<Field Name="SourceAttachID" Type="short" />
		<Field Name="DestAttachID" Type="short" />
		<Field Name="SourcePositionerID" Type="short" />
		<Field Name="DestPositionerID" Type="short" />
	</Table>
	<Table Name="BoneWindModifierModel" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="BoneWindModifierID" Type="int" />
	</Table>
	<Table Name="BoneWindModifiers" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Multiplier" Type="float" ArraySize="3" />
		<Field Name="PhaseMultiplier" Type="float" />
	</Table>
	<Table Name="Bounty" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="QuestID" Type="short" />
		<Field Name="FactionID" Type="short" />
		<Field Name="TurninPlayerConditionID" Type="int" />
	</Table>
	<Table Name="BountySet" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LockedQuestID" Type="short" />
		<Field Name="VisiblePlayerConditionID" Type="int" />
	</Table>
	<Table Name="BroadcastText" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text_Lang" Type="string" />
		<Field Name="Text1_Lang" Type="string" />
		<Field Name="EmoteID" Type="short" ArraySize="3" />
		<Field Name="EmoteDelay" Type="short" ArraySize="3" />
		<Field Name="EmotesID" Type="short" />
		<Field Name="LanguageID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ConditionID" Type="int" />
		<Field Name="SoundEntriesID" Type="int" ArraySize="2" />
	</Table>
	<Table Name="CameraEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CameraEffectEntry" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Duration" Type="float" />
		<Field Name="Delay" Type="float" />
		<Field Name="Phase" Type="float" />
		<Field Name="Amplitude" Type="float" />
		<Field Name="AmplitudeB" Type="float" />
		<Field Name="Frequency" Type="float" />
		<Field Name="RadiusMin" Type="float" />
		<Field Name="RadiusMax" Type="float" />
		<Field Name="AmplitudeCurveID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="EffectType" Type="byte" />
		<Field Name="DirectionType" Type="byte" />
		<Field Name="MovementType" Type="byte" />
		<Field Name="AttenuationType" Type="byte" />
	</Table>
	<Table Name="CameraMode" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PositionOffset" Type="float" ArraySize="3" />
		<Field Name="TargetOffset" Type="float" ArraySize="3" />
		<Field Name="PositionSmoothing" Type="float" />
		<Field Name="RotationSmoothing" Type="float" />
		<Field Name="FieldOfView" Type="float" />
		<Field Name="Flags" Type="short" />
		<Field Name="Type" Type="byte" />
		<Field Name="LockedPositionOffsetBase" Type="byte" />
		<Field Name="LockedPositionOffsetDirection" Type="byte" />
		<Field Name="LockedTargetOffsetBase" Type="byte" />
		<Field Name="LockedTargetOffsetDirection" Type="byte" />
	</Table>
	<Table Name="CastableRaidBuffs" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CastingSpellID" Type="int" />
	</Table>
	<Table Name="CelestialBody" Build="26654">
		<Field Name="BaseFileDataID" Type="int" />
		<Field Name="LightMaskFileDataID" Type="int" />
		<Field Name="GlowMaskFileDataID" Type="int" ArraySize="2" />
		<Field Name="AtmosphericMaskFileDataID" Type="int" />
		<Field Name="AtmosphericModifiedFileDataID" Type="int" />
		<Field Name="GlowModifiedFileDataID" Type="int" ArraySize="2" />
		<Field Name="ScrollURate" Type="float" ArraySize="2" />
		<Field Name="ScrollVRate" Type="float" ArraySize="2" />
		<Field Name="RotateRate" Type="float" />
		<Field Name="GlowMaskScale" Type="float" ArraySize="2" />
		<Field Name="AtmosphericMaskScale" Type="float" />
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="BodyBaseScale" Type="float" />
		<Field Name="SkyArrayBand" Type="short" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Cfg_Categories" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="LocaleMask" Type="short" />
		<Field Name="Create_CharsetMask" Type="byte" />
		<Field Name="Existing_CharsetMask" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Cfg_Configs" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaxDamageReductionPctPhysical" Type="float" />
		<Field Name="PlayerAttackSpeedBase" Type="short" />
		<Field Name="PlayerKillingAllowed" Type="byte" />
		<Field Name="Roleplaying" Type="byte" />
	</Table>
	<Table Name="Cfg_Regions" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Tag" Type="string" />
		<Field Name="Raidorigin" Type="int" />
		<Field Name="Challenge_Origin" Type="int" />
		<Field Name="Region_ID" Type="short" />
		<Field Name="Region_Group_Mask" Type="byte" />
	</Table>
	<Table Name="CharacterFaceBoneSet" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BoneSetFileDataID" Type="int" />
		<Field Name="SexID" Type="byte" />
		<Field Name="FaceVariationIndex" Type="byte" />
		<Field Name="Resolution" Type="byte" />
	</Table>
	<Table Name="CharacterFacialHairStyles" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Geoset" Type="int" ArraySize="5" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="VariationID" Type="byte" />
	</Table>
	<Table Name="CharacterLoadout" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Racemask" Type="long" />
		<Field Name="ChrClassID" Type="byte" />
		<Field Name="Purpose" Type="byte" />
	</Table>
	<Table Name="CharacterLoadoutItem" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
		<Field Name="CharacterLoadoutID" Type="short" />
	</Table>
	<Table Name="CharacterServiceInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FlowTitle_Lang" Type="string" />
		<Field Name="PopupTitle_Lang" Type="string" />
		<Field Name="PopupDescription_Lang" Type="string" />
		<Field Name="BoostType" Type="int" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="Priority" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="ProfessionLevel" Type="int" />
		<Field Name="BoostLevel" Type="int" />
		<Field Name="Expansion" Type="int" />
		<Field Name="PopupUITextureKitID" Type="int" />
	</Table>
	<Table Name="CharBaseInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="ClassID" Type="byte" />
	</Table>
	<Table Name="CharBaseSection" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VariationEnum" Type="byte" />
		<Field Name="ResolutionVariationEnum" Type="byte" />
		<Field Name="LayoutResType" Type="byte" />
	</Table>
	<Table Name="CharComponentTextureLayouts" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Width" Type="short" />
		<Field Name="Height" Type="short" />
	</Table>
	<Table Name="CharComponentTextureSections" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OverlapSectionMask" Type="int" />
		<Field Name="X" Type="short" />
		<Field Name="Y" Type="short" />
		<Field Name="Width" Type="short" />
		<Field Name="Height" Type="short" />
		<Field Name="CharComponentTextureLayoutID" Type="byte" />
		<Field Name="SectionType" Type="byte" />
	</Table>
	<Table Name="CharHairGeosets" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HdCustomGeoFileDataID" Type="int" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="VariationID" Type="byte" />
		<Field Name="VariationType" Type="byte" />
		<Field Name="GeosetID" Type="byte" />
		<Field Name="GeosetType" Type="byte" />
		<Field Name="Showscalp" Type="byte" />
		<Field Name="ColorIndex" Type="byte" />
		<Field Name="CustomGeoFileDataID" Type="int" />
	</Table>
	<Table Name="CharSections" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaterialResourcesID" Type="int" ArraySize="3" />
		<Field Name="Flags" Type="short" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="BaseSection" Type="byte" />
		<Field Name="VariationIndex" Type="byte" />
		<Field Name="ColorIndex" Type="byte" />
	</Table>
	<Table Name="CharShipment" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TreasureID" Type="int" />
		<Field Name="Duration" Type="int" />
		<Field Name="SpellID" Type="int" />
		<Field Name="DummyItemID" Type="int" />
		<Field Name="OnCompleteSpellID" Type="int" />
		<Field Name="ContainerID" Type="short" />
		<Field Name="GarrFollowerID" Type="short" />
		<Field Name="MaxShipments" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CharShipmentContainer" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PendingText_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="WorkingSpellVisualID" Type="int" />
		<Field Name="UiTextureKitID" Type="short" />
		<Field Name="WorkingDisplayInfoID" Type="short" />
		<Field Name="SmallDisplayInfoID" Type="short" />
		<Field Name="MediumDisplayInfoID" Type="short" />
		<Field Name="LargeDisplayInfoID" Type="short" />
		<Field Name="CrossFactionID" Type="short" />
		<Field Name="BaseCapacity" Type="byte" />
		<Field Name="GarrBuildingType" Type="byte" />
		<Field Name="GarrTypeID" Type="byte" />
		<Field Name="MediumThreshold" Type="byte" />
		<Field Name="LargeThreshold" Type="byte" />
		<Field Name="Faction" Type="byte" />
		<Field Name="CompleteSpellVisualID" Type="int" />
	</Table>
	<Table Name="CharStartOutfit" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" ArraySize="24" />
		<Field Name="PetDisplayID" Type="int" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="OutfitID" Type="byte" />
		<Field Name="PetFamilyID" Type="byte" />
	</Table>
	<Table Name="CharTitles" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Name1_Lang" Type="string" />
		<Field Name="Mask_ID" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ChatChannels" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Shortcut_Lang" Type="string" />
		<Field Name="Flags" Type="int" />
		<Field Name="FactionGroup" Type="byte" />
	</Table>
	<Table Name="ChatProfanity" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text" Type="string" />
		<Field Name="Language" Type="byte" />
	</Table>
	<Table Name="ChrClasses" Build="26654">
		<Field Name="PetNameToken" Type="string" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Name_Female_Lang" Type="string" />
		<Field Name="Name_Male_Lang" Type="string" />
		<Field Name="Filename" Type="string" />
		<Field Name="CreateScreenFileDataID" Type="int" />
		<Field Name="SelectScreenFileDataID" Type="int" />
		<Field Name="LowResScreenFileDataID" Type="int" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="StartingLevel" Type="int" />
		<Field Name="Flags" Type="short" />
		<Field Name="CinematicSequenceID" Type="short" />
		<Field Name="DefaultSpec" Type="short" />
		<Field Name="DisplayPower" Type="byte" />
		<Field Name="SpellClassSet" Type="byte" />
		<Field Name="AttackPowerPerStrength" Type="byte" />
		<Field Name="AttackPowerPerAgility" Type="byte" />
		<Field Name="RangedAttackPowerPerAgility" Type="byte" />
		<Field Name="PrimaryStatPriority" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ChrClassesXPowerTypes" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PowerType" Type="byte" />
	</Table>
	<Table Name="ChrClassRaceSex" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="Sex" Type="byte" />
		<Field Name="Flags" Type="int" />
		<Field Name="SoundID" Type="int" />
		<Field Name="VoiceSoundFilterID" Type="int" />
	</Table>
	<Table Name="ChrClassTitle" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Male_Lang" Type="string" />
		<Field Name="Name_Female_Lang" Type="string" />
		<Field Name="ChrClassID" Type="byte" />
	</Table>
	<Table Name="ChrClassUIDisplay" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrClassesID" Type="byte" />
		<Field Name="AdvGuidePlayerConditionID" Type="int" />
		<Field Name="SplashPlayerConditionID" Type="int" />
	</Table>
	<Table Name="ChrClassVillain" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ChrClassID" Type="byte" />
		<Field Name="Gender" Type="byte" />
	</Table>
	<Table Name="ChrCustomization" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Sex" Type="int" />
		<Field Name="BaseSection" Type="int" />
		<Field Name="UiCustomizationType" Type="int" />
		<Field Name="ComponentSection" Type="int" ArraySize="3" />
	</Table>
	<Table Name="ChrRaces" Build="26654">
		<Field Name="ClientPrefix" Type="string" />
		<Field Name="ClientFileString" Type="string" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Name_Female_Lang" Type="string" />
		<Field Name="Name_Lowercase_Lang" Type="string" />
		<Field Name="Name_Female_Lowercase_Lang" Type="string" />
		<Field Name="Flags" Type="int" />
		<Field Name="MaleDisplayID" Type="int" />
		<Field Name="FemaleDisplayID" Type="int" />
		<Field Name="CreateScreenFileDataID" Type="int" />
		<Field Name="SelectScreenFileDataID" Type="int" />
		<Field Name="MaleCustomizeOffset" Type="float" ArraySize="3" />
		<Field Name="FemaleCustomizeOffset" Type="float" ArraySize="3" />
		<Field Name="LowResScreenFileDataID" Type="int" />
		<Field Name="StartingLevel" Type="int" />
		<Field Name="UiDisplayOrder" Type="int" />
		<Field Name="FactionID" Type="short" />
		<Field Name="ResSicknessSpellID" Type="short" />
		<Field Name="SplashSoundID" Type="short" />
		<Field Name="CinematicSequenceID" Type="short" />
		<Field Name="BaseLanguage" Type="byte" />
		<Field Name="CreatureType" Type="byte" />
		<Field Name="Alliance" Type="byte" />
		<Field Name="Race_Related" Type="byte" />
		<Field Name="UnalteredVisualRaceID" Type="byte" />
		<Field Name="CharComponentTextureLayoutID" Type="byte" />
		<Field Name="DefaultClassID" Type="byte" />
		<Field Name="NeutralRaceID" Type="byte" />
		<Field Name="DisplayRaceID" Type="byte" />
		<Field Name="CharComponentTexLayoutHiResID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HighResMaleDisplayID" Type="int" />
		<Field Name="HighResFemaleDisplayID" Type="int" />
		<Field Name="HeritageArmorAchievementID" Type="int" />
		<Field Name="MaleSkeletonFileDataID" Type="int" />
		<Field Name="FemaleSkeletonFileDataID" Type="int" />
		<Field Name="AlteredFormStartVisualKitID" Type="int" ArraySize="3" />
		<Field Name="AlteredFormFinishVisualKitID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="ChrSpecialization" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="FemaleName_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="MasterySpellID" Type="int" ArraySize="2" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PetTalentType" Type="byte" />
		<Field Name="Role" Type="byte" />
		<Field Name="PrimaryStatPriority" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellIconFileID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="AnimReplacements" Type="int" />
	</Table>
	<Table Name="ChrUpgradeBucket" Build="26654">
		<Field Name="ChrSpecializationID" Type="short" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ChrUpgradeBucketSpell" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
	</Table>
	<Table Name="ChrUpgradeTier" Build="26654">
		<Field Name="DisplayName_Lang" Type="string" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="NumTalents" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="CinematicCamera" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundID" Type="int" />
		<Field Name="Origin" Type="float" ArraySize="3" />
		<Field Name="OriginFacing" Type="float" />
		<Field Name="FileDataID" Type="int" />
	</Table>
	<Table Name="CinematicSequences" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundID" Type="int" />
		<Field Name="Camera" Type="short" ArraySize="8" />
	</Table>
	<Table Name="CloakDampening" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Angle" Type="float" ArraySize="5" />
		<Field Name="Dampening" Type="float" ArraySize="5" />
		<Field Name="TailAngle" Type="float" ArraySize="2" />
		<Field Name="TailDampening" Type="float" ArraySize="2" />
		<Field Name="TabardAngle" Type="float" />
		<Field Name="TabardDampening" Type="float" />
		<Field Name="ExpectedWeaponSize" Type="float" />
	</Table>
	<Table Name="CombatCondition" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WorldStateExpressionID" Type="short" />
		<Field Name="SelfConditionID" Type="short" />
		<Field Name="TargetConditionID" Type="short" />
		<Field Name="FriendConditionID" Type="short" ArraySize="2" />
		<Field Name="EnemyConditionID" Type="short" ArraySize="2" />
		<Field Name="FriendConditionOp" Type="byte" ArraySize="2" />
		<Field Name="FriendConditionCount" Type="byte" ArraySize="2" />
		<Field Name="FriendConditionLogic" Type="byte" />
		<Field Name="EnemyConditionOp" Type="byte" ArraySize="2" />
		<Field Name="EnemyConditionCount" Type="byte" ArraySize="2" />
		<Field Name="EnemyConditionLogic" Type="byte" />
	</Table>
	<Table Name="CommentatorStartLocation" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="MapID" Type="int" />
	</Table>
	<Table Name="CommentatorTrackedCooldown" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Priority" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="SpellID" Type="int" />
	</Table>
	<Table Name="ComponentModelFileData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GenderIndex" Type="byte" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="PositionIndex" Type="byte" />
	</Table>
	<Table Name="ComponentTextureFileData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GenderIndex" Type="byte" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="RaceID" Type="byte" />
	</Table>
	<Table Name="ConfigurationWarning" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Warning_Lang" Type="string" />
		<Field Name="Type" Type="int" />
	</Table>
	<Table Name="Contribution" Build="26654">
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ManagedWorldStateInputID" Type="int" />
		<Field Name="UiTextureAtlasMemberID" Type="int" ArraySize="4" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="ConversationLine" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BroadcastTextID" Type="int" />
		<Field Name="SpellVisualKitID" Type="int" />
		<Field Name="AdditionalDuration" Type="int" />
		<Field Name="NextConversationLineID" Type="short" />
		<Field Name="AnimKitID" Type="short" />
		<Field Name="SpeechType" Type="byte" />
		<Field Name="StartAnimation" Type="byte" />
		<Field Name="EndAnimation" Type="byte" />
	</Table>
	<Table Name="Creature" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="NameAlt_Lang" Type="string" />
		<Field Name="Title_Lang" Type="string" />
		<Field Name="TitleAlt_Lang" Type="string" />
		<Field Name="AlwaysItem" Type="int" ArraySize="3" />
		<Field Name="MountCreatureID" Type="int" />
		<Field Name="DisplayID" Type="int" ArraySize="4" />
		<Field Name="DisplayProbability" Type="float" ArraySize="4" />
		<Field Name="CreatureType" Type="byte" />
		<Field Name="CreatureFamily" Type="byte" />
		<Field Name="Classification" Type="byte" />
		<Field Name="StartAnimState" Type="byte" />
	</Table>
	<Table Name="CreatureDifficulty" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" ArraySize="7" />
		<Field Name="FactionID" Type="short" />
		<Field Name="ExpansionID" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
	</Table>
	<Table Name="CreatureDisplayInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureModelScale" Type="float" />
		<Field Name="ModelID" Type="short" />
		<Field Name="NPCSoundID" Type="short" />
		<Field Name="SizeClass" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Gender" Type="byte" />
		<Field Name="ExtendedDisplayInfoID" Type="int" />
		<Field Name="PortraitTextureFileDataID" Type="int" />
		<Field Name="CreatureModelAlpha" Type="byte" />
		<Field Name="SoundID" Type="short" />
		<Field Name="PlayerOverrideScale" Type="float" />
		<Field Name="PortraitCreatureDisplayInfoID" Type="int" />
		<Field Name="BloodID" Type="byte" />
		<Field Name="ParticleColorID" Type="short" />
		<Field Name="CreatureGeosetData" Type="int" />
		<Field Name="ObjectEffectPackageID" Type="short" />
		<Field Name="AnimReplacementSetID" Type="short" />
		<Field Name="UnarmedWeaponType" Type="byte" />
		<Field Name="StateSpellVisualKitID" Type="int" />
		<Field Name="PetInstanceScale" Type="float" />
		<Field Name="MountPoofSpellVisualKitID" Type="int" />
		<Field Name="TextureVariationFileDataID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="CreatureDisplayInfoCond" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="long" />
		<Field Name="CustomOption0_Mask" Type="int" ArraySize="2" />
		<Field Name="CustomOption1_Mask" Type="int" ArraySize="2" />
		<Field Name="CustomOption2_Mask" Type="int" ArraySize="2" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Gender" Type="byte" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="SkinColorMask" Type="int" />
		<Field Name="HairColorMask" Type="int" />
		<Field Name="HairStyleMask" Type="int" />
		<Field Name="FaceStyleMask" Type="int" />
		<Field Name="FacialHairStyleMask" Type="int" />
		<Field Name="CreatureModelDataID" Type="int" />
		<Field Name="TextureVariationFileDataID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="CreatureDisplayInfoEvt" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Fourcc" Type="int" />
		<Field Name="SpellVisualKitID" Type="int" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CreatureDisplayInfoExtra" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BakeMaterialResourcesID" Type="int" />
		<Field Name="HDBakeMaterialResourcesID" Type="int" />
		<Field Name="DisplayRaceID" Type="byte" />
		<Field Name="DisplaySexID" Type="byte" />
		<Field Name="DisplayClassID" Type="byte" />
		<Field Name="SkinID" Type="byte" />
		<Field Name="FaceID" Type="byte" />
		<Field Name="HairStyleID" Type="byte" />
		<Field Name="HairColorID" Type="byte" />
		<Field Name="FacialHairID" Type="byte" />
		<Field Name="CustomDisplayOption" Type="byte" ArraySize="3" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CreatureDisplayInfoTrn" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DstCreatureDisplayInfoID" Type="int" />
		<Field Name="MaxTime" Type="float" />
		<Field Name="DissolveEffectID" Type="int" />
		<Field Name="StartVisualKitID" Type="int" />
		<Field Name="FinishVisualKitID" Type="int" />
	</Table>
	<Table Name="CreatureDispXUiCamera" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureDisplayInfoID" Type="int" />
		<Field Name="UiCameraID" Type="short" />
	</Table>
	<Table Name="CreatureFamily" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="MinScale" Type="float" />
		<Field Name="MaxScale" Type="float" />
		<Field Name="IconFileID" Type="int" />
		<Field Name="SkillLine" Type="short" ArraySize="2" />
		<Field Name="PetFoodMask" Type="short" />
		<Field Name="MinScaleLevel" Type="byte" />
		<Field Name="MaxScaleLevel" Type="byte" />
		<Field Name="PetTalentType" Type="byte" />
	</Table>
	<Table Name="CreatureImmunities" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Mechanic" Type="int" ArraySize="2" />
		<Field Name="School" Type="byte" />
		<Field Name="MechanicsAllowed" Type="byte" />
		<Field Name="EffectsAllowed" Type="byte" />
		<Field Name="StatesAllowed" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="DispelType" Type="int" />
		<Field Name="Effect" Type="int" ArraySize="9" />
		<Field Name="State" Type="int" ArraySize="16" />
	</Table>
	<Table Name="CreatureModelData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelScale" Type="float" />
		<Field Name="FootprintTextureLength" Type="float" />
		<Field Name="FootprintTextureWidth" Type="float" />
		<Field Name="FootprintParticleScale" Type="float" />
		<Field Name="CollisionWidth" Type="float" />
		<Field Name="CollisionHeight" Type="float" />
		<Field Name="MountHeight" Type="float" />
		<Field Name="GeoBox" Type="float" ArraySize="6" />
		<Field Name="WorldEffectScale" Type="float" />
		<Field Name="AttachedEffectScale" Type="float" />
		<Field Name="MissileCollisionRadius" Type="float" />
		<Field Name="MissileCollisionPush" Type="float" />
		<Field Name="MissileCollisionRaise" Type="float" />
		<Field Name="OverrideLootEffectScale" Type="float" />
		<Field Name="OverrideNameScale" Type="float" />
		<Field Name="OverrideSelectionRadius" Type="float" />
		<Field Name="TamedPetBaseScale" Type="float" />
		<Field Name="HoverHeight" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="SizeClass" Type="int" />
		<Field Name="BloodID" Type="int" />
		<Field Name="FootprintTextureID" Type="int" />
		<Field Name="FoleyMaterialID" Type="int" />
		<Field Name="FootstepCameraEffectID" Type="int" />
		<Field Name="DeathThudCameraEffectID" Type="int" />
		<Field Name="SoundID" Type="int" />
		<Field Name="CreatureGeosetDataID" Type="int" />
	</Table>
	<Table Name="CreatureMovementInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SmoothFacingChaseRate" Type="float" />
	</Table>
	<Table Name="CreatureSoundData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FidgetDelaySecondsMin" Type="float" />
		<Field Name="FidgetDelaySecondsMax" Type="float" />
		<Field Name="CreatureImpactType" Type="byte" />
		<Field Name="SoundExertionID" Type="int" />
		<Field Name="SoundExertionCriticalID" Type="int" />
		<Field Name="SoundInjuryID" Type="int" />
		<Field Name="SoundInjuryCriticalID" Type="int" />
		<Field Name="SoundInjuryCrushingBlowID" Type="int" />
		<Field Name="SoundDeathID" Type="int" />
		<Field Name="SoundStunID" Type="int" />
		<Field Name="SoundStandID" Type="int" />
		<Field Name="SoundFootstepID" Type="int" />
		<Field Name="SoundAggroID" Type="int" />
		<Field Name="SoundWingFlapID" Type="int" />
		<Field Name="SoundWingGlideID" Type="int" />
		<Field Name="SoundAlertID" Type="int" />
		<Field Name="NPCSoundID" Type="int" />
		<Field Name="LoopSoundID" Type="int" />
		<Field Name="SoundJumpStartID" Type="int" />
		<Field Name="SoundJumpEndID" Type="int" />
		<Field Name="SoundPetAttackID" Type="int" />
		<Field Name="SoundPetOrderID" Type="int" />
		<Field Name="SoundPetDismissID" Type="int" />
		<Field Name="BirthSoundID" Type="int" />
		<Field Name="SpellCastDirectedSoundID" Type="int" />
		<Field Name="SubmergeSoundID" Type="int" />
		<Field Name="SubmergedSoundID" Type="int" />
		<Field Name="CreatureSoundDataIDPet" Type="int" />
		<Field Name="WindupSoundID" Type="int" />
		<Field Name="WindupCriticalSoundID" Type="int" />
		<Field Name="ChargeSoundID" Type="int" />
		<Field Name="ChargeCriticalSoundID" Type="int" />
		<Field Name="BattleShoutSoundID" Type="int" />
		<Field Name="BattleShoutCriticalSoundID" Type="int" />
		<Field Name="TauntSoundID" Type="int" />
		<Field Name="SoundFidget" Type="int" ArraySize="5" />
		<Field Name="CustomAttack" Type="int" ArraySize="4" />
	</Table>
	<Table Name="CreatureType" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CreatureXContribution" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ContributionID" Type="int" />
	</Table>
	<Table Name="Criteria" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Asset" Type="int" />
		<Field Name="Start_Asset" Type="int" />
		<Field Name="Fail_Asset" Type="int" />
		<Field Name="Modifier_Tree_ID" Type="int" />
		<Field Name="Start_Timer" Type="short" />
		<Field Name="Eligibility_World_State_ID" Type="short" />
		<Field Name="Type" Type="byte" />
		<Field Name="Start_Event" Type="byte" />
		<Field Name="Fail_Event" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Eligibility_World_State_Value" Type="byte" />
	</Table>
	<Table Name="CriteriaTree" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Amount" Type="int" />
		<Field Name="Flags" Type="short" />
		<Field Name="Operator" Type="byte" />
		<Field Name="CriteriaID" Type="int" />
		<Field Name="Parent" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="CriteriaTreeXEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WorldEffectID" Type="short" />
	</Table>
	<Table Name="CurrencyCategory" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ExpansionID" Type="byte" />
	</Table>
	<Table Name="CurrencyTypes" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="MaxQty" Type="int" />
		<Field Name="MaxEarnablePerWeek" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="CategoryID" Type="byte" />
		<Field Name="SpellCategory" Type="byte" />
		<Field Name="Quality" Type="byte" />
		<Field Name="InventoryIconFileID" Type="int" />
		<Field Name="SpellWeight" Type="int" />
	</Table>
	<Table Name="Curve" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="CurvePoint" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="2" />
		<Field Name="CurveID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="DeathThudLookups" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SizeClass" Type="byte" />
		<Field Name="TerrainTypeSoundID" Type="byte" />
		<Field Name="SoundEntryID" Type="int" />
		<Field Name="SoundEntryIDWater" Type="int" />
	</Table>
	<Table Name="DecalProperties" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="InnerRadius" Type="float" />
		<Field Name="OuterRadius" Type="float" />
		<Field Name="Rim" Type="float" />
		<Field Name="Gain" Type="float" />
		<Field Name="ModX" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="FadeIn" Type="float" />
		<Field Name="FadeOut" Type="float" />
		<Field Name="Priority" Type="byte" />
		<Field Name="BlendMode" Type="byte" />
		<Field Name="TopTextureBlendSetID" Type="int" />
		<Field Name="BotTextureBlendSetID" Type="int" />
		<Field Name="GameFlags" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="CasterDecalPropertiesID" Type="int" />
	</Table>
	<Table Name="DeclinedWord" Build="26654">
		<Field Name="Word" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="DeclinedWordCases" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DeclinedWord" Type="string" />
		<Field Name="CaseIndex" Type="byte" />
	</Table>
	<Table Name="DestructibleModelData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="State0WMO" Type="short" />
		<Field Name="State1WMO" Type="short" />
		<Field Name="State2WMO" Type="short" />
		<Field Name="State3WMO" Type="short" />
		<Field Name="HealEffectSpeed" Type="short" />
		<Field Name="State0ImpactEffectDoodadSet" Type="byte" />
		<Field Name="State0AmbientDoodadSet" Type="byte" />
		<Field Name="State0NameSet" Type="byte" />
		<Field Name="State1DestructionDoodadSet" Type="byte" />
		<Field Name="State1ImpactEffectDoodadSet" Type="byte" />
		<Field Name="State1AmbientDoodadSet" Type="byte" />
		<Field Name="State1NameSet" Type="byte" />
		<Field Name="State2DestructionDoodadSet" Type="byte" />
		<Field Name="State2ImpactEffectDoodadSet" Type="byte" />
		<Field Name="State2AmbientDoodadSet" Type="byte" />
		<Field Name="State2NameSet" Type="byte" />
		<Field Name="State3InitDoodadSet" Type="byte" />
		<Field Name="State3AmbientDoodadSet" Type="byte" />
		<Field Name="State3NameSet" Type="byte" />
		<Field Name="EjectDirection" Type="byte" />
		<Field Name="DoNotHighlight" Type="byte" />
		<Field Name="HealEffect" Type="byte" />
	</Table>
	<Table Name="DeviceBlacklist" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VendorID" Type="short" />
		<Field Name="DeviceID" Type="short" />
	</Table>
	<Table Name="DeviceDefaultSettings" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VendorID" Type="short" />
		<Field Name="DeviceID" Type="short" />
		<Field Name="DefaultSetting" Type="byte" />
	</Table>
	<Table Name="Difficulty" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="GroupSizeHealthCurveID" Type="short" />
		<Field Name="GroupSizeDmgCurveID" Type="short" />
		<Field Name="GroupSizeSpellPointsCurveID" Type="short" />
		<Field Name="FallbackDifficultyID" Type="byte" />
		<Field Name="InstanceType" Type="byte" />
		<Field Name="MinPlayers" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="OldEnumValue" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ToggleDifficultyID" Type="byte" />
		<Field Name="ItemContext" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="DissolveEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Ramp" Type="float" />
		<Field Name="StartValue" Type="float" />
		<Field Name="EndValue" Type="float" />
		<Field Name="FadeInTime" Type="float" />
		<Field Name="FadeOutTime" Type="float" />
		<Field Name="Duration" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="FresnelIntensity" Type="float" />
		<Field Name="AttachID" Type="byte" />
		<Field Name="ProjectionType" Type="byte" />
		<Field Name="TextureBlendSetID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="CurveID" Type="int" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="DriverBlacklist" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DriverVersionHi" Type="int" />
		<Field Name="DriverVersionLow" Type="int" />
		<Field Name="VendorID" Type="short" />
		<Field Name="DeviceID" Type="byte" />
		<Field Name="OsVersion" Type="byte" />
		<Field Name="OsBits" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="DungeonEncounter" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="CreatureDisplayID" Type="int" />
		<Field Name="MapID" Type="short" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="Bit" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OrderIndex" Type="int" />
		<Field Name="SpellIconFileID" Type="int" />
	</Table>
	<Table Name="DungeonMap" Build="26654">
		<Field Name="Min" Type="float" ArraySize="2" />
		<Field Name="Max" Type="float" ArraySize="2" />
		<Field Name="MapID" Type="short" />
		<Field Name="ParentWorldMapID" Type="short" />
		<Field Name="FloorIndex" Type="byte" />
		<Field Name="RelativeHeightIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="DungeonMapChunk" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinZ" Type="float" />
		<Field Name="DoodadPlacementID" Type="int" />
		<Field Name="MapID" Type="short" />
		<Field Name="WMOGroupID" Type="short" />
		<Field Name="DungeonMapID" Type="short" />
	</Table>
	<Table Name="DurabilityCosts" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WeaponSubClassCost" Type="short" ArraySize="21" />
		<Field Name="ArmorSubClassCost" Type="short" ArraySize="8" />
	</Table>
	<Table Name="DurabilityQuality" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="EdgeGlowEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Duration" Type="float" />
		<Field Name="FadeIn" Type="float" />
		<Field Name="FadeOut" Type="float" />
		<Field Name="FresnelCoefficient" Type="float" />
		<Field Name="GlowRed" Type="float" />
		<Field Name="GlowGreen" Type="float" />
		<Field Name="GlowBlue" Type="float" />
		<Field Name="GlowAlpha" Type="float" />
		<Field Name="GlowMultiplier" Type="float" />
		<Field Name="InitialDelay" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CurveID" Type="int" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="Emotes" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="long" />
		<Field Name="EmoteSlashCommand" Type="string" />
		<Field Name="EmoteFlags" Type="int" />
		<Field Name="SpellVisualKitID" Type="int" />
		<Field Name="AnimID" Type="short" />
		<Field Name="EmoteSpecProc" Type="byte" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="EmoteSpecProcParam" Type="int" />
		<Field Name="EventSoundID" Type="int" />
	</Table>
	<Table Name="EmotesText" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="EmoteID" Type="short" />
	</Table>
	<Table Name="EmotesTextData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text_Lang" Type="string" />
		<Field Name="RelationshipFlags" Type="byte" />
	</Table>
	<Table Name="EmotesTextSound" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="SexID" Type="byte" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="SoundID" Type="int" />
	</Table>
	<Table Name="EnvironmentalDamage" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VisualkitID" Type="short" />
		<Field Name="EnumID" Type="byte" />
	</Table>
	<Table Name="Exhaustion" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="CombatLogText" Type="string" />
		<Field Name="Xp" Type="int" />
		<Field Name="Factor" Type="float" />
		<Field Name="OutdoorHours" Type="float" />
		<Field Name="InnHours" Type="float" />
		<Field Name="Threshold" Type="float" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Faction" Build="26654">
		<Field Name="ReputationRaceMask" Type="long" ArraySize="4" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ReputationBase" Type="int" ArraySize="4" />
		<Field Name="ParentFactionMod" Type="float" ArraySize="2" />
		<Field Name="ReputationMax" Type="int" ArraySize="4" />
		<Field Name="ReputationIndex" Type="short" />
		<Field Name="ReputationClassMask" Type="short" ArraySize="4" />
		<Field Name="ReputationFlags" Type="short" ArraySize="4" />
		<Field Name="ParentFactionID" Type="short" />
		<Field Name="ParagonFactionID" Type="short" />
		<Field Name="ParentFactionCap" Type="byte" ArraySize="2" />
		<Field Name="Expansion" Type="byte" />
		<Field Name="FriendshipRepID" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="FactionGroup" Build="26654">
		<Field Name="InternalName" Type="string" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaskID" Type="byte" />
		<Field Name="HonorCurrencyTextureFileID" Type="int" />
		<Field Name="ConquestCurrencyTextureFileID" Type="int" />
	</Table>
	<Table Name="FactionTemplate" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Faction" Type="short" />
		<Field Name="Flags" Type="short" />
		<Field Name="Enemies" Type="short" ArraySize="4" />
		<Field Name="Friend" Type="short" ArraySize="4" />
		<Field Name="FactionGroup" Type="byte" />
		<Field Name="FriendGroup" Type="byte" />
		<Field Name="EnemyGroup" Type="byte" />
	</Table>
	<Table Name="FootprintTextures" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TextureBlendsetID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="FileDataID" Type="int" />
	</Table>
	<Table Name="FootstepTerrainLookup" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureFootstepID" Type="short" />
		<Field Name="TerrainSoundID" Type="byte" />
		<Field Name="SoundID" Type="int" />
		<Field Name="SoundIDSplash" Type="int" />
	</Table>
	<Table Name="FriendshipRepReaction" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Reaction_Lang" Type="string" />
		<Field Name="ReactionThreshold" Type="short" />
		<Field Name="FriendshipRepID" Type="byte" />
	</Table>
	<Table Name="FriendshipReputation" Build="26654">
		<Field Name="Description_Lang" Type="string" />
		<Field Name="TextureFileID" Type="int" />
		<Field Name="FactionID" Type="short" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="FullScreenEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Saturation" Type="float" />
		<Field Name="GammaRed" Type="float" />
		<Field Name="GammaGreen" Type="float" />
		<Field Name="GammaBlue" Type="float" />
		<Field Name="MaskOffsetY" Type="float" />
		<Field Name="MaskSizeMultiplier" Type="float" />
		<Field Name="MaskPower" Type="float" />
		<Field Name="ColorMultiplyRed" Type="float" />
		<Field Name="ColorMultiplyGreen" Type="float" />
		<Field Name="ColorMultiplyBlue" Type="float" />
		<Field Name="ColorMultiplyOffsetY" Type="float" />
		<Field Name="ColorMultiplyMultiplier" Type="float" />
		<Field Name="ColorMultiplyPower" Type="float" />
		<Field Name="ColorAdditionRed" Type="float" />
		<Field Name="ColorAdditionGreen" Type="float" />
		<Field Name="ColorAdditionBlue" Type="float" />
		<Field Name="ColorAdditionOffsetY" Type="float" />
		<Field Name="ColorAdditionMultiplier" Type="float" />
		<Field Name="ColorAdditionPower" Type="float" />
		<Field Name="BlurIntensity" Type="float" />
		<Field Name="BlurOffsetY" Type="float" />
		<Field Name="BlurMultiplier" Type="float" />
		<Field Name="BlurPower" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="TextureBlendSetID" Type="int" />
		<Field Name="EffectFadeInMs" Type="int" />
		<Field Name="EffectFadeOutMs" Type="int" />
	</Table>
	<Table Name="GameObjectArtKit" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AttachModelFileID" Type="int" />
		<Field Name="TextureVariationFileID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="GameObjectDiffAnimMap" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AttachmentDisplayID" Type="short" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="Animation" Type="byte" />
	</Table>
	<Table Name="GameObjectDisplayInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="GeoBox" Type="float" ArraySize="6" />
		<Field Name="OverrideLootEffectScale" Type="float" />
		<Field Name="OverrideNameScale" Type="float" />
		<Field Name="ObjectEffectPackageID" Type="short" />
	</Table>
	<Table Name="GameObjectDisplayInfoXSoundKit" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EventIndex" Type="byte" />
		<Field Name="SoundKitID" Type="int" />
	</Table>
	<Table Name="GameObjects" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="Rot" Type="float" ArraySize="4" />
		<Field Name="Scale" Type="float" />
		<Field Name="PropValue" Type="int" ArraySize="8" />
		<Field Name="OwnerID" Type="short" />
		<Field Name="DisplayID" Type="short" />
		<Field Name="PhaseID" Type="short" />
		<Field Name="PhaseGroupID" Type="short" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="TypeID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GameTips" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text_Lang" Type="string" />
		<Field Name="Min_Level" Type="short" />
		<Field Name="Max_Level" Type="short" />
		<Field Name="SortIndex" Type="byte" />
	</Table>
	<Table Name="GarrAbility" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="Flags" Type="short" />
		<Field Name="FactionChangeGarrAbilityID" Type="short" />
		<Field Name="GarrAbilityCategoryID" Type="byte" />
		<Field Name="GarrFollowerTypeID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrAbilityCategory" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="GarrAbilityEffect" Build="26654">
		<Field Name="CombatWeightBase" Type="float" />
		<Field Name="CombatWeightMax" Type="float" />
		<Field Name="ActionValueFlat" Type="float" />
		<Field Name="ActionRecordID" Type="int" />
		<Field Name="GarrAbilityID" Type="short" />
		<Field Name="AbilityAction" Type="byte" />
		<Field Name="AbilityTargetType" Type="byte" />
		<Field Name="GarrMechanicTypeID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ActionRace" Type="byte" />
		<Field Name="ActionHours" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrBuilding" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AllianceName_Lang" Type="string" />
		<Field Name="HordeName_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Tooltip_Lang" Type="string" />
		<Field Name="HordeGameObjectID" Type="int" />
		<Field Name="AllianceGameObjectID" Type="int" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="CurrencyTypeID" Type="short" />
		<Field Name="HordeUiTextureKitID" Type="short" />
		<Field Name="AllianceUiTextureKitID" Type="short" />
		<Field Name="AllianceSceneScriptPackageID" Type="short" />
		<Field Name="HordeSceneScriptPackageID" Type="short" />
		<Field Name="GarrAbilityID" Type="short" />
		<Field Name="BonusGarrAbilityID" Type="short" />
		<Field Name="GoldCost" Type="short" />
		<Field Name="GarrSiteID" Type="byte" />
		<Field Name="BuildingType" Type="byte" />
		<Field Name="UpgradeLevel" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ShipmentCapacity" Type="byte" />
		<Field Name="GarrTypeID" Type="byte" />
		<Field Name="BuildSeconds" Type="int" />
		<Field Name="CurrencyQty" Type="int" />
		<Field Name="MaxAssignments" Type="int" />
	</Table>
	<Table Name="GarrBuildingDoodadSet" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrBuildingID" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="AllianceDoodadSet" Type="byte" />
		<Field Name="HordeDoodadSet" Type="byte" />
		<Field Name="SpecializationID" Type="byte" />
	</Table>
	<Table Name="GarrBuildingPlotInst" Build="26654">
		<Field Name="MapOffset" Type="float" ArraySize="2" />
		<Field Name="UiTextureAtlasMemberID" Type="short" />
		<Field Name="GarrSiteLevelPlotInstID" Type="short" />
		<Field Name="GarrBuildingID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrClassSpec" Build="26654">
		<Field Name="ClassSpec_Lang" Type="string" />
		<Field Name="ClassSpec_Male_Lang" Type="string" />
		<Field Name="ClassSpec_Female_Lang" Type="string" />
		<Field Name="UiTextureAtlasMemberID" Type="short" />
		<Field Name="GarrFollItemSetID" Type="short" />
		<Field Name="FollowerClassLimit" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrClassSpecPlayerCond" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="GarrClassSpecID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="FlavorGarrStringID" Type="int" />
	</Table>
	<Table Name="GarrEncounter" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="CreatureID" Type="int" />
		<Field Name="UiAnimScale" Type="float" />
		<Field Name="UiAnimHeight" Type="float" />
		<Field Name="PortraitFileDataID" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiTextureKitID" Type="int" />
	</Table>
	<Table Name="GarrEncounterSetXEncounter" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrEncounterID" Type="int" />
	</Table>
	<Table Name="GarrEncounterXMechanic" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrMechanicID" Type="byte" />
		<Field Name="GarrMechanicSetID" Type="byte" />
	</Table>
	<Table Name="GarrFollItemSetMember" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
		<Field Name="MinItemLevel" Type="short" />
		<Field Name="ItemSlot" Type="byte" />
	</Table>
	<Table Name="GarrFollower" Build="26654">
		<Field Name="HordeSourceText_Lang" Type="string" />
		<Field Name="AllianceSourceText_Lang" Type="string" />
		<Field Name="TitleName_Lang" Type="string" />
		<Field Name="HordeCreatureID" Type="int" />
		<Field Name="AllianceCreatureID" Type="int" />
		<Field Name="HordeIconFileDataID" Type="int" />
		<Field Name="AllianceIconFileDataID" Type="int" />
		<Field Name="HordeSlottingBroadcastTextID" Type="int" />
		<Field Name="AllySlottingBroadcastTextID" Type="int" />
		<Field Name="HordeGarrFollItemSetID" Type="short" />
		<Field Name="AllianceGarrFollItemSetID" Type="short" />
		<Field Name="ItemLevelWeapon" Type="short" />
		<Field Name="ItemLevelArmor" Type="short" />
		<Field Name="HordeUITextureKitID" Type="short" />
		<Field Name="AllianceUITextureKitID" Type="short" />
		<Field Name="GarrFollowerTypeID" Type="byte" />
		<Field Name="HordeGarrFollRaceID" Type="byte" />
		<Field Name="AllianceGarrFollRaceID" Type="byte" />
		<Field Name="Quality" Type="byte" />
		<Field Name="HordeGarrClassSpecID" Type="byte" />
		<Field Name="AllianceGarrClassSpecID" Type="byte" />
		<Field Name="FollowerLevel" Type="byte" />
		<Field Name="Gender" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="HordeSourceTypeEnum" Type="byte" />
		<Field Name="AllianceSourceTypeEnum" Type="byte" />
		<Field Name="GarrTypeID" Type="byte" />
		<Field Name="Vitality" Type="byte" />
		<Field Name="ChrClassID" Type="byte" />
		<Field Name="HordeFlavorGarrStringID" Type="byte" />
		<Field Name="AllianceFlavorGarrStringID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrFollowerLevelXP" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="XpToNextLevel" Type="short" />
		<Field Name="ShipmentXP" Type="short" />
		<Field Name="FollowerLevel" Type="byte" />
		<Field Name="GarrFollowerTypeID" Type="byte" />
	</Table>
	<Table Name="GarrFollowerQuality" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="XpToNextQuality" Type="int" />
		<Field Name="ShipmentXP" Type="short" />
		<Field Name="Quality" Type="byte" />
		<Field Name="AbilityCount" Type="byte" />
		<Field Name="TraitCount" Type="byte" />
		<Field Name="GarrFollowerTypeID" Type="byte" />
		<Field Name="ClassSpecID" Type="int" />
	</Table>
	<Table Name="GarrFollowerSetXFollower" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrFollowerID" Type="int" />
	</Table>
	<Table Name="GarrFollowerType" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaxItemLevel" Type="short" />
		<Field Name="MaxFollowers" Type="byte" />
		<Field Name="MaxFollowerBuildingType" Type="byte" />
		<Field Name="GarrTypeID" Type="byte" />
		<Field Name="LevelRangeBias" Type="byte" />
		<Field Name="ItemLevelRangeBias" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="GarrFollowerUICreature" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureID" Type="int" />
		<Field Name="Scale" Type="float" />
		<Field Name="FactionIndex" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="GarrFollowerXAbility" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrAbilityID" Type="short" />
		<Field Name="FactionIndex" Type="byte" />
	</Table>
	<Table Name="GarrFollSupportSpell" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AllianceSpellID" Type="int" />
		<Field Name="HordeSpellID" Type="int" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="GarrItemLevelUpgradeData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Operation" Type="int" />
		<Field Name="MinItemLevel" Type="int" />
		<Field Name="MaxItemLevel" Type="int" />
		<Field Name="FollowerTypeID" Type="int" />
	</Table>
	<Table Name="GarrMechanic" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Factor" Type="float" />
		<Field Name="GarrMechanicTypeID" Type="byte" />
		<Field Name="GarrAbilityID" Type="int" />
	</Table>
	<Table Name="GarrMechanicSetXMechanic" Build="26654">
		<Field Name="GarrMechanicID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrMechanicType" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="Category" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="GarrMission" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Location_Lang" Type="string" />
		<Field Name="MissionDuration" Type="int" />
		<Field Name="OfferDuration" Type="int" />
		<Field Name="MapPos" Type="float" ArraySize="2" />
		<Field Name="WorldPos" Type="float" ArraySize="2" />
		<Field Name="TargetItemLevel" Type="short" />
		<Field Name="UiTextureKitID" Type="short" />
		<Field Name="MissionCostCurrencyTypesID" Type="short" />
		<Field Name="TargetLevel" Type="byte" />
		<Field Name="EnvGarrMechanicTypeID" Type="byte" />
		<Field Name="MaxFollowers" Type="byte" />
		<Field Name="OfferedGarrMissionTextureID" Type="byte" />
		<Field Name="GarrMissionTypeID" Type="byte" />
		<Field Name="GarrFollowerTypeID" Type="byte" />
		<Field Name="BaseCompletionChance" Type="byte" />
		<Field Name="FollowerDeathChance" Type="byte" />
		<Field Name="GarrTypeID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TravelDuration" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="MissionCost" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="BaseFollowerXP" Type="int" />
		<Field Name="AreaID" Type="int" />
		<Field Name="OvermaxRewardPackID" Type="int" />
		<Field Name="EnvGarrMechanicID" Type="int" />
	</Table>
	<Table Name="GarrMissionTexture" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="2" />
		<Field Name="UiTextureKitID" Type="short" />
	</Table>
	<Table Name="GarrMissionType" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="UiTextureAtlasMemberID" Type="short" />
		<Field Name="UiTextureKitID" Type="short" />
	</Table>
	<Table Name="GarrMissionXEncounter" Build="26654">
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrEncounterID" Type="int" />
		<Field Name="GarrEncounterSetID" Type="int" />
	</Table>
	<Table Name="GarrMissionXFollower" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrFollowerID" Type="int" />
		<Field Name="GarrFollowerSetID" Type="int" />
	</Table>
	<Table Name="GarrMssnBonusAbility" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Radius" Type="float" />
		<Field Name="DurationSecs" Type="int" />
		<Field Name="GarrAbilityID" Type="short" />
		<Field Name="GarrFollowerTypeID" Type="byte" />
		<Field Name="GarrMissionTextureID" Type="byte" />
	</Table>
	<Table Name="GarrPlot" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="AllianceConstructObjID" Type="int" />
		<Field Name="HordeConstructObjID" Type="int" />
		<Field Name="UiCategoryID" Type="byte" />
		<Field Name="PlotType" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UpgradeRequirement" Type="int" ArraySize="2" />
	</Table>
	<Table Name="GarrPlotBuilding" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrPlotID" Type="byte" />
		<Field Name="GarrBuildingID" Type="byte" />
	</Table>
	<Table Name="GarrPlotInstance" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="GarrPlotID" Type="byte" />
	</Table>
	<Table Name="GarrPlotUICategory" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CategoryName_Lang" Type="string" />
		<Field Name="PlotType" Type="byte" />
	</Table>
	<Table Name="GarrSiteLevel" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TownHallUiPos" Type="float" ArraySize="2" />
		<Field Name="MapID" Type="short" />
		<Field Name="UiTextureKitID" Type="short" />
		<Field Name="UpgradeMovieID" Type="short" />
		<Field Name="UpgradeCost" Type="short" />
		<Field Name="UpgradeGoldCost" Type="short" />
		<Field Name="GarrLevel" Type="byte" />
		<Field Name="GarrSiteID" Type="byte" />
		<Field Name="MaxBuildingLevel" Type="byte" />
	</Table>
	<Table Name="GarrSiteLevelPlotInst" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiMarkerPos" Type="float" ArraySize="2" />
		<Field Name="GarrSiteLevelID" Type="short" />
		<Field Name="GarrPlotInstanceID" Type="byte" />
		<Field Name="UiMarkerSize" Type="byte" />
	</Table>
	<Table Name="GarrSpecialization" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Tooltip_Lang" Type="string" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="Param" Type="float" ArraySize="2" />
		<Field Name="BuildingType" Type="byte" />
		<Field Name="SpecType" Type="byte" />
		<Field Name="RequiredUpgradeLevel" Type="byte" />
	</Table>
	<Table Name="GarrString" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text_Lang" Type="string" />
	</Table>
	<Table Name="GarrTalent" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="ResearchDurationSecs" Type="int" />
		<Field Name="Tier" Type="byte" />
		<Field Name="UiOrder" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GarrTalentTreeID" Type="int" />
		<Field Name="GarrAbilityID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="ResearchCost" Type="int" />
		<Field Name="ResearchCostCurrencyTypesID" Type="int" />
		<Field Name="ResearchGoldCost" Type="int" />
		<Field Name="PerkSpellID" Type="int" />
		<Field Name="PerkPlayerConditionID" Type="int" />
		<Field Name="RespecCost" Type="int" />
		<Field Name="RespecCostCurrencyTypesID" Type="int" />
		<Field Name="RespecDurationSecs" Type="int" />
		<Field Name="RespecGoldCost" Type="int" />
	</Table>
	<Table Name="GarrTalentTree" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiTextureKitID" Type="short" />
		<Field Name="MaxTiers" Type="byte" />
		<Field Name="UiOrder" Type="byte" />
		<Field Name="ClassID" Type="int" />
		<Field Name="GarrTypeID" Type="int" />
	</Table>
	<Table Name="GarrType" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="PrimaryCurrencyTypeID" Type="int" />
		<Field Name="SecondaryCurrencyTypeID" Type="int" />
		<Field Name="ExpansionID" Type="int" />
		<Field Name="MapIDs" Type="int" ArraySize="2" />
	</Table>
	<Table Name="GarrUiAnimClassInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ImpactDelaySecs" Type="float" />
		<Field Name="GarrClassSpecID" Type="byte" />
		<Field Name="MovementType" Type="byte" />
		<Field Name="CastKit" Type="int" />
		<Field Name="ImpactKit" Type="int" />
		<Field Name="TargetImpactKit" Type="int" />
	</Table>
	<Table Name="GarrUiAnimRaceInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaleScale" Type="float" />
		<Field Name="MaleHeight" Type="float" />
		<Field Name="MaleSingleModelScale" Type="float" />
		<Field Name="MaleSingleModelHeight" Type="float" />
		<Field Name="MaleFollowerPageScale" Type="float" />
		<Field Name="MaleFollowerPageHeight" Type="float" />
		<Field Name="FemaleScale" Type="float" />
		<Field Name="FemaleHeight" Type="float" />
		<Field Name="FemaleSingleModelScale" Type="float" />
		<Field Name="FemaleSingleModelHeight" Type="float" />
		<Field Name="FemaleFollowerPageScale" Type="float" />
		<Field Name="FemaleFollowerPageHeight" Type="float" />
		<Field Name="GarrFollRaceID" Type="byte" />
	</Table>
	<Table Name="GemProperties" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="int" />
		<Field Name="Enchant_ID" Type="short" />
		<Field Name="Min_Item_Level" Type="short" />
	</Table>
	<Table Name="GlobalStrings" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseTag" Type="string" />
		<Field Name="TagText_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="GlyphBindableSpell" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
	</Table>
	<Table Name="GlyphExclusiveCategory" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="GlyphProperties" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="SpellIconID" Type="short" />
		<Field Name="GlyphType" Type="byte" />
		<Field Name="GlyphExclusiveCategoryID" Type="byte" />
	</Table>
	<Table Name="GlyphRequiredSpec" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrSpecializationID" Type="short" />
	</Table>
	<Table Name="GMSurveyAnswers" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Answer_Lang" Type="string" />
		<Field Name="Sort_Index" Type="byte" />
	</Table>
	<Table Name="GMSurveyCurrentSurvey" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GMSURVEY_ID" Type="byte" />
	</Table>
	<Table Name="GMSurveyQuestions" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Question_Lang" Type="string" />
	</Table>
	<Table Name="GMSurveySurveys" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Q" Type="byte" ArraySize="15" />
	</Table>
	<Table Name="GroundEffectDoodad" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Animscale" Type="float" />
		<Field Name="Pushscale" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ModelFileID" Type="int" />
	</Table>
	<Table Name="GroundEffectTexture" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DoodadID" Type="short" ArraySize="4" />
		<Field Name="DoodadWeight" Type="byte" ArraySize="4" />
		<Field Name="Sound" Type="byte" />
		<Field Name="Density" Type="int" />
	</Table>
	<Table Name="GroupFinderActivity" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FullName_Lang" Type="string" />
		<Field Name="ShortName_Lang" Type="string" />
		<Field Name="MinGearLevelSuggestion" Type="short" />
		<Field Name="MapID" Type="short" />
		<Field Name="AreaID" Type="short" />
		<Field Name="GroupFinderCategoryID" Type="byte" />
		<Field Name="GroupFinderActivityGrpID" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevelSuggestion" Type="byte" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="DisplayType" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
	</Table>
	<Table Name="GroupFinderActivityGrp" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="GroupFinderCategory" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="GuildColorBackground" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="GuildColorBorder" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="GuildColorEmblem" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="GuildPerkSpells" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
	</Table>
	<Table Name="Heirloom" Build="26654">
		<Field Name="SourceText_Lang" Type="string" />
		<Field Name="ItemID" Type="int" />
		<Field Name="LegacyItemID" Type="int" />
		<Field Name="LegacyUpgradedItemID" Type="int" />
		<Field Name="StaticUpgradedItemID" Type="int" />
		<Field Name="UpgradeItemID" Type="int" ArraySize="3" />
		<Field Name="UpgradeItemBonusListID" Type="short" ArraySize="3" />
		<Field Name="Flags" Type="byte" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="HelmetAnimScaling" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Amount" Type="float" />
		<Field Name="RaceID" Type="int" />
	</Table>
	<Table Name="HelmetGeosetVisData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HideGeoset" Type="int" ArraySize="9" />
	</Table>
	<Table Name="HighlightColor" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StartColor" Type="int" />
		<Field Name="MidColor" Type="int" />
		<Field Name="EndColor" Type="int" />
		<Field Name="Type" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="HolidayDescriptions" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
	</Table>
	<Table Name="HolidayNames" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="Holidays" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Date" Type="int" ArraySize="16" />
		<Field Name="Duration" Type="short" ArraySize="10" />
		<Field Name="Region" Type="short" />
		<Field Name="Looping" Type="byte" />
		<Field Name="CalendarFlags" Type="byte" ArraySize="10" />
		<Field Name="Priority" Type="byte" />
		<Field Name="CalendarFilterType" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="HolidayNameID" Type="int" />
		<Field Name="HolidayDescriptionID" Type="int" />
		<Field Name="TextureFileDataID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="Hotfix" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Tablename" Type="string" />
		<Field Name="Object_ID" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="ImportPriceArmor" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClothModifier" Type="float" />
		<Field Name="LeatherModifier" Type="float" />
		<Field Name="ChainModifier" Type="float" />
		<Field Name="PlateModifier" Type="float" />
	</Table>
	<Table Name="ImportPriceQuality" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="ImportPriceShield" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="ImportPriceWeapon" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Data" Type="float" />
	</Table>
	<Table Name="InvasionClientData" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="IconLocation" Type="float" ArraySize="2" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WorldStateID" Type="int" />
		<Field Name="UiTextureAtlasMemberID" Type="int" />
		<Field Name="ScenarioID" Type="int" />
		<Field Name="WorldQuestID" Type="int" />
		<Field Name="WorldStateValue" Type="int" />
		<Field Name="InvasionEnabledWorldStateID" Type="int" />
	</Table>
	<Table Name="Item" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="SubclassID" Type="byte" />
		<Field Name="Sound_Override_SubclassID" Type="byte" />
		<Field Name="Material" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="SheatheType" Type="byte" />
		<Field Name="ItemGroupSoundsID" Type="byte" />
	</Table>
	<Table Name="ItemAppearance" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemDisplayInfoID" Type="int" />
		<Field Name="DefaultIconFileDataID" Type="int" />
		<Field Name="UiOrder" Type="int" />
		<Field Name="DisplayType" Type="byte" />
	</Table>
	<Table Name="ItemAppearanceXUiCamera" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemAppearanceID" Type="short" />
		<Field Name="UiCameraID" Type="short" />
	</Table>
	<Table Name="ItemArmorQuality" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Qualitymod" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="short" />
	</Table>
	<Table Name="ItemArmorShield" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="short" />
	</Table>
	<Table Name="ItemArmorTotal" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Cloth" Type="float" />
		<Field Name="Leather" Type="float" />
		<Field Name="Mail" Type="float" />
		<Field Name="Plate" Type="float" />
		<Field Name="ItemLevel" Type="short" />
	</Table>
	<Table Name="ItemBagFamily" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="ItemBonus" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="int" ArraySize="3" />
		<Field Name="ParentItemBonusListID" Type="short" />
		<Field Name="Type" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="ItemBonusListLevelDelta" Build="26654">
		<Field Name="ItemLevelDelta" Type="short" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ItemBonusTreeNode" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChildItemBonusTreeID" Type="short" />
		<Field Name="ChildItemBonusListID" Type="short" />
		<Field Name="ChildItemLevelSelectorID" Type="short" />
		<Field Name="ItemContext" Type="byte" />
	</Table>
	<Table Name="ItemChildEquipment" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChildItemID" Type="int" />
		<Field Name="ChildItemEquipSlot" Type="byte" />
	</Table>
	<Table Name="ItemClass" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ClassName_Lang" Type="string" />
		<Field Name="PriceModifier" Type="float" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ItemContextPickerEntry" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemCreationContext" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PVal" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="ItemCurrencyCost" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
	</Table>
	<Table Name="ItemDamageAmmo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="short" />
	</Table>
	<Table Name="ItemDamageOneHand" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="short" />
	</Table>
	<Table Name="ItemDamageOneHandCaster" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="short" />
	</Table>
	<Table Name="ItemDamageTwoHand" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="short" />
	</Table>
	<Table Name="ItemDamageTwoHandCaster" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Quality" Type="float" ArraySize="7" />
		<Field Name="ItemLevel" Type="short" />
	</Table>
	<Table Name="ItemDisenchantLoot" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinLevel" Type="short" />
		<Field Name="MaxLevel" Type="short" />
		<Field Name="SkillRequired" Type="short" />
		<Field Name="Subclass" Type="byte" />
		<Field Name="Quality" Type="byte" />
		<Field Name="ExpansionID" Type="byte" />
	</Table>
	<Table Name="ItemDisplayInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="ItemRangedDisplayInfoID" Type="int" />
		<Field Name="ItemVisual" Type="int" />
		<Field Name="ParticleColorID" Type="int" />
		<Field Name="OverrideSwooshSoundKitID" Type="int" />
		<Field Name="SheatheTransformMatrixID" Type="int" />
		<Field Name="ModelType1" Type="int" />
		<Field Name="StateSpellVisualKitID" Type="int" />
		<Field Name="SheathedSpellVisualKitID" Type="int" />
		<Field Name="UnsheathedSpellVisualKitID" Type="int" />
		<Field Name="ModelResourcesID" Type="int" ArraySize="2" />
		<Field Name="ModelMaterialResourcesID" Type="int" ArraySize="2" />
		<Field Name="GeosetGroup" Type="int" ArraySize="4" />
		<Field Name="AttachmentGeosetGroup" Type="int" ArraySize="4" />
		<Field Name="HelmetGeosetVis" Type="int" ArraySize="2" />
	</Table>
	<Table Name="ItemDisplayInfoMaterialRes" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaterialResourcesID" Type="int" />
		<Field Name="ComponentSection" Type="byte" />
	</Table>
	<Table Name="ItemDisplayXUiCamera" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemDisplayInfoID" Type="int" />
		<Field Name="UiCameraID" Type="short" />
	</Table>
	<Table Name="ItemEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="CoolDownMSec" Type="int" />
		<Field Name="CategoryCoolDownMSec" Type="int" />
		<Field Name="Charges" Type="short" />
		<Field Name="SpellCategoryID" Type="short" />
		<Field Name="ChrSpecializationID" Type="short" />
		<Field Name="LegacySlotIndex" Type="byte" />
		<Field Name="TriggerType" Type="byte" />
	</Table>
	<Table Name="ItemExtendedCost" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" ArraySize="5" />
		<Field Name="CurrencyCount" Type="int" ArraySize="5" />
		<Field Name="ItemCount" Type="short" ArraySize="5" />
		<Field Name="RequiredArenaRating" Type="short" />
		<Field Name="CurrencyID" Type="short" ArraySize="5" />
		<Field Name="ArenaBracket" Type="byte" />
		<Field Name="MinFactionID" Type="byte" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="RequiredAchievement" Type="byte" />
	</Table>
	<Table Name="ItemGroupSounds" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Sound" Type="int" ArraySize="4" />
	</Table>
	<Table Name="ItemLevelSelector" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinItemLevel" Type="short" />
		<Field Name="ItemLevelSelectorQualitySetID" Type="short" />
	</Table>
	<Table Name="ItemLevelSelectorQuality" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QualityItemBonusListID" Type="int" />
		<Field Name="Quality" Type="byte" />
	</Table>
	<Table Name="ItemLevelSelectorQualitySet" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IlvlRare" Type="short" />
		<Field Name="IlvlEpic" Type="short" />
	</Table>
	<Table Name="ItemLimitCategory" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Quantity" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="ItemLimitCategoryCondition" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AddQuantity" Type="byte" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="ItemModifiedAppearance" Build="26654">
		<Field Name="ItemID" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemAppearanceModifierID" Type="byte" />
		<Field Name="ItemAppearanceID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="TransmogSourceTypeEnum" Type="byte" />
	</Table>
	<Table Name="ItemModifiedAppearanceExtra" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="UnequippedIconFileDataID" Type="int" />
		<Field Name="SheatheType" Type="byte" />
		<Field Name="DisplayWeaponSubclassID" Type="byte" />
		<Field Name="DisplayInventoryType" Type="byte" />
	</Table>
	<Table Name="ItemNameDescription" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Color" Type="int" />
	</Table>
	<Table Name="ItemPetFood" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="ItemPriceBase" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Armor" Type="float" />
		<Field Name="Weapon" Type="float" />
		<Field Name="ItemLevel" Type="short" />
	</Table>
	<Table Name="ItemRandomProperties" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Enchantment" Type="short" ArraySize="5" />
	</Table>
	<Table Name="ItemRandomSuffix" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Enchantment" Type="short" ArraySize="5" />
		<Field Name="AllocationPct" Type="short" ArraySize="5" />
	</Table>
	<Table Name="ItemRangedDisplayInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MissileSpellVisualEffectNameID" Type="int" />
		<Field Name="QuiverFileDataID" Type="int" />
		<Field Name="CastSpellVisualID" Type="int" />
		<Field Name="AutoAttackSpellVisualID" Type="int" />
	</Table>
	<Table Name="ItemSearchName" Build="26654">
		<Field Name="AllowableRace" Type="long" />
		<Field Name="Display_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" ArraySize="3" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="OverallQualityID" Type="byte" />
		<Field Name="ExpansionID" Type="byte" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="MinFactionID" Type="short" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="AllowableClass" Type="int" />
		<Field Name="RequiredSkill" Type="short" />
		<Field Name="RequiredSkillRank" Type="short" />
		<Field Name="RequiredAbility" Type="int" />
	</Table>
	<Table Name="ItemSet" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ItemID" Type="int" ArraySize="17" />
		<Field Name="RequiredSkillRank" Type="short" />
		<Field Name="RequiredSkill" Type="int" />
		<Field Name="SetFlags" Type="int" />
	</Table>
	<Table Name="ItemSetSpell" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="ChrSpecID" Type="short" />
		<Field Name="Threshold" Type="byte" />
	</Table>
	<Table Name="ItemSparse" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AllowableRace" Type="long" />
		<Field Name="Display_Lang" Type="string" />
		<Field Name="Display1_Lang" Type="string" />
		<Field Name="Display2_Lang" Type="string" />
		<Field Name="Display3_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Flags" Type="int" ArraySize="4" />
		<Field Name="PriceRandomValue" Type="float" />
		<Field Name="PriceVariance" Type="float" />
		<Field Name="VendorStackCount" Type="int" />
		<Field Name="BuyPrice" Type="int" />
		<Field Name="SellPrice" Type="int" />
		<Field Name="RequiredAbility" Type="int" />
		<Field Name="MaxCount" Type="int" />
		<Field Name="Stackable" Type="int" />
		<Field Name="StatPercentEditor" Type="int" ArraySize="10" />
		<Field Name="StatPercentageOfSocket" Type="float" ArraySize="10" />
		<Field Name="ItemRange" Type="float" />
		<Field Name="BagFamily" Type="int" />
		<Field Name="QualityModifier" Type="float" />
		<Field Name="DurationInInventory" Type="int" />
		<Field Name="DmgVariance" Type="float" />
		<Field Name="AllowableClass" Type="short" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="RequiredSkill" Type="short" />
		<Field Name="RequiredSkillRank" Type="short" />
		<Field Name="MinFactionID" Type="short" />
		<Field Name="Field_29" Type="short" ArraySize="10" />
		<Field Name="ScalingStatDistributionID" Type="short" />
		<Field Name="ItemDelay" Type="short" />
		<Field Name="PageID" Type="short" />
		<Field Name="StartQuestID" Type="short" />
		<Field Name="LockID" Type="short" />
		<Field Name="RandomSelect" Type="short" />
		<Field Name="ItemRandomSuffixGroupID" Type="short" />
		<Field Name="ItemSet" Type="short" />
		<Field Name="ZoneBound" Type="short" />
		<Field Name="InstanceBound" Type="short" />
		<Field Name="TotemCategoryID" Type="short" />
		<Field Name="Socket_Match_Enchantment_ID" Type="short" />
		<Field Name="Gem_Properties" Type="short" />
		<Field Name="LimitCategory" Type="short" />
		<Field Name="RequiredHoliday" Type="short" />
		<Field Name="RequiredTransmogHoliday" Type="short" />
		<Field Name="ItemNameDescriptionID" Type="short" />
		<Field Name="OverallQualityID" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="RequiredLevel" Type="byte" />
		<Field Name="RequiredPVPRank" Type="byte" />
		<Field Name="RequiredPVPMedal" Type="byte" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="ContainerSlots" Type="byte" />
		<Field Name="StatModifier_BonusStat" Type="byte" ArraySize="10" />
		<Field Name="Damage_DamageType" Type="byte" />
		<Field Name="Bonding" Type="byte" />
		<Field Name="LanguageID" Type="byte" />
		<Field Name="PageMaterialID" Type="byte" />
		<Field Name="Material" Type="byte" />
		<Field Name="SheatheType" Type="byte" />
		<Field Name="SocketType" Type="byte" ArraySize="3" />
		<Field Name="SpellWeightCategory" Type="byte" />
		<Field Name="SpellWeight" Type="byte" />
		<Field Name="ArtifactID" Type="byte" />
		<Field Name="ExpansionID" Type="byte" />
	</Table>
	<Table Name="ItemSpec" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpecializationID" Type="short" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
		<Field Name="ItemType" Type="byte" />
		<Field Name="PrimaryStat" Type="byte" />
		<Field Name="SecondaryStat" Type="byte" />
	</Table>
	<Table Name="ItemSpecOverride" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpecID" Type="short" />
	</Table>
	<Table Name="ItemSubClass" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayName_Lang" Type="string" />
		<Field Name="VerboseName_Lang" Type="string" />
		<Field Name="Flags" Type="short" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="SubClassID" Type="byte" />
		<Field Name="PrerequisiteProficiency" Type="byte" />
		<Field Name="PostrequisiteProficiency" Type="byte" />
		<Field Name="DisplayFlags" Type="byte" />
		<Field Name="WeaponSwingSize" Type="byte" />
		<Field Name="AuctionHouseSortOrder" Type="byte" />
	</Table>
	<Table Name="ItemSubClassMask" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Mask" Type="int" />
		<Field Name="ClassID" Type="byte" />
	</Table>
	<Table Name="ItemUpgrade" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CurrencyAmount" Type="int" />
		<Field Name="PrerequisiteID" Type="short" />
		<Field Name="CurrencyType" Type="short" />
		<Field Name="ItemUpgradePathID" Type="byte" />
		<Field Name="ItemLevelIncrement" Type="byte" />
	</Table>
	<Table Name="ItemVisuals" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelFileID" Type="int" ArraySize="5" />
	</Table>
	<Table Name="ItemXBonusTree" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemBonusTreeID" Type="short" />
	</Table>
	<Table Name="JournalEncounter" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Map" Type="float" ArraySize="2" />
		<Field Name="DungeonMapID" Type="short" />
		<Field Name="WorldMapAreaID" Type="short" />
		<Field Name="FirstSectionID" Type="short" />
		<Field Name="JournalInstanceID" Type="short" />
		<Field Name="DifficultyMask" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="OrderIndex" Type="int" />
		<Field Name="MapDisplayConditionID" Type="int" />
	</Table>
	<Table Name="JournalEncounterCreature" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="CreatureDisplayInfoID" Type="int" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="UiModelSceneID" Type="int" />
		<Field Name="JournalEncounterID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="JournalEncounterItem" Build="26654">
		<Field Name="ItemID" Type="int" />
		<Field Name="JournalEncounterID" Type="short" />
		<Field Name="DifficultyMask" Type="byte" />
		<Field Name="FactionMask" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="JournalEncounterSection" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Title_Lang" Type="string" />
		<Field Name="BodyText_Lang" Type="string" />
		<Field Name="IconCreatureDisplayInfoID" Type="int" />
		<Field Name="UiModelSceneID" Type="int" />
		<Field Name="SpellID" Type="int" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="JournalEncounterID" Type="short" />
		<Field Name="NextSiblingSectionID" Type="short" />
		<Field Name="FirstChildSectionID" Type="short" />
		<Field Name="ParentSectionID" Type="short" />
		<Field Name="Flags" Type="short" />
		<Field Name="IconFlags" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="DifficultyMask" Type="byte" />
	</Table>
	<Table Name="JournalEncounterXDifficulty" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyID" Type="byte" />
	</Table>
	<Table Name="JournalEncounterXMapLoc" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Map" Type="float" ArraySize="2" />
		<Field Name="Flags" Type="byte" />
		<Field Name="JournalEncounterID" Type="int" />
		<Field Name="DungeonMapID" Type="int" />
		<Field Name="MapDisplayConditionID" Type="int" />
	</Table>
	<Table Name="JournalInstance" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="ButtonFiledataID" Type="int" />
		<Field Name="ButtonSmallFileDataID" Type="int" />
		<Field Name="BackgroundFiledataID" Type="int" />
		<Field Name="LoreFileDataID" Type="int" />
		<Field Name="MapID" Type="short" />
		<Field Name="AreaID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="JournalItemXDifficulty" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyID" Type="byte" />
	</Table>
	<Table Name="JournalSectionXDifficulty" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyID" Type="byte" />
	</Table>
	<Table Name="JournalTier" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="JournalTierXInstance" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="JournalTierID" Type="short" />
		<Field Name="JournalInstanceID" Type="short" />
	</Table>
	<Table Name="KeystoneAffix" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="FiledataID" Type="int" />
	</Table>
	<Table Name="Languages" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="LanguageWords" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Word" Type="string" />
		<Field Name="LanguageID" Type="byte" />
	</Table>
	<Table Name="LfgDungeonsGroupingMap" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Random_LfgDungeonsID" Type="short" />
		<Field Name="Group_ID" Type="byte" />
	</Table>
	<Table Name="Light" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GameCoords" Type="float" ArraySize="3" />
		<Field Name="GameFalloffStart" Type="float" />
		<Field Name="GameFalloffEnd" Type="float" />
		<Field Name="ContinentID" Type="short" />
		<Field Name="LightParamsID" Type="short" ArraySize="8" />
	</Table>
	<Table Name="LightData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DirectColor" Type="int" />
		<Field Name="AmbientColor" Type="int" />
		<Field Name="SkyTopColor" Type="int" />
		<Field Name="SkyMiddleColor" Type="int" />
		<Field Name="SkyBand1Color" Type="int" />
		<Field Name="SkyBand2Color" Type="int" />
		<Field Name="SkySmogColor" Type="int" />
		<Field Name="SkyFogColor" Type="int" />
		<Field Name="SunColor" Type="int" />
		<Field Name="CloudSunColor" Type="int" />
		<Field Name="CloudEmissiveColor" Type="int" />
		<Field Name="CloudLayer1AmbientColor" Type="int" />
		<Field Name="CloudLayer2AmbientColor" Type="int" />
		<Field Name="OceanCloseColor" Type="int" />
		<Field Name="OceanFarColor" Type="int" />
		<Field Name="RiverCloseColor" Type="int" />
		<Field Name="RiverFarColor" Type="int" />
		<Field Name="ShadowOpacity" Type="int" />
		<Field Name="FogEnd" Type="float" />
		<Field Name="FogScaler" Type="float" />
		<Field Name="CloudDensity" Type="float" />
		<Field Name="FogDensity" Type="float" />
		<Field Name="FogHeight" Type="float" />
		<Field Name="FogHeightScaler" Type="float" />
		<Field Name="FogHeightDensity" Type="float" />
		<Field Name="SunFogAngle" Type="float" />
		<Field Name="EndFogColorDistance" Type="float" />
		<Field Name="SunFogColor" Type="int" />
		<Field Name="EndFogColor" Type="int" />
		<Field Name="FogHeightColor" Type="int" />
		<Field Name="ColorGradingFileDataID" Type="int" />
		<Field Name="HorizonAmbientColor" Type="int" />
		<Field Name="GroundAmbientColor" Type="int" />
		<Field Name="Time" Type="short" />
	</Table>
	<Table Name="LightParams" Build="26654">
		<Field Name="Glow" Type="float" />
		<Field Name="WaterShallowAlpha" Type="float" />
		<Field Name="WaterDeepAlpha" Type="float" />
		<Field Name="OceanShallowAlpha" Type="float" />
		<Field Name="OceanDeepAlpha" Type="float" />
		<Field Name="OverrideCelestialSphere" Type="float" ArraySize="3" />
		<Field Name="LightSkyboxID" Type="short" />
		<Field Name="HighlightSky" Type="byte" />
		<Field Name="CloudTypeID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="LightSkybox" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="CelestialSkyboxFileDataID" Type="int" />
		<Field Name="SkyboxFileDataID" Type="int" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="LiquidMaterial" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LVF" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="LiquidObject" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FlowDirection" Type="float" />
		<Field Name="FlowSpeed" Type="float" />
		<Field Name="LiquidTypeID" Type="short" />
		<Field Name="Fishable" Type="byte" />
		<Field Name="Reflection" Type="byte" />
	</Table>
	<Table Name="LiquidType" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Texture" Type="string" ArraySize="6" />
		<Field Name="SpellID" Type="int" />
		<Field Name="MaxDarkenDepth" Type="float" />
		<Field Name="FogDarkenIntensity" Type="float" />
		<Field Name="AmbDarkenIntensity" Type="float" />
		<Field Name="DirDarkenIntensity" Type="float" />
		<Field Name="ParticleScale" Type="float" />
		<Field Name="Color" Type="int" ArraySize="2" />
		<Field Name="Float" Type="float" ArraySize="18" />
		<Field Name="Int" Type="int" ArraySize="4" />
		<Field Name="Flags" Type="short" />
		<Field Name="LightID" Type="short" />
		<Field Name="SoundBank" Type="byte" />
		<Field Name="ParticleMovement" Type="byte" />
		<Field Name="ParticleTexSlots" Type="byte" />
		<Field Name="MaterialID" Type="byte" />
		<Field Name="FrameCountTexture" Type="byte" ArraySize="6" />
		<Field Name="SoundID" Type="int" />
	</Table>
	<Table Name="LoadingScreens" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="NarrowScreenFileDataID" Type="int" />
		<Field Name="WideScreenFileDataID" Type="int" />
		<Field Name="WideScreen169FileDataID" Type="int" />
	</Table>
	<Table Name="LoadingScreenTaxiSplines" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Locx" Type="float" ArraySize="10" />
		<Field Name="Locy" Type="float" ArraySize="10" />
		<Field Name="PathID" Type="short" />
		<Field Name="LoadingScreenID" Type="short" />
		<Field Name="LegIndex" Type="byte" />
	</Table>
	<Table Name="Locale" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FontFileDataID" Type="int" />
		<Field Name="WowLocale" Type="byte" />
		<Field Name="Secondary" Type="byte" />
		<Field Name="ClientDisplayExpansion" Type="byte" />
	</Table>
	<Table Name="Location" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="Rot" Type="float" ArraySize="3" />
	</Table>
	<Table Name="Lock" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Index" Type="int" ArraySize="8" />
		<Field Name="Skill" Type="short" ArraySize="8" />
		<Field Name="Type" Type="byte" ArraySize="8" />
		<Field Name="Action" Type="byte" ArraySize="8" />
	</Table>
	<Table Name="LockType" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ResourceName_Lang" Type="string" />
		<Field Name="Verb_Lang" Type="string" />
		<Field Name="CursorName" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="LookAtController" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ReactionEnableDistance" Type="float" />
		<Field Name="ReactionGiveupDistance" Type="float" />
		<Field Name="TorsoSpeedFactor" Type="float" />
		<Field Name="HeadSpeedFactor" Type="float" />
		<Field Name="ReactionEnableFOVDeg" Type="short" />
		<Field Name="ReactionGiveupTimeMS" Type="short" />
		<Field Name="ReactionIgnoreTimeMinMS" Type="short" />
		<Field Name="ReactionIgnoreTimeMaxMS" Type="short" />
		<Field Name="MaxTorsoYaw" Type="byte" />
		<Field Name="MaxTorsoYawWhileMoving" Type="byte" />
		<Field Name="MaxHeadYaw" Type="byte" />
		<Field Name="MaxHeadPitch" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ReactionWarmUpTimeMSMin" Type="int" />
		<Field Name="ReactionWarmUpTimeMSMax" Type="int" />
		<Field Name="ReactionGiveupFOVDeg" Type="int" />
		<Field Name="MaxTorsoPitchUp" Type="int" />
		<Field Name="MaxTorsoPitchDown" Type="int" />
	</Table>
	<Table Name="MailTemplate" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Body_Lang" Type="string" />
	</Table>
	<Table Name="ManagedWorldState" Build="26654">
		<Field Name="CurrentStageWorldStateID" Type="int" />
		<Field Name="ProgressWorldStateID" Type="int" />
		<Field Name="UpTimeSecs" Type="int" />
		<Field Name="DownTimeSecs" Type="int" />
		<Field Name="OccurrencesWorldStateID" Type="int" />
		<Field Name="AccumulationStateTargetValue" Type="int" />
		<Field Name="DepletionStateTargetValue" Type="int" />
		<Field Name="AccumulationAmountPerMinute" Type="int" />
		<Field Name="DepletionAmountPerMinute" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ManagedWorldStateBuff" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="OccurrenceValue" Type="int" />
		<Field Name="BuffSpellID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="ManagedWorldStateInput" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ManagedWorldStateID" Type="int" />
		<Field Name="QuestID" Type="int" />
		<Field Name="ValidInputConditionID" Type="int" />
	</Table>
	<Table Name="ManifestInterfaceActionIcon" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ManifestInterfaceData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FilePath" Type="string" />
		<Field Name="FileName" Type="string" />
	</Table>
	<Table Name="ManifestInterfaceItemIcon" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ManifestInterfaceTOCData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FilePath" Type="string" />
	</Table>
	<Table Name="ManifestMP3" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Map" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Directory" Type="string" />
		<Field Name="MapName_Lang" Type="string" />
		<Field Name="MapDescription0_Lang" Type="string" />
		<Field Name="MapDescription1_Lang" Type="string" />
		<Field Name="PvpShortDescription_Lang" Type="string" />
		<Field Name="PvpLongDescription_Lang" Type="string" />
		<Field Name="Flags" Type="int" ArraySize="2" />
		<Field Name="MinimapIconScale" Type="float" />
		<Field Name="Corpse" Type="float" ArraySize="2" />
		<Field Name="AreaTableID" Type="short" />
		<Field Name="LoadingScreenID" Type="short" />
		<Field Name="CorpseMapID" Type="short" />
		<Field Name="TimeOfDayOverride" Type="short" />
		<Field Name="ParentMapID" Type="short" />
		<Field Name="CosmeticParentMapID" Type="short" />
		<Field Name="WindSettingsID" Type="short" />
		<Field Name="InstanceType" Type="byte" />
		<Field Name="MapType" Type="byte" />
		<Field Name="ExpansionID" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="TimeOffset" Type="byte" />
	</Table>
	<Table Name="MapCelestialBody" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CelestialBodyID" Type="short" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="MapChallengeMode" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapID" Type="short" />
		<Field Name="CriteriaCount" Type="short" ArraySize="3" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="MapDifficulty" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Message_Lang" Type="string" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="ResetInterval" Type="byte" />
		<Field Name="MaxPlayers" Type="byte" />
		<Field Name="LockID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ItemContext" Type="byte" />
		<Field Name="ItemContextPickerID" Type="int" />
	</Table>
	<Table Name="MapDifficultyXCondition" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FailureDescription_Lang" Type="string" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="MapLoadingScreen" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Min" Type="float" ArraySize="2" />
		<Field Name="Max" Type="float" ArraySize="2" />
		<Field Name="LoadingScreenID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="MarketingPromotionsXLocale" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AcceptURL" Type="string" />
		<Field Name="AdTexture" Type="int" />
		<Field Name="LogoTexture" Type="int" />
		<Field Name="AcceptButtonTexture" Type="int" />
		<Field Name="DeclineButtonTexture" Type="int" />
		<Field Name="PromotionID" Type="byte" />
		<Field Name="LocaleID" Type="byte" />
	</Table>
	<Table Name="Material" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="FoleySoundID" Type="int" />
		<Field Name="SheatheSoundID" Type="int" />
		<Field Name="UnsheatheSoundID" Type="int" />
	</Table>
	<Table Name="MinorTalent" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="MissileTargeting" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TurnLingering" Type="float" />
		<Field Name="PitchLingering" Type="float" />
		<Field Name="MouseLingering" Type="float" />
		<Field Name="EndOpacity" Type="float" />
		<Field Name="ArcSpeed" Type="float" />
		<Field Name="ArcRepeat" Type="float" />
		<Field Name="ArcWidth" Type="float" />
		<Field Name="ImpactRadius" Type="float" ArraySize="2" />
		<Field Name="ImpactTexRadius" Type="float" />
		<Field Name="ArcTextureFileID" Type="int" />
		<Field Name="ImpactTextureFileID" Type="int" />
		<Field Name="ImpactModelFileID" Type="int" ArraySize="2" />
	</Table>
	<Table Name="ModelAnimCloakDampening" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AnimationDataID" Type="int" />
		<Field Name="CloakDampeningID" Type="int" />
	</Table>
	<Table Name="ModelFileData" Build="26654">
		<Field Name="LodCount" Type="byte" />
		<Field Name="FileDataID" Type="int" IsIndex="true" />
		<Field Name="ModelResourcesID" Type="int" />
	</Table>
	<Table Name="ModelRibbonQuality" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RibbonQualityID" Type="byte" />
	</Table>
	<Table Name="ModifierTree" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Asset" Type="int" />
		<Field Name="SecondaryAsset" Type="int" />
		<Field Name="Parent" Type="int" />
		<Field Name="Type" Type="byte" />
		<Field Name="TertiaryAsset" Type="byte" />
		<Field Name="Operator" Type="byte" />
		<Field Name="Amount" Type="byte" />
	</Table>
	<Table Name="Mount" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="SourceText_Lang" Type="string" />
		<Field Name="SourceSpellID" Type="int" />
		<Field Name="MountFlyRideHeight" Type="float" />
		<Field Name="MountTypeID" Type="short" />
		<Field Name="Flags" Type="short" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="UiModelSceneID" Type="int" />
	</Table>
	<Table Name="MountCapability" Build="26654">
		<Field Name="ReqSpellKnownID" Type="int" />
		<Field Name="ModSpellAuraID" Type="int" />
		<Field Name="ReqRidingSkill" Type="short" />
		<Field Name="ReqAreaID" Type="short" />
		<Field Name="ReqMapID" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ReqSpellAuraID" Type="int" />
	</Table>
	<Table Name="MountTypeXCapability" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MountTypeID" Type="short" />
		<Field Name="MountCapabilityID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="MountXDisplay" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CreatureDisplayInfoID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="Movie" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AudioFileDataID" Type="int" />
		<Field Name="SubtitleFileDataID" Type="int" />
		<Field Name="Volume" Type="byte" />
		<Field Name="KeyID" Type="byte" />
	</Table>
	<Table Name="MovieFileData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Resolution" Type="short" />
	</Table>
	<Table Name="MovieVariation" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="OverlayFileDataID" Type="int" />
	</Table>
	<Table Name="NameGen" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="Sex" Type="byte" />
	</Table>
	<Table Name="NamesProfanity" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Language" Type="byte" />
	</Table>
	<Table Name="NamesReserved" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="NamesReservedLocale" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="LocaleMask" Type="byte" />
	</Table>
	<Table Name="NPCModelItemSlotDisplayInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemDisplayInfoID" Type="int" />
		<Field Name="ItemSlot" Type="byte" />
	</Table>
	<Table Name="NPCSounds" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundID" Type="int" ArraySize="4" />
	</Table>
	<Table Name="ObjectEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Offset" Type="float" ArraySize="3" />
		<Field Name="ObjectEffectGroupID" Type="short" />
		<Field Name="TriggerType" Type="byte" />
		<Field Name="EventType" Type="byte" />
		<Field Name="EffectRecType" Type="byte" />
		<Field Name="Attachment" Type="byte" />
		<Field Name="EffectRecID" Type="int" />
		<Field Name="ObjectEffectModifierID" Type="int" />
	</Table>
	<Table Name="ObjectEffectModifier" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Param" Type="float" ArraySize="4" />
		<Field Name="InputType" Type="byte" />
		<Field Name="MapType" Type="byte" />
		<Field Name="OutputType" Type="byte" />
	</Table>
	<Table Name="ObjectEffectPackageElem" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ObjectEffectPackageID" Type="short" />
		<Field Name="ObjectEffectGroupID" Type="short" />
		<Field Name="StateType" Type="short" />
	</Table>
	<Table Name="OutlineEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Range" Type="float" />
		<Field Name="UnitConditionID" Type="int" />
		<Field Name="PassiveHighlightColorID" Type="int" />
		<Field Name="HighlightColorID" Type="int" />
		<Field Name="Priority" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="OverrideSpellData" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Spells" Type="int" ArraySize="10" />
		<Field Name="PlayerActionbarFileDataID" Type="int" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="PageTextMaterial" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="PaperDollItemFrame" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemButtonName" Type="string" />
		<Field Name="SlotNumber" Type="byte" />
		<Field Name="SlotIconFileID" Type="int" />
	</Table>
	<Table Name="ParagonReputation" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LevelThreshold" Type="int" />
		<Field Name="QuestID" Type="int" />
		<Field Name="FactionID" Type="int" />
	</Table>
	<Table Name="ParticleColor" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Start" Type="int" ArraySize="3" />
		<Field Name="MID" Type="int" ArraySize="3" />
		<Field Name="End" Type="int" ArraySize="3" />
	</Table>
	<Table Name="Path" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Type" Type="byte" />
		<Field Name="SplineType" Type="byte" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
		<Field Name="Alpha" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="PathNode" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LocationID" Type="int" />
		<Field Name="PathID" Type="short" />
		<Field Name="Sequence" Type="short" />
	</Table>
	<Table Name="PathNodeProperty" Build="26654">
		<Field Name="PathID" Type="short" />
		<Field Name="Sequence" Type="short" />
		<Field Name="PropertyIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="int" />
	</Table>
	<Table Name="PathProperty" Build="26654">
		<Field Name="Value" Type="int" />
		<Field Name="PathID" Type="short" />
		<Field Name="PropertyIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Phase" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="short" />
	</Table>
	<Table Name="PhaseShiftZoneSounds" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AreaID" Type="short" />
		<Field Name="PhaseID" Type="short" />
		<Field Name="PhaseGroupID" Type="short" />
		<Field Name="SoundAmbienceID" Type="short" />
		<Field Name="UWSoundAmbienceID" Type="short" />
		<Field Name="WMOAreaID" Type="byte" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="SoundProviderPreferencesID" Type="byte" />
		<Field Name="UWSoundProviderPreferencesID" Type="byte" />
		<Field Name="ZoneIntroMusicID" Type="int" />
		<Field Name="ZoneMusicID" Type="int" />
		<Field Name="UWZoneIntroMusicID" Type="int" />
		<Field Name="UWZoneMusicID" Type="int" />
	</Table>
	<Table Name="PhaseXPhaseGroup" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PhaseID" Type="short" />
	</Table>
	<Table Name="PlayerCondition" Build="26654">
		<Field Name="RaceMask" Type="long" />
		<Field Name="Failure_Description_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MinLevel" Type="short" />
		<Field Name="MaxLevel" Type="short" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="Gender" Type="byte" />
		<Field Name="NativeGender" Type="byte" />
		<Field Name="SkillLogic" Type="int" />
		<Field Name="LanguageID" Type="byte" />
		<Field Name="MinLanguage" Type="byte" />
		<Field Name="MaxLanguage" Type="int" />
		<Field Name="MaxFactionID" Type="short" />
		<Field Name="MaxReputation" Type="byte" />
		<Field Name="ReputationLogic" Type="int" />
		<Field Name="CurrentPvpFaction" Type="byte" />
		<Field Name="MinPVPRank" Type="byte" />
		<Field Name="MaxPVPRank" Type="byte" />
		<Field Name="PvpMedal" Type="byte" />
		<Field Name="PrevQuestLogic" Type="int" />
		<Field Name="CurrQuestLogic" Type="int" />
		<Field Name="CurrentCompletedQuestLogic" Type="int" />
		<Field Name="SpellLogic" Type="int" />
		<Field Name="ItemLogic" Type="int" />
		<Field Name="ItemFlags" Type="byte" />
		<Field Name="AuraSpellLogic" Type="int" />
		<Field Name="WorldStateExpressionID" Type="short" />
		<Field Name="WeatherID" Type="byte" />
		<Field Name="PartyStatus" Type="byte" />
		<Field Name="LifetimeMaxPVPRank" Type="byte" />
		<Field Name="AchievementLogic" Type="int" />
		<Field Name="LfgLogic" Type="int" />
		<Field Name="AreaLogic" Type="int" />
		<Field Name="CurrencyLogic" Type="int" />
		<Field Name="QuestKillID" Type="short" />
		<Field Name="QuestKillLogic" Type="int" />
		<Field Name="MinExpansionLevel" Type="byte" />
		<Field Name="MaxExpansionLevel" Type="byte" />
		<Field Name="MinExpansionTier" Type="byte" />
		<Field Name="MaxExpansionTier" Type="byte" />
		<Field Name="MinGuildLevel" Type="byte" />
		<Field Name="MaxGuildLevel" Type="byte" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="PhaseID" Type="short" />
		<Field Name="PhaseGroupID" Type="int" />
		<Field Name="MinAvgItemLevel" Type="int" />
		<Field Name="MaxAvgItemLevel" Type="int" />
		<Field Name="MinAvgEquippedItemLevel" Type="short" />
		<Field Name="MaxAvgEquippedItemLevel" Type="short" />
		<Field Name="ChrSpecializationIndex" Type="byte" />
		<Field Name="ChrSpecializationRole" Type="byte" />
		<Field Name="PowerType" Type="byte" />
		<Field Name="PowerTypeComp" Type="byte" />
		<Field Name="PowerTypeValue" Type="byte" />
		<Field Name="ModifierTreeID" Type="int" />
		<Field Name="WeaponSubclassMask" Type="int" />
		<Field Name="SkillID" Type="short" ArraySize="4" />
		<Field Name="MinSkill" Type="short" ArraySize="4" />
		<Field Name="MaxSkill" Type="short" ArraySize="4" />
		<Field Name="MinFactionID" Type="int" ArraySize="3" />
		<Field Name="MinReputation" Type="byte" ArraySize="3" />
		<Field Name="PrevQuestID" Type="short" ArraySize="4" />
		<Field Name="CurrQuestID" Type="short" ArraySize="4" />
		<Field Name="CurrentCompletedQuestID" Type="short" ArraySize="4" />
		<Field Name="SpellID" Type="int" ArraySize="4" />
		<Field Name="ItemID" Type="int" ArraySize="4" />
		<Field Name="ItemCount" Type="int" ArraySize="4" />
		<Field Name="Explored" Type="short" ArraySize="2" />
		<Field Name="Time" Type="int" ArraySize="2" />
		<Field Name="AuraSpellID" Type="int" ArraySize="4" />
		<Field Name="AuraStacks" Type="byte" ArraySize="4" />
		<Field Name="Achievement" Type="short" ArraySize="4" />
		<Field Name="LfgStatus" Type="byte" ArraySize="4" />
		<Field Name="LfgCompare" Type="byte" ArraySize="4" />
		<Field Name="LfgValue" Type="int" ArraySize="4" />
		<Field Name="AreaID" Type="short" ArraySize="4" />
		<Field Name="CurrencyID" Type="int" ArraySize="4" />
		<Field Name="CurrencyCount" Type="int" ArraySize="4" />
		<Field Name="QuestKillMonster" Type="int" ArraySize="6" />
		<Field Name="MovementFlags" Type="int" ArraySize="2" />
	</Table>
	<Table Name="Positioner" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StartLife" Type="float" />
		<Field Name="FirstStateID" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="StartLifePercent" Type="byte" />
	</Table>
	<Table Name="PositionerState" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EndLife" Type="float" />
		<Field Name="EndLifePercent" Type="byte" />
		<Field Name="NextStateID" Type="int" />
		<Field Name="TransformMatrixID" Type="int" />
		<Field Name="PosEntryID" Type="int" />
		<Field Name="RotEntryID" Type="int" />
		<Field Name="ScaleEntryID" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="PositionerStateEntry" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ParamA" Type="float" />
		<Field Name="ParamB" Type="float" />
		<Field Name="SrcValType" Type="short" />
		<Field Name="SrcVal" Type="short" />
		<Field Name="DstValType" Type="short" />
		<Field Name="DstVal" Type="short" />
		<Field Name="EntryType" Type="byte" />
		<Field Name="Style" Type="byte" />
		<Field Name="SrcType" Type="byte" />
		<Field Name="DstType" Type="byte" />
		<Field Name="CurveID" Type="int" />
	</Table>
	<Table Name="PowerDisplay" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="GlobalStringBaseTag" Type="string" />
		<Field Name="ActualType" Type="byte" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
	</Table>
	<Table Name="PowerType" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="NameGlobalStringTag" Type="string" />
		<Field Name="CostGlobalStringTag" Type="string" />
		<Field Name="RegenPeace" Type="float" />
		<Field Name="RegenCombat" Type="float" />
		<Field Name="MaxBasePower" Type="short" />
		<Field Name="RegenInterruptTimeMS" Type="short" />
		<Field Name="Flags" Type="short" />
		<Field Name="PowerTypeEnum" Type="byte" />
		<Field Name="MinPower" Type="byte" />
		<Field Name="CenterPower" Type="byte" />
		<Field Name="DefaultPower" Type="byte" />
		<Field Name="DisplayModifier" Type="byte" />
	</Table>
	<Table Name="PrestigeLevelInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="BadgeTextureFileDataID" Type="int" />
		<Field Name="PrestigeLevel" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="PVPBracketTypes" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BracketID" Type="byte" />
		<Field Name="WeeklyQuestID" Type="int" ArraySize="4" />
	</Table>
	<Table Name="PvpReward" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="HonorLevel" Type="int" />
		<Field Name="PrestigeLevel" Type="int" />
		<Field Name="RewardPackID" Type="int" />
	</Table>
	<Table Name="PvpScalingEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="float" />
		<Field Name="PvpScalingEffectTypeID" Type="int" />
		<Field Name="SpecializationID" Type="int" />
	</Table>
	<Table Name="PvpScalingEffectType" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="PvpTalent" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="SpellID" Type="int" />
		<Field Name="OverridesSpellID" Type="int" />
		<Field Name="ActionBarSpellID" Type="int" />
		<Field Name="Field_6" Type="int" />
		<Field Name="Field_7" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="ClassID" Type="int" />
		<Field Name="SpecID" Type="int" />
		<Field Name="Field_11" Type="int" />
	</Table>
	<Table Name="PvpTalentUnlock" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_2" Type="int" />
		<Field Name="Field_3" Type="int" />
		<Field Name="Field_4" Type="int" />
	</Table>
	<Table Name="QuestFactionReward" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="short" ArraySize="10" />
	</Table>
	<Table Name="QuestFeedbackEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="MinimapAtlasMemberID" Type="short" />
		<Field Name="AttachPoint" Type="byte" />
		<Field Name="PassiveHighlightColorType" Type="byte" />
		<Field Name="Priority" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="QuestInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="InfoName_Lang" Type="string" />
		<Field Name="Profession" Type="short" />
		<Field Name="Type" Type="byte" />
		<Field Name="Modifiers" Type="byte" />
	</Table>
	<Table Name="QuestLine" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="QuestLineXQuest" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QuestLineID" Type="short" />
		<Field Name="QuestID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="QuestMoneyReward" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="int" ArraySize="10" />
	</Table>
	<Table Name="QuestObjective" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Amount" Type="int" />
		<Field Name="ObjectID" Type="int" />
		<Field Name="Type" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="StorageIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="QuestPackageItem" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
		<Field Name="PackageID" Type="short" />
		<Field Name="DisplayType" Type="byte" />
		<Field Name="ItemQuantity" Type="int" />
	</Table>
	<Table Name="QuestPOIBlob" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapID" Type="short" />
		<Field Name="WorldMapAreaID" Type="short" />
		<Field Name="NumPoints" Type="byte" />
		<Field Name="Floor" Type="byte" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="QuestID" Type="int" />
		<Field Name="ObjectiveIndex" Type="int" />
	</Table>
	<Table Name="QuestPOIPoint" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="X" Type="short" />
		<Field Name="Y" Type="short" />
	</Table>
	<Table Name="QuestSort" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SortName_Lang" Type="string" />
		<Field Name="UiOrderIndex" Type="byte" />
	</Table>
	<Table Name="QuestV2" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UniqueBitFlag" Type="short" />
	</Table>
	<Table Name="QuestV2CliTask" Build="26654">
		<Field Name="FiltRaces" Type="long" />
		<Field Name="QuestTitle_Lang" Type="string" />
		<Field Name="BulletText_Lang" Type="string" />
		<Field Name="StartItem" Type="int" />
		<Field Name="UniqueBitFlag" Type="short" />
		<Field Name="ConditionID" Type="short" />
		<Field Name="FiltClasses" Type="short" />
		<Field Name="FiltCompletedQuest" Type="short" ArraySize="3" />
		<Field Name="FiltMinSkillID" Type="short" />
		<Field Name="WorldStateExpressionID" Type="short" />
		<Field Name="FiltActiveQuest" Type="byte" />
		<Field Name="FiltCompletedQuestLogic" Type="byte" />
		<Field Name="FiltMaxFactionID" Type="byte" />
		<Field Name="FiltMaxFactionValue" Type="byte" />
		<Field Name="FiltMaxLevel" Type="byte" />
		<Field Name="FiltMinFactionID" Type="byte" />
		<Field Name="FiltMinFactionValue" Type="byte" />
		<Field Name="FiltMinLevel" Type="byte" />
		<Field Name="FiltMinSkillValue" Type="byte" />
		<Field Name="FiltNonActiveQuest" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BreadCrumbID" Type="int" />
		<Field Name="QuestInfoID" Type="int" />
		<Field Name="SandboxScalingID" Type="int" />
	</Table>
	<Table Name="QuestXGroupActivity" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="QuestID" Type="int" />
		<Field Name="GroupFinderActivityID" Type="int" />
	</Table>
	<Table Name="QuestXP" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Difficulty" Type="short" ArraySize="10" />
	</Table>
	<Table Name="RandPropPoints" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Epic" Type="int" ArraySize="5" />
		<Field Name="Superior" Type="int" ArraySize="5" />
		<Field Name="Good" Type="int" ArraySize="5" />
	</Table>
	<Table Name="RelicSlotTierRequirement" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="RelicIndex" Type="byte" />
		<Field Name="RelicTier" Type="byte" />
	</Table>
	<Table Name="RelicTalent" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ArtifactPowerID" Type="short" />
		<Field Name="ArtifactPowerLabel" Type="byte" />
		<Field Name="Type" Type="int" />
		<Field Name="PVal" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="ResearchBranch" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ItemID" Type="int" />
		<Field Name="CurrencyID" Type="short" />
		<Field Name="ResearchFieldID" Type="byte" />
		<Field Name="TextureFileID" Type="int" />
		<Field Name="BigTextureFileID" Type="int" />
	</Table>
	<Table Name="ResearchField" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Slot" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="ResearchProject" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="SpellID" Type="int" />
		<Field Name="ResearchBranchID" Type="short" />
		<Field Name="Rarity" Type="byte" />
		<Field Name="NumSockets" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TextureFileID" Type="int" />
		<Field Name="RequiredWeight" Type="int" />
	</Table>
	<Table Name="ResearchSite" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="QuestPOIBlobID" Type="int" />
		<Field Name="MapID" Type="short" />
		<Field Name="AreaPOIIconEnum" Type="int" />
	</Table>
	<Table Name="Resistances" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="FizzleSoundID" Type="int" />
	</Table>
	<Table Name="RewardPack" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Money" Type="int" />
		<Field Name="ArtifactXPMultiplier" Type="float" />
		<Field Name="ArtifactXPDifficulty" Type="byte" />
		<Field Name="ArtifactXPCategoryID" Type="byte" />
		<Field Name="CharTitleID" Type="int" />
		<Field Name="TreasurePickerID" Type="int" />
	</Table>
	<Table Name="RewardPackXCurrencyType" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CurrencyTypeID" Type="int" />
		<Field Name="Quantity" Type="int" />
	</Table>
	<Table Name="RewardPackXItem" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
		<Field Name="ItemQuantity" Type="int" />
	</Table>
	<Table Name="RibbonQuality" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MaxSampleTimeDelta" Type="float" />
		<Field Name="AngleThreshold" Type="float" />
		<Field Name="MinDistancePerSlice" Type="float" />
		<Field Name="NumStrips" Type="byte" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="RulesetItemUpgrade" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemID" Type="int" />
		<Field Name="ItemUpgradeID" Type="short" />
	</Table>
	<Table Name="SandboxScaling" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MinLevel" Type="int" />
		<Field Name="MaxLevel" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="ScalingStatDistribution" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerLevelToItemLevelCurveID" Type="short" />
		<Field Name="Minlevel" Type="int" />
		<Field Name="Maxlevel" Type="int" />
	</Table>
	<Table Name="Scenario" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="AreaTableID" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Type" Type="byte" />
	</Table>
	<Table Name="ScenarioEventEntry" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TriggerAsset" Type="short" />
		<Field Name="TriggerType" Type="byte" />
	</Table>
	<Table Name="ScenarioStep" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Title_Lang" Type="string" />
		<Field Name="ScenarioID" Type="short" />
		<Field Name="Supersedes" Type="short" />
		<Field Name="RewardQuestID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CriteriatreeID" Type="int" />
		<Field Name="RelatedStep" Type="int" />
	</Table>
	<Table Name="SceneScript" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FirstSceneScriptID" Type="short" />
		<Field Name="NextSceneScriptID" Type="short" />
	</Table>
	<Table Name="SceneScriptGlobalText" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Script" Type="string" />
	</Table>
	<Table Name="SceneScriptPackage" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SceneScriptPackageMember" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SceneScriptPackageID" Type="short" />
		<Field Name="SceneScriptID" Type="short" />
		<Field Name="ChildSceneScriptPackageID" Type="short" />
		<Field Name="OrderIndex" Type="byte" />
	</Table>
	<Table Name="SceneScriptText" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Script" Type="string" />
	</Table>
	<Table Name="ScheduledInterval" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="RepeatType" Type="int" />
		<Field Name="DurationSecs" Type="int" />
		<Field Name="OffsetSecs" Type="int" />
		<Field Name="DateAlignmentType" Type="int" />
	</Table>
	<Table Name="ScheduledWorldState" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ScheduledWorldStateGroupID" Type="int" />
		<Field Name="WorldStateID" Type="int" />
		<Field Name="Value" Type="int" />
		<Field Name="DurationSecs" Type="int" />
		<Field Name="Weight" Type="int" />
		<Field Name="UniqueCategory" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="OrderIndex" Type="int" />
	</Table>
	<Table Name="ScheduledWorldStateGroup" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="ScheduledIntervalID" Type="int" />
		<Field Name="SelectionType" Type="int" />
		<Field Name="SelectionCount" Type="int" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="ScheduledWorldStateXUniqCat" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ScheduledUniqueCategoryID" Type="int" />
	</Table>
	<Table Name="ScreenEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Param" Type="int" ArraySize="4" />
		<Field Name="LightParamsID" Type="short" />
		<Field Name="LightParamsFadeIn" Type="short" />
		<Field Name="LightParamsFadeOut" Type="short" />
		<Field Name="TimeOfDayOverride" Type="short" />
		<Field Name="Effect" Type="byte" />
		<Field Name="LightFlags" Type="byte" />
		<Field Name="EffectMask" Type="byte" />
		<Field Name="FullScreenEffectID" Type="int" />
		<Field Name="SoundAmbienceID" Type="int" />
		<Field Name="ZoneMusicID" Type="int" />
	</Table>
	<Table Name="ScreenLocation" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SDReplacementModel" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SdFileDataID" Type="int" />
	</Table>
	<Table Name="SeamlessSite" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapID" Type="int" />
	</Table>
	<Table Name="ServerMessages" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text_Lang" Type="string" />
	</Table>
	<Table Name="ShadowyEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PrimaryColor" Type="int" />
		<Field Name="SecondaryColor" Type="int" />
		<Field Name="Duration" Type="float" />
		<Field Name="Value" Type="float" />
		<Field Name="FadeInTime" Type="float" />
		<Field Name="FadeOutTime" Type="float" />
		<Field Name="InnerStrength" Type="float" />
		<Field Name="OuterStrength" Type="float" />
		<Field Name="InitialDelay" Type="float" />
		<Field Name="AttachPos" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CurveID" Type="int" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="SkillLine" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayName_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="AlternateVerb_Lang" Type="string" />
		<Field Name="Flags" Type="short" />
		<Field Name="CategoryID" Type="byte" />
		<Field Name="CanLink" Type="byte" />
		<Field Name="SpellIconFileID" Type="int" />
		<Field Name="ParentSkillLineID" Type="int" />
	</Table>
	<Table Name="SkillLineAbility" Build="26654">
		<Field Name="RaceMask" Type="long" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Spell" Type="int" />
		<Field Name="SupercedesSpell" Type="int" />
		<Field Name="SkillLine" Type="short" />
		<Field Name="TrivialSkillLineRankHigh" Type="short" />
		<Field Name="TrivialSkillLineRankLow" Type="short" />
		<Field Name="UniqueBit" Type="short" />
		<Field Name="TradeSkillCategoryID" Type="short" />
		<Field Name="NumSkillUps" Type="byte" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="MinSkillLineRank" Type="short" />
		<Field Name="AcquireMethod" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SkillRaceClassInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="long" />
		<Field Name="SkillID" Type="short" />
		<Field Name="Flags" Type="short" />
		<Field Name="SkillTierID" Type="short" />
		<Field Name="Availability" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="ClassMask" Type="int" />
	</Table>
	<Table Name="SoundAmbience" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="SoundFilterID" Type="int" />
		<Field Name="FlavorSoundFilterID" Type="int" />
		<Field Name="AmbienceID" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SoundAmbienceFlavor" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundEntriesIDDay" Type="int" />
		<Field Name="SoundEntriesIDNight" Type="int" />
	</Table>
	<Table Name="SoundBus" Build="26654">
		<Field Name="DefaultVolume" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="DefaultPlaybackLimit" Type="byte" />
		<Field Name="DefaultPriority" Type="byte" />
		<Field Name="DefaultPriorityPenalty" Type="byte" />
		<Field Name="BusEnumID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="SoundBusOverride" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Volume" Type="float" />
		<Field Name="PlaybackLimit" Type="byte" />
		<Field Name="Priority" Type="byte" />
		<Field Name="PriorityPenalty" Type="byte" />
		<Field Name="SoundBusID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="SoundEmitterPillPoints" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="SoundEmittersID" Type="short" />
	</Table>
	<Table Name="SoundEmitters" Build="26654">
		<Field Name="Name" Type="string" />
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="Direction" Type="float" ArraySize="3" />
		<Field Name="WorldStateExpressionID" Type="short" />
		<Field Name="PhaseID" Type="short" />
		<Field Name="EmitterType" Type="byte" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundEntriesID" Type="int" />
		<Field Name="PhaseGroupID" Type="int" />
	</Table>
	<Table Name="SoundEnvelope" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundKitID" Type="int" />
		<Field Name="CurveID" Type="int" />
		<Field Name="DecayIndex" Type="short" />
		<Field Name="SustainIndex" Type="short" />
		<Field Name="ReleaseIndex" Type="short" />
		<Field Name="EnvelopeType" Type="byte" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="SoundFilter" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SoundFilterElem" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Params" Type="float" ArraySize="9" />
		<Field Name="FilterType" Type="byte" />
	</Table>
	<Table Name="SoundKit" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VolumeFloat" Type="float" />
		<Field Name="MinDistance" Type="float" />
		<Field Name="DistanceCutoff" Type="float" />
		<Field Name="Flags" Type="short" />
		<Field Name="SoundKitAdvancedID" Type="short" />
		<Field Name="SoundType" Type="byte" />
		<Field Name="DialogType" Type="byte" />
		<Field Name="EAXDef" Type="byte" />
		<Field Name="VolumeVariationPlus" Type="float" />
		<Field Name="VolumeVariationMinus" Type="float" />
		<Field Name="PitchVariationPlus" Type="float" />
		<Field Name="PitchVariationMinus" Type="float" />
		<Field Name="PitchAdjust" Type="float" />
		<Field Name="BusOverwriteID" Type="short" />
		<Field Name="MaxInstances" Type="byte" />
	</Table>
	<Table Name="SoundKitAdvanced" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="InnerRadius2D" Type="float" />
		<Field Name="DuckToSFX" Type="float" />
		<Field Name="DuckToMusic" Type="float" />
		<Field Name="InnerRadiusOfInfluence" Type="float" />
		<Field Name="OuterRadiusOfInfluence" Type="float" />
		<Field Name="TimeToDuck" Type="int" />
		<Field Name="TimeToUnduck" Type="int" />
		<Field Name="OuterRadius2D" Type="float" />
		<Field Name="Usage" Type="byte" />
		<Field Name="SoundKitID" Type="int" />
		<Field Name="TimeA" Type="int" />
		<Field Name="TimeB" Type="int" />
		<Field Name="TimeC" Type="int" />
		<Field Name="TimeD" Type="int" />
		<Field Name="RandomOffsetRange" Type="int" />
		<Field Name="TimeIntervalMin" Type="int" />
		<Field Name="TimeIntervalMax" Type="int" />
		<Field Name="DelayMin" Type="int" />
		<Field Name="DelayMax" Type="int" />
		<Field Name="VolumeSliderCategory" Type="byte" />
		<Field Name="DuckToAmbience" Type="float" />
		<Field Name="InsideAngle" Type="float" />
		<Field Name="OutsideAngle" Type="float" />
		<Field Name="OutsideVolume" Type="float" />
		<Field Name="MinRandomPosOffset" Type="byte" />
		<Field Name="MaxRandomPosOffset" Type="short" />
		<Field Name="DuckToDialog" Type="float" />
		<Field Name="DuckToSuppressors" Type="float" />
		<Field Name="MsOffset" Type="int" />
		<Field Name="TimeCooldownMin" Type="int" />
		<Field Name="TimeCooldownMax" Type="int" />
		<Field Name="MaxInstancesBehavior" Type="byte" />
		<Field Name="VolumeControlType" Type="byte" />
		<Field Name="VolumeFadeInTimeMin" Type="int" />
		<Field Name="VolumeFadeInTimeMax" Type="int" />
		<Field Name="VolumeFadeInCurveID" Type="int" />
		<Field Name="VolumeFadeOutTimeMin" Type="int" />
		<Field Name="VolumeFadeOutTimeMax" Type="int" />
		<Field Name="VolumeFadeOutCurveID" Type="int" />
	</Table>
	<Table Name="SoundKitChild" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ParentSoundKitID" Type="int" />
		<Field Name="SoundKitID" Type="int" />
	</Table>
	<Table Name="SoundKitEntry" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundKitID" Type="int" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="Frequency" Type="byte" />
		<Field Name="Volume" Type="float" />
	</Table>
	<Table Name="SoundKitFallback" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundKitID" Type="int" />
		<Field Name="FallbackSoundKitID" Type="int" />
	</Table>
	<Table Name="SoundKitName" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="SoundOverride" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ZoneIntroMusicID" Type="short" />
		<Field Name="ZoneMusicID" Type="short" />
		<Field Name="SoundAmbienceID" Type="short" />
		<Field Name="SoundProviderPreferencesID" Type="byte" />
	</Table>
	<Table Name="SoundProviderPreferences" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description" Type="string" />
		<Field Name="EAXDecayTime" Type="float" />
		<Field Name="EAX2EnvironmentSize" Type="float" />
		<Field Name="EAX2EnvironmentDiffusion" Type="float" />
		<Field Name="EAX2DecayHFRatio" Type="float" />
		<Field Name="EAX2ReflectionsDelay" Type="float" />
		<Field Name="EAX2ReverbDelay" Type="float" />
		<Field Name="EAX2RoomRolloff" Type="float" />
		<Field Name="EAX2AirAbsorption" Type="float" />
		<Field Name="EAX3DecayLFRatio" Type="float" />
		<Field Name="EAX3EchoTime" Type="float" />
		<Field Name="EAX3EchoDepth" Type="float" />
		<Field Name="EAX3ModulationTime" Type="float" />
		<Field Name="EAX3ModulationDepth" Type="float" />
		<Field Name="EAX3HFReference" Type="float" />
		<Field Name="EAX3LFReference" Type="float" />
		<Field Name="Flags" Type="short" />
		<Field Name="EAX2Room" Type="short" />
		<Field Name="EAX2RoomHF" Type="short" />
		<Field Name="EAX2Reflections" Type="short" />
		<Field Name="EAX2Reverb" Type="short" />
		<Field Name="EAXEnvironmentSelection" Type="byte" />
		<Field Name="EAX3RoomLF" Type="byte" />
	</Table>
	<Table Name="SourceInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SourceText_Lang" Type="string" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="PvpFaction" Type="byte" />
	</Table>
	<Table Name="SpamMessages" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Text" Type="string" />
	</Table>
	<Table Name="SpecializationSpells" Build="26654">
		<Field Name="Description_Lang" Type="string" />
		<Field Name="SpellID" Type="int" />
		<Field Name="OverridesSpellID" Type="int" />
		<Field Name="SpecID" Type="short" />
		<Field Name="DisplayOrder" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="Spell" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="NameSubtext_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="AuraDescription_Lang" Type="string" />
	</Table>
	<Table Name="SpellActionBarPref" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="PreferredActionBarMask" Type="short" />
	</Table>
	<Table Name="SpellActivationOverlay" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="OverlayFileDataID" Type="int" />
		<Field Name="Color" Type="int" />
		<Field Name="Scale" Type="float" />
		<Field Name="IconHighlightSpellClassMask" Type="int" ArraySize="4" />
		<Field Name="ScreenLocationID" Type="byte" />
		<Field Name="TriggerType" Type="byte" />
		<Field Name="SoundEntriesID" Type="int" />
	</Table>
	<Table Name="SpellAuraOptions" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ProcCharges" Type="int" />
		<Field Name="ProcTypeMask" Type="int" ArraySize="2" />
		<Field Name="ProcCategoryRecovery" Type="int" />
		<Field Name="CumulativeAura" Type="short" />
		<Field Name="SpellProcsPerMinuteID" Type="short" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="ProcChance" Type="byte" />
	</Table>
	<Table Name="SpellAuraRestrictions" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CasterAuraSpell" Type="int" />
		<Field Name="TargetAuraSpell" Type="int" />
		<Field Name="ExcludeCasterAuraSpell" Type="int" />
		<Field Name="ExcludeTargetAuraSpell" Type="int" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="CasterAuraState" Type="byte" />
		<Field Name="TargetAuraState" Type="byte" />
		<Field Name="ExcludeCasterAuraState" Type="byte" />
		<Field Name="ExcludeTargetAuraState" Type="byte" />
	</Table>
	<Table Name="SpellAuraVisibility" Build="26654">
		<Field Name="Type" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="SpellAuraVisXChrSpec" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ChrSpecializationID" Type="short" />
	</Table>
	<Table Name="SpellCastingRequirements" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="MinFactionID" Type="short" />
		<Field Name="RequiredAreasID" Type="short" />
		<Field Name="RequiresSpellFocus" Type="short" />
		<Field Name="FacingCasterFlags" Type="byte" />
		<Field Name="MinReputation" Type="byte" />
		<Field Name="RequiredAuraVision" Type="byte" />
	</Table>
	<Table Name="SpellCastTimes" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Base" Type="int" />
		<Field Name="Minimum" Type="int" />
		<Field Name="PerLevel" Type="short" />
	</Table>
	<Table Name="SpellCategories" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Category" Type="short" />
		<Field Name="StartRecoveryCategory" Type="short" />
		<Field Name="ChargeCategory" Type="short" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="DefenseType" Type="byte" />
		<Field Name="DispelType" Type="byte" />
		<Field Name="Mechanic" Type="byte" />
		<Field Name="PreventionType" Type="byte" />
	</Table>
	<Table Name="SpellCategory" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ChargeRecoveryTime" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UsesPerWeek" Type="byte" />
		<Field Name="MaxCharges" Type="byte" />
		<Field Name="TypeMask" Type="int" />
	</Table>
	<Table Name="SpellChainEffects" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AvgSegLen" Type="float" />
		<Field Name="NoiseScale" Type="float" />
		<Field Name="TexCoordScale" Type="float" />
		<Field Name="SegDuration" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="JointOffsetRadius" Type="float" />
		<Field Name="MinorJointScale" Type="float" />
		<Field Name="MajorJointScale" Type="float" />
		<Field Name="JointMoveSpeed" Type="float" />
		<Field Name="JointSmoothness" Type="float" />
		<Field Name="MinDurationBetweenJointJumps" Type="float" />
		<Field Name="MaxDurationBetweenJointJumps" Type="float" />
		<Field Name="WaveHeight" Type="float" />
		<Field Name="WaveFreq" Type="float" />
		<Field Name="WaveSpeed" Type="float" />
		<Field Name="MinWaveAngle" Type="float" />
		<Field Name="MaxWaveAngle" Type="float" />
		<Field Name="MinWaveSpin" Type="float" />
		<Field Name="MaxWaveSpin" Type="float" />
		<Field Name="ArcHeight" Type="float" />
		<Field Name="MinArcAngle" Type="float" />
		<Field Name="MaxArcAngle" Type="float" />
		<Field Name="MinArcSpin" Type="float" />
		<Field Name="MaxArcSpin" Type="float" />
		<Field Name="DelayBetweenEffects" Type="float" />
		<Field Name="MinFlickerOnDuration" Type="float" />
		<Field Name="MaxFlickerOnDuration" Type="float" />
		<Field Name="MinFlickerOffDuration" Type="float" />
		<Field Name="MaxFlickerOffDuration" Type="float" />
		<Field Name="PulseSpeed" Type="float" />
		<Field Name="PulseOnLength" Type="float" />
		<Field Name="PulseFadeLength" Type="float" />
		<Field Name="WavePhase" Type="float" />
		<Field Name="TimePerFlipFrame" Type="float" />
		<Field Name="VariancePerFlipFrame" Type="float" />
		<Field Name="TextureCoordScaleU" Type="float" ArraySize="3" />
		<Field Name="TextureCoordScaleV" Type="float" ArraySize="3" />
		<Field Name="TextureRepeatLengthU" Type="float" ArraySize="3" />
		<Field Name="TextureRepeatLengthV" Type="float" ArraySize="3" />
		<Field Name="TextureParticleFileDataID" Type="int" />
		<Field Name="StartWidth" Type="float" />
		<Field Name="EndWidth" Type="float" />
		<Field Name="ParticleScaleMultiplier" Type="float" />
		<Field Name="ParticleEmissionRateMultiplier" Type="float" />
		<Field Name="SegDelay" Type="short" />
		<Field Name="JointCount" Type="short" />
		<Field Name="SpellChainEffectID" Type="short" ArraySize="11" />
		<Field Name="WidthScaleCurveID" Type="short" />
		<Field Name="JointsPerMinorJoint" Type="byte" />
		<Field Name="MinorJointsPerMajorJoint" Type="byte" />
		<Field Name="Alpha" Type="byte" />
		<Field Name="Red" Type="byte" />
		<Field Name="Green" Type="byte" />
		<Field Name="Blue" Type="byte" />
		<Field Name="BlendMode" Type="byte" />
		<Field Name="RenderLayer" Type="byte" />
		<Field Name="NumFlipFramesU" Type="byte" />
		<Field Name="NumFlipFramesV" Type="byte" />
		<Field Name="SoundKitID" Type="int" />
		<Field Name="TextureFileDataID" Type="int" ArraySize="3" />
	</Table>
	<Table Name="SpellClassOptions" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="SpellClassMask" Type="int" ArraySize="4" />
		<Field Name="SpellClassSet" Type="byte" />
		<Field Name="ModalNextSpell" Type="int" />
	</Table>
	<Table Name="SpellCooldowns" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CategoryRecoveryTime" Type="int" />
		<Field Name="RecoveryTime" Type="int" />
		<Field Name="StartRecoveryTime" Type="int" />
		<Field Name="DifficultyID" Type="byte" />
	</Table>
	<Table Name="SpellDescriptionVariables" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Variables" Type="string" />
	</Table>
	<Table Name="SpellDispelType" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="InternalName" Type="string" />
		<Field Name="Mask" Type="byte" />
		<Field Name="ImmunityPossible" Type="byte" />
	</Table>
	<Table Name="SpellDuration" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Duration" Type="int" />
		<Field Name="MaxDuration" Type="int" />
		<Field Name="DurationPerLevel" Type="int" />
	</Table>
	<Table Name="SpellEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Effect" Type="int" />
		<Field Name="EffectBasePoints" Type="int" />
		<Field Name="EffectIndex" Type="int" />
		<Field Name="EffectAura" Type="int" />
		<Field Name="DifficultyID" Type="int" />
		<Field Name="EffectAmplitude" Type="float" />
		<Field Name="EffectAuraPeriod" Type="int" />
		<Field Name="EffectBonusCoefficient" Type="float" />
		<Field Name="EffectChainAmplitude" Type="float" />
		<Field Name="EffectChainTargets" Type="int" />
		<Field Name="EffectDieSides" Type="int" />
		<Field Name="EffectItemType" Type="uint" />
		<Field Name="EffectMechanic" Type="int" />
		<Field Name="EffectPointsPerResource" Type="float" />
		<Field Name="EffectRealPointsPerLevel" Type="float" />
		<Field Name="EffectTriggerSpell" Type="int" />
		<Field Name="EffectPos_Facing" Type="float" />
		<Field Name="EffectAttributes" Type="int" />
		<Field Name="BonusCoefficientFromAP" Type="float" />
		<Field Name="PvpMultiplier" Type="float" />
		<Field Name="Coefficient" Type="float" />
		<Field Name="Variance" Type="float" />
		<Field Name="ResourceCoefficient" Type="float" />
		<Field Name="GroupSizeBasePointsCoefficient" Type="float" />
		<Field Name="EffectSpellClassMask" Type="int" ArraySize="4" />
		<Field Name="EffectMiscValue" Type="int" ArraySize="2" />
		<Field Name="EffectRadiusIndex" Type="int" ArraySize="2" />
		<Field Name="ImplicitTarget" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SpellEffectEmission" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EmissionRate" Type="float" />
		<Field Name="ModelScale" Type="float" />
		<Field Name="AreaModelID" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellEquippedItems" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="EquippedItemInvTypes" Type="int" />
		<Field Name="EquippedItemSubclass" Type="int" />
		<Field Name="EquippedItemClass" Type="byte" />
	</Table>
	<Table Name="SpellFlyout" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RaceMask" Type="long" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="SpellIconFileID" Type="int" />
	</Table>
	<Table Name="SpellFlyoutItem" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="Slot" Type="byte" />
	</Table>
	<Table Name="SpellFocusObject" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
	</Table>
	<Table Name="SpellInterrupts" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="InterruptFlags" Type="short" />
		<Field Name="AuraInterruptFlags" Type="int" ArraySize="2" />
		<Field Name="ChannelInterruptFlags" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SpellItemEnchantment" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="EffectArg" Type="int" ArraySize="3" />
		<Field Name="EffectScalingPoints" Type="float" ArraySize="3" />
		<Field Name="TransmogCost" Type="int" />
		<Field Name="IconFileDataID" Type="int" />
		<Field Name="EffectPointsMin" Type="short" ArraySize="3" />
		<Field Name="ItemVisual" Type="short" />
		<Field Name="Flags" Type="short" />
		<Field Name="RequiredSkillID" Type="short" />
		<Field Name="RequiredSkillRank" Type="short" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="Charges" Type="byte" />
		<Field Name="Effect" Type="byte" ArraySize="3" />
		<Field Name="Condition_ID" Type="byte" />
		<Field Name="MinLevel" Type="byte" />
		<Field Name="MaxLevel" Type="byte" />
		<Field Name="ScalingClass" Type="byte" />
		<Field Name="ScalingClassRestricted" Type="byte" />
		<Field Name="TransmogPlayerConditionID" Type="int" />
	</Table>
	<Table Name="SpellItemEnchantmentCondition" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Lt_Operand" Type="int" ArraySize="5" />
		<Field Name="Lt_OperandType" Type="byte" ArraySize="5" />
		<Field Name="Operator" Type="byte" ArraySize="5" />
		<Field Name="Rt_OperandType" Type="byte" ArraySize="5" />
		<Field Name="Rt_Operand" Type="byte" ArraySize="5" />
		<Field Name="Logic" Type="byte" ArraySize="5" />
	</Table>
	<Table Name="SpellKeyboundOverride" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Function" Type="string" />
		<Field Name="Data" Type="int" />
		<Field Name="Type" Type="byte" />
	</Table>
	<Table Name="SpellLabel" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LabelID" Type="int" />
	</Table>
	<Table Name="SpellLearnSpell" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="LearnSpellID" Type="int" />
		<Field Name="OverridesSpellID" Type="int" />
	</Table>
	<Table Name="SpellLevels" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseLevel" Type="short" />
		<Field Name="MaxLevel" Type="short" />
		<Field Name="SpellLevel" Type="short" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="MaxPassiveAuraLevel" Type="byte" />
	</Table>
	<Table Name="SpellMechanic" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StateName_Lang" Type="string" />
	</Table>
	<Table Name="SpellMisc" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="CastingTimeIndex" Type="short" />
		<Field Name="DurationIndex" Type="short" />
		<Field Name="RangeIndex" Type="short" />
		<Field Name="SchoolMask" Type="byte" />
		<Field Name="SpellIconFileDataID" Type="int" />
		<Field Name="Speed" Type="float" />
		<Field Name="ActiveIconFileDataID" Type="int" />
		<Field Name="LaunchDelay" Type="float" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="Attributes" Type="int" ArraySize="14" />
	</Table>
	<Table Name="SpellMissile" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="DefaultPitchMin" Type="float" />
		<Field Name="DefaultPitchMax" Type="float" />
		<Field Name="DefaultSpeedMin" Type="float" />
		<Field Name="DefaultSpeedMax" Type="float" />
		<Field Name="RandomizeFacingMin" Type="float" />
		<Field Name="RandomizeFacingMax" Type="float" />
		<Field Name="RandomizePitchMin" Type="float" />
		<Field Name="RandomizePitchMax" Type="float" />
		<Field Name="RandomizeSpeedMin" Type="float" />
		<Field Name="RandomizeSpeedMax" Type="float" />
		<Field Name="Gravity" Type="float" />
		<Field Name="MaxDuration" Type="float" />
		<Field Name="CollisionRadius" Type="float" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellMissileMotion" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="ScriptBody" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="MissileCount" Type="byte" />
	</Table>
	<Table Name="SpellPower" Build="26654">
		<Field Name="ManaCost" Type="int" />
		<Field Name="PowerCostPct" Type="float" />
		<Field Name="PowerPctPerSecond" Type="float" />
		<Field Name="RequiredAuraSpellID" Type="int" />
		<Field Name="PowerCostMaxPct" Type="float" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PowerType" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ManaCostPerLevel" Type="int" />
		<Field Name="ManaPerSecond" Type="int" />
		<Field Name="OptionalCost" Type="int" />
		<Field Name="PowerDisplayID" Type="int" />
		<Field Name="AltPowerBarID" Type="int" />
	</Table>
	<Table Name="SpellPowerDifficulty" Build="26654">
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="SpellProceduralEffect" Build="26654">
		<Field Name="Value" Type="float" ArraySize="4" />
		<Field Name="Type" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="SpellProcsPerMinute" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseProcRate" Type="float" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellProcsPerMinuteMod" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Coeff" Type="float" />
		<Field Name="Param" Type="short" />
		<Field Name="Type" Type="byte" />
	</Table>
	<Table Name="SpellRadius" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Radius" Type="float" />
		<Field Name="RadiusPerLevel" Type="float" />
		<Field Name="RadiusMin" Type="float" />
		<Field Name="RadiusMax" Type="float" />
	</Table>
	<Table Name="SpellRange" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayName_Lang" Type="string" />
		<Field Name="DisplayNameShort_Lang" Type="string" />
		<Field Name="RangeMin" Type="float" ArraySize="2" />
		<Field Name="RangeMax" Type="float" ArraySize="2" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellReagents" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="Reagent" Type="int" ArraySize="8" />
		<Field Name="ReagentCount" Type="short" ArraySize="8" />
	</Table>
	<Table Name="SpellReagentsCurrency" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="CurrencyTypesID" Type="short" />
		<Field Name="CurrencyCount" Type="short" />
	</Table>
	<Table Name="SpellScaling" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="ScalesFromItemLevel" Type="short" />
		<Field Name="Class" Type="int" />
		<Field Name="MinScalingLevel" Type="int" />
		<Field Name="MaxScalingLevel" Type="int" />
	</Table>
	<Table Name="SpellShapeshift" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="ShapeshiftExclude" Type="int" ArraySize="2" />
		<Field Name="ShapeshiftMask" Type="int" ArraySize="2" />
		<Field Name="StanceBarOrder" Type="byte" />
	</Table>
	<Table Name="SpellShapeshiftForm" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="DamageVariance" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="CombatRoundTime" Type="short" />
		<Field Name="MountTypeID" Type="short" />
		<Field Name="CreatureType" Type="byte" />
		<Field Name="BonusActionBar" Type="byte" />
		<Field Name="AttackIconFileID" Type="int" />
		<Field Name="CreatureDisplayID" Type="int" ArraySize="4" />
		<Field Name="PresetSpellID" Type="int" ArraySize="8" />
	</Table>
	<Table Name="SpellSpecialUnitEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellVisualEffectNameID" Type="short" />
		<Field Name="PositionerID" Type="int" />
	</Table>
	<Table Name="SpellTargetRestrictions" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ConeDegrees" Type="float" />
		<Field Name="Width" Type="float" />
		<Field Name="Targets" Type="int" />
		<Field Name="TargetCreatureType" Type="short" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="MaxTargets" Type="byte" />
		<Field Name="MaxTargetLevel" Type="int" />
	</Table>
	<Table Name="SpellTotems" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="Totem" Type="int" ArraySize="2" />
		<Field Name="RequiredTotemCategoryID" Type="short" ArraySize="2" />
	</Table>
	<Table Name="SpellVisual" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MissileCastOffset" Type="float" ArraySize="3" />
		<Field Name="MissileImpactOffset" Type="float" ArraySize="3" />
		<Field Name="Flags" Type="int" />
		<Field Name="SpellVisualMissileSetID" Type="short" />
		<Field Name="MissileDestinationAttachment" Type="byte" />
		<Field Name="MissileAttachment" Type="byte" />
		<Field Name="MissileCastPositionerID" Type="int" />
		<Field Name="MissileImpactPositionerID" Type="int" />
		<Field Name="MissileTargetingKit" Type="int" />
		<Field Name="AnimEventSoundID" Type="int" />
		<Field Name="DamageNumberDelay" Type="short" />
		<Field Name="HostileSpellVisualID" Type="int" />
		<Field Name="CasterSpellVisualID" Type="int" />
		<Field Name="LowViolenceSpellVisualID" Type="int" />
	</Table>
	<Table Name="SpellVisualAnim" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="InitialAnimID" Type="short" />
		<Field Name="LoopAnimID" Type="short" />
		<Field Name="AnimKitID" Type="short" />
	</Table>
	<Table Name="SpellVisualColorEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Duration" Type="float" />
		<Field Name="Color" Type="int" />
		<Field Name="ColorMultiplier" Type="float" />
		<Field Name="RedCurveID" Type="short" />
		<Field Name="GreenCurveID" Type="short" />
		<Field Name="BlueCurveID" Type="short" />
		<Field Name="AlphaCurveID" Type="short" />
		<Field Name="OpacityCurveID" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="PositionerID" Type="int" />
	</Table>
	<Table Name="SpellVisualEffectName" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EffectRadius" Type="float" />
		<Field Name="BaseMissileSpeed" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="MinAllowedScale" Type="float" />
		<Field Name="MaxAllowedScale" Type="float" />
		<Field Name="Alpha" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="GenericID" Type="int" />
		<Field Name="TextureFileDataID" Type="int" />
		<Field Name="Type" Type="byte" />
		<Field Name="ModelFileDataID" Type="int" />
		<Field Name="RibbonQualityID" Type="int" />
		<Field Name="DissolveEffectID" Type="int" />
	</Table>
	<Table Name="SpellVisualEvent" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="StartEvent" Type="int" />
		<Field Name="StartMinOffsetMs" Type="int" />
		<Field Name="StartMaxOffsetMs" Type="int" />
		<Field Name="EndEvent" Type="int" />
		<Field Name="EndMinOffsetMs" Type="int" />
		<Field Name="EndMaxOffsetMs" Type="int" />
		<Field Name="TargetType" Type="int" />
		<Field Name="SpellVisualKitID" Type="int" />
	</Table>
	<Table Name="SpellVisualKit" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="FallbackPriority" Type="float" />
		<Field Name="FallbackSpellVisualKitID" Type="int" />
		<Field Name="DelayMin" Type="short" />
		<Field Name="DelayMax" Type="short" />
	</Table>
	<Table Name="SpellVisualKitAreaModel" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ModelFileDataID" Type="int" />
		<Field Name="EmissionRate" Type="float" />
		<Field Name="Spacing" Type="float" />
		<Field Name="ModelScale" Type="float" />
		<Field Name="LifeTime" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="SpellVisualKitEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="EffectType" Type="int" />
		<Field Name="Effect" Type="int" />
	</Table>
	<Table Name="SpellVisualKitModelAttach" Build="26654">
		<Field Name="Offset" Type="float" ArraySize="3" />
		<Field Name="OffsetVariation" Type="float" ArraySize="3" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellVisualEffectNameID" Type="short" />
		<Field Name="AttachmentID" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="PositionerID" Type="short" />
		<Field Name="Yaw" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Roll" Type="float" />
		<Field Name="YawVariation" Type="float" />
		<Field Name="PitchVariation" Type="float" />
		<Field Name="RollVariation" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="ScaleVariation" Type="float" />
		<Field Name="StartAnimID" Type="short" />
		<Field Name="AnimID" Type="short" />
		<Field Name="EndAnimID" Type="short" />
		<Field Name="AnimKitID" Type="short" />
		<Field Name="LowDefModelAttachID" Type="int" />
		<Field Name="StartDelay" Type="float" />
	</Table>
	<Table Name="SpellVisualMissile" Build="26654">
		<Field Name="FollowGroundHeight" Type="int" />
		<Field Name="FollowGroundDropSpeed" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="CastOffset" Type="float" ArraySize="3" />
		<Field Name="ImpactOffset" Type="float" ArraySize="3" />
		<Field Name="SpellVisualEffectNameID" Type="short" />
		<Field Name="CastPositionerID" Type="short" />
		<Field Name="ImpactPositionerID" Type="short" />
		<Field Name="FollowGroundApproach" Type="short" />
		<Field Name="SpellMissileMotionID" Type="short" />
		<Field Name="Attachment" Type="byte" />
		<Field Name="DestinationAttachment" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SoundEntriesID" Type="int" />
		<Field Name="AnimKitID" Type="int" />
	</Table>
	<Table Name="SpellXDescriptionVariables" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SpellID" Type="int" />
		<Field Name="SpellDescriptionVariablesID" Type="int" />
	</Table>
	<Table Name="SpellXSpellVisual" Build="26654">
		<Field Name="SpellVisualID" Type="int" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Probability" Type="float" />
		<Field Name="CasterPlayerConditionID" Type="short" />
		<Field Name="CasterUnitConditionID" Type="short" />
		<Field Name="ViewerPlayerConditionID" Type="short" />
		<Field Name="ViewerUnitConditionID" Type="short" />
		<Field Name="SpellIconFileID" Type="int" />
		<Field Name="ActiveIconFileID" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="DifficultyID" Type="byte" />
		<Field Name="Priority" Type="byte" />
	</Table>
	<Table Name="Startup_Strings" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Message_Lang" Type="string" />
	</Table>
	<Table Name="StartupFiles" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="Locale" Type="int" />
		<Field Name="BytesRequired" Type="int" />
	</Table>
	<Table Name="Stationery" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ItemID" Type="int" />
		<Field Name="TextureFileDataID" Type="int" ArraySize="2" />
	</Table>
	<Table Name="SummonProperties" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="Control" Type="int" />
		<Field Name="Faction" Type="int" />
		<Field Name="Title" Type="int" />
		<Field Name="Slot" Type="int" />
	</Table>
	<Table Name="TactKey" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Key" Type="byte" ArraySize="16" />
	</Table>
	<Table Name="TactKeyLookup" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TACTID" Type="byte" ArraySize="8" />
	</Table>
	<Table Name="Talent" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Description_Lang" Type="string" />
		<Field Name="SpellID" Type="int" />
		<Field Name="OverridesSpellID" Type="int" />
		<Field Name="SpecID" Type="short" />
		<Field Name="TierID" Type="byte" />
		<Field Name="ColumnIndex" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CategoryMask" Type="byte" ArraySize="2" />
		<Field Name="ClassID" Type="byte" />
	</Table>
	<Table Name="TaxiNodes" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="MountCreatureID" Type="int" ArraySize="2" />
		<Field Name="MapOffset" Type="float" ArraySize="2" />
		<Field Name="Facing" Type="float" />
		<Field Name="FlightMapOffset" Type="float" ArraySize="2" />
		<Field Name="ContinentID" Type="short" />
		<Field Name="ConditionID" Type="short" />
		<Field Name="CharacterBitNumber" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UiTextureKitID" Type="int" />
		<Field Name="SpecialIconConditionID" Type="int" />
	</Table>
	<Table Name="TaxiPath" Build="26654">
		<Field Name="FromTaxiNode" Type="short" />
		<Field Name="ToTaxiNode" Type="short" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Cost" Type="int" />
	</Table>
	<Table Name="TaxiPathNode" Build="26654">
		<Field Name="Loc" Type="float" ArraySize="3" />
		<Field Name="PathID" Type="short" />
		<Field Name="ContinentID" Type="short" />
		<Field Name="NodeIndex" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Delay" Type="int" />
		<Field Name="ArrivalEventID" Type="short" />
		<Field Name="DepartureEventID" Type="short" />
	</Table>
	<Table Name="TerrainMaterial" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Shader" Type="byte" />
		<Field Name="EnvMapDiffuseFileID" Type="int" />
		<Field Name="EnvMapSpecularFileID" Type="int" />
	</Table>
	<Table Name="TerrainType" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TerrainDesc" Type="string" />
		<Field Name="FootstepSprayRun" Type="short" />
		<Field Name="FootstepSprayWalk" Type="short" />
		<Field Name="SoundID" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="TerrainTypeSounds" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
	</Table>
	<Table Name="TextureBlendSet" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TextureFileDataID" Type="int" ArraySize="3" />
		<Field Name="TextureScrollRateU" Type="float" ArraySize="3" />
		<Field Name="TextureScrollRateV" Type="float" ArraySize="3" />
		<Field Name="TextureScaleU" Type="float" ArraySize="3" />
		<Field Name="TextureScaleV" Type="float" ArraySize="3" />
		<Field Name="ModX" Type="float" ArraySize="4" />
		<Field Name="SwizzleRed" Type="byte" />
		<Field Name="SwizzleGreen" Type="byte" />
		<Field Name="SwizzleBlue" Type="byte" />
		<Field Name="SwizzleAlpha" Type="byte" />
	</Table>
	<Table Name="TextureFileData" Build="26654">
		<Field Name="FileDataID" Type="int" IsIndex="true" />
		<Field Name="MaterialResourcesID" Type="int" />
		<Field Name="UsageType" Type="byte" />
	</Table>
	<Table Name="TotemCategory" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="TotemCategoryMask" Type="int" />
		<Field Name="TotemCategoryType" Type="byte" />
	</Table>
	<Table Name="Toy" Build="26654">
		<Field Name="SourceText_Lang" Type="string" />
		<Field Name="ItemID" Type="int" />
		<Field Name="Flags" Type="byte" />
		<Field Name="SourceTypeEnum" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="TradeSkillCategory" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="SkilllineID" Type="short" />
		<Field Name="ParenttradeskillcategoryID" Type="short" />
		<Field Name="Orderindex" Type="short" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="TradeSkillItem" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ItemLevel" Type="short" />
		<Field Name="RequiredLevel" Type="byte" />
	</Table>
	<Table Name="TransformMatrix" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="Yaw" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Roll" Type="float" />
		<Field Name="Scale" Type="float" />
	</Table>
	<Table Name="TransmogHoliday" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="RequiredTransmogHoliday" Type="int" />
	</Table>
	<Table Name="TransmogSet" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ParentTransmogSetID" Type="short" />
		<Field Name="UiOrder" Type="short" />
		<Field Name="ExpansionID" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="TrackingQuestID" Type="int" />
		<Field Name="ClassMask" Type="int" />
		<Field Name="ItemNameDescriptionID" Type="int" />
		<Field Name="TransmogSetGroupID" Type="int" />
	</Table>
	<Table Name="TransmogSetGroup" Build="26654">
		<Field Name="Name_Lang" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="TransmogSetItem" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TransmogSetID" Type="int" />
		<Field Name="ItemModifiedAppearanceID" Type="int" />
		<Field Name="Flags" Type="int" />
	</Table>
	<Table Name="TransportAnimation" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TimeIndex" Type="int" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="SequenceID" Type="byte" />
	</Table>
	<Table Name="TransportPhysics" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WaveAmp" Type="float" />
		<Field Name="WaveTimeScale" Type="float" />
		<Field Name="RollAmp" Type="float" />
		<Field Name="RollTimeScale" Type="float" />
		<Field Name="PitchAmp" Type="float" />
		<Field Name="PitchTimeScale" Type="float" />
		<Field Name="MaxBank" Type="float" />
		<Field Name="MaxBankTurnSpeed" Type="float" />
		<Field Name="SpeedDampThresh" Type="float" />
		<Field Name="SpeedDamp" Type="float" />
	</Table>
	<Table Name="TransportRotation" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TimeIndex" Type="int" />
		<Field Name="Rot" Type="float" ArraySize="4" />
	</Table>
	<Table Name="Trophy" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="GameObjectDisplayInfoID" Type="short" />
		<Field Name="TrophyTypeID" Type="byte" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="UiCamera" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Pos" Type="float" ArraySize="3" />
		<Field Name="LookAt" Type="float" ArraySize="3" />
		<Field Name="Up" Type="float" ArraySize="3" />
		<Field Name="AnimFrame" Type="short" />
		<Field Name="UiCameraTypeID" Type="byte" />
		<Field Name="AnimVariation" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="AnimID" Type="int" />
	</Table>
	<Table Name="UiCameraType" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="Width" Type="int" />
		<Field Name="Height" Type="int" />
	</Table>
	<Table Name="UiCamFbackTransmogChrRace" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiCameraID" Type="short" />
		<Field Name="ChrRaceID" Type="byte" />
		<Field Name="Gender" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
		<Field Name="Variation" Type="byte" />
	</Table>
	<Table Name="UiCamFbackTransmogWeapon" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiCameraID" Type="short" />
		<Field Name="ItemClass" Type="byte" />
		<Field Name="ItemSubclass" Type="byte" />
		<Field Name="InventoryType" Type="byte" />
	</Table>
	<Table Name="UIExpansionDisplayInfo" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ExpansionLogo" Type="int" />
		<Field Name="ExpansionBanner" Type="int" />
		<Field Name="ExpansionLevel" Type="int" />
	</Table>
	<Table Name="UIExpansionDisplayInfoIcon" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FeatureDescription_Lang" Type="string" />
		<Field Name="ParentID" Type="int" />
		<Field Name="FeatureIcon" Type="int" />
	</Table>
	<Table Name="UiMapPOI" Build="26654">
		<Field Name="ContinentID" Type="int" />
		<Field Name="WorldLoc" Type="float" ArraySize="3" />
		<Field Name="UiTextureAtlasMemberID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="PoiDataType" Type="short" />
		<Field Name="PoiData" Type="short" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="UiModelScene" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiSystemType" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="UiModelSceneActor" Build="26654">
		<Field Name="ScriptTag" Type="string" />
		<Field Name="Position" Type="float" ArraySize="3" />
		<Field Name="OrientationYaw" Type="float" />
		<Field Name="OrientationPitch" Type="float" />
		<Field Name="OrientationRoll" Type="float" />
		<Field Name="NormalizedScale" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiModelSceneActorDisplayID" Type="int" />
	</Table>
	<Table Name="UiModelSceneActorDisplay" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="AnimSpeed" Type="float" />
		<Field Name="Alpha" Type="float" />
		<Field Name="Scale" Type="float" />
		<Field Name="AnimationID" Type="int" />
		<Field Name="SequenceVariation" Type="int" />
	</Table>
	<Table Name="UiModelSceneCamera" Build="26654">
		<Field Name="ScriptTag" Type="string" />
		<Field Name="Target" Type="float" ArraySize="3" />
		<Field Name="ZoomedTargetOffset" Type="float" ArraySize="3" />
		<Field Name="Yaw" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Roll" Type="float" />
		<Field Name="ZoomedYawOffset" Type="float" />
		<Field Name="ZoomedPitchOffset" Type="float" />
		<Field Name="ZoomedRollOffset" Type="float" />
		<Field Name="ZoomDistance" Type="float" />
		<Field Name="MinZoomDistance" Type="float" />
		<Field Name="MaxZoomDistance" Type="float" />
		<Field Name="Flags" Type="byte" />
		<Field Name="CameraType" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
	</Table>
	<Table Name="UiTextureAtlas" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="AtlasHeight" Type="short" />
		<Field Name="AtlasWidth" Type="short" />
	</Table>
	<Table Name="UiTextureAtlasMember" Build="26654">
		<Field Name="CommittedName" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="UiTextureAtlasID" Type="short" />
		<Field Name="CommittedLeft" Type="short" />
		<Field Name="CommittedRight" Type="short" />
		<Field Name="CommittedTop" Type="short" />
		<Field Name="CommittedBottom" Type="short" />
		<Field Name="CommittedFlags" Type="byte" />
	</Table>
	<Table Name="UiTextureKit" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="KitPrefix" Type="string" />
	</Table>
	<Table Name="UnitBlood" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="PlayerCritBloodSpurtID" Type="int" />
		<Field Name="PlayerHitBloodSpurtID" Type="int" />
		<Field Name="DefaultBloodSpurtID" Type="int" />
		<Field Name="PlayerOmniCritBloodSpurtID" Type="int" />
		<Field Name="PlayerOmniHitBloodSpurtID" Type="int" />
		<Field Name="DefaultOmniBloodSpurtID" Type="int" />
	</Table>
	<Table Name="UnitBloodLevels" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Violencelevel" Type="byte" ArraySize="3" />
	</Table>
	<Table Name="UnitCondition" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Value" Type="int" ArraySize="8" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Variable" Type="byte" ArraySize="8" />
		<Field Name="Op" Type="byte" ArraySize="8" />
	</Table>
	<Table Name="UnitPowerBar" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Cost_Lang" Type="string" />
		<Field Name="OutOfError_Lang" Type="string" />
		<Field Name="ToolTip_Lang" Type="string" />
		<Field Name="RegenerationPeace" Type="float" />
		<Field Name="RegenerationCombat" Type="float" />
		<Field Name="FileDataID" Type="int" ArraySize="6" />
		<Field Name="Color" Type="int" ArraySize="6" />
		<Field Name="StartInset" Type="float" />
		<Field Name="EndInset" Type="float" />
		<Field Name="StartPower" Type="short" />
		<Field Name="Flags" Type="short" />
		<Field Name="CenterPower" Type="byte" />
		<Field Name="BarType" Type="byte" />
		<Field Name="MinPower" Type="int" />
		<Field Name="MaxPower" Type="int" />
	</Table>
	<Table Name="UnitTest" Build="26654">
		<Field Name="Field_1" Type="string" />
		<Field Name="Field_2" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Field_4" Type="int" />
		<Field Name="Field_5" Type="int" />
	</Table>
	<Table Name="Vehicle" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="TurnSpeed" Type="float" />
		<Field Name="PitchSpeed" Type="float" />
		<Field Name="PitchMin" Type="float" />
		<Field Name="PitchMax" Type="float" />
		<Field Name="MouseLookOffsetPitch" Type="float" />
		<Field Name="CameraFadeDistScalarMin" Type="float" />
		<Field Name="CameraFadeDistScalarMax" Type="float" />
		<Field Name="CameraPitchOffset" Type="float" />
		<Field Name="FacingLimitRight" Type="float" />
		<Field Name="FacingLimitLeft" Type="float" />
		<Field Name="CameraYawOffset" Type="float" />
		<Field Name="SeatID" Type="short" ArraySize="8" />
		<Field Name="VehicleUIIndicatorID" Type="short" />
		<Field Name="PowerDisplayID" Type="short" ArraySize="3" />
		<Field Name="FlagsB" Type="byte" />
		<Field Name="UiLocomotionType" Type="byte" />
		<Field Name="MissileTargetingID" Type="int" />
	</Table>
	<Table Name="VehicleSeat" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Flags" Type="int" />
		<Field Name="FlagsB" Type="int" />
		<Field Name="FlagsC" Type="int" />
		<Field Name="AttachmentOffset" Type="float" ArraySize="3" />
		<Field Name="EnterPreDelay" Type="float" />
		<Field Name="EnterSpeed" Type="float" />
		<Field Name="EnterGravity" Type="float" />
		<Field Name="EnterMinDuration" Type="float" />
		<Field Name="EnterMaxDuration" Type="float" />
		<Field Name="EnterMinArcHeight" Type="float" />
		<Field Name="EnterMaxArcHeight" Type="float" />
		<Field Name="ExitPreDelay" Type="float" />
		<Field Name="ExitSpeed" Type="float" />
		<Field Name="ExitGravity" Type="float" />
		<Field Name="ExitMinDuration" Type="float" />
		<Field Name="ExitMaxDuration" Type="float" />
		<Field Name="ExitMinArcHeight" Type="float" />
		<Field Name="ExitMaxArcHeight" Type="float" />
		<Field Name="PassengerYaw" Type="float" />
		<Field Name="PassengerPitch" Type="float" />
		<Field Name="PassengerRoll" Type="float" />
		<Field Name="VehicleEnterAnimDelay" Type="float" />
		<Field Name="VehicleExitAnimDelay" Type="float" />
		<Field Name="CameraEnteringDelay" Type="float" />
		<Field Name="CameraEnteringDuration" Type="float" />
		<Field Name="CameraExitingDelay" Type="float" />
		<Field Name="CameraExitingDuration" Type="float" />
		<Field Name="CameraOffset" Type="float" ArraySize="3" />
		<Field Name="CameraPosChaseRate" Type="float" />
		<Field Name="CameraFacingChaseRate" Type="float" />
		<Field Name="CameraEnteringZoom" Type="float" />
		<Field Name="CameraSeatZoomMin" Type="float" />
		<Field Name="CameraSeatZoomMax" Type="float" />
		<Field Name="UiSkinFileDataID" Type="int" />
		<Field Name="EnterAnimStart" Type="short" />
		<Field Name="EnterAnimLoop" Type="short" />
		<Field Name="RideAnimStart" Type="short" />
		<Field Name="RideAnimLoop" Type="short" />
		<Field Name="RideUpperAnimStart" Type="short" />
		<Field Name="RideUpperAnimLoop" Type="short" />
		<Field Name="ExitAnimStart" Type="short" />
		<Field Name="ExitAnimLoop" Type="short" />
		<Field Name="ExitAnimEnd" Type="short" />
		<Field Name="VehicleEnterAnim" Type="short" />
		<Field Name="VehicleExitAnim" Type="short" />
		<Field Name="VehicleRideAnimLoop" Type="short" />
		<Field Name="EnterAnimKitID" Type="short" />
		<Field Name="RideAnimKitID" Type="short" />
		<Field Name="ExitAnimKitID" Type="short" />
		<Field Name="VehicleEnterAnimKitID" Type="short" />
		<Field Name="VehicleRideAnimKitID" Type="short" />
		<Field Name="VehicleExitAnimKitID" Type="short" />
		<Field Name="CameraModeID" Type="short" />
		<Field Name="AttachmentID" Type="byte" />
		<Field Name="PassengerAttachmentID" Type="byte" />
		<Field Name="VehicleEnterAnimBone" Type="byte" />
		<Field Name="VehicleExitAnimBone" Type="byte" />
		<Field Name="VehicleRideAnimLoopBone" Type="byte" />
		<Field Name="VehicleAbilityDisplay" Type="byte" />
		<Field Name="EnterUISoundID" Type="int" />
		<Field Name="ExitUISoundID" Type="int" />
	</Table>
	<Table Name="VehicleUIIndicator" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BackgroundTextureFileID" Type="int" />
	</Table>
	<Table Name="VehicleUIIndSeat" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="XPos" Type="float" />
		<Field Name="YPos" Type="float" />
		<Field Name="VirtualSeatIndex" Type="byte" />
	</Table>
	<Table Name="Vignette" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="MaxHeight" Type="float" />
		<Field Name="MinHeight" Type="float" />
		<Field Name="QuestFeedbackEffectID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="VisibleTrackingQuestID" Type="int" />
	</Table>
	<Table Name="VirtualAttachment" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="PositionerID" Type="short" />
	</Table>
	<Table Name="VirtualAttachmentCustomization" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="VirtualAttachmentID" Type="short" />
		<Field Name="PositionerID" Type="short" />
	</Table>
	<Table Name="VocalUISounds" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VocalUIEnum" Type="byte" />
		<Field Name="RaceID" Type="byte" />
		<Field Name="ClassID" Type="byte" />
		<Field Name="NormalSoundID" Type="int" ArraySize="2" />
	</Table>
	<Table Name="WbAccessControlList" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="URL" Type="string" />
		<Field Name="GrantFlags" Type="short" />
		<Field Name="RevokeFlags" Type="byte" />
		<Field Name="WowEditInternal" Type="byte" />
		<Field Name="RegionID" Type="byte" />
	</Table>
	<Table Name="WbCertWhitelist" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Domain" Type="string" />
		<Field Name="GrantAccess" Type="byte" />
		<Field Name="RevokeAccess" Type="byte" />
		<Field Name="WowEditInternal" Type="byte" />
	</Table>
	<Table Name="WeaponImpactSounds" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WeaponSubClassID" Type="byte" />
		<Field Name="ParrySoundType" Type="byte" />
		<Field Name="ImpactSource" Type="byte" />
		<Field Name="ImpactSoundID" Type="int" ArraySize="11" />
		<Field Name="CritImpactSoundID" Type="int" ArraySize="11" />
		<Field Name="PierceImpactSoundID" Type="int" ArraySize="11" />
		<Field Name="PierceCritImpactSoundID" Type="int" ArraySize="11" />
	</Table>
	<Table Name="WeaponSwingSounds2" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SwingType" Type="byte" />
		<Field Name="Crit" Type="byte" />
		<Field Name="SoundID" Type="int" />
	</Table>
	<Table Name="WeaponTrail" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="Yaw" Type="float" />
		<Field Name="Pitch" Type="float" />
		<Field Name="Roll" Type="float" />
		<Field Name="TextureFileDataID" Type="int" ArraySize="3" />
		<Field Name="TextureScrollRateU" Type="float" ArraySize="3" />
		<Field Name="TextureScrollRateV" Type="float" ArraySize="3" />
		<Field Name="TextureScaleU" Type="float" ArraySize="3" />
		<Field Name="TextureScaleV" Type="float" ArraySize="3" />
	</Table>
	<Table Name="WeaponTrailModelDef" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="LowDefFileDataID" Type="int" />
		<Field Name="WeaponTrailID" Type="short" />
	</Table>
	<Table Name="WeaponTrailParam" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Duration" Type="float" />
		<Field Name="FadeOutTime" Type="float" />
		<Field Name="EdgeLifeSpan" Type="float" />
		<Field Name="InitialDelay" Type="float" />
		<Field Name="SmoothSampleAngle" Type="float" />
		<Field Name="Hand" Type="byte" />
		<Field Name="OverrideAttachTop" Type="byte" />
		<Field Name="OverrideAttachBot" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="Weather" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Intensity" Type="float" ArraySize="2" />
		<Field Name="TransitionSkyBox" Type="float" />
		<Field Name="EffectColor" Type="float" ArraySize="3" />
		<Field Name="Scale" Type="float" />
		<Field Name="Volatility" Type="float" />
		<Field Name="TwinkleIntensity" Type="float" />
		<Field Name="FallModifier" Type="float" />
		<Field Name="RotationalSpeed" Type="float" />
		<Field Name="SoundAmbienceID" Type="short" />
		<Field Name="Type" Type="byte" />
		<Field Name="EffectType" Type="byte" />
		<Field Name="WindSettingsID" Type="byte" />
		<Field Name="AmbienceID" Type="int" />
		<Field Name="EffectTextureFileDataID" Type="int" />
	</Table>
	<Table Name="WindSettings" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="BaseMag" Type="float" />
		<Field Name="BaseDir" Type="float" ArraySize="3" />
		<Field Name="VarianceMagOver" Type="float" />
		<Field Name="VarianceMagUnder" Type="float" />
		<Field Name="VarianceDir" Type="float" ArraySize="3" />
		<Field Name="MaxStepMag" Type="float" />
		<Field Name="MaxStepDir" Type="float" ArraySize="3" />
		<Field Name="Frequency" Type="float" />
		<Field Name="Duration" Type="float" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="WMOAreaTable" Build="26654">
		<Field Name="AreaName_Lang" Type="string" />
		<Field Name="WMOGroupID" Type="int" />
		<Field Name="AmbienceID" Type="short" />
		<Field Name="ZoneMusic" Type="short" />
		<Field Name="IntroSound" Type="short" />
		<Field Name="AreaTableID" Type="short" />
		<Field Name="UwIntroSound" Type="short" />
		<Field Name="NameSetID" Type="short" />
		<Field Name="SoundProviderPref" Type="byte" />
		<Field Name="SoundProviderPrefUnderwater" Type="byte" />
		<Field Name="Flags" Type="byte" />
		<Field Name="UwZoneMusic" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="InlineWMOID" Type="int" />
	</Table>
	<Table Name="WMOMinimapTexture" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="FileDataID" Type="int" />
		<Field Name="GroupNum" Type="short" />
		<Field Name="BlockX" Type="byte" />
		<Field Name="BlockY" Type="byte" />
	</Table>
	<Table Name="World_PVP_Area" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Area_ID" Type="short" />
		<Field Name="Next_Time_Worldstate" Type="short" />
		<Field Name="Game_Time_Worldstate" Type="short" />
		<Field Name="Battle_Populate_Time" Type="short" />
		<Field Name="Map_ID" Type="short" />
		<Field Name="Min_Level" Type="byte" />
		<Field Name="Max_Level" Type="byte" />
	</Table>
	<Table Name="WorldBossLockout" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="TrackingQuestID" Type="short" />
	</Table>
	<Table Name="WorldChunkSounds" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="MapID" Type="short" />
		<Field Name="ChunkX" Type="byte" />
		<Field Name="ChunkY" Type="byte" />
		<Field Name="SubchunkX" Type="byte" />
		<Field Name="SubchunkY" Type="byte" />
		<Field Name="SoundOverrideID" Type="byte" />
	</Table>
	<Table Name="WorldEffect" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TargetAsset" Type="int" />
		<Field Name="CombatConditionID" Type="short" />
		<Field Name="TargetType" Type="byte" />
		<Field Name="WhenToDisplay" Type="byte" />
		<Field Name="QuestFeedbackEffectID" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
	</Table>
	<Table Name="WorldElapsedTimer" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name_Lang" Type="string" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Type" Type="byte" />
	</Table>
	<Table Name="WorldMapArea" Build="26654">
		<Field Name="AreaName" Type="string" />
		<Field Name="LocLeft" Type="float" />
		<Field Name="LocRight" Type="float" />
		<Field Name="LocTop" Type="float" />
		<Field Name="LocBottom" Type="float" />
		<Field Name="Flags" Type="int" />
		<Field Name="MapID" Type="short" />
		<Field Name="AreaID" Type="short" />
		<Field Name="DisplayMapID" Type="short" />
		<Field Name="DefaultDungeonFloor" Type="short" />
		<Field Name="ParentWorldMapID" Type="short" />
		<Field Name="LevelRangeMin" Type="byte" />
		<Field Name="LevelRangeMax" Type="byte" />
		<Field Name="BountySetID" Type="byte" />
		<Field Name="BountyDisplayLocation" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="VisibilityPlayerConditionID" Type="int" />
	</Table>
	<Table Name="WorldMapContinent" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="ContinentOffset" Type="float" ArraySize="2" />
		<Field Name="Scale" Type="float" />
		<Field Name="TaxiMin" Type="float" ArraySize="2" />
		<Field Name="TaxiMax" Type="float" ArraySize="2" />
		<Field Name="MapID" Type="short" />
		<Field Name="WorldMapID" Type="short" />
		<Field Name="LeftBoundary" Type="byte" />
		<Field Name="RightBoundary" Type="byte" />
		<Field Name="TopBoundary" Type="byte" />
		<Field Name="BottomBoundary" Type="byte" />
		<Field Name="Flags" Type="byte" />
	</Table>
	<Table Name="WorldMapOverlay" Build="26654">
		<Field Name="TextureName" Type="string" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="TextureWidth" Type="short" />
		<Field Name="TextureHeight" Type="short" />
		<Field Name="MapAreaID" Type="int" />
		<Field Name="OffsetX" Type="int" />
		<Field Name="OffsetY" Type="int" />
		<Field Name="HitRectTop" Type="int" />
		<Field Name="HitRectLeft" Type="int" />
		<Field Name="HitRectBottom" Type="int" />
		<Field Name="HitRectRight" Type="int" />
		<Field Name="PlayerConditionID" Type="int" />
		<Field Name="Flags" Type="int" />
		<Field Name="AreaID" Type="int" ArraySize="4" />
	</Table>
	<Table Name="WorldMapTransforms" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Region" Type="float" ArraySize="6" />
		<Field Name="RegionOffset" Type="float" ArraySize="2" />
		<Field Name="RegionScale" Type="float" />
		<Field Name="MapID" Type="short" />
		<Field Name="AreaID" Type="short" />
		<Field Name="NewMapID" Type="short" />
		<Field Name="NewDungeonMapID" Type="short" />
		<Field Name="NewAreaID" Type="short" />
		<Field Name="Flags" Type="byte" />
		<Field Name="Priority" Type="int" />
	</Table>
	<Table Name="WorldStateExpression" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Expression" Type="string" />
	</Table>
	<Table Name="WorldStateUI" Build="26654">
		<Field Name="Icon" Type="string" />
		<Field Name="ExtendedUI" Type="string" />
		<Field Name="DynamicTooltip_Lang" Type="string" />
		<Field Name="String_Lang" Type="string" />
		<Field Name="Tooltip_Lang" Type="string" />
		<Field Name="MapID" Type="short" />
		<Field Name="AreaID" Type="short" />
		<Field Name="PhaseID" Type="short" />
		<Field Name="PhaseGroupID" Type="short" />
		<Field Name="StateVariable" Type="short" />
		<Field Name="ExtendedUIStateVariable" Type="short" ArraySize="3" />
		<Field Name="OrderIndex" Type="byte" />
		<Field Name="PhaseUseFlags" Type="byte" />
		<Field Name="Type" Type="byte" />
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DynamicIconFileID" Type="int" />
		<Field Name="DynamicFlashIconFileID" Type="int" />
	</Table>
	<Table Name="WorldStateZoneSounds" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="WMOAreaID" Type="int" />
		<Field Name="WorldStateID" Type="short" />
		<Field Name="WorldStateValue" Type="short" />
		<Field Name="AreaID" Type="short" />
		<Field Name="ZoneIntroMusicID" Type="short" />
		<Field Name="ZoneMusicID" Type="short" />
		<Field Name="SoundAmbienceID" Type="short" />
		<Field Name="SoundProviderPreferencesID" Type="byte" />
	</Table>
	<Table Name="ZoneIntroMusicTable" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="MinDelayMinutes" Type="short" />
		<Field Name="Priority" Type="byte" />
		<Field Name="SoundID" Type="int" />
	</Table>
	<Table Name="ZoneLight" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Name" Type="string" />
		<Field Name="MapID" Type="short" />
		<Field Name="LightID" Type="short" />
	</Table>
	<Table Name="ZoneLightPoint" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="Pos" Type="float" ArraySize="2" />
		<Field Name="PointOrder" Type="byte" />
	</Table>
	<Table Name="ZoneMusic" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="SetName" Type="string" />
		<Field Name="SilenceIntervalMin" Type="int" ArraySize="2" />
		<Field Name="SilenceIntervalMax" Type="int" ArraySize="2" />
		<Field Name="Sounds" Type="int" ArraySize="2" />
	</Table>
	<Table Name="ZoneStory" Build="26654">
		<Field Name="ID" Type="int" IsIndex="true" />
		<Field Name="DisplayAchievementID" Type="int" />
		<Field Name="DisplayWorldMapAreaID" Type="int" />
		<Field Name="PlayerFactionGroupID" Type="byte" />
	</Table>
</Definition>
