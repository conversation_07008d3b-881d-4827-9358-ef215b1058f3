
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>Menu Items</title>
<meta name="GENERATOR" content="WinCHM">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style>
html,body { 
	/* Default Font */
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11pt;
}
</style>




<style type="text/css">

#winchm_template_navigation{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-SIZE: 11px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}

#winchm_template_title{
	PADDING-RIGHT: 2px; 
	PADDING-LEFT: 2px; 
	FONT-WEIGHT: bold; 
	FONT-SIZE: 14px; 
	PADDING-BOTTOM: 2px; 
	MARGIN: 0px; 
	PADDING-TOP: 2px; 
	FONT-FAMILY: Verdana;
}


</style>
</head>

<body topMargin=0 marginheight="0" marginwidth="10">
<TABLE style="HEIGHT: 50px; WIDTH: 100%" cellSpacing=0 cellPadding=0 width="100%" border=0>
  
  <TR>
    <TD>
      <DIV id=winchm_template_navigation><FONT color=#000000>WDBX 
      Editor&nbsp;Help </FONT> </DIV>
      <DIV id=winchm_template_title><FONT color=#000080>Menu Items</FONT></DIV></TD>
    <TD align=right width=50><A href="Introduction.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="FindReplace.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></TD></TR></TABLE>   

<HR>
<BR>
<P>Below are the menu items located in the top nav bar.</P>
<P>
<DIV><STRONG>File Menu</STRONG></DIV>
<UL>
  <LI>
  <DIV>Open - This allows the choice of multiple of any of 
  the available items</DIV>
  <LI>
  <DIV>Open from MPQ - Will extract the DBC and DB2 files 
  from a designated MPQ archive</DIV>
  <LI>
  <DIV>Open from CASC - Selecting a WoW client directory 
  will allow the ability to extract any DBC and DB2 files</DIV>
  <LI>
  <DIV>Save - WIll save the currently in view file</DIV>
  <LI>
  <DIV>Save All - Will save all loaded files to a specified fodler</DIV>
  <LI>
  <DIV>Reload - Will reload the current file from the base 
  file removing all changes that have not been saved</DIV>
  <LI>
  <DIV>Close - Will close the current file and remove it 
  from the editor</DIV>
  <LI>
  <DIV>Close All - Will close all files and remove them 
  from the editor</DIV></LI></UL>

<I>Note: Legion ADB files require the DB2 counterpart to be loaded first as the DB2 contains key information for parsing the ADB. The program will prioritise DB2 over ADB if both are loaded together.</I>

  <P><STRONG>Edit Menu</STRONG>          
    </P>
<UL>
  <LI>
  <DIV> Undo</DIV>
  <LI>
  <DIV>          
    Redo</DIV>
  <LI>
  <DIV>          
   Go To - Will go to the row with the input          
   ID number</DIV>
  <LI>
  <DIV>          
    <A href="FindReplace.htm">Find and Replace</a></DIV></LI></UL>

<I>Note: Undo and Redo history is lost each time a new 
  file is edited</I>

  <P><A href="Import_Export.htm"><STRONG>Export 
</STRONG></A>          
    </P>
<UL>
  <LI>
  <DIV>          
    To SQL - Will create and insert all data into a 
  MySQL          
    database</DIV>
  <LI>
  <DIV>          
    To SQL File - Generates a MySQL .sql          
    file</DIV>
  <LI>
  <DIV>          
    To          
    CSV</DIV>
  <LI>
  <DIV>          
    To MPQ - Will either generate a new MPQ file or 
  append to an existing          
    one</DIV>
 <LI>
  <DIV>          
    To          
    JSON</DIV>
</LI></UL>
  <P><A href="Import_Export.htm"><STRONG>Import 
</STRONG></A>          
    </P>
<UL>
  <LI>
  <DIV>          
    From          
    SQL</DIV>
  <LI>
  <DIV>          
    From          
    CSV</DIV></LI></UL>
  <P><A href="Tools.htm"><STRONG>Tools</STRONG> </A>          
    </P>
<UL>
  <LI>
  <DIV>          
    Definition Editor - Allows the ability to create, 
  edit and delete          
    definitions</DIV>
  <LI>
  <DIV>          
    WotLK Item Import - Fixes the red question mark 
  issue when creating custom items in the database for          
    WotLK</DIV>
  <LI>
  <DIV>          
    WDB5 Parser - Attempts to creates definitions for 
  Legion DB2          
    files</DIV></LI></UL>
<P><STRONG>Context Menus</STRONG></P>
<UL>
  <LI>
  <DIV>          
    File List View - Has the option to open and close specific files
</DIV>
<LI>
<DIV>
Row Header - Has the option to Go To and to copy and paste a row's data
</DIV>

</LI></UL>

  <P>      The following shortcuts are 
also available for these items:</P>
  <P>
<TABLE style="HEIGHT: 167px; WIDTH: 752px; BORDER-COLLAPSE: 
collapse" borderColor=#000000 cellSpacing=0 cellPadding=2 width="752" border=1>
  
  <TR>
    <TD width="25%"><STRONG>&nbsp;Shortcut</STRONG></TD>
    <TD width="25%"><STRONG>&nbsp;Action</STRONG></TD></TR>
  <TR>
    <TD width="50%">
      <P>Control + S</P></TD>
    <TD width="50%">&nbsp;Save File</TD></TR>
<TR>
    <TD width="50%">
      <P>Control + F12</P></TD>
    <TD width="50%">&nbsp;Save As</TD></TR>
<TR>
    <TD width="50%">
      <P>Control + G</P></TD>
    <TD width="50%">&nbsp;Go To</TD></TR><TR>
    <TD width="50%">
      <P>Control + Shift + S</P></TD>
    <TD width="50%">&nbsp;Save All</TD></TR><TR>
    <TD width="50%">
      <P>Control + F</P></TD>
    <TD width="50%">&nbsp;Find</TD></TR><TR>
    <TD width="50%">
      <P>Control + H</P></TD>
    <TD width="50%">&nbsp;Replace</TD></TR><TR>
    <TD width="50%">
      <P>Control + R</P></TD>
    <TD width="50%">&nbsp;Reload</TD></TR><TR>
    <TD width="50%">
      <P>Control + W</P></TD>
    <TD width="50%">&nbsp;Close</TD></TR>
<TR>
    <TD width="50%">
      <P>Control + Shift + W</P></TD>
    <TD width="50%">&nbsp;Close All</TD></TR>
<TR>
    <TD width="50%">
      <P>Control + Z</P></TD>
    <TD width="50%">&nbsp;Undo</TD></TR><TR>
    <TD width="50%">
      <P>Control + Y</P></TD>
    <TD width="50%">&nbsp;Redo</TD></TR>
<TR>
    <TD width="50%">
      <P>Control + N</P></TD>
    <TD width="50%">&nbsp;New Record</TD></TR>
<TR>
    <TD width="50%">
      <P>Control + I</P></TD>
    <TD width="50%">&nbsp;Insert Record</TD></TR>


</TABLE>          
    </P><BR> <BR><BR>

<HR>

<P></FONT></P>
<P align=right><A href="Introduction.htm" title="Previous"><img id="winchm_template_prev" alt="Previous" src="Main%20Screen/btn_prev_n.gif" border="0"></a>&nbsp;<A href="FindReplace.htm" title="Next"><img id="winchm_template_next" alt="Next" src="Main%20Screen/btn_next_n.gif" border="0"></a></P>
</body>
</html>
