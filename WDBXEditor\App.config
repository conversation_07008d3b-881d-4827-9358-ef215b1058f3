﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="WDBXEditor.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
  </startup>
  <appSettings>
    <add key="EnableWindowsFormsHighDpiAutoResizing" value="true" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <userSettings>
    <WDBXEditor.Properties.Settings>
      <setting name="Host" serializeAs="String">
        <value />
      </setting>
      <setting name="Port" serializeAs="String">
        <value>3306</value>
      </setting>
      <setting name="User" serializeAs="String">
        <value />
      </setting>
      <setting name="Password" serializeAs="String">
        <value />
      </setting>
      <setting name="ReleaseURL" serializeAs="String">
        <value>https://github.com/WowDevTools/WDBXEditor/releases</value>
      </setting>
      <setting name="ReleaseAPI" serializeAs="String">
        <value>http://api.github.com/repos/WowDevTools/WDBXEditor/releases/latest</value>
      </setting>
      <setting name="UserAgent" serializeAs="String">
        <value>Mozilla/4.0 (Compatible; Windows NT 5.1; MSIE 6.0) (compatible; MSIE 6.0; Windows NT 5.1; WDBXEditor</value>
      </setting>
      <setting name="RecentMPQ" serializeAs="String">
        <value />
      </setting>
      <setting name="RecentCASC" serializeAs="String">
        <value />
      </setting>
    </WDBXEditor.Properties.Settings>
  </userSettings>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
  <system.data>
    <DbProviderFactories>
      <remove invariant="MySql.Data.MySqlClient" />
      <add name="MySQL Data Provider" invariant="MySql.Data.MySqlClient" description=".Net Framework Data Provider for MySQL" type="MySql.Data.MySqlClient.MySqlClientFactory, MySql.Data, Version=*******, Culture=neutral, PublicKeyToken=c5687fc88969c44d" />
    </DbProviderFactories>
  </system.data>
</configuration>